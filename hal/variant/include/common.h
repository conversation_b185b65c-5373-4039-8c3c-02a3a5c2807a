#pragma once

#include <com.autolink.variant.pb.h>

#include <iomanip>
#include <iostream>
#include <sstream>
#include <string>
#include <variant>
#include <vector>

#include "variant_log.h"

using namespace autolink::platform::variant;
typedef struct ErrInfo_ {
  pb::ErrorCode code;
  std::string msg;
} ErrInfo;

static const char *toString(pb::ValueType type) {
  switch (type) {
    case pb::ValueType::VALUE_TYPE_INT32:
      return "int32";
    case pb::ValueType::VALUE_TYPE_INT32_VEC:
      return "int32_vec";
    case pb::ValueType::VALUE_TYPE_INT64:
      return "int64";
    case pb::ValueType::VALUE_TYPE_INT64_VEC:
      return "int64_vec";
    case pb::ValueType::VALUE_TYPE_FLOAT:
      return "float";
    case pb::ValueType::VALUE_TYPE_FLOAT_VEC:
      return "float_vec";
    case pb::ValueType::VALUE_TYPE_DOUBLE:
      return "double";
    case pb::ValueType::VALUE_TYPE_DOUBLE_VEC:
      return "double_vec";
    case pb::ValueType::VALUE_TYPE_STRING:
      return "string";
    case pb::ValueType::VALUE_TYPE_STRING_VEC:
      return "string_vec";
    case pb::ValueType::VALUE_TYPE_BYTES:
      return "bytes";
    default:
      break;
  }
  return "unknown";
}

static const std::string toString(const pb::VariantValue &value) {
  std::stringstream ss;
  ss << "type: ";
  if (value.has_type()) {
    ss << toString(value.type());
  } else {
    ss << "unknown";
  }
  ss << ",value: ";
  if (value.has_int32_array()) {
    for (const auto &item : value.int32_array().values()) {
      ss << " " << item;
    }
    return ss.str();
  }

  if (value.has_int64_array()) {
    for (const auto &item : value.int64_array().values()) {
      ss << " " << item;
    }
    return ss.str();
  }

  if (value.has_float_array()) {
    for (const auto &item : value.float_array().values()) {
      ss << " " << item;
    }
    return ss.str();
  }

  if (value.has_double_array()) {
    for (const auto &item : value.double_array().values()) {
      ss << " " << item;
    }
    return ss.str();
  }

  if (value.has_string_array()) {
    for (const auto &item : value.string_array().values()) {
      ss << " " << item << std::endl;
    }
    return ss.str();
  }

  if (value.has_bytes_value()) {
    int count = 0;
    for (const auto &item : value.bytes_value().value()) {
      ss << " " << std::hex << std::setw(2) << std::setfill('0')
         << (int32_t)(item & 0xFF);
      if (++count % 20 == 0) {
        ss << "    -> " << std::dec << count / 20 << std::endl;
      }
    }
    return ss.str();
  }
  return ss.str();
}

static void dumpValue(const std::string &key, const pb::VariantValue &value) {
  std::stringstream ss;
  ss << "dump value for " << key << ":" << std::endl;
  if (value.has_int32_array()) {
    ss << "int32 values: " << std::endl;
    for (const auto &item : value.int32_array().values()) {
      ss << " " << item;
    }
    ss << std::endl;
  }

  if (value.has_int64_array()) {
    ss << "int64 values: " << std::endl;
    for (const auto &item : value.int64_array().values()) {
      ss << " " << item;
    }
    ss << std::endl;
  }

  if (value.has_float_array()) {
    ss << "float values: " << std::endl;
    for (const auto &item : value.float_array().values()) {
      ss << " " << item;
    }
    ss << std::endl;
  }

  if (value.has_double_array()) {
    ss << "double values: " << std::endl;
    for (const auto &item : value.double_array().values()) {
      ss << " " << item;
    }
    ss << std::endl;
  }

  if (value.has_string_array()) {
    ss << "string values: " << std::endl;
    for (const auto &item : value.string_array().values()) {
      ss << " " << item << std::endl;
    }
    ss << std::endl;
  }

  if (value.has_bytes_value()) {
    ss << "bytes value: " << std::endl;
    int count = 0;
    for (const auto &item : value.bytes_value().value()) {
      ss << " " << std::hex << std::setw(2) << std::setfill('0')
         << (int32_t)(item & 0xFF);
      if (++count % 20 == 0) {
        ss << "    -> " << std::dec << count / 20 << std::endl;
      }
    }
    ss << std::endl;
  }
  ALOGD("%s", ss.str().c_str());
}