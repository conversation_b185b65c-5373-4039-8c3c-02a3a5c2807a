#pragma once
#include <com.autolink.variant.pb.h>

#include <map>
#include <set>
#include <string>

using autolink::platform::variant::pb::VariantClient;

typedef struct VariantTransportCfg {
  bool persist;
  std::set<VariantClient> clients;

} VariantTransportCfg_t;

/**
 * @brief 数据更新后，需要确切知道fdbus对端处理结果的属性配置在这里
 */
static std::map<std::string, VariantTransportCfg_t> kVariantTransportCfgs = {
    {"ro.vendor.autolink.car.carcfg",
     {true, {VariantClient::ANDROID_VARIANT, VariantClient::QNX_ADAS}}},
    {"CFG_DID_F011",
     {true, {VariantClient::ANDROID_VARIANT, VariantClient::QNX_ADAS}}},
};

/**
 * @brief
 * 判断对应的key的值是否是需要可靠的传输到fdbus对端，目前仅有整车配置字有该需求
 * @param key
 * @return
 */
static bool is_critical_msg(const std::string key) {
  return kVariantTransportCfgs.count(key) > 0;
}