#pragma once

#include <cstdint>
#include <iostream>
#include <vector>

class CRC32 {
 private:
  uint32_t table[256];

  // 私有构造函数，防止外部实例化
  CRC32() { generateTable(); }

  void generateTable() {
    const uint32_t polynomial = 0x04C11DB7;
    for (uint32_t i = 0; i < 256; i++) {
      uint32_t c = i << 24;
      for (int j = 0; j < 8; j++) {
        if (c & 0x80000000) {
          c = (c << 1) ^ polynomial;
        } else {
          c = c << 1;
        }
      }
      table[i] = c;
    }
  }

 public:
  // 禁止拷贝和赋值
  CRC32(const CRC32&) = delete;
  CRC32& operator=(const CRC32&) = delete;

  // 获取单例实例，线程安全
  static CRC32& getInstance() {
    static CRC32 instance;
    return instance;
  }

  uint32_t calculate(const uint8_t* data, size_t length) const {
    uint32_t crc = 0xFFFFFFFF;
    for (size_t i = 0; i < length; i++) {
      uint8_t index = (crc >> 24) ^ data[i];
      crc = (crc << 8) ^ table[index];
    }
    return crc ^ 0xFFFFFFFF;
  }
};