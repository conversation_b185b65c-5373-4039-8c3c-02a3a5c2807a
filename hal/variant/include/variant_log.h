#pragma once
#include <stdio.h>
// #define USE_STDIO

#ifdef ANDROID
#include <log/log.h>
#endif

#ifdef __QNXNTO__
#ifndef LOG_TAG
#define LOG_TAG ""
#endif
#include <log.h>
#ifdef USE_STDIO
#define ALOGV(fmt, ...)                                              \
  do {                                                               \
    printf("[V]%s:%s  " fmt "\n", LOG_TAG, __func__, ##__VA_ARGS__); \
  } while (0)
#define ALOGD(fmt, ...)                                             \
  do {                                                              \
    printf("[D]%s:%s " fmt "\n", LOG_TAG, __func__, ##__VA_ARGS__); \
  } while (0)
#define ALOGI(fmt, ...)                                             \
  do {                                                              \
    printf("[I]%s:%s " fmt "\n", LOG_TAG, __func__, ##__VA_ARGS__); \
  } while (0)
#define ALOGW(fmt, ...)                                             \
  do {                                                              \
    printf("[W]%s:%s " fmt "\n", LOG_TAG, __func__, ##__VA_ARGS__); \
  } while (0)
#define ALOGE(fmt, ...)                                             \
  do {                                                              \
    printf("[E]%s:%s " fmt "\n", LOG_TAG, __func__, ##__VA_ARGS__); \
  } while (0)
#else
#define ALOGV LOG_TAG_DEBUG
#define ALOGD LOG_TAG_DEBUG
#define ALOGI LOG_TAG_INFO
#define ALOGW LOG_TAG_WARNING
#define ALOGE LOG_TAG_ERROR
#endif

#endif