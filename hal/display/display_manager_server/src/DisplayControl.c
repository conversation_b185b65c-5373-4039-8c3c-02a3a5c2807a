#include <fcntl.h>
#include <unistd.h>
#include <pthread.h>


#include "DisplayControl.h"
#include "display_log.h"


/**
 *根据屏幕displayId来设置屏幕亮度
 *@param displayId:屏幕id
 *@param level:设置屏幕亮度的值 范围0~100
 *@return 0:DISPCTRL_SUCCESS -1:DISPCTRL_ERROR_INVALID_DISPLAY_ID -2:DISPCTRL_ERROR_INVALID_PARAMETER -3:DISPCTRL_ERROR_INVALID_PARAMETER
 */
 int disp_dispctrl_set_display_brightness(int displayId, int level) {
    if (level < 0 || level > 100) {
        return DISPCTRL_ERROR_UNSUPPORTED_OPERATION;
    }
    int res = dispctrl_set_display_backlight_brightness(displayId, level);
    DISPLAYCTRL_SLOGI("Set display brightness level=%d in displayId=%d,res=%d",level,displayId,res);
    return res;

 }

/**
 *根据屏幕displayId来获取屏幕亮度值 合法范围0~100
 *@param displayId:屏幕id
 *@return 0:DISPCTRL_SUCCESS -1:DISPCTRL_ERROR_INVALID_DISPLAY_ID -2:DISPCTRL_ERROR_INVALID_PARAMETER -3:DISPCTRL_ERROR_INVALID_PARAMETER
 */
 int disp_dispctrl_get_display_backlight_brightness(int displayId) {
    int level = dispctrl_get_display_backlight_brightness(displayId);
    //临时返回100 待BSP层提供接口
    DISPLAYCTRL_SLOGI("Get display brightness level=%d in displayId=%d",level,displayId);
    return level;

 }

/**
 *根据屏幕displayId来设置屏幕背光状态 合法范围0和1
 *@param displayId:屏幕id
 *@param powerstate:背光灭(0) 背光亮(1)
 *@return 0:DISPCTRL_SUCCESS -1:DISPCTRL_ERROR_INVALID_DISPLAY_ID -2:DISPCTRL_ERROR_INVALID_PARAMETER -3:DISPCTRL_ERROR_INVALID_PARAMETER
 */
 int disp_dispctrl_set_display_powerstate(int displayId, int powerstate) {
    int res = dispctrl_set_display_power_state(displayId,powerstate);
    DISPLAYCTRL_SLOGI("Set display powerstate powerstate=%d in displayId=%d,res=%d",powerstate,displayId,res);
    return res;

 }

/**
 *根据屏幕displayId来获取屏幕背光状态 返回结果合法范围0和1
 *@param displayId:屏幕id
 *@return 0:DISPCTRL_SUCCESS -1:DISPCTRL_ERROR_INVALID_DISPLAY_ID -2:DISPCTRL_ERROR_INVALID_PARAMETER -3:DISPCTRL_ERROR_INVALID_PARAMETER
 */
 int disp_dispctrl_get_display_power_state(int displayId) {
    int powerstate = dispctrl_get_display_power_state(displayId);
    DISPLAYCTRL_SLOGI("Get display powerstate=%d in displayId=%d",powerstate,displayId);
    return powerstate;

 }

/**
 *根据屏幕displayId来获取屏幕软件版本信息
 *@param displayId:屏幕id
 *@param version:版本信息 传入的参数地址容量至少5个字节 为了安全起见建议长度16字节
 *@return 0:DISPCTRL_SUCCESS -1:DISPCTRL_ERROR_INVALID_DISPLAY_ID -2:DISPCTRL_ERROR_INVALID_PARAMETER -3:DISPCTRL_ERROR_INVALID_PARAMETER
 */
 int disp_dispctrl_get_display_software_version(int displayId,char* version, size_t versionSize) {
    int res = dispctrl_get_display_hardware_version(displayId,version,versionSize);
    DISPLAYCTRL_SLOGI("Get sw version message,res=%d",res);
    return res;

 }

/**
 * *根据屏幕displayId来获取屏幕硬件版本信息
 *@param displayId:屏幕id
 *@param version:版本信息 传入的参数地址容量至少5个字节 为了安全起见建议长度16字节
 *@return 0:DISPCTRL_SUCCESS -1:DISPCTRL_ERROR_INVALID_DISPLAY_ID -2:DISPCTRL_ERROR_INVALID_PARAMETER -3:DISPCTRL_ERROR_INVALID_PARAMETER
 */
int disp_dispctrl_get_display_hardware_version(int displayId, char* version, size_t versionSize) {
    int res = dispctrl_get_display_hardware_version(displayId,version,versionSize);
    DISPLAYCTRL_SLOGI("Get hw version message,res=%d",res);
    return res;

}

/**
 * *根据屏幕displayId来获取屏幕温度
 *@param displayId:屏幕id
 *@return temp:屏幕温度
 *@return 0:DISPCTRL_SUCCESS -1:DISPCTRL_ERROR_INVALID_DISPLAY_ID -2:DISPCTRL_ERROR_INVALID_PARAMETER -3:DISPCTRL_ERROR_INVALID_PARAMETER
 */
 int disp_dispctrl_get_display_temperature(int displayId) {
     int temp = dispctrl_get_display_temperature(displayId);
     return temp;

 }


