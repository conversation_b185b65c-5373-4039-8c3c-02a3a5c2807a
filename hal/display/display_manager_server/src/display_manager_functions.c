
#include "display_manager_functions.h"
#include "display_ctrl_api.h"


pthread_mutex_t send_queue_mutex = PTHREAD_MUTEX_INITIALIZER;
int exit_flag = 0;


Queue send_queue;
int hab_handle;
int gDisplayid = 0;

void queue_init(Queue* queue) {
    queue->front = NULL;
    queue->rear = NULL;
    queue->size = 0;
}

int queue_is_empty(Queue* queue) {
    return queue->size == 0;
}

void queue_enqueue(Queue* queue, DisplayData data) {
    QueueNode* newNode = (QueueNode*)malloc(sizeof(QueueNode));
    if (newNode == NULL) {
        DISPLAYCTRL_SLOGE("Failed to allocate memory for queue node");
        return;
    }
    newNode->data = data;
    newNode->next = NULL;

    if (queue_is_empty(queue)) {
        queue->front = newNode;
        queue->rear = newNode;
    } else {
        queue->rear->next = newNode;
        queue->rear = newNode;
    }
    queue->size++;
}

int queue_dequeue(Queue* queue, DisplayData* data) {
    if (queue_is_empty(queue)) {
        return -1;
    }
    QueueNode* temp = queue->front;
    *data = temp->data;
    queue->front = queue->front->next;
    if (queue->front == NULL) {
        queue->rear = NULL;
    }
    free(temp);
    queue->size--;
    return 0;
}

void * temp_monitor_func(void *arg){
	int ret = 0;
	int screen_celsius_degrees = 0;
	uint32_t size_bytes = sizeof(DisplayData);
    uint32_t flags = 0;

	
	while (!exit_flag) {

		screen_celsius_degrees = dispctrl_get_display_temperature(gDisplayid);
		
		if(DISPCTRL_ERROR_INVALID_DISPLAY_ID == screen_celsius_degrees)
		{
			 DISPLAYCTRL_SLOGI("GET_TEMP screen_celsius_degrees fail\n");
		}
		if(screen_celsius_degrees > 80)
		{
			DISPLAYCTRL_SLOGI("GET_TEMP screen_celsius_degrees %d\n", screen_celsius_degrees);
			send_callback_data.cmd = CMD_GET_DISP_TEMP;
			send_callback_data.displayid = gDisplayid;
			send_callback_data.display_temp = screen_celsius_degrees;
			ret = habmm_socket_send(hab_handle, (void *)&send_callback_data, size_bytes, flags);
			if (ret == 0) {
	                DISPLAYCTRL_SLOGI("temp_monitor_func Data sent successfully for displayid=%d, display_temp=%d\n", send_callback_data.displayid, send_callback_data.display_temp);
	        } else {
	                DISPLAYCTRL_SLOGE("temp_monitor_func Error sending data for displayid=%d, display_temp=%d: %d\n", send_callback_data.displayid, send_callback_data.display_temp, ret);
	        }
		} 		 
       
		sleep(5);
	}

}

void* send_data(void* arg) {
    
    uint32_t size_bytes = sizeof(DisplayData);
    uint32_t flags = 0;

    while (!exit_flag) {
        sem_wait(&send_queue_sem);
        pthread_mutex_lock(&send_queue_mutex);
        if (!queue_is_empty(&send_queue)) {

            if (queue_dequeue(&send_queue, &send_callback_data) == 0) {
				DISPLAYCTRL_SLOGI("Data sent successfully for displayid=%d, cmd=%d\n", send_callback_data.displayid, send_callback_data.cmd);
                int ret = habmm_socket_send(hab_handle, (void *)&send_callback_data, size_bytes, flags);
                if (ret == 0) {
                    DISPLAYCTRL_SLOGI("Data sent successfully for displayid=%d, cmd=%d\n", send_callback_data.displayid, send_callback_data.cmd);
                } else {
                    DISPLAYCTRL_SLOGE("Error sending data for displayid=%d, cmd=%d: %d\n", send_callback_data.displayid, send_callback_data.cmd, ret);
                }
            }
        }
        pthread_mutex_unlock(&send_queue_mutex);
       // sleep(1);
    }
    return NULL;
}


void* receive_data(void* arg) {
    DisplayData data;
    uint32_t flags = 0x00;
	int hardware_displayid;
	int curbrightness = 0;
	int curpowerstate = 0;
	int curlcdtemp = 0;
	DISPLAYCTRL_SLOGI("Received receive_data\n");
    while (!exit_flag) {
        uint32_t size = DISPLAY_RPC_HAB_BUF_MAX_LEN;
        memset((char *)&data,  0x00, sizeof(DisplayData));
		memset((char *)&send_callback_data,  0x00, sizeof(DisplayData));
        int ret = habmm_socket_recv(hab_handle, (void *)&data, &size, (uint32_t)HYPERVISOR_NO_TIMEOUT_VAL, flags);
        /* check package integrity */
        if (ret == 0) {
            switch (data.cmd) {
                case CMD_SET_BRIGHTNESS:
                    DISPLAYCTRL_SLOGI("Received command: SET_BRIGHTNESS, displayid=%d, brightness=%d\n",
                           data.displayid, data.brightness);
                    // Map the displayid at the protocol layer to the driver layer id (1→0)
                    hardware_displayid = data.displayid - 1;
					gDisplayid = hardware_displayid;
                    int brightnessResult = dispctrl_set_display_backlight_brightness(hardware_displayid, data.brightness);
                    if (brightnessResult == 0) {
                     DISPLAYCTRL_SLOGI("SET_BRIGHTNESS succeeded for displayid=%d\n", data.displayid);
                    } else {
                     DISPLAYCTRL_SLOGE("SET_BRIGHTNESS failed for displayid=%d\n", data.displayid);
                    }
					send_callback_data.cmd = CMD_SET_BRIGHTNESS;
                    break;
                case CMD_SET_POWER_STATE:
                    DISPLAYCTRL_SLOGI("Received command: SET_POWER_STATE, displayid=%d, power_state=%d\n",
                           data.displayid, data.power_state);
					hardware_displayid = data.displayid - 1;
                    int powerStateResult = dispctrl_set_display_power_state(hardware_displayid, data.power_state);
                    if (powerStateResult == DISPCTRL_SUCCESS) {
                     DISPLAYCTRL_SLOGI("SET_BRIGHTNESS succeeded for displayid=%d\n", hardware_displayid);
                    } else {
                     DISPLAYCTRL_SLOGE("SET_BRIGHTNESS failed for displayid=%d\n", hardware_displayid);
                    }
					curpowerstate = data.power_state;
					send_callback_data.cmd = CMD_SET_POWER_STATE;
                    break;
                case CMD_SET_DAY_NIGHT_MODE:
                    DISPLAYCTRL_SLOGI("Received command: SET_DAY_NIGHT_MODE, displayid=%d, day_night_mode=%d\n",
                           data.displayid, data.day_night_mode);
                    break;
                case CMD_GET_DISPLAY_INFO:					
                    DISPLAYCTRL_SLOGI("Received command: GET_DISPLAY_INFO, displayid=%d, display_info=%d\n",
                           data.displayid, data.display_info);
					hardware_displayid = data.displayid - 1;
					curbrightness = dispctrl_get_display_backlight_brightness(hardware_displayid);
					curpowerstate = dispctrl_get_display_power_state(hardware_displayid);
					curlcdtemp = dispctrl_get_display_temperature(hardware_displayid);
					data.brightness = curbrightness;
					send_callback_data.cmd = CMD_GET_DISPLAY_INFO;
                    break;
                default:
                    DISPLAYCTRL_SLOGI("Received unknown command: %d\n", data.cmd);
                    break;
            }
			
			//send_callback_data.cmd = CMD_SEND_RETINFO;
			send_callback_data.display_temp = curlcdtemp;
			send_callback_data.displayid = data.displayid;
			send_callback_data.power_state = curpowerstate;
			send_callback_data.brightness = data.brightness;
			 DISPLAYCTRL_SLOGI(", curlcdtemp=%d, curpowerstate=%d, data.brightness=%d\n",
                           curlcdtemp, curpowerstate, data.brightness);
			queue_enqueue(&send_queue, send_callback_data);
			sem_post(&send_queue_sem);
						
        } else {
            if (ret < 0) {
				habmm_socket_close(hab_handle);
				sleep(30);
				if (habmm_socket_open(&hab_handle, MM_DISP_5, 0xffffffff, 0) != 0) {
					DISPLAYCTRL_SLOGE("Failed to open HAB channel.\n");
				}
				DISPLAYCTRL_SLOGE("Failed to recv habmm socket");
				//      break;
            }
        }
    }
    return NULL;
}