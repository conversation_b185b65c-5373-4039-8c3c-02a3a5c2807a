#include "display_manager_functions.h"
#include <habmm.h>
#include "DisplayServiceFdbus.h"


slog2_buffer_set_config_t buffer_cfg = {
    .num_buffers = 1,
    .buffer_set_name = "displayctrl",
    .verbosity_level = _SLOG_INFO,
    .buffer_config = {
        {.buffer_name = "displayctrl_buffer", .num_pages = 4},
    }
};

slog2_buffer_t displayctrl_buffer;


pthread_t recv_thread;
pthread_t send_thread;
pthread_t temp_monitor_thread;

static sigset_t g_sigset;

static const int exceptsigs[] = {
    SIGCHLD, SIGIO, SIGURG, SIGWINCH,
    SIGTTIN, SIGTTOU, SIGCONT, SIGSEGV,
    -1,
};


void init_send_queue() {
    queue_init(&send_queue);
}

using namespace displayservice;

int main() {
    int                      iStatus         = 0x00;
    int rc = -1;
    struct hab_socket_info socket_info;
    uint32_t mm_ip_id = MM_DISP_5;
    uint32_t timeout = 0xFFFFFFFF;
    uint32_t flags = 0;
	DisplayServiceFdbus * m_DisplayServiceFdbus = new DisplayServiceFdbus();
   /* init slog buffer */
    if (-1 == slog2_register(&buffer_cfg, &displayctrl_buffer, 0))
    {
        printf("Error registering dv_slogger2 buffer!\n");
        return -1;
    }
    sem_init(&send_queue_sem, 0, 0);
    queue_init(&send_queue);
	
	///*
	if (m_DisplayServiceFdbus != nullptr) {
        m_DisplayServiceFdbus->init();
    }
 //   */
    
	DISPLAYCTRL_SLOGI("enter main 00\n");
    sigfillset(&g_sigset);
	
    for (int i = 0; exceptsigs[i] != -1; i++){
        sigdelset(&g_sigset, exceptsigs[i]);
    }
	
    pthread_sigmask(SIG_BLOCK, &g_sigset, NULL);

    if (habmm_socket_open(&hab_handle, mm_ip_id, timeout, flags) != 0) {
        DISPLAYCTRL_SLOGE("Failed to open HAB channel.\n");
        return -1;
    }
	DISPLAYCTRL_SLOGI("enter main 01\n");
	fflush(stdout);

    rc = habmm_socket_query(hab_handle, &socket_info, 0);
    if (rc != 0) {
        DISPLAYCTRL_SLOGE("habmm_socket_query failed, rc = %d\n", rc);
        habmm_socket_close(hab_handle);
        return rc;
    }

    DISPLAYCTRL_SLOGI("fd = %d\n", hab_handle);
    DISPLAYCTRL_SLOGI("socket_info.vmid_remote = %d\n", socket_info.vmid_remote);
    DISPLAYCTRL_SLOGI("socket_info.vmid_local = %d\n", socket_info.vmid_local);

	/*
	snprintf(thread_name, sizeof(thread_name), "signal_thrd");
    rc = OSAL_ThreadCreate(QCARCAM_THRD_PRIO, &signal_thread, 0, 0, thread_name, &signal_thread_handle);
    if (rc)
    {
	    DISPLAYCTRL_SLOGE("OSAL_ThreadCreate failed : %s", thread_name);
	    goto fail;
    }
    */

	if (pthread_create(&temp_monitor_thread, NULL, temp_monitor_func, NULL) != 0) {
        DISPLAYCTRL_SLOGE("Failed to create receive thread.\n");
        habmm_socket_close(hab_handle);
        goto fail;
    }
	DISPLAYCTRL_SLOGI("enter main 02\n");
    if (pthread_create(&recv_thread, NULL, receive_data, NULL) != 0) {
        DISPLAYCTRL_SLOGE("Failed to create receive thread.\n");
        habmm_socket_close(hab_handle);
        goto fail;
    }

    if (pthread_create(&send_thread, NULL, send_data, NULL) != 0) {
        DISPLAYCTRL_SLOGE("Failed to create send thread.\n");
        habmm_socket_close(hab_handle);
        goto fail;
    }
	
    //exit_flag = 1;
fail:
    pthread_join(recv_thread, NULL);
    pthread_join(send_thread, NULL);
	pthread_join(temp_monitor_thread, NULL);
	
    habmm_socket_close(hab_handle);
	delete m_DisplayServiceFdbus;
    return iStatus;
}
