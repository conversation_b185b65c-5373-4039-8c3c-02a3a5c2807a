/******************************************************************************
/							   Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#include <errno.h>
#include <fdbus/fdbus.h>
#include <fdbus/CFdbProtoMsgBuilder.h>

#include "DisplayControl.h"
#include "DisplayFDBusServer.h"
#include "display_log.h"

namespace displayservice
{
	DisplayFDBusServer::DisplayFDBusServer(const char *name, CBaseWorker *worker)
		  : CBaseServer(name, worker)
	{
		DISPLAYCTRL_SLOGI(" DisplayFDBusServer new instance.");
	}

	DisplayFDBusServer::~DisplayFDBusServer() {
		DISPLAYCTRL_SLOGI(" Destroy DisplayFDBusServer instance.");
	}

	void DisplayFDBusServer::notifyDispControlSessionEvent(uint32_t code,const DispControlPropValue& PropValue) {
		CFdbProtoMsgBuilder builder(PropValue);
		broadcast(code,builder);
	}

	void DisplayFDBusServer::onOnline(const CFdbOnlineInfo &info) {
		DISPLAYCTRL_SLOGI(" DisplayFDBusServer server session up: %d\n", info.mSid);
	}

	void DisplayFDBusServer::onOffline(const CFdbOnlineInfo &info) {
		DISPLAYCTRL_SLOGI(" DisplayFDBusServer server session shutdown: %d\n", info.mSid);
	}

	void DisplayFDBusServer::onInvoke(CBaseJob::Ptr &msg_ref) {
		auto msg = castToMessage<CBaseMessage *>(msg_ref);

		DISPLAYCTRL_SLOGI(" DisplayFDBusServer::onInvoke,msg->code=%d\n",msg->code());
		switch (msg->code()) {
			case PowerMGRMsgId::DISP_CONTROL_MSG: {
				DispControlPropValue session;
				CFdbProtoMsgParser parser(session);
				if (!msg->deserialize(parser)) {
					msg->status(msg_ref, FdbMsgStatusCode::FDB_ST_MSG_DECODE_FAIL, "Fail to decode request!");
					return;
				}

				talkWithILCM(session);
				CFdbProtoMsgBuilder builder(session);
				auto to_be_release = msg->ownBuffer();
				msg->reply(msg_ref, builder);
				msg->releaseBuffer(to_be_release);
			}
				break;
			default:
				break;
		}
	}

  

	void DisplayFDBusServer::talkWithILCM(DispControlPropValue PropValue) {
		int eventId = PropValue.eventid();
		DISPLAYCTRL_SLOGI(" talkWithILCM eventId=%d\n",eventId);
		switch(eventId) {
			 case DispControlEventId::DISP_BRIGHTNESS_SET: {//设置屏幕亮度
			 		if(PropValue.has_brightnessvalue()) {
			 		int displayId,level;
                    DispBrightnessValue brightnessSet = PropValue.brightnessvalue();
                    displayId =  brightnessSet.displayid();
                    level = brightnessSet.level();
                    DISPLAYCTRL_SLOGI("talkWithILCM brightness set displayId=%d,level=%d\n",displayId,level);
                    disp_dispctrl_set_display_brightness(displayId,level);

                    //紧接着返回屏幕亮度信息
                    level = disp_dispctrl_get_display_backlight_brightness(displayId);
                    DispControlPropValue session;
                    session.set_eventid(DispControlEventId::DISP_BRIGHTNESS_NOTICE);
                    session.mutable_brightnessvalue()->set_displayid(displayId);
                    session.mutable_brightnessvalue()->set_level(level);
                    notifyDispControlSessionEvent(PowerMGRMsgId::DISP_CONTROL_MSG,session);
	                } else {
	                   DISPLAYCTRL_SLOGI("talkWithILCM has_brightnessvalue()=false\n");
	                }
				}
				 break;
			 case DispControlEventId::DISP_BRIGHTNESS_GET: {//获取屏幕亮度
				 if(PropValue.has_dispidbrightnessget()) {
					 DispControlPropValue session;
					 int displayId = PropValue.dispidbrightnessget();
					 int level = disp_dispctrl_get_display_backlight_brightness(displayId);
					 DISPLAYCTRL_SLOGI("talkWithILCM brightness get displayId=%d,level=%d\n",displayId, level);
					 session.set_eventid(DispControlEventId::DISP_BRIGHTNESS_NOTICE);
					 session.mutable_brightnessvalue()->set_displayid(displayId);
					 session.mutable_brightnessvalue()->set_level(level);
					 notifyDispControlSessionEvent(PowerMGRMsgId::DISP_CONTROL_MSG,session);
				 } else {
					 DISPLAYCTRL_SLOGI("talkWithILCM has_dispidbrightnessget()=false\n");
				 }
			 }
				 break;
			 case DispControlEventId::DISP_POWERSTATE_SET: {//设置屏幕和背光亮灭
				 if(PropValue.has_powerstate()) {
					 int displayId;
					 DispPowerStateValue powerState = PropValue.powerstate();
					 displayId = powerState.displayid();
					 bool powerOn = powerState.poweron();
					// /*
					 DISPLAYCTRL_SLOGI("talkWithILCM powerstate set displayId=%d,powerOn=%s\n",displayId,powerOn ? "true" : "false");
					 disp_dispctrl_set_display_powerstate(displayId, (powerOn ? DISPCTRL_POWERSTATE_ON : DISPCTRL_POWERSTATE_OFF));

					 //紧接着返回状态
					 int powerOnOff = disp_dispctrl_get_display_power_state(displayId);
					//*/
					 DispControlPropValue session;
					 session.set_eventid(DispControlEventId::DISP_POWERSTATE_NOTICE);
					 session.mutable_powerstate()->set_displayid(displayId);
					 session.mutable_powerstate()->set_poweron(((powerOnOff == 1) ? true : false));
					 notifyDispControlSessionEvent(PowerMGRMsgId::DISP_CONTROL_MSG,session);
				 } else {
					 DISPLAYCTRL_SLOGI("talkWithILCM has_powerstate()=false\n");
				 }
			 }
				 break;
			 case DispControlEventId::DISP_POWERSTATE_GET: {//获取背光亮灭情况
				 if (PropValue.has_dispidpowerstateget()) {
					 DispControlPropValue session;
					int displayId = PropValue.dispidpowerstateget();
					int powerOnOff = disp_dispctrl_get_display_power_state(displayId);
					DISPLAYCTRL_SLOGI("talkWithILCM powerstate get displayId=%d,powerOnOff=%d\n",displayId,powerOnOff);
					 session.set_eventid(DispControlEventId::DISP_POWERSTATE_NOTICE);
					 session.mutable_powerstate()->set_displayid(displayId);
					 session.mutable_powerstate()->set_poweron(((powerOnOff == 1) ? true : false));
					 notifyDispControlSessionEvent(PowerMGRMsgId::DISP_CONTROL_MSG,session);
				 } else {
					 DISPLAYCTRL_SLOGI("talkWithILCM has_dispidpowerstateget()=false\n");
				 }
			 }
				break;
			default:
				break;
				
		}
	}
} //namespace powerservice