/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <fdbus/fdbus.h>
#include <fdbus/CFdbProtoMsgBuilder.h>

#include "DisplayServiceFdbus.h"
#include "display_log.h"

//#include "PowerDefine.h"
//#include "PowerLog.h"

namespace displayservice
{

using namespace ipc::fdbus;
static CBaseWorker main_worker("display_fdbus_callback_thread");

// class PowerServiceFdbus
DisplayServiceFdbus::DisplayServiceFdbus() {
    m_DisplayServer = nullptr;
}

DisplayServiceFdbus::~DisplayServiceFdbus() {
    if (m_DisplayServer != nullptr) {
        delete m_DisplayServer;
    }
    m_DisplayServer = nullptr;
}

void DisplayServiceFdbus::init() {
    std::string server_name("autolink_display_service");
    std::string url(FDB_URL_SVC);
    url += server_name;
    if(m_DisplayServer == nullptr) {
        /* start fdbus context thread */
        CFdbContext::enableLogger(true);
        FDB_CONTEXT->start();
        CBaseWorker *worker_ptr = &main_worker;
        /* start worker thread */
        worker_ptr->start();
        /* create servers and bind the address: svc://service_name */
        server_name += "_server";
        m_DisplayServer = new DisplayFDBusServer(server_name.c_str(), worker_ptr);
    }
    m_DisplayServer->bind(url.c_str());
    DISPLAYCTRL_SLOGI("xqh DisplayServiceFdbus bind begin.\n");
}
/*
void DisplayServiceFdbus::notifyPowerSessionEvent(uint32_t code,const PowerPropValue& value) {
    if (m_DisplayServer != nullptr) {
        m_DisplayServer->notifyPowerSessionEvent(code,value);
    }else{
        LOG_D("m_PowerServer is null.\n");
    }
}

void DisplayServiceFdbus::talkWithGateWayALSleepComplete() {
    if (m_DisplayServer != nullptr) {
        m_DisplayServer->talkWithGateWayALSleepComplete();
    }else{
        LOG_D("talkWithGateWayALSleepComplete,m_PowerServer is null.\n");
    }
}

void DisplayServiceFdbus::notifyPowerMGRSTRState(uint32_t code) {
    if (m_DisplayServer != nullptr) {
        m_DisplayServer->notifyPowerMGRSTRState(code);
    }else{
        LOG_D("notifyPowerMGRSTRState,m_PowerServer is null.\n");
    }
}
*/
} // namespace powerservice
