// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: autolink.powerclient.ext.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_autolink_2epowerclient_2eext_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_autolink_2epowerclient_2eext_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019005 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/message_lite.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_util.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_autolink_2epowerclient_2eext_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_autolink_2epowerclient_2eext_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[7]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
namespace autolink {
class DispBrightnessValue;
struct DispBrightnessValueDefaultTypeInternal;
extern DispBrightnessValueDefaultTypeInternal _DispBrightnessValue_default_instance_;
class DispControlPropValue;
struct DispControlPropValueDefaultTypeInternal;
extern DispControlPropValueDefaultTypeInternal _DispControlPropValue_default_instance_;
class DispHwVersionNotice;
struct DispHwVersionNoticeDefaultTypeInternal;
extern DispHwVersionNoticeDefaultTypeInternal _DispHwVersionNotice_default_instance_;
class DispPowerStateValue;
struct DispPowerStateValueDefaultTypeInternal;
extern DispPowerStateValueDefaultTypeInternal _DispPowerStateValue_default_instance_;
class DispSwVersionNotice;
struct DispSwVersionNoticeDefaultTypeInternal;
extern DispSwVersionNoticeDefaultTypeInternal _DispSwVersionNotice_default_instance_;
class ILCMControlClientProp;
struct ILCMControlClientPropDefaultTypeInternal;
extern ILCMControlClientPropDefaultTypeInternal _ILCMControlClientProp_default_instance_;
class ILCMSuspendPrepareAckDelay;
struct ILCMSuspendPrepareAckDelayDefaultTypeInternal;
extern ILCMSuspendPrepareAckDelayDefaultTypeInternal _ILCMSuspendPrepareAckDelay_default_instance_;
}  // namespace autolink
PROTOBUF_NAMESPACE_OPEN
template<> ::autolink::DispBrightnessValue* Arena::CreateMaybeMessage<::autolink::DispBrightnessValue>(Arena*);
template<> ::autolink::DispControlPropValue* Arena::CreateMaybeMessage<::autolink::DispControlPropValue>(Arena*);
template<> ::autolink::DispHwVersionNotice* Arena::CreateMaybeMessage<::autolink::DispHwVersionNotice>(Arena*);
template<> ::autolink::DispPowerStateValue* Arena::CreateMaybeMessage<::autolink::DispPowerStateValue>(Arena*);
template<> ::autolink::DispSwVersionNotice* Arena::CreateMaybeMessage<::autolink::DispSwVersionNotice>(Arena*);
template<> ::autolink::ILCMControlClientProp* Arena::CreateMaybeMessage<::autolink::ILCMControlClientProp>(Arena*);
template<> ::autolink::ILCMSuspendPrepareAckDelay* Arena::CreateMaybeMessage<::autolink::ILCMSuspendPrepareAckDelay>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace autolink {

enum PowerMGRMsgId : int {
  POWER_STR_REGISTER = 5,
  POWER_STR_SUSPEND_ACK_OK = 6,
  POWER_STR_SUSPEND_ACK_ERROR = 7,
  POWER_STR_SUSPEND_ACK_DELAY = 8,
  POWER_STR_RESUME_ACK_OK = 9,
  POWER_STR_RESUME_ACK_ERROR = 10,
  POWER_READY_MSG = 11,
  ILCM_RESP_READY_MSG = 12,
  PM_STATE_SHUTDOWN_PREPARE_NOTICE = 13,
  PM_STATE_SUSPEND_NOTICE = 14,
  PM_STATE_RESUME_NOTICE = 15,
  PM_STATE_RESUME_COMPLETE_NOTICE = 16,
  DISP_CONTROL_MSG = 17
};
bool PowerMGRMsgId_IsValid(int value);
constexpr PowerMGRMsgId PowerMGRMsgId_MIN = POWER_STR_REGISTER;
constexpr PowerMGRMsgId PowerMGRMsgId_MAX = DISP_CONTROL_MSG;
constexpr int PowerMGRMsgId_ARRAYSIZE = PowerMGRMsgId_MAX + 1;

const std::string& PowerMGRMsgId_Name(PowerMGRMsgId value);
template<typename T>
inline const std::string& PowerMGRMsgId_Name(T enum_t_value) {
  static_assert(::std::is_same<T, PowerMGRMsgId>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function PowerMGRMsgId_Name.");
  return PowerMGRMsgId_Name(static_cast<PowerMGRMsgId>(enum_t_value));
}
bool PowerMGRMsgId_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, PowerMGRMsgId* value);
enum DispControlEventId : int {
  DISP_BRIGHTNESS_SET = 100,
  DISP_BRIGHTNESS_GET = 101,
  DISP_BRIGHTNESS_NOTICE = 102,
  DISP_POWERSTATE_SET = 103,
  DISP_POWERSTATE_GET = 104,
  DISP_POWERSTATE_NOTICE = 105,
  DISP_HW_VERSION_GET = 106,
  DISP_HW_VERSION_NOTICE = 107,
  DISP_SW_VERSION_GET = 108,
  DISP_SW_VERSION_NOTICE = 109,
  DISP_ANIM_STATE_SET = 110
};
bool DispControlEventId_IsValid(int value);
constexpr DispControlEventId DispControlEventId_MIN = DISP_BRIGHTNESS_SET;
constexpr DispControlEventId DispControlEventId_MAX = DISP_ANIM_STATE_SET;
constexpr int DispControlEventId_ARRAYSIZE = DispControlEventId_MAX + 1;

const std::string& DispControlEventId_Name(DispControlEventId value);
template<typename T>
inline const std::string& DispControlEventId_Name(T enum_t_value) {
  static_assert(::std::is_same<T, DispControlEventId>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function DispControlEventId_Name.");
  return DispControlEventId_Name(static_cast<DispControlEventId>(enum_t_value));
}
bool DispControlEventId_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, DispControlEventId* value);
// ===================================================================

class DispBrightnessValue final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:autolink.DispBrightnessValue) */ {
 public:
  inline DispBrightnessValue() : DispBrightnessValue(nullptr) {}
  ~DispBrightnessValue() override;
  explicit constexpr DispBrightnessValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DispBrightnessValue(const DispBrightnessValue& from);
  DispBrightnessValue(DispBrightnessValue&& from) noexcept
    : DispBrightnessValue() {
    *this = ::std::move(from);
  }

  inline DispBrightnessValue& operator=(const DispBrightnessValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline DispBrightnessValue& operator=(DispBrightnessValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const DispBrightnessValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const DispBrightnessValue* internal_default_instance() {
    return reinterpret_cast<const DispBrightnessValue*>(
               &_DispBrightnessValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(DispBrightnessValue& a, DispBrightnessValue& b) {
    a.Swap(&b);
  }
  inline void Swap(DispBrightnessValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DispBrightnessValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DispBrightnessValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DispBrightnessValue>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const DispBrightnessValue& from);
  void MergeFrom(const DispBrightnessValue& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(DispBrightnessValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.DispBrightnessValue";
  }
  protected:
  explicit DispBrightnessValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDisplayIdFieldNumber = 1,
    kLevelFieldNumber = 2,
  };
  // required uint32 displayId = 1;
  bool has_displayid() const;
  private:
  bool _internal_has_displayid() const;
  public:
  void clear_displayid();
  uint32_t displayid() const;
  void set_displayid(uint32_t value);
  private:
  uint32_t _internal_displayid() const;
  void _internal_set_displayid(uint32_t value);
  public:

  // required uint32 level = 2;
  bool has_level() const;
  private:
  bool _internal_has_level() const;
  public:
  void clear_level();
  uint32_t level() const;
  void set_level(uint32_t value);
  private:
  uint32_t _internal_level() const;
  void _internal_set_level(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:autolink.DispBrightnessValue)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t displayid_;
  uint32_t level_;
  friend struct ::TableStruct_autolink_2epowerclient_2eext_2eproto;
};
// -------------------------------------------------------------------

class DispPowerStateValue final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:autolink.DispPowerStateValue) */ {
 public:
  inline DispPowerStateValue() : DispPowerStateValue(nullptr) {}
  ~DispPowerStateValue() override;
  explicit constexpr DispPowerStateValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DispPowerStateValue(const DispPowerStateValue& from);
  DispPowerStateValue(DispPowerStateValue&& from) noexcept
    : DispPowerStateValue() {
    *this = ::std::move(from);
  }

  inline DispPowerStateValue& operator=(const DispPowerStateValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline DispPowerStateValue& operator=(DispPowerStateValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const DispPowerStateValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const DispPowerStateValue* internal_default_instance() {
    return reinterpret_cast<const DispPowerStateValue*>(
               &_DispPowerStateValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(DispPowerStateValue& a, DispPowerStateValue& b) {
    a.Swap(&b);
  }
  inline void Swap(DispPowerStateValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DispPowerStateValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DispPowerStateValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DispPowerStateValue>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const DispPowerStateValue& from);
  void MergeFrom(const DispPowerStateValue& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(DispPowerStateValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.DispPowerStateValue";
  }
  protected:
  explicit DispPowerStateValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDisplayIdFieldNumber = 1,
    kPowerOnFieldNumber = 2,
  };
  // required uint32 displayId = 1;
  bool has_displayid() const;
  private:
  bool _internal_has_displayid() const;
  public:
  void clear_displayid();
  uint32_t displayid() const;
  void set_displayid(uint32_t value);
  private:
  uint32_t _internal_displayid() const;
  void _internal_set_displayid(uint32_t value);
  public:

  // required bool powerOn = 2;
  bool has_poweron() const;
  private:
  bool _internal_has_poweron() const;
  public:
  void clear_poweron();
  bool poweron() const;
  void set_poweron(bool value);
  private:
  bool _internal_poweron() const;
  void _internal_set_poweron(bool value);
  public:

  // @@protoc_insertion_point(class_scope:autolink.DispPowerStateValue)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t displayid_;
  bool poweron_;
  friend struct ::TableStruct_autolink_2epowerclient_2eext_2eproto;
};
// -------------------------------------------------------------------

class DispHwVersionNotice final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:autolink.DispHwVersionNotice) */ {
 public:
  inline DispHwVersionNotice() : DispHwVersionNotice(nullptr) {}
  ~DispHwVersionNotice() override;
  explicit constexpr DispHwVersionNotice(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DispHwVersionNotice(const DispHwVersionNotice& from);
  DispHwVersionNotice(DispHwVersionNotice&& from) noexcept
    : DispHwVersionNotice() {
    *this = ::std::move(from);
  }

  inline DispHwVersionNotice& operator=(const DispHwVersionNotice& from) {
    CopyFrom(from);
    return *this;
  }
  inline DispHwVersionNotice& operator=(DispHwVersionNotice&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const DispHwVersionNotice& default_instance() {
    return *internal_default_instance();
  }
  static inline const DispHwVersionNotice* internal_default_instance() {
    return reinterpret_cast<const DispHwVersionNotice*>(
               &_DispHwVersionNotice_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(DispHwVersionNotice& a, DispHwVersionNotice& b) {
    a.Swap(&b);
  }
  inline void Swap(DispHwVersionNotice* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DispHwVersionNotice* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DispHwVersionNotice* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DispHwVersionNotice>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const DispHwVersionNotice& from);
  void MergeFrom(const DispHwVersionNotice& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(DispHwVersionNotice* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.DispHwVersionNotice";
  }
  protected:
  explicit DispHwVersionNotice(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHwVersionMsgFieldNumber = 1,
    kSizeFieldNumber = 2,
  };
  // required string HwVersionMsg = 1;
  bool has_hwversionmsg() const;
  private:
  bool _internal_has_hwversionmsg() const;
  public:
  void clear_hwversionmsg();
  const std::string& hwversionmsg() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_hwversionmsg(ArgT0&& arg0, ArgT... args);
  std::string* mutable_hwversionmsg();
  PROTOBUF_NODISCARD std::string* release_hwversionmsg();
  void set_allocated_hwversionmsg(std::string* hwversionmsg);
  private:
  const std::string& _internal_hwversionmsg() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_hwversionmsg(const std::string& value);
  std::string* _internal_mutable_hwversionmsg();
  public:

  // required uint32 size = 2;
  bool has_size() const;
  private:
  bool _internal_has_size() const;
  public:
  void clear_size();
  uint32_t size() const;
  void set_size(uint32_t value);
  private:
  uint32_t _internal_size() const;
  void _internal_set_size(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:autolink.DispHwVersionNotice)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr hwversionmsg_;
  uint32_t size_;
  friend struct ::TableStruct_autolink_2epowerclient_2eext_2eproto;
};
// -------------------------------------------------------------------

class DispSwVersionNotice final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:autolink.DispSwVersionNotice) */ {
 public:
  inline DispSwVersionNotice() : DispSwVersionNotice(nullptr) {}
  ~DispSwVersionNotice() override;
  explicit constexpr DispSwVersionNotice(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DispSwVersionNotice(const DispSwVersionNotice& from);
  DispSwVersionNotice(DispSwVersionNotice&& from) noexcept
    : DispSwVersionNotice() {
    *this = ::std::move(from);
  }

  inline DispSwVersionNotice& operator=(const DispSwVersionNotice& from) {
    CopyFrom(from);
    return *this;
  }
  inline DispSwVersionNotice& operator=(DispSwVersionNotice&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const DispSwVersionNotice& default_instance() {
    return *internal_default_instance();
  }
  static inline const DispSwVersionNotice* internal_default_instance() {
    return reinterpret_cast<const DispSwVersionNotice*>(
               &_DispSwVersionNotice_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(DispSwVersionNotice& a, DispSwVersionNotice& b) {
    a.Swap(&b);
  }
  inline void Swap(DispSwVersionNotice* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DispSwVersionNotice* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DispSwVersionNotice* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DispSwVersionNotice>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const DispSwVersionNotice& from);
  void MergeFrom(const DispSwVersionNotice& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(DispSwVersionNotice* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.DispSwVersionNotice";
  }
  protected:
  explicit DispSwVersionNotice(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSwVersionMsgFieldNumber = 1,
    kSizeFieldNumber = 2,
  };
  // required string SwVersionMsg = 1;
  bool has_swversionmsg() const;
  private:
  bool _internal_has_swversionmsg() const;
  public:
  void clear_swversionmsg();
  const std::string& swversionmsg() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_swversionmsg(ArgT0&& arg0, ArgT... args);
  std::string* mutable_swversionmsg();
  PROTOBUF_NODISCARD std::string* release_swversionmsg();
  void set_allocated_swversionmsg(std::string* swversionmsg);
  private:
  const std::string& _internal_swversionmsg() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_swversionmsg(const std::string& value);
  std::string* _internal_mutable_swversionmsg();
  public:

  // required uint32 size = 2;
  bool has_size() const;
  private:
  bool _internal_has_size() const;
  public:
  void clear_size();
  uint32_t size() const;
  void set_size(uint32_t value);
  private:
  uint32_t _internal_size() const;
  void _internal_set_size(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:autolink.DispSwVersionNotice)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr swversionmsg_;
  uint32_t size_;
  friend struct ::TableStruct_autolink_2epowerclient_2eext_2eproto;
};
// -------------------------------------------------------------------

class ILCMControlClientProp final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:autolink.ILCMControlClientProp) */ {
 public:
  inline ILCMControlClientProp() : ILCMControlClientProp(nullptr) {}
  ~ILCMControlClientProp() override;
  explicit constexpr ILCMControlClientProp(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ILCMControlClientProp(const ILCMControlClientProp& from);
  ILCMControlClientProp(ILCMControlClientProp&& from) noexcept
    : ILCMControlClientProp() {
    *this = ::std::move(from);
  }

  inline ILCMControlClientProp& operator=(const ILCMControlClientProp& from) {
    CopyFrom(from);
    return *this;
  }
  inline ILCMControlClientProp& operator=(ILCMControlClientProp&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const ILCMControlClientProp& default_instance() {
    return *internal_default_instance();
  }
  static inline const ILCMControlClientProp* internal_default_instance() {
    return reinterpret_cast<const ILCMControlClientProp*>(
               &_ILCMControlClientProp_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(ILCMControlClientProp& a, ILCMControlClientProp& b) {
    a.Swap(&b);
  }
  inline void Swap(ILCMControlClientProp* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ILCMControlClientProp* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ILCMControlClientProp* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ILCMControlClientProp>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const ILCMControlClientProp& from);
  void MergeFrom(const ILCMControlClientProp& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ILCMControlClientProp* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.ILCMControlClientProp";
  }
  protected:
  explicit ILCMControlClientProp(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kClientIdFieldNumber = 1,
    kClientPidFieldNumber = 2,
    kClientPriorityFieldNumber = 3,
  };
  // required string clientId = 1;
  bool has_clientid() const;
  private:
  bool _internal_has_clientid() const;
  public:
  void clear_clientid();
  const std::string& clientid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_clientid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_clientid();
  PROTOBUF_NODISCARD std::string* release_clientid();
  void set_allocated_clientid(std::string* clientid);
  private:
  const std::string& _internal_clientid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_clientid(const std::string& value);
  std::string* _internal_mutable_clientid();
  public:

  // required uint32 clientPid = 2;
  bool has_clientpid() const;
  private:
  bool _internal_has_clientpid() const;
  public:
  void clear_clientpid();
  uint32_t clientpid() const;
  void set_clientpid(uint32_t value);
  private:
  uint32_t _internal_clientpid() const;
  void _internal_set_clientpid(uint32_t value);
  public:

  // required uint32 clientPriority = 3;
  bool has_clientpriority() const;
  private:
  bool _internal_has_clientpriority() const;
  public:
  void clear_clientpriority();
  uint32_t clientpriority() const;
  void set_clientpriority(uint32_t value);
  private:
  uint32_t _internal_clientpriority() const;
  void _internal_set_clientpriority(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:autolink.ILCMControlClientProp)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr clientid_;
  uint32_t clientpid_;
  uint32_t clientpriority_;
  friend struct ::TableStruct_autolink_2epowerclient_2eext_2eproto;
};
// -------------------------------------------------------------------

class ILCMSuspendPrepareAckDelay final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:autolink.ILCMSuspendPrepareAckDelay) */ {
 public:
  inline ILCMSuspendPrepareAckDelay() : ILCMSuspendPrepareAckDelay(nullptr) {}
  ~ILCMSuspendPrepareAckDelay() override;
  explicit constexpr ILCMSuspendPrepareAckDelay(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ILCMSuspendPrepareAckDelay(const ILCMSuspendPrepareAckDelay& from);
  ILCMSuspendPrepareAckDelay(ILCMSuspendPrepareAckDelay&& from) noexcept
    : ILCMSuspendPrepareAckDelay() {
    *this = ::std::move(from);
  }

  inline ILCMSuspendPrepareAckDelay& operator=(const ILCMSuspendPrepareAckDelay& from) {
    CopyFrom(from);
    return *this;
  }
  inline ILCMSuspendPrepareAckDelay& operator=(ILCMSuspendPrepareAckDelay&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const ILCMSuspendPrepareAckDelay& default_instance() {
    return *internal_default_instance();
  }
  static inline const ILCMSuspendPrepareAckDelay* internal_default_instance() {
    return reinterpret_cast<const ILCMSuspendPrepareAckDelay*>(
               &_ILCMSuspendPrepareAckDelay_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(ILCMSuspendPrepareAckDelay& a, ILCMSuspendPrepareAckDelay& b) {
    a.Swap(&b);
  }
  inline void Swap(ILCMSuspendPrepareAckDelay* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ILCMSuspendPrepareAckDelay* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ILCMSuspendPrepareAckDelay* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ILCMSuspendPrepareAckDelay>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const ILCMSuspendPrepareAckDelay& from);
  void MergeFrom(const ILCMSuspendPrepareAckDelay& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ILCMSuspendPrepareAckDelay* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.ILCMSuspendPrepareAckDelay";
  }
  protected:
  explicit ILCMSuspendPrepareAckDelay(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDelayReasonFieldNumber = 3,
    kClientPropFieldNumber = 1,
    kDelayTimeFieldNumber = 2,
  };
  // required string delayReason = 3;
  bool has_delayreason() const;
  private:
  bool _internal_has_delayreason() const;
  public:
  void clear_delayreason();
  const std::string& delayreason() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_delayreason(ArgT0&& arg0, ArgT... args);
  std::string* mutable_delayreason();
  PROTOBUF_NODISCARD std::string* release_delayreason();
  void set_allocated_delayreason(std::string* delayreason);
  private:
  const std::string& _internal_delayreason() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_delayreason(const std::string& value);
  std::string* _internal_mutable_delayreason();
  public:

  // required .autolink.ILCMControlClientProp clientProp = 1;
  bool has_clientprop() const;
  private:
  bool _internal_has_clientprop() const;
  public:
  void clear_clientprop();
  const ::autolink::ILCMControlClientProp& clientprop() const;
  PROTOBUF_NODISCARD ::autolink::ILCMControlClientProp* release_clientprop();
  ::autolink::ILCMControlClientProp* mutable_clientprop();
  void set_allocated_clientprop(::autolink::ILCMControlClientProp* clientprop);
  private:
  const ::autolink::ILCMControlClientProp& _internal_clientprop() const;
  ::autolink::ILCMControlClientProp* _internal_mutable_clientprop();
  public:
  void unsafe_arena_set_allocated_clientprop(
      ::autolink::ILCMControlClientProp* clientprop);
  ::autolink::ILCMControlClientProp* unsafe_arena_release_clientprop();

  // required uint32 delayTime = 2;
  bool has_delaytime() const;
  private:
  bool _internal_has_delaytime() const;
  public:
  void clear_delaytime();
  uint32_t delaytime() const;
  void set_delaytime(uint32_t value);
  private:
  uint32_t _internal_delaytime() const;
  void _internal_set_delaytime(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:autolink.ILCMSuspendPrepareAckDelay)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr delayreason_;
  ::autolink::ILCMControlClientProp* clientprop_;
  uint32_t delaytime_;
  friend struct ::TableStruct_autolink_2epowerclient_2eext_2eproto;
};
// -------------------------------------------------------------------

class DispControlPropValue final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:autolink.DispControlPropValue) */ {
 public:
  inline DispControlPropValue() : DispControlPropValue(nullptr) {}
  ~DispControlPropValue() override;
  explicit constexpr DispControlPropValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DispControlPropValue(const DispControlPropValue& from);
  DispControlPropValue(DispControlPropValue&& from) noexcept
    : DispControlPropValue() {
    *this = ::std::move(from);
  }

  inline DispControlPropValue& operator=(const DispControlPropValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline DispControlPropValue& operator=(DispControlPropValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const DispControlPropValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const DispControlPropValue* internal_default_instance() {
    return reinterpret_cast<const DispControlPropValue*>(
               &_DispControlPropValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(DispControlPropValue& a, DispControlPropValue& b) {
    a.Swap(&b);
  }
  inline void Swap(DispControlPropValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DispControlPropValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DispControlPropValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DispControlPropValue>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const DispControlPropValue& from);
  void MergeFrom(const DispControlPropValue& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(DispControlPropValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.DispControlPropValue";
  }
  protected:
  explicit DispControlPropValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBrightnessValueFieldNumber = 2,
    kPowerStateFieldNumber = 4,
    kHwVersionFieldNumber = 7,
    kSwVersionFieldNumber = 9,
    kDispIdBrightnessGetFieldNumber = 3,
    kDispIdPowerStateGetFieldNumber = 5,
    kDispIdHwVersionGetFieldNumber = 6,
    kDispIdSwVersionGetFieldNumber = 8,
    kDispAnimStateFieldNumber = 10,
    kEventIdFieldNumber = 1,
  };
  // optional .autolink.DispBrightnessValue brightnessValue = 2;
  bool has_brightnessvalue() const;
  private:
  bool _internal_has_brightnessvalue() const;
  public:
  void clear_brightnessvalue();
  const ::autolink::DispBrightnessValue& brightnessvalue() const;
  PROTOBUF_NODISCARD ::autolink::DispBrightnessValue* release_brightnessvalue();
  ::autolink::DispBrightnessValue* mutable_brightnessvalue();
  void set_allocated_brightnessvalue(::autolink::DispBrightnessValue* brightnessvalue);
  private:
  const ::autolink::DispBrightnessValue& _internal_brightnessvalue() const;
  ::autolink::DispBrightnessValue* _internal_mutable_brightnessvalue();
  public:
  void unsafe_arena_set_allocated_brightnessvalue(
      ::autolink::DispBrightnessValue* brightnessvalue);
  ::autolink::DispBrightnessValue* unsafe_arena_release_brightnessvalue();

  // optional .autolink.DispPowerStateValue powerState = 4;
  bool has_powerstate() const;
  private:
  bool _internal_has_powerstate() const;
  public:
  void clear_powerstate();
  const ::autolink::DispPowerStateValue& powerstate() const;
  PROTOBUF_NODISCARD ::autolink::DispPowerStateValue* release_powerstate();
  ::autolink::DispPowerStateValue* mutable_powerstate();
  void set_allocated_powerstate(::autolink::DispPowerStateValue* powerstate);
  private:
  const ::autolink::DispPowerStateValue& _internal_powerstate() const;
  ::autolink::DispPowerStateValue* _internal_mutable_powerstate();
  public:
  void unsafe_arena_set_allocated_powerstate(
      ::autolink::DispPowerStateValue* powerstate);
  ::autolink::DispPowerStateValue* unsafe_arena_release_powerstate();

  // optional .autolink.DispHwVersionNotice HwVersion = 7;
  bool has_hwversion() const;
  private:
  bool _internal_has_hwversion() const;
  public:
  void clear_hwversion();
  const ::autolink::DispHwVersionNotice& hwversion() const;
  PROTOBUF_NODISCARD ::autolink::DispHwVersionNotice* release_hwversion();
  ::autolink::DispHwVersionNotice* mutable_hwversion();
  void set_allocated_hwversion(::autolink::DispHwVersionNotice* hwversion);
  private:
  const ::autolink::DispHwVersionNotice& _internal_hwversion() const;
  ::autolink::DispHwVersionNotice* _internal_mutable_hwversion();
  public:
  void unsafe_arena_set_allocated_hwversion(
      ::autolink::DispHwVersionNotice* hwversion);
  ::autolink::DispHwVersionNotice* unsafe_arena_release_hwversion();

  // optional .autolink.DispSwVersionNotice SwVersion = 9;
  bool has_swversion() const;
  private:
  bool _internal_has_swversion() const;
  public:
  void clear_swversion();
  const ::autolink::DispSwVersionNotice& swversion() const;
  PROTOBUF_NODISCARD ::autolink::DispSwVersionNotice* release_swversion();
  ::autolink::DispSwVersionNotice* mutable_swversion();
  void set_allocated_swversion(::autolink::DispSwVersionNotice* swversion);
  private:
  const ::autolink::DispSwVersionNotice& _internal_swversion() const;
  ::autolink::DispSwVersionNotice* _internal_mutable_swversion();
  public:
  void unsafe_arena_set_allocated_swversion(
      ::autolink::DispSwVersionNotice* swversion);
  ::autolink::DispSwVersionNotice* unsafe_arena_release_swversion();

  // optional uint32 DispIdBrightnessGet = 3;
  bool has_dispidbrightnessget() const;
  private:
  bool _internal_has_dispidbrightnessget() const;
  public:
  void clear_dispidbrightnessget();
  uint32_t dispidbrightnessget() const;
  void set_dispidbrightnessget(uint32_t value);
  private:
  uint32_t _internal_dispidbrightnessget() const;
  void _internal_set_dispidbrightnessget(uint32_t value);
  public:

  // optional uint32 DispIdPowerStateGet = 5;
  bool has_dispidpowerstateget() const;
  private:
  bool _internal_has_dispidpowerstateget() const;
  public:
  void clear_dispidpowerstateget();
  uint32_t dispidpowerstateget() const;
  void set_dispidpowerstateget(uint32_t value);
  private:
  uint32_t _internal_dispidpowerstateget() const;
  void _internal_set_dispidpowerstateget(uint32_t value);
  public:

  // optional uint32 DispIdHwVersionGet = 6;
  bool has_dispidhwversionget() const;
  private:
  bool _internal_has_dispidhwversionget() const;
  public:
  void clear_dispidhwversionget();
  uint32_t dispidhwversionget() const;
  void set_dispidhwversionget(uint32_t value);
  private:
  uint32_t _internal_dispidhwversionget() const;
  void _internal_set_dispidhwversionget(uint32_t value);
  public:

  // optional uint32 DispIdSwVersionGet = 8;
  bool has_dispidswversionget() const;
  private:
  bool _internal_has_dispidswversionget() const;
  public:
  void clear_dispidswversionget();
  uint32_t dispidswversionget() const;
  void set_dispidswversionget(uint32_t value);
  private:
  uint32_t _internal_dispidswversionget() const;
  void _internal_set_dispidswversionget(uint32_t value);
  public:

  // optional uint32 DispAnimState = 10;
  bool has_dispanimstate() const;
  private:
  bool _internal_has_dispanimstate() const;
  public:
  void clear_dispanimstate();
  uint32_t dispanimstate() const;
  void set_dispanimstate(uint32_t value);
  private:
  uint32_t _internal_dispanimstate() const;
  void _internal_set_dispanimstate(uint32_t value);
  public:

  // required .autolink.DispControlEventId eventId = 1;
  bool has_eventid() const;
  private:
  bool _internal_has_eventid() const;
  public:
  void clear_eventid();
  ::autolink::DispControlEventId eventid() const;
  void set_eventid(::autolink::DispControlEventId value);
  private:
  ::autolink::DispControlEventId _internal_eventid() const;
  void _internal_set_eventid(::autolink::DispControlEventId value);
  public:

  // @@protoc_insertion_point(class_scope:autolink.DispControlPropValue)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::autolink::DispBrightnessValue* brightnessvalue_;
  ::autolink::DispPowerStateValue* powerstate_;
  ::autolink::DispHwVersionNotice* hwversion_;
  ::autolink::DispSwVersionNotice* swversion_;
  uint32_t dispidbrightnessget_;
  uint32_t dispidpowerstateget_;
  uint32_t dispidhwversionget_;
  uint32_t dispidswversionget_;
  uint32_t dispanimstate_;
  int eventid_;
  friend struct ::TableStruct_autolink_2epowerclient_2eext_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// DispBrightnessValue

// required uint32 displayId = 1;
inline bool DispBrightnessValue::_internal_has_displayid() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool DispBrightnessValue::has_displayid() const {
  return _internal_has_displayid();
}
inline void DispBrightnessValue::clear_displayid() {
  displayid_ = 0u;
  _has_bits_[0] &= ~0x00000001u;
}
inline uint32_t DispBrightnessValue::_internal_displayid() const {
  return displayid_;
}
inline uint32_t DispBrightnessValue::displayid() const {
  // @@protoc_insertion_point(field_get:autolink.DispBrightnessValue.displayId)
  return _internal_displayid();
}
inline void DispBrightnessValue::_internal_set_displayid(uint32_t value) {
  _has_bits_[0] |= 0x00000001u;
  displayid_ = value;
}
inline void DispBrightnessValue::set_displayid(uint32_t value) {
  _internal_set_displayid(value);
  // @@protoc_insertion_point(field_set:autolink.DispBrightnessValue.displayId)
}

// required uint32 level = 2;
inline bool DispBrightnessValue::_internal_has_level() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool DispBrightnessValue::has_level() const {
  return _internal_has_level();
}
inline void DispBrightnessValue::clear_level() {
  level_ = 0u;
  _has_bits_[0] &= ~0x00000002u;
}
inline uint32_t DispBrightnessValue::_internal_level() const {
  return level_;
}
inline uint32_t DispBrightnessValue::level() const {
  // @@protoc_insertion_point(field_get:autolink.DispBrightnessValue.level)
  return _internal_level();
}
inline void DispBrightnessValue::_internal_set_level(uint32_t value) {
  _has_bits_[0] |= 0x00000002u;
  level_ = value;
}
inline void DispBrightnessValue::set_level(uint32_t value) {
  _internal_set_level(value);
  // @@protoc_insertion_point(field_set:autolink.DispBrightnessValue.level)
}

// -------------------------------------------------------------------

// DispPowerStateValue

// required uint32 displayId = 1;
inline bool DispPowerStateValue::_internal_has_displayid() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool DispPowerStateValue::has_displayid() const {
  return _internal_has_displayid();
}
inline void DispPowerStateValue::clear_displayid() {
  displayid_ = 0u;
  _has_bits_[0] &= ~0x00000001u;
}
inline uint32_t DispPowerStateValue::_internal_displayid() const {
  return displayid_;
}
inline uint32_t DispPowerStateValue::displayid() const {
  // @@protoc_insertion_point(field_get:autolink.DispPowerStateValue.displayId)
  return _internal_displayid();
}
inline void DispPowerStateValue::_internal_set_displayid(uint32_t value) {
  _has_bits_[0] |= 0x00000001u;
  displayid_ = value;
}
inline void DispPowerStateValue::set_displayid(uint32_t value) {
  _internal_set_displayid(value);
  // @@protoc_insertion_point(field_set:autolink.DispPowerStateValue.displayId)
}

// required bool powerOn = 2;
inline bool DispPowerStateValue::_internal_has_poweron() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool DispPowerStateValue::has_poweron() const {
  return _internal_has_poweron();
}
inline void DispPowerStateValue::clear_poweron() {
  poweron_ = false;
  _has_bits_[0] &= ~0x00000002u;
}
inline bool DispPowerStateValue::_internal_poweron() const {
  return poweron_;
}
inline bool DispPowerStateValue::poweron() const {
  // @@protoc_insertion_point(field_get:autolink.DispPowerStateValue.powerOn)
  return _internal_poweron();
}
inline void DispPowerStateValue::_internal_set_poweron(bool value) {
  _has_bits_[0] |= 0x00000002u;
  poweron_ = value;
}
inline void DispPowerStateValue::set_poweron(bool value) {
  _internal_set_poweron(value);
  // @@protoc_insertion_point(field_set:autolink.DispPowerStateValue.powerOn)
}

// -------------------------------------------------------------------

// DispHwVersionNotice

// required string HwVersionMsg = 1;
inline bool DispHwVersionNotice::_internal_has_hwversionmsg() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool DispHwVersionNotice::has_hwversionmsg() const {
  return _internal_has_hwversionmsg();
}
inline void DispHwVersionNotice::clear_hwversionmsg() {
  hwversionmsg_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& DispHwVersionNotice::hwversionmsg() const {
  // @@protoc_insertion_point(field_get:autolink.DispHwVersionNotice.HwVersionMsg)
  return _internal_hwversionmsg();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DispHwVersionNotice::set_hwversionmsg(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 hwversionmsg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:autolink.DispHwVersionNotice.HwVersionMsg)
}
inline std::string* DispHwVersionNotice::mutable_hwversionmsg() {
  std::string* _s = _internal_mutable_hwversionmsg();
  // @@protoc_insertion_point(field_mutable:autolink.DispHwVersionNotice.HwVersionMsg)
  return _s;
}
inline const std::string& DispHwVersionNotice::_internal_hwversionmsg() const {
  return hwversionmsg_.Get();
}
inline void DispHwVersionNotice::_internal_set_hwversionmsg(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  hwversionmsg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DispHwVersionNotice::_internal_mutable_hwversionmsg() {
  _has_bits_[0] |= 0x00000001u;
  return hwversionmsg_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DispHwVersionNotice::release_hwversionmsg() {
  // @@protoc_insertion_point(field_release:autolink.DispHwVersionNotice.HwVersionMsg)
  if (!_internal_has_hwversionmsg()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = hwversionmsg_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (hwversionmsg_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    hwversionmsg_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void DispHwVersionNotice::set_allocated_hwversionmsg(std::string* hwversionmsg) {
  if (hwversionmsg != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  hwversionmsg_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), hwversionmsg,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (hwversionmsg_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    hwversionmsg_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:autolink.DispHwVersionNotice.HwVersionMsg)
}

// required uint32 size = 2;
inline bool DispHwVersionNotice::_internal_has_size() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool DispHwVersionNotice::has_size() const {
  return _internal_has_size();
}
inline void DispHwVersionNotice::clear_size() {
  size_ = 0u;
  _has_bits_[0] &= ~0x00000002u;
}
inline uint32_t DispHwVersionNotice::_internal_size() const {
  return size_;
}
inline uint32_t DispHwVersionNotice::size() const {
  // @@protoc_insertion_point(field_get:autolink.DispHwVersionNotice.size)
  return _internal_size();
}
inline void DispHwVersionNotice::_internal_set_size(uint32_t value) {
  _has_bits_[0] |= 0x00000002u;
  size_ = value;
}
inline void DispHwVersionNotice::set_size(uint32_t value) {
  _internal_set_size(value);
  // @@protoc_insertion_point(field_set:autolink.DispHwVersionNotice.size)
}

// -------------------------------------------------------------------

// DispSwVersionNotice

// required string SwVersionMsg = 1;
inline bool DispSwVersionNotice::_internal_has_swversionmsg() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool DispSwVersionNotice::has_swversionmsg() const {
  return _internal_has_swversionmsg();
}
inline void DispSwVersionNotice::clear_swversionmsg() {
  swversionmsg_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& DispSwVersionNotice::swversionmsg() const {
  // @@protoc_insertion_point(field_get:autolink.DispSwVersionNotice.SwVersionMsg)
  return _internal_swversionmsg();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DispSwVersionNotice::set_swversionmsg(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 swversionmsg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:autolink.DispSwVersionNotice.SwVersionMsg)
}
inline std::string* DispSwVersionNotice::mutable_swversionmsg() {
  std::string* _s = _internal_mutable_swversionmsg();
  // @@protoc_insertion_point(field_mutable:autolink.DispSwVersionNotice.SwVersionMsg)
  return _s;
}
inline const std::string& DispSwVersionNotice::_internal_swversionmsg() const {
  return swversionmsg_.Get();
}
inline void DispSwVersionNotice::_internal_set_swversionmsg(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  swversionmsg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DispSwVersionNotice::_internal_mutable_swversionmsg() {
  _has_bits_[0] |= 0x00000001u;
  return swversionmsg_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DispSwVersionNotice::release_swversionmsg() {
  // @@protoc_insertion_point(field_release:autolink.DispSwVersionNotice.SwVersionMsg)
  if (!_internal_has_swversionmsg()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = swversionmsg_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (swversionmsg_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    swversionmsg_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void DispSwVersionNotice::set_allocated_swversionmsg(std::string* swversionmsg) {
  if (swversionmsg != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  swversionmsg_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), swversionmsg,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (swversionmsg_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    swversionmsg_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:autolink.DispSwVersionNotice.SwVersionMsg)
}

// required uint32 size = 2;
inline bool DispSwVersionNotice::_internal_has_size() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool DispSwVersionNotice::has_size() const {
  return _internal_has_size();
}
inline void DispSwVersionNotice::clear_size() {
  size_ = 0u;
  _has_bits_[0] &= ~0x00000002u;
}
inline uint32_t DispSwVersionNotice::_internal_size() const {
  return size_;
}
inline uint32_t DispSwVersionNotice::size() const {
  // @@protoc_insertion_point(field_get:autolink.DispSwVersionNotice.size)
  return _internal_size();
}
inline void DispSwVersionNotice::_internal_set_size(uint32_t value) {
  _has_bits_[0] |= 0x00000002u;
  size_ = value;
}
inline void DispSwVersionNotice::set_size(uint32_t value) {
  _internal_set_size(value);
  // @@protoc_insertion_point(field_set:autolink.DispSwVersionNotice.size)
}

// -------------------------------------------------------------------

// ILCMControlClientProp

// required string clientId = 1;
inline bool ILCMControlClientProp::_internal_has_clientid() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool ILCMControlClientProp::has_clientid() const {
  return _internal_has_clientid();
}
inline void ILCMControlClientProp::clear_clientid() {
  clientid_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& ILCMControlClientProp::clientid() const {
  // @@protoc_insertion_point(field_get:autolink.ILCMControlClientProp.clientId)
  return _internal_clientid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ILCMControlClientProp::set_clientid(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 clientid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:autolink.ILCMControlClientProp.clientId)
}
inline std::string* ILCMControlClientProp::mutable_clientid() {
  std::string* _s = _internal_mutable_clientid();
  // @@protoc_insertion_point(field_mutable:autolink.ILCMControlClientProp.clientId)
  return _s;
}
inline const std::string& ILCMControlClientProp::_internal_clientid() const {
  return clientid_.Get();
}
inline void ILCMControlClientProp::_internal_set_clientid(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  clientid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ILCMControlClientProp::_internal_mutable_clientid() {
  _has_bits_[0] |= 0x00000001u;
  return clientid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ILCMControlClientProp::release_clientid() {
  // @@protoc_insertion_point(field_release:autolink.ILCMControlClientProp.clientId)
  if (!_internal_has_clientid()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = clientid_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (clientid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    clientid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void ILCMControlClientProp::set_allocated_clientid(std::string* clientid) {
  if (clientid != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  clientid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), clientid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (clientid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    clientid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:autolink.ILCMControlClientProp.clientId)
}

// required uint32 clientPid = 2;
inline bool ILCMControlClientProp::_internal_has_clientpid() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool ILCMControlClientProp::has_clientpid() const {
  return _internal_has_clientpid();
}
inline void ILCMControlClientProp::clear_clientpid() {
  clientpid_ = 0u;
  _has_bits_[0] &= ~0x00000002u;
}
inline uint32_t ILCMControlClientProp::_internal_clientpid() const {
  return clientpid_;
}
inline uint32_t ILCMControlClientProp::clientpid() const {
  // @@protoc_insertion_point(field_get:autolink.ILCMControlClientProp.clientPid)
  return _internal_clientpid();
}
inline void ILCMControlClientProp::_internal_set_clientpid(uint32_t value) {
  _has_bits_[0] |= 0x00000002u;
  clientpid_ = value;
}
inline void ILCMControlClientProp::set_clientpid(uint32_t value) {
  _internal_set_clientpid(value);
  // @@protoc_insertion_point(field_set:autolink.ILCMControlClientProp.clientPid)
}

// required uint32 clientPriority = 3;
inline bool ILCMControlClientProp::_internal_has_clientpriority() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool ILCMControlClientProp::has_clientpriority() const {
  return _internal_has_clientpriority();
}
inline void ILCMControlClientProp::clear_clientpriority() {
  clientpriority_ = 0u;
  _has_bits_[0] &= ~0x00000004u;
}
inline uint32_t ILCMControlClientProp::_internal_clientpriority() const {
  return clientpriority_;
}
inline uint32_t ILCMControlClientProp::clientpriority() const {
  // @@protoc_insertion_point(field_get:autolink.ILCMControlClientProp.clientPriority)
  return _internal_clientpriority();
}
inline void ILCMControlClientProp::_internal_set_clientpriority(uint32_t value) {
  _has_bits_[0] |= 0x00000004u;
  clientpriority_ = value;
}
inline void ILCMControlClientProp::set_clientpriority(uint32_t value) {
  _internal_set_clientpriority(value);
  // @@protoc_insertion_point(field_set:autolink.ILCMControlClientProp.clientPriority)
}

// -------------------------------------------------------------------

// ILCMSuspendPrepareAckDelay

// required .autolink.ILCMControlClientProp clientProp = 1;
inline bool ILCMSuspendPrepareAckDelay::_internal_has_clientprop() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || clientprop_ != nullptr);
  return value;
}
inline bool ILCMSuspendPrepareAckDelay::has_clientprop() const {
  return _internal_has_clientprop();
}
inline void ILCMSuspendPrepareAckDelay::clear_clientprop() {
  if (clientprop_ != nullptr) clientprop_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
inline const ::autolink::ILCMControlClientProp& ILCMSuspendPrepareAckDelay::_internal_clientprop() const {
  const ::autolink::ILCMControlClientProp* p = clientprop_;
  return p != nullptr ? *p : reinterpret_cast<const ::autolink::ILCMControlClientProp&>(
      ::autolink::_ILCMControlClientProp_default_instance_);
}
inline const ::autolink::ILCMControlClientProp& ILCMSuspendPrepareAckDelay::clientprop() const {
  // @@protoc_insertion_point(field_get:autolink.ILCMSuspendPrepareAckDelay.clientProp)
  return _internal_clientprop();
}
inline void ILCMSuspendPrepareAckDelay::unsafe_arena_set_allocated_clientprop(
    ::autolink::ILCMControlClientProp* clientprop) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(clientprop_);
  }
  clientprop_ = clientprop;
  if (clientprop) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:autolink.ILCMSuspendPrepareAckDelay.clientProp)
}
inline ::autolink::ILCMControlClientProp* ILCMSuspendPrepareAckDelay::release_clientprop() {
  _has_bits_[0] &= ~0x00000002u;
  ::autolink::ILCMControlClientProp* temp = clientprop_;
  clientprop_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::autolink::ILCMControlClientProp* ILCMSuspendPrepareAckDelay::unsafe_arena_release_clientprop() {
  // @@protoc_insertion_point(field_release:autolink.ILCMSuspendPrepareAckDelay.clientProp)
  _has_bits_[0] &= ~0x00000002u;
  ::autolink::ILCMControlClientProp* temp = clientprop_;
  clientprop_ = nullptr;
  return temp;
}
inline ::autolink::ILCMControlClientProp* ILCMSuspendPrepareAckDelay::_internal_mutable_clientprop() {
  _has_bits_[0] |= 0x00000002u;
  if (clientprop_ == nullptr) {
    auto* p = CreateMaybeMessage<::autolink::ILCMControlClientProp>(GetArenaForAllocation());
    clientprop_ = p;
  }
  return clientprop_;
}
inline ::autolink::ILCMControlClientProp* ILCMSuspendPrepareAckDelay::mutable_clientprop() {
  ::autolink::ILCMControlClientProp* _msg = _internal_mutable_clientprop();
  // @@protoc_insertion_point(field_mutable:autolink.ILCMSuspendPrepareAckDelay.clientProp)
  return _msg;
}
inline void ILCMSuspendPrepareAckDelay::set_allocated_clientprop(::autolink::ILCMControlClientProp* clientprop) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete clientprop_;
  }
  if (clientprop) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::autolink::ILCMControlClientProp>::GetOwningArena(clientprop);
    if (message_arena != submessage_arena) {
      clientprop = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, clientprop, submessage_arena);
    }
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  clientprop_ = clientprop;
  // @@protoc_insertion_point(field_set_allocated:autolink.ILCMSuspendPrepareAckDelay.clientProp)
}

// required uint32 delayTime = 2;
inline bool ILCMSuspendPrepareAckDelay::_internal_has_delaytime() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool ILCMSuspendPrepareAckDelay::has_delaytime() const {
  return _internal_has_delaytime();
}
inline void ILCMSuspendPrepareAckDelay::clear_delaytime() {
  delaytime_ = 0u;
  _has_bits_[0] &= ~0x00000004u;
}
inline uint32_t ILCMSuspendPrepareAckDelay::_internal_delaytime() const {
  return delaytime_;
}
inline uint32_t ILCMSuspendPrepareAckDelay::delaytime() const {
  // @@protoc_insertion_point(field_get:autolink.ILCMSuspendPrepareAckDelay.delayTime)
  return _internal_delaytime();
}
inline void ILCMSuspendPrepareAckDelay::_internal_set_delaytime(uint32_t value) {
  _has_bits_[0] |= 0x00000004u;
  delaytime_ = value;
}
inline void ILCMSuspendPrepareAckDelay::set_delaytime(uint32_t value) {
  _internal_set_delaytime(value);
  // @@protoc_insertion_point(field_set:autolink.ILCMSuspendPrepareAckDelay.delayTime)
}

// required string delayReason = 3;
inline bool ILCMSuspendPrepareAckDelay::_internal_has_delayreason() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool ILCMSuspendPrepareAckDelay::has_delayreason() const {
  return _internal_has_delayreason();
}
inline void ILCMSuspendPrepareAckDelay::clear_delayreason() {
  delayreason_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& ILCMSuspendPrepareAckDelay::delayreason() const {
  // @@protoc_insertion_point(field_get:autolink.ILCMSuspendPrepareAckDelay.delayReason)
  return _internal_delayreason();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ILCMSuspendPrepareAckDelay::set_delayreason(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 delayreason_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:autolink.ILCMSuspendPrepareAckDelay.delayReason)
}
inline std::string* ILCMSuspendPrepareAckDelay::mutable_delayreason() {
  std::string* _s = _internal_mutable_delayreason();
  // @@protoc_insertion_point(field_mutable:autolink.ILCMSuspendPrepareAckDelay.delayReason)
  return _s;
}
inline const std::string& ILCMSuspendPrepareAckDelay::_internal_delayreason() const {
  return delayreason_.Get();
}
inline void ILCMSuspendPrepareAckDelay::_internal_set_delayreason(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  delayreason_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ILCMSuspendPrepareAckDelay::_internal_mutable_delayreason() {
  _has_bits_[0] |= 0x00000001u;
  return delayreason_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ILCMSuspendPrepareAckDelay::release_delayreason() {
  // @@protoc_insertion_point(field_release:autolink.ILCMSuspendPrepareAckDelay.delayReason)
  if (!_internal_has_delayreason()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = delayreason_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (delayreason_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    delayreason_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void ILCMSuspendPrepareAckDelay::set_allocated_delayreason(std::string* delayreason) {
  if (delayreason != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  delayreason_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), delayreason,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (delayreason_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    delayreason_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:autolink.ILCMSuspendPrepareAckDelay.delayReason)
}

// -------------------------------------------------------------------

// DispControlPropValue

// required .autolink.DispControlEventId eventId = 1;
inline bool DispControlPropValue::_internal_has_eventid() const {
  bool value = (_has_bits_[0] & 0x00000200u) != 0;
  return value;
}
inline bool DispControlPropValue::has_eventid() const {
  return _internal_has_eventid();
}
inline void DispControlPropValue::clear_eventid() {
  eventid_ = 100;
  _has_bits_[0] &= ~0x00000200u;
}
inline ::autolink::DispControlEventId DispControlPropValue::_internal_eventid() const {
  return static_cast< ::autolink::DispControlEventId >(eventid_);
}
inline ::autolink::DispControlEventId DispControlPropValue::eventid() const {
  // @@protoc_insertion_point(field_get:autolink.DispControlPropValue.eventId)
  return _internal_eventid();
}
inline void DispControlPropValue::_internal_set_eventid(::autolink::DispControlEventId value) {
  assert(::autolink::DispControlEventId_IsValid(value));
  _has_bits_[0] |= 0x00000200u;
  eventid_ = value;
}
inline void DispControlPropValue::set_eventid(::autolink::DispControlEventId value) {
  _internal_set_eventid(value);
  // @@protoc_insertion_point(field_set:autolink.DispControlPropValue.eventId)
}

// optional .autolink.DispBrightnessValue brightnessValue = 2;
inline bool DispControlPropValue::_internal_has_brightnessvalue() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || brightnessvalue_ != nullptr);
  return value;
}
inline bool DispControlPropValue::has_brightnessvalue() const {
  return _internal_has_brightnessvalue();
}
inline void DispControlPropValue::clear_brightnessvalue() {
  if (brightnessvalue_ != nullptr) brightnessvalue_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::autolink::DispBrightnessValue& DispControlPropValue::_internal_brightnessvalue() const {
  const ::autolink::DispBrightnessValue* p = brightnessvalue_;
  return p != nullptr ? *p : reinterpret_cast<const ::autolink::DispBrightnessValue&>(
      ::autolink::_DispBrightnessValue_default_instance_);
}
inline const ::autolink::DispBrightnessValue& DispControlPropValue::brightnessvalue() const {
  // @@protoc_insertion_point(field_get:autolink.DispControlPropValue.brightnessValue)
  return _internal_brightnessvalue();
}
inline void DispControlPropValue::unsafe_arena_set_allocated_brightnessvalue(
    ::autolink::DispBrightnessValue* brightnessvalue) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(brightnessvalue_);
  }
  brightnessvalue_ = brightnessvalue;
  if (brightnessvalue) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:autolink.DispControlPropValue.brightnessValue)
}
inline ::autolink::DispBrightnessValue* DispControlPropValue::release_brightnessvalue() {
  _has_bits_[0] &= ~0x00000001u;
  ::autolink::DispBrightnessValue* temp = brightnessvalue_;
  brightnessvalue_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::autolink::DispBrightnessValue* DispControlPropValue::unsafe_arena_release_brightnessvalue() {
  // @@protoc_insertion_point(field_release:autolink.DispControlPropValue.brightnessValue)
  _has_bits_[0] &= ~0x00000001u;
  ::autolink::DispBrightnessValue* temp = brightnessvalue_;
  brightnessvalue_ = nullptr;
  return temp;
}
inline ::autolink::DispBrightnessValue* DispControlPropValue::_internal_mutable_brightnessvalue() {
  _has_bits_[0] |= 0x00000001u;
  if (brightnessvalue_ == nullptr) {
    auto* p = CreateMaybeMessage<::autolink::DispBrightnessValue>(GetArenaForAllocation());
    brightnessvalue_ = p;
  }
  return brightnessvalue_;
}
inline ::autolink::DispBrightnessValue* DispControlPropValue::mutable_brightnessvalue() {
  ::autolink::DispBrightnessValue* _msg = _internal_mutable_brightnessvalue();
  // @@protoc_insertion_point(field_mutable:autolink.DispControlPropValue.brightnessValue)
  return _msg;
}
inline void DispControlPropValue::set_allocated_brightnessvalue(::autolink::DispBrightnessValue* brightnessvalue) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete brightnessvalue_;
  }
  if (brightnessvalue) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::autolink::DispBrightnessValue>::GetOwningArena(brightnessvalue);
    if (message_arena != submessage_arena) {
      brightnessvalue = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, brightnessvalue, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  brightnessvalue_ = brightnessvalue;
  // @@protoc_insertion_point(field_set_allocated:autolink.DispControlPropValue.brightnessValue)
}

// optional uint32 DispIdBrightnessGet = 3;
inline bool DispControlPropValue::_internal_has_dispidbrightnessget() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool DispControlPropValue::has_dispidbrightnessget() const {
  return _internal_has_dispidbrightnessget();
}
inline void DispControlPropValue::clear_dispidbrightnessget() {
  dispidbrightnessget_ = 0u;
  _has_bits_[0] &= ~0x00000010u;
}
inline uint32_t DispControlPropValue::_internal_dispidbrightnessget() const {
  return dispidbrightnessget_;
}
inline uint32_t DispControlPropValue::dispidbrightnessget() const {
  // @@protoc_insertion_point(field_get:autolink.DispControlPropValue.DispIdBrightnessGet)
  return _internal_dispidbrightnessget();
}
inline void DispControlPropValue::_internal_set_dispidbrightnessget(uint32_t value) {
  _has_bits_[0] |= 0x00000010u;
  dispidbrightnessget_ = value;
}
inline void DispControlPropValue::set_dispidbrightnessget(uint32_t value) {
  _internal_set_dispidbrightnessget(value);
  // @@protoc_insertion_point(field_set:autolink.DispControlPropValue.DispIdBrightnessGet)
}

// optional .autolink.DispPowerStateValue powerState = 4;
inline bool DispControlPropValue::_internal_has_powerstate() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || powerstate_ != nullptr);
  return value;
}
inline bool DispControlPropValue::has_powerstate() const {
  return _internal_has_powerstate();
}
inline void DispControlPropValue::clear_powerstate() {
  if (powerstate_ != nullptr) powerstate_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
inline const ::autolink::DispPowerStateValue& DispControlPropValue::_internal_powerstate() const {
  const ::autolink::DispPowerStateValue* p = powerstate_;
  return p != nullptr ? *p : reinterpret_cast<const ::autolink::DispPowerStateValue&>(
      ::autolink::_DispPowerStateValue_default_instance_);
}
inline const ::autolink::DispPowerStateValue& DispControlPropValue::powerstate() const {
  // @@protoc_insertion_point(field_get:autolink.DispControlPropValue.powerState)
  return _internal_powerstate();
}
inline void DispControlPropValue::unsafe_arena_set_allocated_powerstate(
    ::autolink::DispPowerStateValue* powerstate) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(powerstate_);
  }
  powerstate_ = powerstate;
  if (powerstate) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:autolink.DispControlPropValue.powerState)
}
inline ::autolink::DispPowerStateValue* DispControlPropValue::release_powerstate() {
  _has_bits_[0] &= ~0x00000002u;
  ::autolink::DispPowerStateValue* temp = powerstate_;
  powerstate_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::autolink::DispPowerStateValue* DispControlPropValue::unsafe_arena_release_powerstate() {
  // @@protoc_insertion_point(field_release:autolink.DispControlPropValue.powerState)
  _has_bits_[0] &= ~0x00000002u;
  ::autolink::DispPowerStateValue* temp = powerstate_;
  powerstate_ = nullptr;
  return temp;
}
inline ::autolink::DispPowerStateValue* DispControlPropValue::_internal_mutable_powerstate() {
  _has_bits_[0] |= 0x00000002u;
  if (powerstate_ == nullptr) {
    auto* p = CreateMaybeMessage<::autolink::DispPowerStateValue>(GetArenaForAllocation());
    powerstate_ = p;
  }
  return powerstate_;
}
inline ::autolink::DispPowerStateValue* DispControlPropValue::mutable_powerstate() {
  ::autolink::DispPowerStateValue* _msg = _internal_mutable_powerstate();
  // @@protoc_insertion_point(field_mutable:autolink.DispControlPropValue.powerState)
  return _msg;
}
inline void DispControlPropValue::set_allocated_powerstate(::autolink::DispPowerStateValue* powerstate) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete powerstate_;
  }
  if (powerstate) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::autolink::DispPowerStateValue>::GetOwningArena(powerstate);
    if (message_arena != submessage_arena) {
      powerstate = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, powerstate, submessage_arena);
    }
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  powerstate_ = powerstate;
  // @@protoc_insertion_point(field_set_allocated:autolink.DispControlPropValue.powerState)
}

// optional uint32 DispIdPowerStateGet = 5;
inline bool DispControlPropValue::_internal_has_dispidpowerstateget() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool DispControlPropValue::has_dispidpowerstateget() const {
  return _internal_has_dispidpowerstateget();
}
inline void DispControlPropValue::clear_dispidpowerstateget() {
  dispidpowerstateget_ = 0u;
  _has_bits_[0] &= ~0x00000020u;
}
inline uint32_t DispControlPropValue::_internal_dispidpowerstateget() const {
  return dispidpowerstateget_;
}
inline uint32_t DispControlPropValue::dispidpowerstateget() const {
  // @@protoc_insertion_point(field_get:autolink.DispControlPropValue.DispIdPowerStateGet)
  return _internal_dispidpowerstateget();
}
inline void DispControlPropValue::_internal_set_dispidpowerstateget(uint32_t value) {
  _has_bits_[0] |= 0x00000020u;
  dispidpowerstateget_ = value;
}
inline void DispControlPropValue::set_dispidpowerstateget(uint32_t value) {
  _internal_set_dispidpowerstateget(value);
  // @@protoc_insertion_point(field_set:autolink.DispControlPropValue.DispIdPowerStateGet)
}

// optional uint32 DispIdHwVersionGet = 6;
inline bool DispControlPropValue::_internal_has_dispidhwversionget() const {
  bool value = (_has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool DispControlPropValue::has_dispidhwversionget() const {
  return _internal_has_dispidhwversionget();
}
inline void DispControlPropValue::clear_dispidhwversionget() {
  dispidhwversionget_ = 0u;
  _has_bits_[0] &= ~0x00000040u;
}
inline uint32_t DispControlPropValue::_internal_dispidhwversionget() const {
  return dispidhwversionget_;
}
inline uint32_t DispControlPropValue::dispidhwversionget() const {
  // @@protoc_insertion_point(field_get:autolink.DispControlPropValue.DispIdHwVersionGet)
  return _internal_dispidhwversionget();
}
inline void DispControlPropValue::_internal_set_dispidhwversionget(uint32_t value) {
  _has_bits_[0] |= 0x00000040u;
  dispidhwversionget_ = value;
}
inline void DispControlPropValue::set_dispidhwversionget(uint32_t value) {
  _internal_set_dispidhwversionget(value);
  // @@protoc_insertion_point(field_set:autolink.DispControlPropValue.DispIdHwVersionGet)
}

// optional .autolink.DispHwVersionNotice HwVersion = 7;
inline bool DispControlPropValue::_internal_has_hwversion() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  PROTOBUF_ASSUME(!value || hwversion_ != nullptr);
  return value;
}
inline bool DispControlPropValue::has_hwversion() const {
  return _internal_has_hwversion();
}
inline void DispControlPropValue::clear_hwversion() {
  if (hwversion_ != nullptr) hwversion_->Clear();
  _has_bits_[0] &= ~0x00000004u;
}
inline const ::autolink::DispHwVersionNotice& DispControlPropValue::_internal_hwversion() const {
  const ::autolink::DispHwVersionNotice* p = hwversion_;
  return p != nullptr ? *p : reinterpret_cast<const ::autolink::DispHwVersionNotice&>(
      ::autolink::_DispHwVersionNotice_default_instance_);
}
inline const ::autolink::DispHwVersionNotice& DispControlPropValue::hwversion() const {
  // @@protoc_insertion_point(field_get:autolink.DispControlPropValue.HwVersion)
  return _internal_hwversion();
}
inline void DispControlPropValue::unsafe_arena_set_allocated_hwversion(
    ::autolink::DispHwVersionNotice* hwversion) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(hwversion_);
  }
  hwversion_ = hwversion;
  if (hwversion) {
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:autolink.DispControlPropValue.HwVersion)
}
inline ::autolink::DispHwVersionNotice* DispControlPropValue::release_hwversion() {
  _has_bits_[0] &= ~0x00000004u;
  ::autolink::DispHwVersionNotice* temp = hwversion_;
  hwversion_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::autolink::DispHwVersionNotice* DispControlPropValue::unsafe_arena_release_hwversion() {
  // @@protoc_insertion_point(field_release:autolink.DispControlPropValue.HwVersion)
  _has_bits_[0] &= ~0x00000004u;
  ::autolink::DispHwVersionNotice* temp = hwversion_;
  hwversion_ = nullptr;
  return temp;
}
inline ::autolink::DispHwVersionNotice* DispControlPropValue::_internal_mutable_hwversion() {
  _has_bits_[0] |= 0x00000004u;
  if (hwversion_ == nullptr) {
    auto* p = CreateMaybeMessage<::autolink::DispHwVersionNotice>(GetArenaForAllocation());
    hwversion_ = p;
  }
  return hwversion_;
}
inline ::autolink::DispHwVersionNotice* DispControlPropValue::mutable_hwversion() {
  ::autolink::DispHwVersionNotice* _msg = _internal_mutable_hwversion();
  // @@protoc_insertion_point(field_mutable:autolink.DispControlPropValue.HwVersion)
  return _msg;
}
inline void DispControlPropValue::set_allocated_hwversion(::autolink::DispHwVersionNotice* hwversion) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete hwversion_;
  }
  if (hwversion) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::autolink::DispHwVersionNotice>::GetOwningArena(hwversion);
    if (message_arena != submessage_arena) {
      hwversion = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, hwversion, submessage_arena);
    }
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  hwversion_ = hwversion;
  // @@protoc_insertion_point(field_set_allocated:autolink.DispControlPropValue.HwVersion)
}

// optional uint32 DispIdSwVersionGet = 8;
inline bool DispControlPropValue::_internal_has_dispidswversionget() const {
  bool value = (_has_bits_[0] & 0x00000080u) != 0;
  return value;
}
inline bool DispControlPropValue::has_dispidswversionget() const {
  return _internal_has_dispidswversionget();
}
inline void DispControlPropValue::clear_dispidswversionget() {
  dispidswversionget_ = 0u;
  _has_bits_[0] &= ~0x00000080u;
}
inline uint32_t DispControlPropValue::_internal_dispidswversionget() const {
  return dispidswversionget_;
}
inline uint32_t DispControlPropValue::dispidswversionget() const {
  // @@protoc_insertion_point(field_get:autolink.DispControlPropValue.DispIdSwVersionGet)
  return _internal_dispidswversionget();
}
inline void DispControlPropValue::_internal_set_dispidswversionget(uint32_t value) {
  _has_bits_[0] |= 0x00000080u;
  dispidswversionget_ = value;
}
inline void DispControlPropValue::set_dispidswversionget(uint32_t value) {
  _internal_set_dispidswversionget(value);
  // @@protoc_insertion_point(field_set:autolink.DispControlPropValue.DispIdSwVersionGet)
}

// optional .autolink.DispSwVersionNotice SwVersion = 9;
inline bool DispControlPropValue::_internal_has_swversion() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  PROTOBUF_ASSUME(!value || swversion_ != nullptr);
  return value;
}
inline bool DispControlPropValue::has_swversion() const {
  return _internal_has_swversion();
}
inline void DispControlPropValue::clear_swversion() {
  if (swversion_ != nullptr) swversion_->Clear();
  _has_bits_[0] &= ~0x00000008u;
}
inline const ::autolink::DispSwVersionNotice& DispControlPropValue::_internal_swversion() const {
  const ::autolink::DispSwVersionNotice* p = swversion_;
  return p != nullptr ? *p : reinterpret_cast<const ::autolink::DispSwVersionNotice&>(
      ::autolink::_DispSwVersionNotice_default_instance_);
}
inline const ::autolink::DispSwVersionNotice& DispControlPropValue::swversion() const {
  // @@protoc_insertion_point(field_get:autolink.DispControlPropValue.SwVersion)
  return _internal_swversion();
}
inline void DispControlPropValue::unsafe_arena_set_allocated_swversion(
    ::autolink::DispSwVersionNotice* swversion) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(swversion_);
  }
  swversion_ = swversion;
  if (swversion) {
    _has_bits_[0] |= 0x00000008u;
  } else {
    _has_bits_[0] &= ~0x00000008u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:autolink.DispControlPropValue.SwVersion)
}
inline ::autolink::DispSwVersionNotice* DispControlPropValue::release_swversion() {
  _has_bits_[0] &= ~0x00000008u;
  ::autolink::DispSwVersionNotice* temp = swversion_;
  swversion_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::autolink::DispSwVersionNotice* DispControlPropValue::unsafe_arena_release_swversion() {
  // @@protoc_insertion_point(field_release:autolink.DispControlPropValue.SwVersion)
  _has_bits_[0] &= ~0x00000008u;
  ::autolink::DispSwVersionNotice* temp = swversion_;
  swversion_ = nullptr;
  return temp;
}
inline ::autolink::DispSwVersionNotice* DispControlPropValue::_internal_mutable_swversion() {
  _has_bits_[0] |= 0x00000008u;
  if (swversion_ == nullptr) {
    auto* p = CreateMaybeMessage<::autolink::DispSwVersionNotice>(GetArenaForAllocation());
    swversion_ = p;
  }
  return swversion_;
}
inline ::autolink::DispSwVersionNotice* DispControlPropValue::mutable_swversion() {
  ::autolink::DispSwVersionNotice* _msg = _internal_mutable_swversion();
  // @@protoc_insertion_point(field_mutable:autolink.DispControlPropValue.SwVersion)
  return _msg;
}
inline void DispControlPropValue::set_allocated_swversion(::autolink::DispSwVersionNotice* swversion) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete swversion_;
  }
  if (swversion) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::autolink::DispSwVersionNotice>::GetOwningArena(swversion);
    if (message_arena != submessage_arena) {
      swversion = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, swversion, submessage_arena);
    }
    _has_bits_[0] |= 0x00000008u;
  } else {
    _has_bits_[0] &= ~0x00000008u;
  }
  swversion_ = swversion;
  // @@protoc_insertion_point(field_set_allocated:autolink.DispControlPropValue.SwVersion)
}

// optional uint32 DispAnimState = 10;
inline bool DispControlPropValue::_internal_has_dispanimstate() const {
  bool value = (_has_bits_[0] & 0x00000100u) != 0;
  return value;
}
inline bool DispControlPropValue::has_dispanimstate() const {
  return _internal_has_dispanimstate();
}
inline void DispControlPropValue::clear_dispanimstate() {
  dispanimstate_ = 0u;
  _has_bits_[0] &= ~0x00000100u;
}
inline uint32_t DispControlPropValue::_internal_dispanimstate() const {
  return dispanimstate_;
}
inline uint32_t DispControlPropValue::dispanimstate() const {
  // @@protoc_insertion_point(field_get:autolink.DispControlPropValue.DispAnimState)
  return _internal_dispanimstate();
}
inline void DispControlPropValue::_internal_set_dispanimstate(uint32_t value) {
  _has_bits_[0] |= 0x00000100u;
  dispanimstate_ = value;
}
inline void DispControlPropValue::set_dispanimstate(uint32_t value) {
  _internal_set_dispanimstate(value);
  // @@protoc_insertion_point(field_set:autolink.DispControlPropValue.DispAnimState)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace autolink

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::autolink::PowerMGRMsgId> : ::std::true_type {};
template <> struct is_proto_enum< ::autolink::DispControlEventId> : ::std::true_type {};

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_autolink_2epowerclient_2eext_2eproto
