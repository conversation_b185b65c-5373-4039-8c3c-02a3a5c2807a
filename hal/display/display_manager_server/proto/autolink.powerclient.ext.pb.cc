// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: autolink.powerclient.ext.proto

#include "autolink.powerclient.ext.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/io/zero_copy_stream_impl_lite.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace autolink {
constexpr DispBrightnessValue::DispBrightnessValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : displayid_(0u)
  , level_(0u){}
struct DispBrightnessValueDefaultTypeInternal {
  constexpr DispBrightnessValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DispBrightnessValueDefaultTypeInternal() {}
  union {
    DispBrightnessValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DispBrightnessValueDefaultTypeInternal _DispBrightnessValue_default_instance_;
constexpr DispPowerStateValue::DispPowerStateValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : displayid_(0u)
  , poweron_(false){}
struct DispPowerStateValueDefaultTypeInternal {
  constexpr DispPowerStateValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DispPowerStateValueDefaultTypeInternal() {}
  union {
    DispPowerStateValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DispPowerStateValueDefaultTypeInternal _DispPowerStateValue_default_instance_;
constexpr DispHwVersionNotice::DispHwVersionNotice(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : hwversionmsg_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , size_(0u){}
struct DispHwVersionNoticeDefaultTypeInternal {
  constexpr DispHwVersionNoticeDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DispHwVersionNoticeDefaultTypeInternal() {}
  union {
    DispHwVersionNotice _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DispHwVersionNoticeDefaultTypeInternal _DispHwVersionNotice_default_instance_;
constexpr DispSwVersionNotice::DispSwVersionNotice(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : swversionmsg_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , size_(0u){}
struct DispSwVersionNoticeDefaultTypeInternal {
  constexpr DispSwVersionNoticeDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DispSwVersionNoticeDefaultTypeInternal() {}
  union {
    DispSwVersionNotice _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DispSwVersionNoticeDefaultTypeInternal _DispSwVersionNotice_default_instance_;
constexpr ILCMControlClientProp::ILCMControlClientProp(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : clientid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , clientpid_(0u)
  , clientpriority_(0u){}
struct ILCMControlClientPropDefaultTypeInternal {
  constexpr ILCMControlClientPropDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ILCMControlClientPropDefaultTypeInternal() {}
  union {
    ILCMControlClientProp _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ILCMControlClientPropDefaultTypeInternal _ILCMControlClientProp_default_instance_;
constexpr ILCMSuspendPrepareAckDelay::ILCMSuspendPrepareAckDelay(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : delayreason_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , clientprop_(nullptr)
  , delaytime_(0u){}
struct ILCMSuspendPrepareAckDelayDefaultTypeInternal {
  constexpr ILCMSuspendPrepareAckDelayDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ILCMSuspendPrepareAckDelayDefaultTypeInternal() {}
  union {
    ILCMSuspendPrepareAckDelay _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ILCMSuspendPrepareAckDelayDefaultTypeInternal _ILCMSuspendPrepareAckDelay_default_instance_;
constexpr DispControlPropValue::DispControlPropValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : brightnessvalue_(nullptr)
  , powerstate_(nullptr)
  , hwversion_(nullptr)
  , swversion_(nullptr)
  , dispidbrightnessget_(0u)
  , dispidpowerstateget_(0u)
  , dispidhwversionget_(0u)
  , dispidswversionget_(0u)
  , dispanimstate_(0u)
  , eventid_(100)
{}
struct DispControlPropValueDefaultTypeInternal {
  constexpr DispControlPropValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DispControlPropValueDefaultTypeInternal() {}
  union {
    DispControlPropValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DispControlPropValueDefaultTypeInternal _DispControlPropValue_default_instance_;
}  // namespace autolink
namespace autolink {
bool PowerMGRMsgId_IsValid(int value) {
  switch (value) {
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 14:
    case 15:
    case 16:
    case 17:
      return true;
    default:
      return false;
  }
}

static ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<std::string> PowerMGRMsgId_strings[13] = {};

static const char PowerMGRMsgId_names[] =
  "DISP_CONTROL_MSG"
  "ILCM_RESP_READY_MSG"
  "PM_STATE_RESUME_COMPLETE_NOTICE"
  "PM_STATE_RESUME_NOTICE"
  "PM_STATE_SHUTDOWN_PREPARE_NOTICE"
  "PM_STATE_SUSPEND_NOTICE"
  "POWER_READY_MSG"
  "POWER_STR_REGISTER"
  "POWER_STR_RESUME_ACK_ERROR"
  "POWER_STR_RESUME_ACK_OK"
  "POWER_STR_SUSPEND_ACK_DELAY"
  "POWER_STR_SUSPEND_ACK_ERROR"
  "POWER_STR_SUSPEND_ACK_OK";

static const ::PROTOBUF_NAMESPACE_ID::internal::EnumEntry PowerMGRMsgId_entries[] = {
  { {PowerMGRMsgId_names + 0, 16}, 17 },
  { {PowerMGRMsgId_names + 16, 19}, 12 },
  { {PowerMGRMsgId_names + 35, 31}, 16 },
  { {PowerMGRMsgId_names + 66, 22}, 15 },
  { {PowerMGRMsgId_names + 88, 32}, 13 },
  { {PowerMGRMsgId_names + 120, 23}, 14 },
  { {PowerMGRMsgId_names + 143, 15}, 11 },
  { {PowerMGRMsgId_names + 158, 18}, 5 },
  { {PowerMGRMsgId_names + 176, 26}, 10 },
  { {PowerMGRMsgId_names + 202, 23}, 9 },
  { {PowerMGRMsgId_names + 225, 27}, 8 },
  { {PowerMGRMsgId_names + 252, 27}, 7 },
  { {PowerMGRMsgId_names + 279, 24}, 6 },
};

static const int PowerMGRMsgId_entries_by_number[] = {
  7, // 5 -> POWER_STR_REGISTER
  12, // 6 -> POWER_STR_SUSPEND_ACK_OK
  11, // 7 -> POWER_STR_SUSPEND_ACK_ERROR
  10, // 8 -> POWER_STR_SUSPEND_ACK_DELAY
  9, // 9 -> POWER_STR_RESUME_ACK_OK
  8, // 10 -> POWER_STR_RESUME_ACK_ERROR
  6, // 11 -> POWER_READY_MSG
  1, // 12 -> ILCM_RESP_READY_MSG
  4, // 13 -> PM_STATE_SHUTDOWN_PREPARE_NOTICE
  5, // 14 -> PM_STATE_SUSPEND_NOTICE
  3, // 15 -> PM_STATE_RESUME_NOTICE
  2, // 16 -> PM_STATE_RESUME_COMPLETE_NOTICE
  0, // 17 -> DISP_CONTROL_MSG
};

const std::string& PowerMGRMsgId_Name(
    PowerMGRMsgId value) {
  static const bool dummy =
      ::PROTOBUF_NAMESPACE_ID::internal::InitializeEnumStrings(
          PowerMGRMsgId_entries,
          PowerMGRMsgId_entries_by_number,
          13, PowerMGRMsgId_strings);
  (void) dummy;
  int idx = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumName(
      PowerMGRMsgId_entries,
      PowerMGRMsgId_entries_by_number,
      13, value);
  return idx == -1 ? ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString() :
                     PowerMGRMsgId_strings[idx].get();
}
bool PowerMGRMsgId_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, PowerMGRMsgId* value) {
  int int_value;
  bool success = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumValue(
      PowerMGRMsgId_entries, 13, name, &int_value);
  if (success) {
    *value = static_cast<PowerMGRMsgId>(int_value);
  }
  return success;
}
bool DispControlEventId_IsValid(int value) {
  switch (value) {
    case 100:
    case 101:
    case 102:
    case 103:
    case 104:
    case 105:
    case 106:
    case 107:
    case 108:
    case 109:
    case 110:
      return true;
    default:
      return false;
  }
}

static ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<std::string> DispControlEventId_strings[11] = {};

static const char DispControlEventId_names[] =
  "DISP_ANIM_STATE_SET"
  "DISP_BRIGHTNESS_GET"
  "DISP_BRIGHTNESS_NOTICE"
  "DISP_BRIGHTNESS_SET"
  "DISP_HW_VERSION_GET"
  "DISP_HW_VERSION_NOTICE"
  "DISP_POWERSTATE_GET"
  "DISP_POWERSTATE_NOTICE"
  "DISP_POWERSTATE_SET"
  "DISP_SW_VERSION_GET"
  "DISP_SW_VERSION_NOTICE";

static const ::PROTOBUF_NAMESPACE_ID::internal::EnumEntry DispControlEventId_entries[] = {
  { {DispControlEventId_names + 0, 19}, 110 },
  { {DispControlEventId_names + 19, 19}, 101 },
  { {DispControlEventId_names + 38, 22}, 102 },
  { {DispControlEventId_names + 60, 19}, 100 },
  { {DispControlEventId_names + 79, 19}, 106 },
  { {DispControlEventId_names + 98, 22}, 107 },
  { {DispControlEventId_names + 120, 19}, 104 },
  { {DispControlEventId_names + 139, 22}, 105 },
  { {DispControlEventId_names + 161, 19}, 103 },
  { {DispControlEventId_names + 180, 19}, 108 },
  { {DispControlEventId_names + 199, 22}, 109 },
};

static const int DispControlEventId_entries_by_number[] = {
  3, // 100 -> DISP_BRIGHTNESS_SET
  1, // 101 -> DISP_BRIGHTNESS_GET
  2, // 102 -> DISP_BRIGHTNESS_NOTICE
  8, // 103 -> DISP_POWERSTATE_SET
  6, // 104 -> DISP_POWERSTATE_GET
  7, // 105 -> DISP_POWERSTATE_NOTICE
  4, // 106 -> DISP_HW_VERSION_GET
  5, // 107 -> DISP_HW_VERSION_NOTICE
  9, // 108 -> DISP_SW_VERSION_GET
  10, // 109 -> DISP_SW_VERSION_NOTICE
  0, // 110 -> DISP_ANIM_STATE_SET
};

const std::string& DispControlEventId_Name(
    DispControlEventId value) {
  static const bool dummy =
      ::PROTOBUF_NAMESPACE_ID::internal::InitializeEnumStrings(
          DispControlEventId_entries,
          DispControlEventId_entries_by_number,
          11, DispControlEventId_strings);
  (void) dummy;
  int idx = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumName(
      DispControlEventId_entries,
      DispControlEventId_entries_by_number,
      11, value);
  return idx == -1 ? ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString() :
                     DispControlEventId_strings[idx].get();
}
bool DispControlEventId_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, DispControlEventId* value) {
  int int_value;
  bool success = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumValue(
      DispControlEventId_entries, 11, name, &int_value);
  if (success) {
    *value = static_cast<DispControlEventId>(int_value);
  }
  return success;
}

// ===================================================================

class DispBrightnessValue::_Internal {
 public:
  using HasBits = decltype(std::declval<DispBrightnessValue>()._has_bits_);
  static void set_has_displayid(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_level(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000003) ^ 0x00000003) != 0;
  }
};

DispBrightnessValue::DispBrightnessValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:autolink.DispBrightnessValue)
}
DispBrightnessValue::DispBrightnessValue(const DispBrightnessValue& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  ::memcpy(&displayid_, &from.displayid_,
    static_cast<size_t>(reinterpret_cast<char*>(&level_) -
    reinterpret_cast<char*>(&displayid_)) + sizeof(level_));
  // @@protoc_insertion_point(copy_constructor:autolink.DispBrightnessValue)
}

inline void DispBrightnessValue::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&displayid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&level_) -
    reinterpret_cast<char*>(&displayid_)) + sizeof(level_));
}

DispBrightnessValue::~DispBrightnessValue() {
  // @@protoc_insertion_point(destructor:autolink.DispBrightnessValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<std::string>();
}

inline void DispBrightnessValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void DispBrightnessValue::ArenaDtor(void* object) {
  DispBrightnessValue* _this = reinterpret_cast< DispBrightnessValue* >(object);
  (void)_this;
}
void DispBrightnessValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DispBrightnessValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DispBrightnessValue::Clear() {
// @@protoc_insertion_point(message_clear_start:autolink.DispBrightnessValue)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    ::memset(&displayid_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&level_) -
        reinterpret_cast<char*>(&displayid_)) + sizeof(level_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* DispBrightnessValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required uint32 displayId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_displayid(&has_bits);
          displayid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // required uint32 level = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_level(&has_bits);
          level_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DispBrightnessValue::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:autolink.DispBrightnessValue)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required uint32 displayId = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_displayid(), target);
  }

  // required uint32 level = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_level(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:autolink.DispBrightnessValue)
  return target;
}

size_t DispBrightnessValue::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:autolink.DispBrightnessValue)
  size_t total_size = 0;

  if (_internal_has_displayid()) {
    // required uint32 displayId = 1;
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_displayid());
  }

  if (_internal_has_level()) {
    // required uint32 level = 2;
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_level());
  }

  return total_size;
}
size_t DispBrightnessValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:autolink.DispBrightnessValue)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000003) ^ 0x00000003) == 0) {  // All required fields are present.
    // required uint32 displayId = 1;
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_displayid());

    // required uint32 level = 2;
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_level());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DispBrightnessValue::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::PROTOBUF_NAMESPACE_ID::internal::DownCast<const DispBrightnessValue*>(
      &from));
}

void DispBrightnessValue::MergeFrom(const DispBrightnessValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:autolink.DispBrightnessValue)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      displayid_ = from.displayid_;
    }
    if (cached_has_bits & 0x00000002u) {
      level_ = from.level_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void DispBrightnessValue::CopyFrom(const DispBrightnessValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:autolink.DispBrightnessValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DispBrightnessValue::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void DispBrightnessValue::InternalSwap(DispBrightnessValue* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DispBrightnessValue, level_)
      + sizeof(DispBrightnessValue::level_)
      - PROTOBUF_FIELD_OFFSET(DispBrightnessValue, displayid_)>(
          reinterpret_cast<char*>(&displayid_),
          reinterpret_cast<char*>(&other->displayid_));
}

std::string DispBrightnessValue::GetTypeName() const {
  return "autolink.DispBrightnessValue";
}


// ===================================================================

class DispPowerStateValue::_Internal {
 public:
  using HasBits = decltype(std::declval<DispPowerStateValue>()._has_bits_);
  static void set_has_displayid(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_poweron(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000003) ^ 0x00000003) != 0;
  }
};

DispPowerStateValue::DispPowerStateValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:autolink.DispPowerStateValue)
}
DispPowerStateValue::DispPowerStateValue(const DispPowerStateValue& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  ::memcpy(&displayid_, &from.displayid_,
    static_cast<size_t>(reinterpret_cast<char*>(&poweron_) -
    reinterpret_cast<char*>(&displayid_)) + sizeof(poweron_));
  // @@protoc_insertion_point(copy_constructor:autolink.DispPowerStateValue)
}

inline void DispPowerStateValue::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&displayid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&poweron_) -
    reinterpret_cast<char*>(&displayid_)) + sizeof(poweron_));
}

DispPowerStateValue::~DispPowerStateValue() {
  // @@protoc_insertion_point(destructor:autolink.DispPowerStateValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<std::string>();
}

inline void DispPowerStateValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void DispPowerStateValue::ArenaDtor(void* object) {
  DispPowerStateValue* _this = reinterpret_cast< DispPowerStateValue* >(object);
  (void)_this;
}
void DispPowerStateValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DispPowerStateValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DispPowerStateValue::Clear() {
// @@protoc_insertion_point(message_clear_start:autolink.DispPowerStateValue)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    ::memset(&displayid_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&poweron_) -
        reinterpret_cast<char*>(&displayid_)) + sizeof(poweron_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* DispPowerStateValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required uint32 displayId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_displayid(&has_bits);
          displayid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // required bool powerOn = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_poweron(&has_bits);
          poweron_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DispPowerStateValue::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:autolink.DispPowerStateValue)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required uint32 displayId = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_displayid(), target);
  }

  // required bool powerOn = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_poweron(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:autolink.DispPowerStateValue)
  return target;
}

size_t DispPowerStateValue::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:autolink.DispPowerStateValue)
  size_t total_size = 0;

  if (_internal_has_displayid()) {
    // required uint32 displayId = 1;
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_displayid());
  }

  if (_internal_has_poweron()) {
    // required bool powerOn = 2;
    total_size += 1 + 1;
  }

  return total_size;
}
size_t DispPowerStateValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:autolink.DispPowerStateValue)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000003) ^ 0x00000003) == 0) {  // All required fields are present.
    // required uint32 displayId = 1;
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_displayid());

    // required bool powerOn = 2;
    total_size += 1 + 1;

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DispPowerStateValue::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::PROTOBUF_NAMESPACE_ID::internal::DownCast<const DispPowerStateValue*>(
      &from));
}

void DispPowerStateValue::MergeFrom(const DispPowerStateValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:autolink.DispPowerStateValue)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      displayid_ = from.displayid_;
    }
    if (cached_has_bits & 0x00000002u) {
      poweron_ = from.poweron_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void DispPowerStateValue::CopyFrom(const DispPowerStateValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:autolink.DispPowerStateValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DispPowerStateValue::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void DispPowerStateValue::InternalSwap(DispPowerStateValue* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DispPowerStateValue, poweron_)
      + sizeof(DispPowerStateValue::poweron_)
      - PROTOBUF_FIELD_OFFSET(DispPowerStateValue, displayid_)>(
          reinterpret_cast<char*>(&displayid_),
          reinterpret_cast<char*>(&other->displayid_));
}

std::string DispPowerStateValue::GetTypeName() const {
  return "autolink.DispPowerStateValue";
}


// ===================================================================

class DispHwVersionNotice::_Internal {
 public:
  using HasBits = decltype(std::declval<DispHwVersionNotice>()._has_bits_);
  static void set_has_hwversionmsg(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_size(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000003) ^ 0x00000003) != 0;
  }
};

DispHwVersionNotice::DispHwVersionNotice(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:autolink.DispHwVersionNotice)
}
DispHwVersionNotice::DispHwVersionNotice(const DispHwVersionNotice& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  hwversionmsg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    hwversionmsg_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_hwversionmsg()) {
    hwversionmsg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_hwversionmsg(), 
      GetArenaForAllocation());
  }
  size_ = from.size_;
  // @@protoc_insertion_point(copy_constructor:autolink.DispHwVersionNotice)
}

inline void DispHwVersionNotice::SharedCtor() {
hwversionmsg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  hwversionmsg_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
size_ = 0u;
}

DispHwVersionNotice::~DispHwVersionNotice() {
  // @@protoc_insertion_point(destructor:autolink.DispHwVersionNotice)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<std::string>();
}

inline void DispHwVersionNotice::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  hwversionmsg_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DispHwVersionNotice::ArenaDtor(void* object) {
  DispHwVersionNotice* _this = reinterpret_cast< DispHwVersionNotice* >(object);
  (void)_this;
}
void DispHwVersionNotice::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DispHwVersionNotice::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DispHwVersionNotice::Clear() {
// @@protoc_insertion_point(message_clear_start:autolink.DispHwVersionNotice)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    hwversionmsg_.ClearNonDefaultToEmpty();
  }
  size_ = 0u;
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* DispHwVersionNotice::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required string HwVersionMsg = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_hwversionmsg();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // required uint32 size = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_size(&has_bits);
          size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DispHwVersionNotice::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:autolink.DispHwVersionNotice)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required string HwVersionMsg = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_hwversionmsg(), target);
  }

  // required uint32 size = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_size(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:autolink.DispHwVersionNotice)
  return target;
}

size_t DispHwVersionNotice::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:autolink.DispHwVersionNotice)
  size_t total_size = 0;

  if (_internal_has_hwversionmsg()) {
    // required string HwVersionMsg = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_hwversionmsg());
  }

  if (_internal_has_size()) {
    // required uint32 size = 2;
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_size());
  }

  return total_size;
}
size_t DispHwVersionNotice::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:autolink.DispHwVersionNotice)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000003) ^ 0x00000003) == 0) {  // All required fields are present.
    // required string HwVersionMsg = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_hwversionmsg());

    // required uint32 size = 2;
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_size());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DispHwVersionNotice::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::PROTOBUF_NAMESPACE_ID::internal::DownCast<const DispHwVersionNotice*>(
      &from));
}

void DispHwVersionNotice::MergeFrom(const DispHwVersionNotice& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:autolink.DispHwVersionNotice)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_hwversionmsg(from._internal_hwversionmsg());
    }
    if (cached_has_bits & 0x00000002u) {
      size_ = from.size_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void DispHwVersionNotice::CopyFrom(const DispHwVersionNotice& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:autolink.DispHwVersionNotice)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DispHwVersionNotice::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void DispHwVersionNotice::InternalSwap(DispHwVersionNotice* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &hwversionmsg_, lhs_arena,
      &other->hwversionmsg_, rhs_arena
  );
  swap(size_, other->size_);
}

std::string DispHwVersionNotice::GetTypeName() const {
  return "autolink.DispHwVersionNotice";
}


// ===================================================================

class DispSwVersionNotice::_Internal {
 public:
  using HasBits = decltype(std::declval<DispSwVersionNotice>()._has_bits_);
  static void set_has_swversionmsg(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_size(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000003) ^ 0x00000003) != 0;
  }
};

DispSwVersionNotice::DispSwVersionNotice(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:autolink.DispSwVersionNotice)
}
DispSwVersionNotice::DispSwVersionNotice(const DispSwVersionNotice& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  swversionmsg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    swversionmsg_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_swversionmsg()) {
    swversionmsg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_swversionmsg(), 
      GetArenaForAllocation());
  }
  size_ = from.size_;
  // @@protoc_insertion_point(copy_constructor:autolink.DispSwVersionNotice)
}

inline void DispSwVersionNotice::SharedCtor() {
swversionmsg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  swversionmsg_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
size_ = 0u;
}

DispSwVersionNotice::~DispSwVersionNotice() {
  // @@protoc_insertion_point(destructor:autolink.DispSwVersionNotice)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<std::string>();
}

inline void DispSwVersionNotice::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  swversionmsg_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DispSwVersionNotice::ArenaDtor(void* object) {
  DispSwVersionNotice* _this = reinterpret_cast< DispSwVersionNotice* >(object);
  (void)_this;
}
void DispSwVersionNotice::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DispSwVersionNotice::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DispSwVersionNotice::Clear() {
// @@protoc_insertion_point(message_clear_start:autolink.DispSwVersionNotice)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    swversionmsg_.ClearNonDefaultToEmpty();
  }
  size_ = 0u;
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* DispSwVersionNotice::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required string SwVersionMsg = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_swversionmsg();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // required uint32 size = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_size(&has_bits);
          size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DispSwVersionNotice::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:autolink.DispSwVersionNotice)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required string SwVersionMsg = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_swversionmsg(), target);
  }

  // required uint32 size = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_size(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:autolink.DispSwVersionNotice)
  return target;
}

size_t DispSwVersionNotice::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:autolink.DispSwVersionNotice)
  size_t total_size = 0;

  if (_internal_has_swversionmsg()) {
    // required string SwVersionMsg = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_swversionmsg());
  }

  if (_internal_has_size()) {
    // required uint32 size = 2;
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_size());
  }

  return total_size;
}
size_t DispSwVersionNotice::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:autolink.DispSwVersionNotice)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000003) ^ 0x00000003) == 0) {  // All required fields are present.
    // required string SwVersionMsg = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_swversionmsg());

    // required uint32 size = 2;
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_size());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DispSwVersionNotice::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::PROTOBUF_NAMESPACE_ID::internal::DownCast<const DispSwVersionNotice*>(
      &from));
}

void DispSwVersionNotice::MergeFrom(const DispSwVersionNotice& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:autolink.DispSwVersionNotice)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_swversionmsg(from._internal_swversionmsg());
    }
    if (cached_has_bits & 0x00000002u) {
      size_ = from.size_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void DispSwVersionNotice::CopyFrom(const DispSwVersionNotice& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:autolink.DispSwVersionNotice)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DispSwVersionNotice::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void DispSwVersionNotice::InternalSwap(DispSwVersionNotice* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &swversionmsg_, lhs_arena,
      &other->swversionmsg_, rhs_arena
  );
  swap(size_, other->size_);
}

std::string DispSwVersionNotice::GetTypeName() const {
  return "autolink.DispSwVersionNotice";
}


// ===================================================================

class ILCMControlClientProp::_Internal {
 public:
  using HasBits = decltype(std::declval<ILCMControlClientProp>()._has_bits_);
  static void set_has_clientid(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_clientpid(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_clientpriority(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000007) ^ 0x00000007) != 0;
  }
};

ILCMControlClientProp::ILCMControlClientProp(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:autolink.ILCMControlClientProp)
}
ILCMControlClientProp::ILCMControlClientProp(const ILCMControlClientProp& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  clientid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    clientid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_clientid()) {
    clientid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_clientid(), 
      GetArenaForAllocation());
  }
  ::memcpy(&clientpid_, &from.clientpid_,
    static_cast<size_t>(reinterpret_cast<char*>(&clientpriority_) -
    reinterpret_cast<char*>(&clientpid_)) + sizeof(clientpriority_));
  // @@protoc_insertion_point(copy_constructor:autolink.ILCMControlClientProp)
}

inline void ILCMControlClientProp::SharedCtor() {
clientid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  clientid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&clientpid_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&clientpriority_) -
    reinterpret_cast<char*>(&clientpid_)) + sizeof(clientpriority_));
}

ILCMControlClientProp::~ILCMControlClientProp() {
  // @@protoc_insertion_point(destructor:autolink.ILCMControlClientProp)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<std::string>();
}

inline void ILCMControlClientProp::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  clientid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ILCMControlClientProp::ArenaDtor(void* object) {
  ILCMControlClientProp* _this = reinterpret_cast< ILCMControlClientProp* >(object);
  (void)_this;
}
void ILCMControlClientProp::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ILCMControlClientProp::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ILCMControlClientProp::Clear() {
// @@protoc_insertion_point(message_clear_start:autolink.ILCMControlClientProp)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    clientid_.ClearNonDefaultToEmpty();
  }
  if (cached_has_bits & 0x00000006u) {
    ::memset(&clientpid_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&clientpriority_) -
        reinterpret_cast<char*>(&clientpid_)) + sizeof(clientpriority_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* ILCMControlClientProp::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required string clientId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_clientid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // required uint32 clientPid = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_clientpid(&has_bits);
          clientpid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // required uint32 clientPriority = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _Internal::set_has_clientpriority(&has_bits);
          clientpriority_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ILCMControlClientProp::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:autolink.ILCMControlClientProp)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required string clientId = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_clientid(), target);
  }

  // required uint32 clientPid = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_clientpid(), target);
  }

  // required uint32 clientPriority = 3;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_clientpriority(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:autolink.ILCMControlClientProp)
  return target;
}

size_t ILCMControlClientProp::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:autolink.ILCMControlClientProp)
  size_t total_size = 0;

  if (_internal_has_clientid()) {
    // required string clientId = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_clientid());
  }

  if (_internal_has_clientpid()) {
    // required uint32 clientPid = 2;
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_clientpid());
  }

  if (_internal_has_clientpriority()) {
    // required uint32 clientPriority = 3;
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_clientpriority());
  }

  return total_size;
}
size_t ILCMControlClientProp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:autolink.ILCMControlClientProp)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000007) ^ 0x00000007) == 0) {  // All required fields are present.
    // required string clientId = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_clientid());

    // required uint32 clientPid = 2;
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_clientpid());

    // required uint32 clientPriority = 3;
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_clientpriority());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ILCMControlClientProp::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::PROTOBUF_NAMESPACE_ID::internal::DownCast<const ILCMControlClientProp*>(
      &from));
}

void ILCMControlClientProp::MergeFrom(const ILCMControlClientProp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:autolink.ILCMControlClientProp)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_clientid(from._internal_clientid());
    }
    if (cached_has_bits & 0x00000002u) {
      clientpid_ = from.clientpid_;
    }
    if (cached_has_bits & 0x00000004u) {
      clientpriority_ = from.clientpriority_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void ILCMControlClientProp::CopyFrom(const ILCMControlClientProp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:autolink.ILCMControlClientProp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ILCMControlClientProp::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void ILCMControlClientProp::InternalSwap(ILCMControlClientProp* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &clientid_, lhs_arena,
      &other->clientid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ILCMControlClientProp, clientpriority_)
      + sizeof(ILCMControlClientProp::clientpriority_)
      - PROTOBUF_FIELD_OFFSET(ILCMControlClientProp, clientpid_)>(
          reinterpret_cast<char*>(&clientpid_),
          reinterpret_cast<char*>(&other->clientpid_));
}

std::string ILCMControlClientProp::GetTypeName() const {
  return "autolink.ILCMControlClientProp";
}


// ===================================================================

class ILCMSuspendPrepareAckDelay::_Internal {
 public:
  using HasBits = decltype(std::declval<ILCMSuspendPrepareAckDelay>()._has_bits_);
  static const ::autolink::ILCMControlClientProp& clientprop(const ILCMSuspendPrepareAckDelay* msg);
  static void set_has_clientprop(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_delaytime(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_delayreason(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000007) ^ 0x00000007) != 0;
  }
};

const ::autolink::ILCMControlClientProp&
ILCMSuspendPrepareAckDelay::_Internal::clientprop(const ILCMSuspendPrepareAckDelay* msg) {
  return *msg->clientprop_;
}
ILCMSuspendPrepareAckDelay::ILCMSuspendPrepareAckDelay(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:autolink.ILCMSuspendPrepareAckDelay)
}
ILCMSuspendPrepareAckDelay::ILCMSuspendPrepareAckDelay(const ILCMSuspendPrepareAckDelay& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  delayreason_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    delayreason_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_delayreason()) {
    delayreason_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_delayreason(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_clientprop()) {
    clientprop_ = new ::autolink::ILCMControlClientProp(*from.clientprop_);
  } else {
    clientprop_ = nullptr;
  }
  delaytime_ = from.delaytime_;
  // @@protoc_insertion_point(copy_constructor:autolink.ILCMSuspendPrepareAckDelay)
}

inline void ILCMSuspendPrepareAckDelay::SharedCtor() {
delayreason_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  delayreason_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&clientprop_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&delaytime_) -
    reinterpret_cast<char*>(&clientprop_)) + sizeof(delaytime_));
}

ILCMSuspendPrepareAckDelay::~ILCMSuspendPrepareAckDelay() {
  // @@protoc_insertion_point(destructor:autolink.ILCMSuspendPrepareAckDelay)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<std::string>();
}

inline void ILCMSuspendPrepareAckDelay::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  delayreason_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete clientprop_;
}

void ILCMSuspendPrepareAckDelay::ArenaDtor(void* object) {
  ILCMSuspendPrepareAckDelay* _this = reinterpret_cast< ILCMSuspendPrepareAckDelay* >(object);
  (void)_this;
}
void ILCMSuspendPrepareAckDelay::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ILCMSuspendPrepareAckDelay::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ILCMSuspendPrepareAckDelay::Clear() {
// @@protoc_insertion_point(message_clear_start:autolink.ILCMSuspendPrepareAckDelay)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      delayreason_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(clientprop_ != nullptr);
      clientprop_->Clear();
    }
  }
  delaytime_ = 0u;
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* ILCMSuspendPrepareAckDelay::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required .autolink.ILCMControlClientProp clientProp = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_clientprop(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // required uint32 delayTime = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_delaytime(&has_bits);
          delaytime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // required string delayReason = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_delayreason();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ILCMSuspendPrepareAckDelay::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:autolink.ILCMSuspendPrepareAckDelay)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .autolink.ILCMControlClientProp clientProp = 1;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::clientprop(this), target, stream);
  }

  // required uint32 delayTime = 2;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_delaytime(), target);
  }

  // required string delayReason = 3;
  if (cached_has_bits & 0x00000001u) {
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_delayreason(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:autolink.ILCMSuspendPrepareAckDelay)
  return target;
}

size_t ILCMSuspendPrepareAckDelay::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:autolink.ILCMSuspendPrepareAckDelay)
  size_t total_size = 0;

  if (_internal_has_delayreason()) {
    // required string delayReason = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_delayreason());
  }

  if (_internal_has_clientprop()) {
    // required .autolink.ILCMControlClientProp clientProp = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *clientprop_);
  }

  if (_internal_has_delaytime()) {
    // required uint32 delayTime = 2;
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_delaytime());
  }

  return total_size;
}
size_t ILCMSuspendPrepareAckDelay::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:autolink.ILCMSuspendPrepareAckDelay)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000007) ^ 0x00000007) == 0) {  // All required fields are present.
    // required string delayReason = 3;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_delayreason());

    // required .autolink.ILCMControlClientProp clientProp = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *clientprop_);

    // required uint32 delayTime = 2;
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_delaytime());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ILCMSuspendPrepareAckDelay::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::PROTOBUF_NAMESPACE_ID::internal::DownCast<const ILCMSuspendPrepareAckDelay*>(
      &from));
}

void ILCMSuspendPrepareAckDelay::MergeFrom(const ILCMSuspendPrepareAckDelay& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:autolink.ILCMSuspendPrepareAckDelay)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_delayreason(from._internal_delayreason());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_clientprop()->::autolink::ILCMControlClientProp::MergeFrom(from._internal_clientprop());
    }
    if (cached_has_bits & 0x00000004u) {
      delaytime_ = from.delaytime_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void ILCMSuspendPrepareAckDelay::CopyFrom(const ILCMSuspendPrepareAckDelay& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:autolink.ILCMSuspendPrepareAckDelay)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ILCMSuspendPrepareAckDelay::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (_internal_has_clientprop()) {
    if (!clientprop_->IsInitialized()) return false;
  }
  return true;
}

void ILCMSuspendPrepareAckDelay::InternalSwap(ILCMSuspendPrepareAckDelay* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &delayreason_, lhs_arena,
      &other->delayreason_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ILCMSuspendPrepareAckDelay, delaytime_)
      + sizeof(ILCMSuspendPrepareAckDelay::delaytime_)
      - PROTOBUF_FIELD_OFFSET(ILCMSuspendPrepareAckDelay, clientprop_)>(
          reinterpret_cast<char*>(&clientprop_),
          reinterpret_cast<char*>(&other->clientprop_));
}

std::string ILCMSuspendPrepareAckDelay::GetTypeName() const {
  return "autolink.ILCMSuspendPrepareAckDelay";
}


// ===================================================================

class DispControlPropValue::_Internal {
 public:
  using HasBits = decltype(std::declval<DispControlPropValue>()._has_bits_);
  static void set_has_eventid(HasBits* has_bits) {
    (*has_bits)[0] |= 512u;
  }
  static const ::autolink::DispBrightnessValue& brightnessvalue(const DispControlPropValue* msg);
  static void set_has_brightnessvalue(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_dispidbrightnessget(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static const ::autolink::DispPowerStateValue& powerstate(const DispControlPropValue* msg);
  static void set_has_powerstate(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_dispidpowerstateget(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static void set_has_dispidhwversionget(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
  static const ::autolink::DispHwVersionNotice& hwversion(const DispControlPropValue* msg);
  static void set_has_hwversion(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_dispidswversionget(HasBits* has_bits) {
    (*has_bits)[0] |= 128u;
  }
  static const ::autolink::DispSwVersionNotice& swversion(const DispControlPropValue* msg);
  static void set_has_swversion(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_dispanimstate(HasBits* has_bits) {
    (*has_bits)[0] |= 256u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000200) ^ 0x00000200) != 0;
  }
};

const ::autolink::DispBrightnessValue&
DispControlPropValue::_Internal::brightnessvalue(const DispControlPropValue* msg) {
  return *msg->brightnessvalue_;
}
const ::autolink::DispPowerStateValue&
DispControlPropValue::_Internal::powerstate(const DispControlPropValue* msg) {
  return *msg->powerstate_;
}
const ::autolink::DispHwVersionNotice&
DispControlPropValue::_Internal::hwversion(const DispControlPropValue* msg) {
  return *msg->hwversion_;
}
const ::autolink::DispSwVersionNotice&
DispControlPropValue::_Internal::swversion(const DispControlPropValue* msg) {
  return *msg->swversion_;
}
DispControlPropValue::DispControlPropValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:autolink.DispControlPropValue)
}
DispControlPropValue::DispControlPropValue(const DispControlPropValue& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  if (from._internal_has_brightnessvalue()) {
    brightnessvalue_ = new ::autolink::DispBrightnessValue(*from.brightnessvalue_);
  } else {
    brightnessvalue_ = nullptr;
  }
  if (from._internal_has_powerstate()) {
    powerstate_ = new ::autolink::DispPowerStateValue(*from.powerstate_);
  } else {
    powerstate_ = nullptr;
  }
  if (from._internal_has_hwversion()) {
    hwversion_ = new ::autolink::DispHwVersionNotice(*from.hwversion_);
  } else {
    hwversion_ = nullptr;
  }
  if (from._internal_has_swversion()) {
    swversion_ = new ::autolink::DispSwVersionNotice(*from.swversion_);
  } else {
    swversion_ = nullptr;
  }
  ::memcpy(&dispidbrightnessget_, &from.dispidbrightnessget_,
    static_cast<size_t>(reinterpret_cast<char*>(&eventid_) -
    reinterpret_cast<char*>(&dispidbrightnessget_)) + sizeof(eventid_));
  // @@protoc_insertion_point(copy_constructor:autolink.DispControlPropValue)
}

inline void DispControlPropValue::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&brightnessvalue_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&dispanimstate_) -
    reinterpret_cast<char*>(&brightnessvalue_)) + sizeof(dispanimstate_));
eventid_ = 100;
}

DispControlPropValue::~DispControlPropValue() {
  // @@protoc_insertion_point(destructor:autolink.DispControlPropValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<std::string>();
}

inline void DispControlPropValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete brightnessvalue_;
  if (this != internal_default_instance()) delete powerstate_;
  if (this != internal_default_instance()) delete hwversion_;
  if (this != internal_default_instance()) delete swversion_;
}

void DispControlPropValue::ArenaDtor(void* object) {
  DispControlPropValue* _this = reinterpret_cast< DispControlPropValue* >(object);
  (void)_this;
}
void DispControlPropValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DispControlPropValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DispControlPropValue::Clear() {
// @@protoc_insertion_point(message_clear_start:autolink.DispControlPropValue)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      GOOGLE_DCHECK(brightnessvalue_ != nullptr);
      brightnessvalue_->Clear();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(powerstate_ != nullptr);
      powerstate_->Clear();
    }
    if (cached_has_bits & 0x00000004u) {
      GOOGLE_DCHECK(hwversion_ != nullptr);
      hwversion_->Clear();
    }
    if (cached_has_bits & 0x00000008u) {
      GOOGLE_DCHECK(swversion_ != nullptr);
      swversion_->Clear();
    }
  }
  if (cached_has_bits & 0x000000f0u) {
    ::memset(&dispidbrightnessget_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&dispidswversionget_) -
        reinterpret_cast<char*>(&dispidbrightnessget_)) + sizeof(dispidswversionget_));
  }
  if (cached_has_bits & 0x00000300u) {
    dispanimstate_ = 0u;
    eventid_ = 100;
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* DispControlPropValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required .autolink.DispControlEventId eventId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::autolink::DispControlEventId_IsValid(val))) {
            _internal_set_eventid(static_cast<::autolink::DispControlEventId>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // optional .autolink.DispBrightnessValue brightnessValue = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_brightnessvalue(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional uint32 DispIdBrightnessGet = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _Internal::set_has_dispidbrightnessget(&has_bits);
          dispidbrightnessget_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .autolink.DispPowerStateValue powerState = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_powerstate(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional uint32 DispIdPowerStateGet = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          _Internal::set_has_dispidpowerstateget(&has_bits);
          dispidpowerstateget_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional uint32 DispIdHwVersionGet = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          _Internal::set_has_dispidhwversionget(&has_bits);
          dispidhwversionget_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .autolink.DispHwVersionNotice HwVersion = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_hwversion(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional uint32 DispIdSwVersionGet = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          _Internal::set_has_dispidswversionget(&has_bits);
          dispidswversionget_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .autolink.DispSwVersionNotice SwVersion = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          ptr = ctx->ParseMessage(_internal_mutable_swversion(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional uint32 DispAnimState = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 80)) {
          _Internal::set_has_dispanimstate(&has_bits);
          dispanimstate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DispControlPropValue::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:autolink.DispControlPropValue)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .autolink.DispControlEventId eventId = 1;
  if (cached_has_bits & 0x00000200u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_eventid(), target);
  }

  // optional .autolink.DispBrightnessValue brightnessValue = 2;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::brightnessvalue(this), target, stream);
  }

  // optional uint32 DispIdBrightnessGet = 3;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_dispidbrightnessget(), target);
  }

  // optional .autolink.DispPowerStateValue powerState = 4;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::powerstate(this), target, stream);
  }

  // optional uint32 DispIdPowerStateGet = 5;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_dispidpowerstateget(), target);
  }

  // optional uint32 DispIdHwVersionGet = 6;
  if (cached_has_bits & 0x00000040u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_dispidhwversionget(), target);
  }

  // optional .autolink.DispHwVersionNotice HwVersion = 7;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::hwversion(this), target, stream);
  }

  // optional uint32 DispIdSwVersionGet = 8;
  if (cached_has_bits & 0x00000080u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_dispidswversionget(), target);
  }

  // optional .autolink.DispSwVersionNotice SwVersion = 9;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        9, _Internal::swversion(this), target, stream);
  }

  // optional uint32 DispAnimState = 10;
  if (cached_has_bits & 0x00000100u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(10, this->_internal_dispanimstate(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:autolink.DispControlPropValue)
  return target;
}

size_t DispControlPropValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:autolink.DispControlPropValue)
  size_t total_size = 0;

  // required .autolink.DispControlEventId eventId = 1;
  if (_internal_has_eventid()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_eventid());
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    // optional .autolink.DispBrightnessValue brightnessValue = 2;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *brightnessvalue_);
    }

    // optional .autolink.DispPowerStateValue powerState = 4;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *powerstate_);
    }

    // optional .autolink.DispHwVersionNotice HwVersion = 7;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *hwversion_);
    }

    // optional .autolink.DispSwVersionNotice SwVersion = 9;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *swversion_);
    }

    // optional uint32 DispIdBrightnessGet = 3;
    if (cached_has_bits & 0x00000010u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_dispidbrightnessget());
    }

    // optional uint32 DispIdPowerStateGet = 5;
    if (cached_has_bits & 0x00000020u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_dispidpowerstateget());
    }

    // optional uint32 DispIdHwVersionGet = 6;
    if (cached_has_bits & 0x00000040u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_dispidhwversionget());
    }

    // optional uint32 DispIdSwVersionGet = 8;
    if (cached_has_bits & 0x00000080u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_dispidswversionget());
    }

  }
  // optional uint32 DispAnimState = 10;
  if (cached_has_bits & 0x00000100u) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_dispanimstate());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DispControlPropValue::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::PROTOBUF_NAMESPACE_ID::internal::DownCast<const DispControlPropValue*>(
      &from));
}

void DispControlPropValue::MergeFrom(const DispControlPropValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:autolink.DispControlPropValue)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_mutable_brightnessvalue()->::autolink::DispBrightnessValue::MergeFrom(from._internal_brightnessvalue());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_powerstate()->::autolink::DispPowerStateValue::MergeFrom(from._internal_powerstate());
    }
    if (cached_has_bits & 0x00000004u) {
      _internal_mutable_hwversion()->::autolink::DispHwVersionNotice::MergeFrom(from._internal_hwversion());
    }
    if (cached_has_bits & 0x00000008u) {
      _internal_mutable_swversion()->::autolink::DispSwVersionNotice::MergeFrom(from._internal_swversion());
    }
    if (cached_has_bits & 0x00000010u) {
      dispidbrightnessget_ = from.dispidbrightnessget_;
    }
    if (cached_has_bits & 0x00000020u) {
      dispidpowerstateget_ = from.dispidpowerstateget_;
    }
    if (cached_has_bits & 0x00000040u) {
      dispidhwversionget_ = from.dispidhwversionget_;
    }
    if (cached_has_bits & 0x00000080u) {
      dispidswversionget_ = from.dispidswversionget_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  if (cached_has_bits & 0x00000300u) {
    if (cached_has_bits & 0x00000100u) {
      dispanimstate_ = from.dispanimstate_;
    }
    if (cached_has_bits & 0x00000200u) {
      eventid_ = from.eventid_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void DispControlPropValue::CopyFrom(const DispControlPropValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:autolink.DispControlPropValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DispControlPropValue::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (_internal_has_brightnessvalue()) {
    if (!brightnessvalue_->IsInitialized()) return false;
  }
  if (_internal_has_powerstate()) {
    if (!powerstate_->IsInitialized()) return false;
  }
  if (_internal_has_hwversion()) {
    if (!hwversion_->IsInitialized()) return false;
  }
  if (_internal_has_swversion()) {
    if (!swversion_->IsInitialized()) return false;
  }
  return true;
}

void DispControlPropValue::InternalSwap(DispControlPropValue* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DispControlPropValue, dispanimstate_)
      + sizeof(DispControlPropValue::dispanimstate_)
      - PROTOBUF_FIELD_OFFSET(DispControlPropValue, brightnessvalue_)>(
          reinterpret_cast<char*>(&brightnessvalue_),
          reinterpret_cast<char*>(&other->brightnessvalue_));
  swap(eventid_, other->eventid_);
}

std::string DispControlPropValue::GetTypeName() const {
  return "autolink.DispControlPropValue";
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace autolink
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::autolink::DispBrightnessValue* Arena::CreateMaybeMessage< ::autolink::DispBrightnessValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::autolink::DispBrightnessValue >(arena);
}
template<> PROTOBUF_NOINLINE ::autolink::DispPowerStateValue* Arena::CreateMaybeMessage< ::autolink::DispPowerStateValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::autolink::DispPowerStateValue >(arena);
}
template<> PROTOBUF_NOINLINE ::autolink::DispHwVersionNotice* Arena::CreateMaybeMessage< ::autolink::DispHwVersionNotice >(Arena* arena) {
  return Arena::CreateMessageInternal< ::autolink::DispHwVersionNotice >(arena);
}
template<> PROTOBUF_NOINLINE ::autolink::DispSwVersionNotice* Arena::CreateMaybeMessage< ::autolink::DispSwVersionNotice >(Arena* arena) {
  return Arena::CreateMessageInternal< ::autolink::DispSwVersionNotice >(arena);
}
template<> PROTOBUF_NOINLINE ::autolink::ILCMControlClientProp* Arena::CreateMaybeMessage< ::autolink::ILCMControlClientProp >(Arena* arena) {
  return Arena::CreateMessageInternal< ::autolink::ILCMControlClientProp >(arena);
}
template<> PROTOBUF_NOINLINE ::autolink::ILCMSuspendPrepareAckDelay* Arena::CreateMaybeMessage< ::autolink::ILCMSuspendPrepareAckDelay >(Arena* arena) {
  return Arena::CreateMessageInternal< ::autolink::ILCMSuspendPrepareAckDelay >(arena);
}
template<> PROTOBUF_NOINLINE ::autolink::DispControlPropValue* Arena::CreateMaybeMessage< ::autolink::DispControlPropValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::autolink::DispControlPropValue >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
