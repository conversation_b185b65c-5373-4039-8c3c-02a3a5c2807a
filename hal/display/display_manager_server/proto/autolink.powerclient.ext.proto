syntax = "proto2";
option optimize_for = LITE_RUNTIME;

package autolink;

enum PowerMGRMsgId {
    POWER_STR_REGISTER                  = 5;//qnx app向 PowerHal 注册监听 STR       序列结构: ILCMControlClientProp
    POWER_STR_SUSPEND_ACK_OK            = 6;//STR SUSPEND的时候，qnx app向 PowerHal 回复ACK OK     序列结构: ILCMControlClientProp
    POWER_STR_SUSPEND_ACK_ERROR         = 7;//STR SUSPEND的时候，qnx app向 PowerHal 回复ACK ERROR  序列结构: ILCMSuspendPrepareAckDelay
    POWER_STR_SUSPEND_ACK_DELAY         = 8;//STR SUSPEND的时候，qnx app向 PowerHal 回复ACK DELAY  序列结构: ILCMControlClientProp
    POWER_STR_RESUME_ACK_OK             = 9;//STR RESUME 的时候，qnx app向 PowerHal 回复ACK OK                    序列结构: ILCMControlClientProp
    POWER_STR_RESUME_ACK_ERROR          = 10;//STR RESUME 的时候，qnx app向 PowerHal 回复ACK ERROR                序列结构: ILCMControlClientProp
    POWER_READY_MSG                     = 11;//通知 ILCM，PowerHal已经准备就绪
    ILCM_RESP_READY_MSG                 = 12;//ILCM 收到 POWER_READY_MSG 之后回复告知 PowerHal 它已经READY        序列结构: ILCMControlClientProp
    PM_STATE_SHUTDOWN_PREPARE_NOTICE    = 13;//通知ILCM已经进入 STR PREPARE 生命周期
    PM_STATE_SUSPEND_NOTICE             = 14;//通知ILCM已经进入 STR SUSPEND 生命周期
    PM_STATE_RESUME_NOTICE              = 15;//通知ILCM已经进入 STR RESUME 生命周期
    PM_STATE_RESUME_COMPLETE_NOTICE     = 16;//通知ILCM已经进入 STR RESUME COMPLETE 生命周期
    DISP_CONTROL_MSG                    = 17;//用于ILCM和PowerHal相互通信 业务主要是屏的控制，具体逻辑由DispControlEventId决定
}

enum DispControlEventId {
    DISP_BRIGHTNESS_SET    = 100;//ILCM 向 PowerHal 发送屏幕亮度设置的申请
    DISP_BRIGHTNESS_GET    = 101;//ILCM 向 PowerHal 发送获取屏幕亮度的申请
    DISP_BRIGHTNESS_NOTICE = 102;//PowerHal 向 ILCM 反馈屏幕亮度值
    DISP_POWERSTATE_SET    = 103;//ILCM 向 PowerHal 发送操作屏幕背光的申请
    DISP_POWERSTATE_GET    = 104;//ILCM 向 PowerHal 发送获取屏幕背光状态的申请
    DISP_POWERSTATE_NOTICE = 105;//PowerHal 向 ILCM 反馈屏幕背光亮灭状态
    DISP_HW_VERSION_GET    = 106;//ILCM 向 PowerHal 发送获取屏幕硬件版本信息的申请
    DISP_HW_VERSION_NOTICE = 107;//PowerHal 向 ILCM 反馈屏幕硬件版本信息
    DISP_SW_VERSION_GET    = 108;//ILCM 向 PowerHal 发送获取屏幕软件版本信息的申请
    DISP_SW_VERSION_NOTICE = 109;//PowerHal 向 ILCM 反馈屏幕软件版本信息
    DISP_ANIM_STATE_SET    = 110;//ILCM 向 PowerHal 发送动画播放状态
}

//通过 DISP_CONTROL_MSG msgcode
//来传递对应displayId屏幕亮度level值 
//同时用于 ILCM 设置屏幕亮度和 PowerHal 反馈屏幕亮度值
//ILCM 使用 DispControlEventId::DISP_BRIGHTNESS_SET 设置屏幕亮度值
//PowerHal 使用 DispControlEventId::DISP_BRIGHTNESS_NOTICE 反馈屏幕亮度值
//ILCM 使用 DispControlEventId::DISP_BRIGHTNESS_GET + BriDispIdGet 来获取屏幕亮度值
message DispBrightnessValue {
    required uint32 displayId = 1;
    required uint32 level     = 2;
}

//通过 DISP_CONTROL_MSG msgcode
//来传递对应displayId背光状态布尔值 背光亮(true) 背光灭(false)
//同时用于 ILCM 设置屏背光和 PowerHal 反馈屏幕背光值
//ILCM 使用 DispControlEventId::DISP_POWERSTATE_SET 设置屏幕亮度值
//PowerHal 使用 DispControlEventId::DISP_POWERSTATE_NOTICE 反馈屏幕亮度值
//ILCM 使用 DispControlEventId::DISP_POWERSTATE_GET + DispIdPowerStateGet 获取对应DisplayId背光状态
message DispPowerStateValue {
    required uint32 displayId = 1;
    required bool   powerOn   = 2;
}

//PowerHal 通过 DISP_CONTROL_MSG msgcode
//向 ILCM 告知对应displayId屏幕硬件版本信息
//前提是 ILCM 通过 DispIdHwVersionGet 发起请求
//对应eventid = DispControlEventId::DISP_HW_VERSION_GET
message DispHwVersionNotice {
    required string HwVersionMsg = 1;
    required uint32 size         = 2;
}

//PowerHal 通过 DISP_CONTROL_MSG msgcode
//向 ILCM 告知对应displayId屏幕软件版本信息
//前提是 ILCM 通过 DispIdSwVersionGet 发起请求
//对应eventid = DispControlEventId::DISP_SW_VERSION_GET
message DispSwVersionNotice {
    required string SwVersionMsg = 1;
    required uint32 size         = 2;
}

//ILCM 和 power-hal 关于注册client 和 ack应答结构
message ILCMControlClientProp{
    required string clientId        = 1;        //clientId    
    required uint32 clientPid       = 2;        //clientPid
    required uint32 clientPriority  = 3;        //client优先级，保留此属性
}

message ILCMSuspendPrepareAckDelay{
    required ILCMControlClientProp clientProp   = 1;    //ilcm-client 属性
    required uint32 delayTime                   = 2;    //Delay 时间
    required string delayReason                 = 3;    //Delay 原因
}


message DispControlPropValue {
    required DispControlEventId eventId           = 1;//必选项 决定屏幕控制业务类型
    optional DispBrightnessValue brightnessValue  = 2;//可选项 用于传递屏幕亮度业务
    optional uint32 DispIdBrightnessGet           = 3;//可选项 ILCM 获取对应 displayId 屏幕亮度
    optional DispPowerStateValue powerState       = 4;//可选项 用于传递屏幕背光状态
    optional uint32 DispIdPowerStateGet           = 5;//可选项 ILCM 获取对应 displayId 屏幕背光状态
    optional uint32 DispIdHwVersionGet            = 6;//可选项 ILCM 获取对应 displayId 屏幕硬件版本信息
    optional DispHwVersionNotice HwVersion        = 7;//可选项 PowerHal 通知屏幕硬件版本信息
    optional uint32 DispIdSwVersionGet            = 8;//可选项 ILCM 获取对应 displayId 屏幕软件版本信息
    optional DispSwVersionNotice SwVersion        = 9;//可选项 PowerHal 通知屏幕软件版本信息
    optional uint32 DispAnimState                 = 10;//可选项 用于传递动画播放状态
}

