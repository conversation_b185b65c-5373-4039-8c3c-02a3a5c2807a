/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef _DISPLAY_SERVICE_FDBUS_H_
#define _DISPLAY_SERVICE_FDBUS_H_

#include <iostream>

#include "DisplayFDBusServer.h"
// #include "autolink.powerclient.pb.h"


namespace displayservice
{
//using autolink::PowerMode;
//using autolink::PowerPropValue;
//using autolink::PowerEventId;
//using autolink::PowerMessageId;

class DisplayFDBusServer;
class DisplayServiceFdbus
{
public:
    DisplayServiceFdbus();
    virtual ~DisplayServiceFdbus();
    void init();
 //   void notifyPowerSessionEvent(uint32_t code,const PowerPropValue& value);
  //  void talkWithGateWayALSleepComplete();
   // void notifyPowerMGRSTRState(uint32_t code);

private:
    DisplayFDBusServer * m_DisplayServer;
};

} // namespace displayservice

#endif

