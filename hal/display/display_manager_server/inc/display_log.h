#ifndef DISPLAY_LOG_H
#define DISPLAY_LOG_H

#include <stdint.h>
#include <fcntl.h>
#include <stdio.h>
#include <stdlib.h>
#include <sys/slog2.h>
#include <sys/slogcodes.h>
#include "logger_utils.h"

extern slog2_buffer_t displayctrl_buffer;

#define LOG_DISPLAYCTRL 20000
#define displayctrl_log(majorid, minorid, severity, fmt, ...) \
    (void)slog2f(displayctrl_buffer,(uint16_t)(majorid),(uint8_t)(severity),"%s[%s:%d]: " fmt,__progname,__BASEFILE__,__LINE__, ##__VA_ARGS__)
#define DISPLAYCTRL_SLOG(sev, format, ...) \
    displayctrl_log(LOG_DISPLAYCTRL, 0, sev, format, ##__VA_ARGS__)
#define DISPLAYCTRL_SLOGE(format, ...) DISPLAYCTRL_SLOG(_SLOG_ERROR, format, ##__VA_ARGS__)
#define DISPLAYCTRL_SLOGI(format, ...) DISPLAYCTRL_SLOG(_SLOG_INFO, format, ##__VA_ARGS__)

#endif

