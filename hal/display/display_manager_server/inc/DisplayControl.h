#ifndef _DISPLAY_CONTROL_H
#define _DISPLAY_CONTROL_H
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif //__cplusplus


#include "display_ctrl_api.h"


int disp_dispctrl_set_display_brightness(int displayId, int level);
int disp_dispctrl_get_display_backlight_brightness(int displayId);
int disp_dispctrl_set_display_powerstate(int displayId, int powerstate);
int disp_dispctrl_get_display_power_state(int displayId);
int disp_dispctrl_get_display_software_version(int displayId, char* version, size_t versionSize);
int disp_dispctrl_get_display_hardware_version(int displayId, char* version, size_t versionSize);
int disp_dispctrl_get_display_temperature(int displayId);

#ifdef __cplusplus
}
#endif //__cplusplus

#endif //_DISPLAY_CONTROL_H

