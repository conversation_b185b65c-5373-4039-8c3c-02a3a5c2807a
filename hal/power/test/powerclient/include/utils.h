/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef _POWER_UTILS_H_
#define _POWER_UTILS_H_

template<typename ENUM>
inline constexpr int32_t toInt(ENUM const value) {
     return static_cast<int32_t>(value);
}

template<typename ENUM>
inline constexpr int32_t toUInt(ENUM const value) {
     return static_cast<uint32_t>(value);
}

template<typename ENUM>
inline constexpr int32_t toUInt64(ENUM const value) {
     return static_cast<uint64_t>(value);
}

#endif