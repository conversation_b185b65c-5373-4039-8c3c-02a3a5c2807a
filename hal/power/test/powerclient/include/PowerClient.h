/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef _POWER_CLIENT_H_
#define _POWER_CLIENT_H_

#include <iostream>
#include <list>
#include <string>
#include "PowerClientType.h"
#include "com.autolink.power.pb.h"
#include "com.autolink.power.priv.pb.h"

namespace powerclient
{
    using autolink::PowerPropValue;
    using autolink::PowerMessageId;

    using autolink::DispControlPropValue;
    using autolink::PowerMGRMsgId;
    using autolink::DispSwVersionNotice;
    using autolink::DispHwVersionNotice;
    using autolink::DispPowerStateValue;
    using autolink::DispBrightnessValue;
    using autolink::DispControlEventId;

    class IPowerClientCallback
    {
    public:
        IPowerClientCallback() = default;
        virtual ~IPowerClientCallback() = default;
        virtual void onConnect() = 0;
        virtual void onDisconnect() = 0;
        virtual void onRevicesMsg(uint32_t code, const PowerPropValue& data) = 0;
    };
    class PowerClient
    {
    public:
        PowerClient();
        virtual ~PowerClient();
        void connectService(IPowerClientCallback *callback, std::vector<PowerMessageId> &list);
        void disConnectService();
        void subScribe(PowerMessageId id);
        int sendMsg(uint32_t code, const PowerPropValue& data);
        int sendDispControlMsg(uint32_t code, const DispControlPropValue& data);
    };
} // namespace powerclient

#endif //_POWER_CLIENT_H_
