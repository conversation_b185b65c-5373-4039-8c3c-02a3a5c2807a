#ifndef _POWERCLIENT_SERVICE_H_
#define _POWERCLIENT_SERVICE_H_

#include "com.autolink.power.pb.h"
#include "com.autolink.power.priv.pb.h"
#include "PowerClient.h"
#include "PowerClientType.h"
#include <cstdint>
#include <stdint.h>
#include <screen/screen.h>
#include <sys/keycodes.h>

namespace powerclient
{

using autolink::PowerPropValue;
using autolink::PowerEventId;
using autolink::PowerMessageId;
using autolink::PowerMode;

using autolink::DispControlPropValue;
using autolink::PowerMGRMsgId;
using autolink::DispSwVersionNotice;
using autolink::DispHwVersionNotice;
using autolink::DispPowerStateValue;
using autolink::DispBrightnessValue;
using autolink::DispControlEventId;


using powerclient::IPowerClientCallback;
using powerclient::PowerClient;

class PowerClientService : public IPowerClientCallback
{
public:
    PowerClientService();
    ~PowerClientService();
    static PowerClientService* getInstance();
    void init();
    void onConnect();
    void onDisconnect();
    void onRevicesMsg(uint32_t code, const PowerPropValue& data);
    void SetUtTestCase(UTTestCode ut_code, int value);
    void RunUtTestCase();

private:
    void sendPowerPropToServer(uint32_t code, const PowerPropValue& data);
    void startSTRPowerOffTimer();
    void replyPowerOffInAndroidSleep(uint32_t timerId,uint32_t time);
    void DispControlTimer();
    void testDispControlTimer(uint32_t timerId,uint32_t time);
    void sendDispControlToServer(const DispControlPropValue& data);
    

private:
    PowerClient* m_Client;
    static std::mutex m_InstanceMutex;
    static PowerClientService* m_PowerClientService;
    bool isConnectComd = false;
};
}  //namespace powerclient
#endif //_POWERCLIENT_SERVICE_H_