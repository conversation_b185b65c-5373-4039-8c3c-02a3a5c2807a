/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/

#ifndef _POWER_LOG_DEFINE_H_
#define _POWER_LOG_DEFINE_H_

#ifdef AUTOLINK_POWERHAL_QNX

#include <amss/core/logger_codes.h>

#include <sys/slog.h>
#include <inttypes.h>
#include <sys/slog2.h>
#include <sys/slogcodes.h>

#include <cstdint>
#include <iostream>
#include "string"
#include <log.h> //qnx using
#else
#include <log/log.h>
#include <cstdint>
#include <iostream>
#include "string"
#endif //AUTOLINK_POWERHAL_QNX

#ifdef AUTOLINK_POWERHAL_QNX
    void power_slog2_init();
    #define LOG_TAG "PowerClient"
    #if 1
        extern int power_slog2fa_warning(const char *format, ...);
        extern int power_slog2fa_error(const char *format, ...);
        extern int power_slog2fa_info(const char *format, ...);
        extern int power_slog2fa_debug(const char *format, ...);

        #define LOG_D(...) power_slog2fa_debug(__VA_ARGS__);
        #define LOG_I(...) power_slog2fa_info(__VA_ARGS__);
        #define LOG_W(...) power_slog2fa_warning(__VA_ARGS__);
        #define LOG_E(...) power_slog2fa_error(__VA_ARGS__);
    #else
        extern char * __progname;
        #define POWER_SLOG(_tag,log_type,fmt,...)  (void)slog2f(NULL,(uint16_t)(1),(uint8_t)(log_type),"[ALPowerHal][%s]:%s:" fmt,__progname,_tag,##__VA_ARGS__)

        #define LOG_D(...) POWER_SLOG(LOG_TAG,_SLOG_DEBUG1, __VA_ARGS__)
        #define LOG_I(...) POWER_SLOG(LOG_TAG,_SLOG_INFO,__VA_ARGS__)
        #define LOG_W(...) POWER_SLOG(LOG_TAG,_SLOG_WARNING,__VA_ARGS__)
        #define LOG_E(...) POWER_SLOG(LOG_TAG,_SLOG_ERROR, __VA_ARGS__)
        #define LOG_F(...) POWER_SLOG(LOG_TAG,_SLOG_CRITICAL,__VA_ARGS__)
    #endif

#else //AUTOLINK_POWERHAL_QNX

    #define LOG_TAG "PowerClient"
    #define LOG_D(fmt, ...) ALOGD("[%s:%d]" fmt "", __FUNCTION__, __LINE__, ##__VA_ARGS__)
    #define LOG_I(fmt, ...) ALOGI("[%s:%d]" fmt "", __FUNCTION__, __LINE__, ##__VA_ARGS__)
    #define LOG_W(fmt, ...) ALOGw("[%s:%d]" fmt "", __FUNCTION__, __LINE__, ##__VA_ARGS__)
    #define LOG_E(fmt, ...) ALOGE("[%s:%d]" fmt "", __FUNCTION__, __LINE__, ##__VA_ARGS__)

#endif //#ifdef AUTOLINK_POWERHAL_QNX

#endif //_POWER_LOG_DEFINE_H_
