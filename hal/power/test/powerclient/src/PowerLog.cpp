/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#include <errno.h>
#include <stdio.h>
#include <stdarg.h>
#include <locale.h>
#include <PowerLog.h>
#include "amss/core/logger_utils.h"

#define MAX_LEN 4096
#define POWER_LINE __LINE__
#define POWER_LOG(sv, fmt, ...)               \
{                                            \
    logger_log(QCLOG_AMSS_QNP_SERVICES_TEST, \
                POWER_LINE, sv,               \
                fmt, ##__VA_ARGS__);          \
}

#define POWER_LOG_ERR(fmt, ...) POWER_LOG(QCLOG_ERROR, fmt, ##__VA_ARGS__)
#define POWER_LOG_WARN(fmt, ...) POWER_LOG(_SLOG_WARNING, fmt, ##__VA_ARGS__)
#define POWER_LOG_INFO(fmt, ...) POWER_LOG(QCLOG_INFO, fmt, ##__VA_ARGS__)
#define POWER_LOG_DEBUG(fmt, ...) POWER_LOG(QCLOG_DEBUG1, fmt, ##__VA_ARGS__)

/**This is our default buffer*/
static slog2_buffer_t amss_default_slog2_buffer;

/*Our buffer cfg*/
const static slog2_buffer_set_config_t buffer_cfg={
    .num_buffers=1,
    .buffer_set_name="ALPowerSlog",
    .verbosity_level=SLOG2_DEBUG1,
    .buffer_config={{.buffer_name="host_server",.num_pages=512}},
    .max_retries = 5
};

void power_slog2_init(void)
{
    setlocale(LC_ALL, "en_US.UTF-8");
    if(-1 == slog2_register(&buffer_cfg,&amss_default_slog2_buffer,0)){
        slogf(_SLOG_SETCODE(QCLOG_AMSS_QNP_SERVICES_PMIC_SERVICE,1),
              _SLOG_ERROR,"Couldn't register slog2 buffer");
        amss_default_slog2_buffer = NULL;
    }else{
        slog2_set_default_buffer(amss_default_slog2_buffer);
    }
}

int power_slog2fa_warning(const char *format, ...)
{
    setlocale(LC_ALL, "en_US.UTF-8");
    va_list aptr;
    char buffer[MAX_LEN + 1] = {0};
    va_start(aptr, format);
    int ret = vsnprintf(buffer, MAX_LEN, format, aptr);
    va_end(aptr);
    if (ret > 0)
    {
        POWER_LOG_WARN("%s", buffer);
    }
    else
    {
        POWER_LOG_WARN("power_slog2fa_warning error");
    }
    return 0;
}
int power_slog2fa_error(const char *format, ...)
{
    setlocale(LC_ALL, "en_US.UTF-8");
    va_list aptr;
    char buffer[MAX_LEN + 1] = {0};
    va_start(aptr, format);
    int ret = vsnprintf(buffer, MAX_LEN, format, aptr);
    va_end(aptr);
    if (ret > 0)
    {
        POWER_LOG_ERR("%s", buffer);
    }
    else
    {
        POWER_LOG_ERR("power_slog2fa_error error");
    }
    return 0;
}
int power_slog2fa_info(const char *format, ...)
{
    setlocale(LC_ALL, "en_US.UTF-8");
    va_list aptr;
    char buffer[MAX_LEN + 1] = {0};
    va_start(aptr, format);
    int ret = vsnprintf(buffer, MAX_LEN, format, aptr);
    va_end(aptr);
    if (ret > 0)
    {
        POWER_LOG_INFO("%s", buffer);
    }
    else
    {
        POWER_LOG_INFO("power_slog2fa_info error");
    }
    return 0;
}
int power_slog2fa_debug(const char *format, ...)
{
    setlocale(LC_ALL, "en_US.UTF-8");
    va_list aptr;
    char buffer[MAX_LEN + 1] = {0};
    va_start(aptr, format);
    int ret = vsnprintf(buffer, MAX_LEN, format, aptr);
    va_end(aptr);
    if (ret > 0)
    {
        POWER_LOG_DEBUG("%s", buffer);
    }
    else
    {
        POWER_LOG_DEBUG("power_slog2fa_debug error.");
    }
    return 0;
}