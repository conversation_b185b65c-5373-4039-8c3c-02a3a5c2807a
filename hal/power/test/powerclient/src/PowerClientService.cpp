/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#include <stdio.h>
#include <functional>
#include <iostream>
#include <fdbus/CBaseWorker.h>
#include <fdbus/CBaseJob.h>
#include <PowerLog.h>

#include "PowerTimer.h"
#include "PowerClientType.h"
#include "PowerClientService.h"
#include "utils.h"

#define KEY_PRESS		KEY_DOWN|KEY_SYM_VALID
#define KEY_RELEASE		KEY_SYM_VALID

namespace powerclient 
{
using namespace ipc::fdbus;
using autolink::PowerMGRMsgId;

std::mutex PowerClientService::m_InstanceMutex;
PowerClientService* PowerClientService::m_PowerClientService = nullptr;
static CBaseWorker powerclient_worker("power_client_thread", FDB_WORKER_DEFAULT, 3000);
static uint32_t count = 0;

PowerClientService::PowerClientService() {
    m_Client = nullptr;
}

PowerClientService::~PowerClientService() {
    if (m_Client != nullptr) {
        delete m_Client;
        m_Client = nullptr;
    } 
}

PowerClientService* PowerClientService::getInstance() {
    m_InstanceMutex.lock();
    if (m_PowerClientService == nullptr) {
        m_PowerClientService = new PowerClientService();
    }
    m_InstanceMutex.unlock();
    return m_PowerClientService;
}

static int prepare_key_event(screen_event_t screen_ev, int action, int keycode)
{
	int rc;

	rc = screen_set_event_property_iv(screen_ev, SCREEN_PROPERTY_FLAGS, &action);
	if (rc) {
		//LOGE("screen_set_event_property_iv(SCREEN_PROPERTY_FLAGS) failed. \
				flag=%d errno=%d", action, errno);
		return rc;
	}

	rc = screen_set_event_property_iv(screen_ev, SCREEN_PROPERTY_KEY_CAP, &keycode);
	if (rc) {
		//LOGE("screen_set_event_property_iv(SCREEN_PROPERTY_CAP) failed. \
				keycode=%d errno=%d", keycode, errno);
		return rc;
	}

	return 0;
}

static int send_key_event(screen_display_t screen_disp, int keycode)
{
	screen_event_t screen_ev;
	int val;
	int rc = 0;

	rc = screen_create_event(&screen_ev);
	if (rc) {
		//LOGE("screen_create_event failed. errno=%d", errno);
		return rc;
	}

	val = SCREEN_EVENT_KEYBOARD;
	rc = screen_set_event_property_iv(screen_ev, SCREEN_PROPERTY_TYPE, &val);
	if (rc) {
		//LOGE("screen_set_event_property_iv failed. errno=%d", errno);
		goto err;
	}

	// simulate power key press down
	rc = prepare_key_event(screen_ev, KEY_PRESS, keycode);
	if (rc)
		goto err;

	rc = screen_inject_event(screen_disp, screen_ev);
	if (rc) {
		//LOGE("key press. screen_inject_event failed. errno=%d", errno);
		goto err;
	}

	// simulate power key release
	if (prepare_key_event(screen_ev, KEY_RELEASE, keycode))
		goto err;
	rc = screen_inject_event(screen_disp, screen_ev);
	if (rc) {
		//LOGE("key release. screen_inject_event failed. errno=%d", errno);
		goto err;
	}

err:
	screen_destroy_event(screen_ev);
	return rc;
}

void PowerClientService::init() {
    if (m_Client == nullptr) {
        m_Client = new PowerClient();
    }
    /*start comd broadcast worker thread*/
    powerclient_worker.start();
    std::vector<PowerMessageId> subList;
    subList.push_back(PowerMessageId::MSG_POWERSERVICE_TO_GATEWAY);
    m_Client->connectService(this, subList);
    subList.clear();
    LOG_I("PowerClientService start: connectService done.\n");
}

void PowerClientService::sendPowerPropToServer(uint32_t code, const PowerPropValue& data) {
    if (isConnectComd) {
        int retry = 2;
        int ret = 0;
        while (retry--) {
            ret = m_Client->sendMsg(code, data);
            if (ret == PowerClientMessageState::Message_OK) {
                break;
            }
        }
    }else{
        LOG_E("PowerClientService sendPowerSignalToMCU error--not connect ALC \n");
    }
}

void PowerClientService::sendDispControlToServer(const DispControlPropValue& data) {
    if (isConnectComd) {
        m_Client->sendDispControlMsg(PowerMGRMsgId::DISP_CONTROL_MSG, data);
    } else {
        LOG_E("PowerClientService sendPowerSignalToMCU error--not connect ALC \n");
    }
}

void PowerClientService::startSTRPowerOffTimer() {
    LOG_I("startSTRPowerOffTimer.\n");
    if(!PowerTimer::getInstance()->isInitialized()) {
        PowerTimer::getInstance()->init();//确保初始化完成了
    }
    PowerTimer::getInstance()->startTimer(POWER_TIMER_POWER_OFF,
        std::bind(&PowerClientService::replyPowerOffInAndroidSleep, this, std::placeholders::_1, std::placeholders::_2), 2000);
}

void PowerClientService::DispControlTimer() {
    LOG_I("DispControlTimer.\n");
    if(!PowerTimer::getInstance()->isInitialized()) {
        PowerTimer::getInstance()->init();//确保初始化完成了
    }
    PowerTimer::getInstance()->startTimer(POWER_TIMER_DISP_CONTROL_TEST,
        std::bind(&PowerClientService::testDispControlTimer, this, std::placeholders::_1, std::placeholders::_2), 2000);
}

void PowerClientService::testDispControlTimer(uint32_t timerId,uint32_t time) {

    // LOG_I("PowerClientService::replyPowerOffInAndroidSleep-timerId=%d,time=%d\n",timerId,time);
    PowerPropValue data;
    data.set_eventid(PowerEventId::PWR_MCU_RESP_POWER_MODE_EVENT);
    static int mode = 1;
    mode++;
    
    data.set_value(mode);//STR下电
    sendPowerPropToServer(toUInt(PowerMessageId::MSG_GATEWAY_TO_POWERSERVICE),data);

    if (mode <= 7)  DispControlTimer();

    // DispControlPropValue data;
    // if (count == 0) {
    //     count ++;
    //     data.set_eventid(DispControlEventId::DISP_BRIGHTNESS_SET);//设置屏亮度
    //     data.mutable_brightnessvalue()->set_displayid(0);//0:中控屏
    //     data.mutable_brightnessvalue()->set_level(30);
    //     sendDispControlToServer(data);
    //     DispControlTimer();
    //     LOG_I("TestTimer::DISP_BRIGHTNESS_SET-30");
    // } else if (count == 1) {
    //     count ++;
    //     data.set_eventid(DispControlEventId::DISP_BRIGHTNESS_GET);//获取屏亮度
    //     data.set_dispidbrightnessget(0);
    //     sendDispControlToServer(data);
    //     DispControlTimer();
    //     LOG_I("TestTimer::DISP_BRIGHTNESS_GET");
    // } else if(count == 2) {
    //     count ++;
    //     data.set_eventid(DispControlEventId::DISP_BRIGHTNESS_SET);//设置屏亮度
    //     data.mutable_brightnessvalue()->set_displayid(0);//0:中控屏
    //     data.mutable_brightnessvalue()->set_level(100);
    //     sendDispControlToServer(data);
    //     DispControlTimer();
    //     LOG_I("TestTimer::DISP_BRIGHTNESS_SET-100");
    // } else if(count == 3) {
    //     count ++;
    //     data.set_eventid(DispControlEventId::DISP_POWERSTATE_SET);//设置背光
    //     data.mutable_powerstate()->set_displayid(0);
    //     data.mutable_powerstate()->set_poweron(false);//熄灭背光
    //     sendDispControlToServer(data);
    //     DispControlTimer();
    //     LOG_I("TestTimer::DISP_POWERSTATE_SET-ON");
    // } else if(count == 4) {
    //     count ++;
    //     data.set_eventid(DispControlEventId::DISP_POWERSTATE_GET);//获取屏背光状态
    //     data.set_dispidpowerstateget(0);
    //     sendDispControlToServer(data);
    //     DispControlTimer();
    //     LOG_I("TestTimer::DISP_POWERSTATE_GET");
    // } else if(count == 5) {
    //     count ++;
    //     data.set_eventid(DispControlEventId::DISP_POWERSTATE_SET);//设置背光
    //     data.mutable_powerstate()->set_displayid(0);
    //     data.mutable_powerstate()->set_poweron(true);//点亮背光
    //     sendDispControlToServer(data);
    //     DispControlTimer();
    //     LOG_I("TestTimer::DISP_POWERSTATE_SET-ON");
    // } else if(count == 6) {
    //     count ++;
    //     data.set_eventid(DispControlEventId::DISP_POWERSTATE_GET);//获取屏背光状态
    //     data.set_dispidpowerstateget(0);
    //     sendDispControlToServer(data);
    //     DispControlTimer();
    //     LOG_I("TestTimer::DISP_POWERSTATE_GET");
    // } else if (count == 7) {
    //     count ++;
    //     data.set_eventid(DispControlEventId::DISP_HW_VERSION_GET);//获取屏硬件版本信息
    //     data.set_dispidhwversionget(0);
    //     sendDispControlToServer(data);
    //     DispControlTimer();
    //     LOG_I("TestTimer::DISP_HW_VERSION_GET");
    // } else if (count == 8) {
    //     count = 0;
    //     data.set_eventid(DispControlEventId::DISP_SW_VERSION_GET);//获取屏软件版本信息
    //     data.set_dispidswversionget(0);
    //     sendDispControlToServer(data);
    // }
}

void PowerClientService::replyPowerOffInAndroidSleep(uint32_t timerId,uint32_t time) {
    LOG_I("PowerClientService::replyPowerOffInAndroidSleep-timerId=%d,time=%d\n",timerId,time);
    PowerPropValue data;
    data.set_eventid(PowerEventId::PWR_MCU_REQ_POWER_OFF_EVENT);
    data.set_value(13);//STR下电
    sendPowerPropToServer(toUInt(PowerMessageId::MSG_GATEWAY_TO_POWERSERVICE),data);
}

void PowerClientService::onConnect() {
    isConnectComd = true;
    LOG_I("PowerClientService onConnect done \n");
    RunUtTestCase();
    //add tese code
    // DispControlTimer();
    // startSTRPowerOffTimer();
}

void PowerClientService::onDisconnect() {
    isConnectComd = false;
    LOG_E("PowerClientService onDisconnect done \n");
}

void PowerClientService::onRevicesMsg(uint32_t code, const PowerPropValue& data) {
    LOG_I("PowerClientService onRevicesMsg--eventid()=%d,value()=%d,code=%d.\n",toInt(data.eventid()),toInt(data.value()),code);
    //CBaseJob* job = new PowerMessageJob(data, size);
    //powerclient_worker.sendAsync(job);
}
static bool gbISrunUT = false;
static UTTestCode gut_code = UTTestCode_None;
static int gvalue = 0;

void PowerClientService::SetUtTestCase(UTTestCode ut_code, int value)
{
    gbISrunUT = true;
    gut_code = ut_code;
    gvalue = value;
}

void PowerClientService::RunUtTestCase()
{
    if(!gbISrunUT) return;

    PowerPropValue data;
    switch (gut_code)
    {
    case UTTestCode_PowerOn:
        data.set_eventid(PowerEventId::PWR_MCU_POWER_ON_EVENT);
        break;
    case UTTestCode_PowerOff:
        data.set_eventid(PowerEventId::PWR_MCU_REQ_POWER_OFF_EVENT);
        break;
    case UTTestCode_PowerMode:
        data.set_eventid(PowerEventId::PWR_MCU_RESP_POWER_MODE_EVENT);
        break;
    case UTTestCode_PowerReset:
        data.set_eventid(PowerEventId::PWR_MCU_REQ_RESET_EVENT);
        break;
    case UTTestCode_PowerVoltage:
        data.set_eventid(PowerEventId::PWR_MCU_RESP_VOLTAGE_EVENT);
        break;
    case UTTestCode_PowerReason:
        data.set_eventid(PowerEventId::PWR_MCU_RESP_POWERON_REASON_EVENT);
        break;
    default:
        break;
    }
    data.set_value(gvalue);

    sendPowerPropToServer(toUInt(PowerMessageId::MSG_GATEWAY_TO_POWERSERVICE),data);
}



} // namespace powerclient
