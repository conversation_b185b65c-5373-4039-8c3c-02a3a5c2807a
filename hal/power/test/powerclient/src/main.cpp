/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef AUTOLINK_POWERHAL_QNX
#include <hidl/HidlSupport.h>
#include <hidl/HidlTransportSupport.h>
#endif //AUTOLINK_POWERHAL_QNX

#include <stdio.h>
#include <semaphore.h>
#include <string>
#include <sys/types.h>
#include <sys/resource.h>
#include <unistd.h>
#include "PowerLog.h"
#include "PowerClientService.h"

using namespace powerclient;

#ifndef AUTOLINK_POWERHAL_QNX
//QNX不需要
using android::hardware::configureRpcThreadpool;
using android::hardware::joinRpcThreadpool;
#endif //AUTOLINK_POWERHAL_QNX


void printhelp()
{
    printf("Usage: powerclient [options]\n");
    printf("Options:\n");
    printf("  -h, --help\t\tDisplay this help message\n");
    printf("  -v, --version\t\tDisplay version information\n");
    printf("  -repPowerReason reason, reason=n\t\tSet PWR_MCU_RESP_POWERON_REASON_EVENT\n");
    printf("  -rspVoltage value, value=n\t\tSet PWR_MCU_RESP_VOLTAGE_EVENT\n");
    printf("  -rspPowerMode value, value=n\t\tSet PWR_MCU_RESP_POWER_MODE_EVENT\n");
    printf("  -rspReset value, value=n\t\tSet PWR_MCU_REQ_RESET_EVENT\n");
    printf("  -reqPowerOn value, value=n\t\tSet PWR_MCU_POWER_ON_EVENT\n");
    printf("  -reqPowerOff value, value=n\t\tSet PWR_MCU_REQ_POWER_OFF_EVENT\n");
    fflush(stdout); // 强制刷新缓冲区
}

int main(int argc, char **argv)
{
    #ifdef AUTOLINK_POWERHAL_QNX
    struct rlimit my_limits;
    my_limits.rlim_cur = 512 * 1024 * 1024; // 512MB
    my_limits.rlim_max = 512 * 1024 * 1024;
    power_slog2_init();
    if (-1 == setrlimit(RLIMIT_AS, &my_limits)) {
        LOG_E("setrlimit() failed! errno: %d", errno);
    }
    sem_t sem;
    sem_init(&sem, 0, 0);
    if(argc <= 2) 
    {
        printhelp();
        return 0;
    }
    else
    {
        PowerClientService::getInstance()->init();
        std::string arg1 = argv[1];
        std::string arg2 = argv[2];

        UTTestCode code = UTTestCode_None;
        if (arg1 == "-reqPowerOn")
        {
            code = UTTestCode_PowerOn;
        }
        else if (arg1 == "-reqPowerOff")
        {
            code = UTTestCode_PowerOff;
        }
        else if (arg1 == "-rspPowerMode")
        {
            code = UTTestCode_PowerMode;
        }
        else if (arg1 == "-rspReset")
        {
            code = UTTestCode_PowerReset;
        }
        else if (arg1 == "-rspVoltage")
        {
            code = UTTestCode_PowerVoltage;
        }
        else if (arg1 == "-repPowerReason")
        {
            code = UTTestCode_PowerReason;
        }

        PowerClientService::getInstance()->SetUtTestCase(code, stoi(arg2));
    }
    sem_wait(&sem);
    return 0;

    #else //AUTOLINK_POWERHAL_QNX

    PowerClientService::getInstance()->init();
    configureRpcThreadpool(5, true /*callerWillJoin*/);
	joinRpcThreadpool();
	return 0;
    #endif //AUTOLINK_POWERHAL_QNX
}