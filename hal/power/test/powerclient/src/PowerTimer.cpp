/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#include <stdio.h>
#include <iostream>
#include <pthread.h>
#include <thread>
#include <unistd.h>
#include <string.h>

#include "PowerTimer.h"
#include "PowerLog.h"

PowerTimer *PowerTimer::getInstance() {
    static pthread_mutex_t mtx = PTHREAD_MUTEX_INITIALIZER;
    static PowerTimer* instance;
    pthread_mutex_lock(&mtx);
    if (instance == nullptr) {
        instance = new PowerTimer();
    }
    pthread_mutex_unlock(&mtx);
    return instance;
}

PowerTimer::PowerTimer()
    : mInitialized(false),
      mTid(0),
      mCond(PTHREAD_COND_INITIALIZER),
      mMtx(PTHREAD_MUTEX_INITIALIZER) {
    //LOG_I("new PowerTimer instance.\n");
}

PowerTimer::~PowerTimer() {
    if (mInitialized) {
        LOG_E("~PowerTimer\n");
        pthread_mutex_destroy(&mMtx);
        pthread_cond_destroy(&mCond);
        pthread_condattr_destroy(&mCondAttr);
        pthread_attr_destroy(&mThreadAttr);
        mInitialized = false;
    }
}

bool PowerTimer::isInitialized() {
    return mInitialized;
}

void PowerTimer::init() {
    if (false == mInitialized) {
        #ifdef  AUTOLINK_POWERHAL_QNX
        struct sched_param param;
        #endif
        mInitialized = true;
        /* Create channel & server thread */
        pthread_attr_init(&mThreadAttr);
        pthread_attr_setdetachstate(&mThreadAttr, PTHREAD_CREATE_DETACHED);
        pthread_attr_setinheritsched(&mThreadAttr, PTHREAD_EXPLICIT_SCHED);

        #ifdef  AUTOLINK_POWERHAL_QNX
        pthread_attr_getschedparam(&mThreadAttr, &param);
        param.sched_priority = TIMER_THREAD_PRIO;
        pthread_attr_setschedparam(&mThreadAttr, &param);
        #endif

        pthread_condattr_init(&mCondAttr);
        pthread_condattr_setclock(&mCondAttr, CLOCK_MONOTONIC);
        pthread_cond_init(&mCond, &mCondAttr);

        if (pthread_create(&mTid, &mThreadAttr, PowerTimer::threadEntry, this) != 0) {
           LOG_E("%s,pthread_create faild.\n", __func__);
           mInitialized = false;
        } else {
           LOG_I("%s,pthread_create suscc.\n", __func__);
        }
        pthread_setname_np(mTid, "PowerHalTimer");
    }
}

void PowerTimer::stop() {
    pthread_mutex_lock(&mMtx);
    mThreadRun.store(0, std::memory_order_release);  // 原子写入
    pthread_cond_signal(&mCond);  // 强制唤醒等待线程
    pthread_mutex_unlock(&mMtx);
}

void PowerTimer::clean() {
    mInitialized = false;
    pthread_mutex_unlock(&mMtx);
    pthread_attr_destroy(&mThreadAttr);
    pthread_condattr_destroy(&mCondAttr);
    pthread_mutex_destroy(&mMtx);
    pthread_cond_destroy(&mCond);
}

void PowerTimer::cleanThread(void *arg) {
    PowerTimer::getInstance()->clean();
}

void *PowerTimer::threadEntry(void *arg) {
    PowerTimer::getInstance()->run();
    return nullptr;
}

void PowerTimer::startTimer(uint8_t timerId, TimerCallback timerCallback, uint32_t time) {
    LOG_I("startTimer,timerId=%d,time=%d\n",timerId,time);
    if ((timerId >= TIMER_ID_SIZE) || (timerCallback == nullptr)) {
        return;
    }
    struct timespec upTime = {0};

    clock_gettime(CLOCK_MONOTONIC, &upTime);
    pthread_mutex_lock(&mMtx);
    mTimerList[timerId].time = time;
    mTimerList[timerId].forceStop = false;
    mTimerList[timerId].startTime.tv_sec = upTime.tv_sec + time / 1000;
    mTimerList[timerId].startTime.tv_nsec = upTime.tv_nsec + time % 1000 * 1000 * 1000;
    mTimerList[timerId].timerCallback = timerCallback;
    pthread_cond_signal(&mCond);
    pthread_mutex_unlock(&mMtx);
}

void PowerTimer::stopTimer(uint8_t timerId) {
    LOG_E("stopTimer,timerId=%d\n",timerId);
    if (timerId >= TIMER_ID_SIZE) {
        return;
    }
    pthread_mutex_lock(&mMtx);
    mTimerList[timerId].forceStop = true;
    mTimerList[timerId].time = 0;
    mTimerList[timerId].startTime.tv_sec = 0;
    mTimerList[timerId].startTime.tv_nsec = 0;
    pthread_cond_signal(&mCond);
    pthread_mutex_unlock(&mMtx);
}

void PowerTimer::getWaitTime(struct timespec &waitTime) {
    uint8_t setFlg = 0;
    for (uint8_t i = 0; i < mTimerList.size(); i++) {
        if (mTimerList[i].startTime.tv_sec != 0) {
            if (setFlg == 0) {
                waitTime.tv_sec = mTimerList[i].startTime.tv_sec;
                waitTime.tv_nsec = mTimerList[i].startTime.tv_nsec;
                setFlg = 1;
            } else if (compareTime(mTimerList[i].startTime, waitTime) < 0) {
                waitTime.tv_sec = mTimerList[i].startTime.tv_sec;
                waitTime.tv_nsec = mTimerList[i].startTime.tv_nsec;
            }
        }
    }

    if (!setFlg) {
        struct timespec upTime = {0};
        clock_gettime(CLOCK_MONOTONIC, &upTime);
        waitTime.tv_sec = upTime.tv_sec + 10;
        waitTime.tv_nsec = upTime.tv_nsec;
    }
}

int8_t PowerTimer::compareTime(struct timespec firstTime, struct timespec secondTime) {
    int8_t ret = 0;
    uint64_t firstMills = firstTime.tv_sec * 1000 + firstTime.tv_nsec / 1000 / 1000;
    uint64_t secondMills = secondTime.tv_sec * 1000 + secondTime.tv_nsec / 1000 / 1000;

    if (firstMills > secondMills){
        ret = 1;
    } else if (firstMills < secondMills) {
        ret = -1;
    } else {
        ret = 0;
    }
    return ret;
}

void PowerTimer::run() {
    struct timespec waitTime = {0};
    //mThreadRun = 1;
    mThreadRun.store(1, std::memory_order_release);  // 使用原子操作

    pthread_cleanup_push(PowerTimer::cleanThread, nullptr);
    while (mThreadRun.load(std::memory_order_acquire)) {
        pthread_mutex_lock(&mMtx);

        std::this_thread::sleep_for(std::chrono::milliseconds(5));//注意：这个定时器需要大于5ms
        memset(&waitTime, 0x00, sizeof(waitTime));
        getWaitTime(waitTime);
        pthread_cond_timedwait(&mCond, &mMtx, &waitTime);
        checkTimeout();

        pthread_mutex_unlock(&mMtx);
    }
    pthread_cleanup_pop(0);
}

void PowerTimer::checkTimeout() {
    struct timespec upTime = {0};
    clock_gettime(CLOCK_MONOTONIC, &upTime);
    for (uint8_t i = 0; i < mTimerList.size(); i++) {
        if (mTimerList[i].startTime.tv_sec != 0) {
            if (compareTime(mTimerList[i].startTime, upTime) <= 0) {
                mTimerList[i].startTime.tv_sec = 0;
                mTimerList[i].startTime.tv_nsec = 0;
                    if (mTimerList[i].timerCallback && !mTimerList[i].forceStop) {
                        mTimerList[i].forceStop = true;
                        // 使用线程池或异步执行
                        auto cb = mTimerList[i].timerCallback;
                        auto time = mTimerList[i].time;
                        auto index = i;
                        //LOG_I("checkTimeout,timerId=%d,time=%d\n",index,time);
                        std::thread([cb, index, time]() { cb(index,time); }).detach();
                    }
            }
        }
    }
}