/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/

#include <stdio.h>
#include <iostream>
#include <string.h>
#include <unistd.h>
#include <fdbus/fdbus.h>
#include <fdbus/cJSON/cJSON.h>
#include <fdbus/CFdbCJsonMsgBuilder.h>
#include <fdbus/CFdbProtoMsgBuilder.h>
#include "PowerClient.h"
#include "PowerLog.h"
#include "utils.h"

#define FDBUS_INVOKE_TIMEOUT 1000 // unit ms
using namespace ipc::fdbus;
using namespace autolink;

namespace powerclient
{
    using namespace std;

    class PowerClientFdbus : public CBaseClient
    {
    public:
        PowerClientFdbus(const char *name, IPowerClientCallback *callback, CBaseWorker *worker = 0)
            : CBaseClient(name, worker), mCallback(callback), mSessionId(0) {
            std::vector<PowerMessageId>().swap(mSubList);
            enableUDP(true);
            enableAysncRead(true);
            enableAysncWrite(true);
        }
        ~PowerClientFdbus() {
            std::vector<PowerMessageId>().swap(mSubList);
            disconnect();
        }

        void setSubscribeEvent(PowerMessageId id) {
            mSubList.push_back(id);
        }
        void subscribeEvent() {
            if (mIsOnline) {
                CFdbMsgSubscribeList subscribe_list;
                for (auto item : mSubList) {
                    LOG_I("addNotifyItem...item=%d\n",item);
                    addNotifyItem(subscribe_list, item);
                }
                addNotifyItem(subscribe_list, PowerMGRMsgId::DISP_CONTROL_MSG);
                subscribe(subscribe_list);
            } else {
                LOG_I("subscribeEvent...mIsOnline=false");
            }
        }

        int sendmsg(uint32_t code, const PowerPropValue& data, uint32_t timeout, uint8_t retry) {
            PowerClientMessageState ret = PowerClientMessageState::Message_OK;
            while (retry--) {
                ret = sendmsg(code, data, timeout);
                if (ret != PowerClientMessageState::Message_Failed) {
                    break;
                }
            }
            return ret;
        }

        int sendDispControlMsg(uint32_t code, const DispControlPropValue& data, uint32_t timeout, uint8_t retry) {
            PowerClientMessageState ret = PowerClientMessageState::Message_OK;
            while (retry--) {
                ret = sendDispControlMsg(code, data, timeout);
                if (ret != PowerClientMessageState::Message_Failed) {
                    break;
                }
            }
            return ret;
        }

        PowerClientMessageState sendmsg(uint32_t code, const PowerPropValue& data, uint32_t timeout) {
            PowerClientMessageState result = PowerClientMessageState::Message_OK;
            if (mIsOnline) {
                CFdbProtoMsgBuilder builder(data);
                CBaseJob::Ptr ref(new CBaseMessage(code));
                bool ret = invoke(ref, builder,timeout); // sync invoke
                if (!ret) {
                    LOG_E("PowerClient--invoke(%d, data.eventid()=%d,data.value()=%d) = false...Done",code,toInt(data.eventid()),toInt(data.value()));
                    result = PowerClientMessageState::Message_Failed;
                    return result;
                }
                int32_t error_code = FdbMsgStatusCode::FDB_ST_OK;
                auto msg = castToMessage<CBaseMessage *>(ref);
                if (msg->isStatus()) {
                    std::string reason;
                    if (!msg->decodeStatus(error_code, reason)) {
                        error_code = FdbMsgStatusCode::FDB_ST_MSG_DECODE_FAIL;
                        LOG_E("reply decode fail!");
                    }
                }
                if (FdbMsgStatusCode::FDB_ST_OK == error_code) {
                    result = PowerClientMessageState::Message_OK;
                    LOG_I("PowerClient--invoke(%d, data.eventid()=%d,data.value()=%d) ...Success"
                        ,code,toInt(data.eventid()),toInt(data.value()));
                } else if (FdbMsgStatusCode::FDB_ST_AUTO_REPLY_OK == error_code) {
                    result = PowerClientMessageState::Message_OK;
                    LOG_I("PowerClient--invoke(%d, data.eventid()=%d,data.value()=%d) ...Success"
                        ,code,toInt(data.eventid()),toInt(data.value()));
                } else if (FdbMsgStatusCode::FDB_ST_TIMEOUT == error_code) {
                    result = PowerClientMessageState::Message_TimeOut;
                    LOG_E("PowerClient--invoke(%d, data.eventid()=%d,data.value()=%d) ...Timeout"
                        ,code,toInt(data.eventid()),toInt(data.value()));
                } else {
                    result = PowerClientMessageState::Message_Failed;
                    LOG_E("PowerClient--invoke(%d, data.eventid()=%d,data.value()=%d) ...Error"
                        ,code,toInt(data.eventid()),toInt(data.value()));
                }
            } else {
                LOG_E("PowerClient--server is offline!");
                result = PowerClientMessageState::Message_Failed;
            }
            return result;
        }

        PowerClientMessageState sendDispControlMsg(uint32_t code, const DispControlPropValue& data, uint32_t timeout) {
            PowerClientMessageState result = PowerClientMessageState::Message_OK;
            if (mIsOnline) {
                CFdbProtoMsgBuilder builder(data);
                CBaseJob::Ptr ref(new CBaseMessage(code));
                bool ret = invoke(ref, builder,timeout); // sync invoke
                if (!ret) {
                    LOG_E("sendDispControlMsg--invoke(%d, data.eventid()=%d...Done",code,toInt(data.eventid()));
                    result = PowerClientMessageState::Message_Failed;
                    return result;
                }
                int32_t error_code = FdbMsgStatusCode::FDB_ST_OK;
                auto msg = castToMessage<CBaseMessage *>(ref);
                if (msg->isStatus()) {
                    std::string reason;
                    if (!msg->decodeStatus(error_code, reason)) {
                        error_code = FdbMsgStatusCode::FDB_ST_MSG_DECODE_FAIL;
                        LOG_E("reply decode fail!");
                    }
                }
                if (FdbMsgStatusCode::FDB_ST_OK == error_code) {
                    result = PowerClientMessageState::Message_OK;
                    LOG_I("sendDispControlMsg--invoke(%d, data.eventid()=%d) ...Success"
                        ,code,toInt(data.eventid()));
                } else if (FdbMsgStatusCode::FDB_ST_AUTO_REPLY_OK == error_code) {
                    result = PowerClientMessageState::Message_OK;
                    LOG_I("sendDispControlMsg--invoke(%d, data.eventid()=%d) ...Success"
                        ,code,toInt(data.eventid()));
                } else if (FdbMsgStatusCode::FDB_ST_TIMEOUT == error_code) {
                    result = PowerClientMessageState::Message_TimeOut;
                    LOG_E("sendDispControlMsg--invoke(%d, data.eventid()=%d) ...Timeout"
                        ,code,toInt(data.eventid()));
                } else {
                    result = PowerClientMessageState::Message_Failed;
                    LOG_E("sendDispControlMsg--invoke(%d, data.eventid()=%d) ...Error"
                        ,code,toInt(data.eventid()));
                }
            } else {
                LOG_E("sendDispControlMsg--server is offline!");
                result = PowerClientMessageState::Message_Failed;
            }
            return result;
        }

    protected:
        /* called when connected to the server */
        void onOnline(const CFdbOnlineInfo &info) {
            LOG_I("[PowerService]on connect sid:%d,FirstOrLast:%d\n", info.mSid, info.mFirstOrLast);
            mIsOnline = true;
            mSessionId = info.mSid;
            subscribeEvent();
            if (mCallback != nullptr)
                mCallback->onConnect();
        }

        /* called when disconnected from server */
        void onOffline(const CFdbOnlineInfo &info) {
            LOG_I("[PowerService]on offline sid:%d,FirstOrLast:%d\n", info.mSid, info.mFirstOrLast);
            if (mCallback != nullptr)
                mCallback->onDisconnect();
            mIsOnline = false;
        }

        /* called when events broadcasted from server is received */
        void onBroadcast(CBaseJob::Ptr &msg_ref) {
            auto msg = castToMessage<CBaseMessage *>(msg_ref);
            switch(msg->code()) {
                case PowerMessageId::MSG_POWERSERVICE_TO_AUTOLINK:  {
                    PowerPropValue propValue;
                    CFdbProtoMsgParser parser(propValue);
                    if (!msg->deserialize(parser)) {
                        LOG_E("[ViClient] onBroadcast EventId=%d deserialize fail! \n", msg->code());
                        return;
                    }
                    LOG_I("onBroadcast--msg->code=%d,propValue.eventid():%d,propValue.value():%d,.\n",msg->code(),propValue.eventid(),propValue.value());
                    mCallback->onRevicesMsg(msg->code(), propValue);
                }
                    break;
                case PowerMessageId::MSG_POWERSERVICE_TO_ALL: {
                    PowerPropValue propValue;
                    CFdbProtoMsgParser parser(propValue);
                    if (!msg->deserialize(parser)) {
                        LOG_E("[ViClient] onBroadcast EventId=%d deserialize fail! \n", msg->code());
                        return;
                    }
                    LOG_I("onBroadcast--msg->code=%d,propValue.eventid():%d,propValue.value():%d,.\n",msg->code(),propValue.eventid(),propValue.value());
                    mCallback->onRevicesMsg(msg->code(), propValue);
                }
                    break;
                case PowerMGRMsgId::PM_STATE_SHUTDOWN_PREPARE_NOTICE: {
                    LOG_I("onBroadcast--PM_STATE_PREPARE_NOTICE.\n");
                }
                    break;
                case PowerMGRMsgId::PM_STATE_SUSPEND_NOTICE: {
                    LOG_I("onBroadcast--PM_STATE_SUSPEND_NOTICE.\n");
                }
                    break;
                case PowerMGRMsgId::PM_STATE_RESUME_NOTICE: {
                    LOG_I("onBroadcast--PM_STATE_RESUME_NOTICE.\n");
                }
                    break;
                case PowerMGRMsgId::PM_STATE_RESUME_COMPLETE_NOTICE: {
                    LOG_I("onBroadcast--PM_STATE_COMPLETE_NOTICE.\n");
                }
                    break;
                case PowerMGRMsgId::DISP_CONTROL_MSG: {
                    LOG_I("onBroadcast--DISP_CONTROL_MSG.\n");
                    DispControlPropValue propValue;
                    CFdbProtoMsgParser parser(propValue);
                    if (!msg->deserialize(parser)) {
                        LOG_E("[ViClient] onBroadcast EventId=%d deserialize fail! \n", msg->code());
                        return;
                    }
                    receiveDispControlMsg(propValue);
                }
                    break;
                default:
                    break;
            }
        }
        void receiveDispControlMsg(DispControlPropValue propValue) {
            if (!propValue.has_eventid()) {
                LOG_E("receiveDispControlMsg,has_eventid=false.\n");
                return;
            }
            int eventId = propValue.eventid();
            LOG_E("receiveDispControlMsg,eventId=%d.\n",eventId);
            switch(eventId) {
                case DispControlEventId::DISP_BRIGHTNESS_NOTICE: {
                    if (propValue.has_brightnessvalue()) {
                       DispBrightnessValue BValue = propValue.brightnessvalue();
                       int displayId = BValue.displayid();
                       int level = BValue.level();
                       LOG_I("receiveDispControlMsg,displayId=%d,level=%d.\n",displayId,level);
                    } else {
                        LOG_I("receiveDispControlMsg,has_brightnessvalue=false.\n");
                    }
                }
                    break;
                case DispControlEventId::DISP_POWERSTATE_NOTICE: {
                    if (propValue.has_powerstate()) {
                       DispPowerStateValue PValue = propValue.powerstate();
                       int displayId = PValue.displayid();
                       bool powerState = PValue.poweron();
                       LOG_I("receiveDispControlMsg,displayId=%d,powerState=%s.\n",displayId,powerState ? "true" : "false");
                    } else {
                        LOG_I("receiveDispControlMsg,has_powerstate=false.\n");
                    }
                }
                    break;
                case DispControlEventId::DISP_HW_VERSION_NOTICE: {
                    if (propValue.has_hwversion()) {
                       LOG_I("receiveDispControlMsg,DISP_HW_VERSION_NOTICE");
                       int size = propValue.hwversion().size();
                       std::string str(propValue.hwversion().hwversionmsg());
                       LOG_I("receiveDispControlMsg,HWVersionStr=%s,size=%d.\n",str,size);
                    } else {
                        LOG_I("receiveDispControlMsg,has_hwversion=false.\n");
                    }
                }
                    break;
                case DispControlEventId::DISP_SW_VERSION_NOTICE: {
                    if (propValue.has_swversion()) {
                        LOG_I("receiveDispControlMsg,DISP_SW_VERSION_NOTICE");
                       int size = propValue.swversion().size();
                       std::string str(propValue.swversion().swversionmsg());
                       LOG_I("receiveDispControlMsg,SWVersionStr=%s,size=%d.\n",str,size);
                    } else {
                        LOG_I("receiveDispControlMsg,has_swersion=false.\n");
                    }
                }
                    break;
                default:
                    break;
            }

        }

        void onReply(CBaseJob::Ptr &msg_ref) {
            LOG_I("onReply--\n",__FUNCTION__);
        }

        /* check if something happen... */
        void onStatus(CBaseJob::Ptr &msg_ref, int32_t error_code, const char *description) {
            auto msg = castToMessage<CBaseMessage *>(msg_ref);
            if (msg->isSubscribe()) {
                LOG_E("subscribe is ok! sn: %d is received.\n", msg->sn());
            } else {
                LOG_E("subscribe is error! sn: %d is received.\n", msg->sn());
            }
            LOG_I("Reason: %s\n", description);
        }

    private:
        std::shared_ptr<IPowerClientCallback> mCallback;
        bool mIsOnline = false;
        FdbSessionId_t mSessionId;
        std::vector<PowerMessageId> mSubList;
    };

    static PowerClientFdbus *mPowerClient = nullptr;

    PowerClient::PowerClient(){
        //Do nothing
    }

    PowerClient::~PowerClient() {
        if (mPowerClient != nullptr) {
            delete mPowerClient;
        }
        mPowerClient = nullptr;
    }

    void PowerClient::connectService(IPowerClientCallback *callback, std::vector<PowerMessageId> &list) {
        LOG_D("%s line: %d \n", __FUNCTION__, __LINE__);
        std::string server_name("autolink_power_service");
        std::string url(FDB_URL_SVC);
        url += server_name;
        if (mPowerClient == nullptr) {
            CFdbContext::enableLogger(false);
            FDB_CONTEXT->start();
            CBaseWorker *worker_ptr = new CBaseWorker("PowerClientFdbus");
            worker_ptr->start();

            mPowerClient = new PowerClientFdbus("PowerClient", callback, worker_ptr);
            if (mPowerClient != nullptr) {
                for (auto it : list) {
                    mPowerClient->setSubscribeEvent(it);
                }
                mPowerClient->enableReconnect(true);
                mPowerClient->setOnlineChannelType(FDB_SEC_NO_CHECK);
                mPowerClient->connect(url.c_str());
            }
        }
    }

    void PowerClient::disConnectService() {
        if (mPowerClient != nullptr) {
            mPowerClient->disconnect(FDB_INVALID_ID);
        }
    }

    void PowerClient::subScribe(PowerMessageId id) {
        if (mPowerClient != nullptr) {
            mPowerClient->setSubscribeEvent(id);
        }
    }

    int PowerClient::sendMsg(uint32_t code, const PowerPropValue& data) {
        int ret = 0;
        if (mPowerClient != nullptr) {
            ret = mPowerClient->sendmsg(code, data, FDBUS_INVOKE_TIMEOUT, 2);
        } else {
            ret = -1;
            LOG_E("PowerClient::%s mPowerClient nullptr.\n", __FUNCTION__);
        }
        return ret;
    }

    int PowerClient::sendDispControlMsg(uint32_t code, const DispControlPropValue& data) {
        int ret = 0;
        if (mPowerClient != nullptr) {
            ret = mPowerClient->sendDispControlMsg(code, data, FDBUS_INVOKE_TIMEOUT, 2);
        } else {
            ret = -1;
            LOG_E("PowerClient::%s mPowerClient nullptr.\n", __FUNCTION__);
        }
        return ret;
    }
} // namespace powerclient