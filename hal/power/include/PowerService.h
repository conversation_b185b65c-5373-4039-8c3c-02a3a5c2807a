/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/

#ifndef _POWER_SERVICE_H_
#define _POWER_SERVICE_H_

#include <array>
#include <stdint.h>
#include <mutex>

#include "PowerDefine.h"
#include "com.autolink.power.pb.h"
#include "PowerClientSetJob.h"
#include "PowerFDBusClient.h"
#include "PowerServiceFdbus.h"
#include "PowerServiceManager.h"


namespace powerservice
{

using autolink::PowerPropValue;
using autolink::PowerMode;
using autolink::PowerMessageId;
using autolink::PowerEventId;

class PowerService
{
public:
    virtual ~PowerService();
    void init();
    static PowerService* getInstance();
    void notifyPowerSessionEvent(uint32_t code,const PowerPropValue& value,int32_t signalId);
    void talkWithALC(uint8_t const *data,uint16_t size);
    void talkWithGateWayALSleepComplete();
    void notifyPowerMGRSTRState(uint32_t code);
private:
    PowerService();
    bool isNeedBroadcast(int32_t signalId);

private:
    //static std::mutex m_InstanceMutex;
    static PowerService* m_PowerService;
    PowerServiceFdbus* m_PowerServiceFdbus = nullptr;
    PowerFDBusClient* m_PowerFDBusClient = nullptr;
    PowerServiceManager* m_PowerServiceManager = nullptr;
};

} // namespace powerservice
#endif //_POWER_SERVICE_H_
