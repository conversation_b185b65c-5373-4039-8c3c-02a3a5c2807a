#ifndef _DISPLAY_POWERSTATE_CONTROL_
#define _DISPLAY_POWERSTATE_CONTROL_
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif //__cplusplus

#ifdef AUTOLINK_HAS_ALC_POWERCLIENT
#define DISPCTRL_POWERSTATE_ON 1
#define DISPCTRL_POWERSTATE_OFF 0
#else
#include "amss/inc/display_ctrl_api.h"
#endif //AUTOLINK_HAS_ALC_POWERCLIENT

void sendPowerOrSleepKey(int key);
bool request_gvm_power_state();
int power_dispctrl_set_display_brightness(int displayId, int level);
int power_dispctrl_get_display_backlight_brightness(int displayId);
int power_dispctrl_set_display_powerstate(int displayId, int powerstate);
int power_dispctrl_get_display_power_state(int displayId);
int power_dispctrl_get_display_software_version(int displayId, char* version, size_t versionSize);
int power_dispctrl_get_display_hardware_version(int displayId, char* version, size_t versionSize);
int power_dispctrl_get_display_temperature(int displayId);
#ifdef __cplusplus
}
#endif //__cplusplus

#endif //_DISPLAY_POWERSTATE_CONTROL_