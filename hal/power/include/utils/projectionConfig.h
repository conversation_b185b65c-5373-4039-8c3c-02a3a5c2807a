#ifndef __POWER_PROJECTION_CONFIG__
#define __POWER_PROJECTION_CONFIG__
#include "PowerLog.h"

namespace powerservice
{
//powerhal作为ALC的client跟MCU通讯则 g_autolink_has_alc_powerclient=true
//目前8255 D01自研项目powerhal作为ALC FDBus client
//8775北汽项目powerhal不跟ALC通讯 作为server跟卓驭和车联内部进程通讯
//目前争取两个项目复用同一个仓库代码
#ifdef AUTOLINK_HAS_ALC_POWERCLIENT
    static const bool g_autolink_has_alc_powerclient = true;//8255
#else
    static const bool g_autolink_has_alc_powerclient = false;//8775
#endif //AUTOLINK_HAS_ALC_POWERCLIENT

inline constexpr static const bool hasALCPowerClient() {
    return g_autolink_has_alc_powerclient;
}
}// namespace powerservice
#endif //__POWER_PROJECTION_CONFIG__