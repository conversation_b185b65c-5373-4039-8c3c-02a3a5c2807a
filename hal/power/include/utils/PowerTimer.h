#ifndef __POWER_TIMER_H__
#define __POWER_TIMER_H__

#include <atomic>
#include <array>
#include <functional>
#include <pthread.h>
#include <stdint.h>
#include <time.h>

#define TIMER_THREAD_PRIO (40)
constexpr const uint16_t RESEND_INTERVA = 300; //ms
constexpr const uint16_t HEART_BEAT_INTERVAL = 1000; //ms

enum TIMERID {
    POWER_TIMER_POWER_READY   = 0,
    POWER_TIMER_POWER_OFF     = 1,
    POWER_TIMER_HEART_BAET    = 2,
    POWER_TIMER_LCD_STATE     = 3,
    POWER_TIMER_TEST          = 4,
    TIMER_ID_SIZE,
};

typedef std::function<void(uint32_t,uint32_t)> TimerCallback;

struct TInfo {
    struct timespec startTime;
    TimerCallback timerCallback;
    bool forceStop;
    uint32_t time;
};//TimerInfo

class PowerTimer {
public:
    PowerTimer();
    ~PowerTimer();
    static PowerTimer* getInstance();
    void init();
    void stop();
    static void cleanThread(void* arg);
    static void* threadEntry(void* arg);
    void startTimer(uint8_t timerId, TimerCallback timerCallback, uint32_t time);
    void stopTimer(uint8_t timerId);
    bool isInitialized();

private:
    void getWaitTime(struct timespec& waitTime);
    int8_t compareTime(struct timespec firstTime, struct timespec secondTime);
    void run();
    void clean();
    void checkTimeout();

private:
    std::array<TInfo, TIMER_ID_SIZE> mTimerList;
    bool mInitialized;
    pthread_attr_t mThreadAttr;
    pthread_condattr_t mCondAttr;
    pthread_t mTid;
    pthread_cond_t mCond;
    pthread_mutex_t mMtx;
    std::atomic<int> mThreadRun{0};
};

#endif  //__POWER_TIMER_H__