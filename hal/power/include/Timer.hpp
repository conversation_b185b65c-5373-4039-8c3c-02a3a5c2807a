#include <iostream>
#include <functional>
#include <atomic>
#include <cstring>
#include <signal.h>
#include <time.h>
#include <pthread.h>

namespace qnx {

class Timer {
public:
    using Callback = std::function<void(int)>;

    Timer() : mRunning(false), mIndex(0) {}
    
    ~Timer() {
        stop();
    }

    // 禁止拷贝和赋值
    Timer(const Timer&) = delete;
    Timer& operator=(const Timer&) = delete;

    // 启动一次性定时器（毫秒）
    bool startOnce(uint32_t milliseconds, const Callback& callback) {
        return start(milliseconds, 0, callback);
    }

    // 启动周期性定时器（毫秒）
    bool startPeriodic(uint32_t milliseconds, const Callback& callback) {
        return start(milliseconds, milliseconds, callback);
    }

    // 停止定时器
    void stop() {
        if (mRunning) {
            timer_delete(m_timerId);
            mRunning = false;
        }
    }

    // 检查定时器是否运行中
    bool isRunning() const {
        return mRunning;
    }

private:
    // 启动定时器（初始延迟和周期，单位毫秒）
    bool start(uint32_t initialDelayMs, uint32_t periodMs, const Callback& callback) {
        stop();  // 先停止现有定时器

        // 准备信号值，传递回调函数指针
        mCallback = callback;

        // 创建定时器
        struct sigevent se;
        memset(&se, 0, sizeof(se));
        se.sigev_notify = SIGEV_THREAD;  // 线程回调模式
        se.sigev_value.sival_ptr  = this;
        se.sigev_notify_function = &Timer::timerCallback;
        se.sigev_notify_attributes = nullptr;

        if (timer_create(CLOCK_REALTIME, &se, &m_timerId) == -1) {
            std::cerr << "Failed to create timer: " << strerror(errno) << std::endl;
            return false;
        }

        // 设置定时器参数
        itimerspec its;
        its.it_value.tv_sec = initialDelayMs / 1000;
        its.it_value.tv_nsec = (initialDelayMs % 1000) * 1000000;
        
        if (periodMs > 0) {
            its.it_interval.tv_sec = periodMs / 1000;
            its.it_interval.tv_nsec = (periodMs % 1000) * 1000000;
        } else {
            // 一次性定时器
            its.it_interval.tv_sec = 0;
            its.it_interval.tv_nsec = 0;
        }

        // 启动定时器
        if (timer_settime(m_timerId, 0, &its, nullptr) == -1) {
            std::cerr << "Failed to set timer: " << strerror(errno) << std::endl;
            return false;
        }

        mRunning = true;
        return true;
    }

    void timerThisCallBack()
    {
        if(mCallback)   mCallback(++mIndex);
    }

    // 定时器回调静态函数
    static void timerCallback(union sigval sv) {
        Timer *timer  = static_cast<Timer *>(sv.sival_ptr);
        try {
            if (timer) {
                timer->timerThisCallBack();
                // (*callback)(++mIndex);
            }
        } catch (const std::exception& e) {
            std::cerr << "Timer callback exception: " << e.what() << std::endl;
        } catch (...) {
            std::cerr << "Unknown exception in timer callback" << std::endl;
        }
    }

private:
    timer_t m_timerId;      // 定时器ID
    Callback mCallback = nullptr;
    std::atomic<bool> mRunning;  // 运行状态标志
    std::atomic<int>  mIndex;    //回调次数索引
};

} // namespace qnx