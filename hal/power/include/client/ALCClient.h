/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef _ALCOM_CLIENT_H_
#define _ALCOM_CLIENT_H_
#include <iostream>
#include <list>
#include <string>
#include "ALCClientType.h"
namespace autolink
{
    class IALCClientCallback
    {
    public:
        IALCClientCallback() = default;
        virtual ~IALCClientCallback() = default;
        virtual void onConnect() = 0;
        virtual void onDisconnect() = 0;
        virtual void onRevicesMsg(uint32_t code, uint8_t const *data, uint16_t size) = 0;
    };
    class ALCClient
    {
    public:
        ALCClient();
        virtual ~ALCClient();
        void connectService(IALCClientCallback *callback, std::vector<EMessageId> &list);
        void disConnectService();
        void subScribe(EMessageId id);
        int sendMsg(uint32_t code, uint8_t const *data, uint16_t size);
    };
} // namespace autolink

#endif //_ALCOM_CLIENT_H_
