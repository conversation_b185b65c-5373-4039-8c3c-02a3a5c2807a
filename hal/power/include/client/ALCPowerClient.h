/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef _ALC_POWER_CLIENT_H_
#define _ALC_POWER_CLIENT_H_


namespace autolink
{
    class IALCPowerClientCallback {
    public:
        IALCPowerClientCallback() = default;
        virtual ~IALCPowerClientCallback() = default;
        virtual void onConnect() = 0;
        virtual void onDisconnect() = 0;
        virtual void onRevicesMsg(int32_t index, uint8_t const *data, uint16_t size) = 0; 
    };

    class ALCClient;
    class IALCClientCallback;

    class ALCPowerClient {
    public:
        ALCPowerClient();
        virtual ~ALCPowerClient();
        void connectService(IALCPowerClientCallback *callback);
        void disConnectService();
        int sendMsg(int16_t index, uint8_t const *data, uint16_t size);

    private:
        ALCClient* mALCClinet;
        IALCClientCallback* mPowerCallback;
    };
} // namespace autolink

#endif //_ALC_POWER_CLIENT_H_
