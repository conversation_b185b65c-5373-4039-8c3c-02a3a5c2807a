/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/

#ifndef _POWER_CLIENT_SET_JOB_H_
#define _POWER_CLIENT_SET_JOB_H_

#include <fdbus/fdbus.h>
#include "ALCClientType.h"
#include "ALCPowerClient.h"
#include "com.autolink.power.pb.h"

namespace powerservice
{
using ipc::fdbus::CBaseJob;
using ipc::fdbus::CBaseWorker;
using autolink::ALCPowerClient;

class PowerClientSetJob : public CBaseJob
{
public:
    PowerClientSetJob(uint8_t const *data, uint16_t size, ALCPowerClient* client);

protected:
    void run(Ptr &ref);

private:
    void setPowerSignalData();

private:
    uint8_t *m_DataArr;
    uint16_t m_ArrSize;
    ALCPowerClient* m_ALCClinet;
};
} // namespace powerservice
#endif //_POWER_CLIENT_SET_JOB_H_