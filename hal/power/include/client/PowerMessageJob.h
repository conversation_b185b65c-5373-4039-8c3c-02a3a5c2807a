#ifndef _POWERMESSAGE_JOB_H_
#define _POWERMESSAGE_JOB_H_

#include <stdint.h>
#include <fdbus/fdbus.h>

#include "PowerDefine.h"
#include "com.autolink.power.pb.h"

namespace powerservice
{

using ipc::fdbus::CBaseJob;
using ipc::fdbus::CBaseWorker;
using autolink::PowerPropValue;
using autolink::PowerMessageId;

class PowerMessageJob : public CBaseJob
{
public:
    explicit PowerMessageJob(uint8_t const* data,uint16_t size);
    ~PowerMessageJob();

protected:
    void run(Ptr &ref);

private:
    void revicePowerMsgFromServer();
    bool isMCUSignalID(int32_t signalId);
    uint64_t array_to_uint64_le(uint8_t const *data,uint16_t size);

private:
    uint8_t *m_DataArr;
    uint16_t m_ArrSize;
};

} // namespace powerservice
#endif //_POWERMESSAGE_JOB_H_