/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef _ALCOM_CLIENT_TYPE_H_
#define _ALCOM_CLIENT_TYPE_H_
/*
 * Define message ID; should be the same as client.
 * NTF is alcom service -> alcom client
 * REQ is alcom client -> alcom service
 */
enum EMessageId
{
    REQ_MSG_ALC_TP_POWER = 10,
    NTF_MSG_ALC_TP_POWER = 11,
    NTF_MSG_ALC_CLIENT_LIST = 0xFF,
};

enum EModuleName
{
    ALC_CLIENT_SPECIAL,
    ALC_CLIENT_VEHICLE,
    ALC_CLIENT_POWER,
};

enum ALCClientMessageState
{
    Message_Failed = -1,
    Message_OK,
    Message_TimeOut,
};

#endif //_ALCOM_CLIENT_TYPE_H_
