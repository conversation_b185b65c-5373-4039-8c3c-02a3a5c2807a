#ifndef _POWER_COMD_CLIENT_H_
#define _POWER_COMD_CLIENT_H_

#include <cstdint>
#include <stdint.h>

#include "com.autolink.power.pb.h"
#include "ALCPowerClient.h"
#include "PowerDefine.h"

namespace powerservice
{

using autolink::IALCPowerClientCallback;
using autolink::ALCPowerClient;
using autolink::PowerPropValue;
using autolink::PowerMode;

class PowerFDBusClient : public IALCPowerClientCallback
{
public:
    PowerFDBusClient();
    ~PowerFDBusClient();
    void init();
    void replySignalToALC(const PowerPropValue& PropValue,int32_t signalId);
    static PowerFDBusClient* getInstance();
    uint32_t getlocalPowerMode();
    void sendPowerSignalToALC(uint8_t const *data,uint16_t size);

private:
    void onConnect();
    void onDisconnect();
    void onRevicesMsg(int32_t index, uint8_t const *data, uint16_t size);
    void onPowerReadyTimeOut(uint32_t timerId,uint32_t time);
    void startPowerReadyTimer();
    void startPowerOffTimer();
    void stopPowerOffTimer();
    void startHeartBeatTimer();
    void stopHeartBeatTimer();
    void HeartBeatSendTimer(uint32_t timerId,uint32_t time);
    void sendPowerReadyToALC();
    void sendHeartBeatToALC();
    void setPowerOnReady(bool set);
    bool isPowerOnReady();
    std::array<uint8_t, 8> uint64_to_bytes(uint64_t num);
    void setlocalPowerMode(uint32_t mode);
    void restartPowerReadyHandshakeIfNeed(uint32_t mode);
    static void *moinitorLaPowerStatusThread(void* arg);
    void replyPowerOffInAndroidSleep(uint32_t timerId, uint64_t time);
    void setPMStatus(PM_STATE state);

private:
    ALCPowerClient* mComdClient;
    bool isConnectComd;
    static bool mIsPowerOnReady;
    static PM_STATE mPMStatus;
    static uint32_t powerModeValue;
    static const uint64_t mALSTRSleepWaitSec = 15;
    static const uint64_t mALNormalSleepWaitSec = 3;
};

} // namespace powerservice
#endif