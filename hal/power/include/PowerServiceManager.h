/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef __POWER_SERVICE_MANAGER_H_
#define __POWER_SERVICE_MANAGER_H_

#include <map>
#include <pthread.h>
#include "com.autolink.power.pb.h"
#include "PowerDefine.h"
#include "powerControl.h"

namespace powerservice
{

using autolink::PowerPropValue;

class PowerService;
class PowerServiceManager
{
public:
    PowerServiceManager();
    ~PowerServiceManager();
    void updatePowerSessionValue(const PowerPropValue& powerValue);

private:
    pthread_mutex_t m_Mtx;
};
} // namespace powerservice

#endif