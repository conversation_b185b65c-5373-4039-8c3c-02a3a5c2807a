/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef _PM_LIFECYCLE_LISTENER_H_
#define _PM_LIFECYCLE_LISTENER_H_

#include <stdint.h>
#include <amss/core/dcmd_pm.h>

#ifdef __cplusplus
extern "C" {
#endif

struct _pm_client_s ;
typedef struct _pm_client_s* pm_client_t;

struct pm_ops_s {
    int (*prepare) (void *ctxt);
    int (*suspend) (void *ctxt);
    int (*resume ) (void *ctxt);
    int (*complete)(void *ctxt);
    int (*volt_nok)(void *ctxt);
    int (*volt_ok) (void *ctxt);
};

/**=========================================================================

FUNCTION pm_register

@brief  This function creates thread within the process, creates a channel between client
        and server and then registers with PM server. Upon reception of pulse this function
        will execute the associated callback.
        Pulses can be of following types -
        1)Prepare
        2)Suspend
        3)Resume
        4)Complete
        5)System Voltage not okay
        6)System voltage okay


@param [in] clientname name of client that is going to be registered to server.
@param [in] level      level on which client wants to register itself.
@param [in] ops        pointer to structure which consists function pointer
                       to  prepare ,suspend and resume callbacks.
@param [in] flags      used by  client library while registering client through
                       devctl  api.
@param [out]hdl        Optional output handle, to be used during deregister time to
                       free up resources allocated during registration.


@dependencies
  None

@return
  0 on success , non-zero on failure.

@sideeffects

==========================================================================*/

int pm_register(const char *name,
                enum pm_prio_level level,
                const struct pm_ops_s *ops ,
                int  flags,
                void *ctxt,
                pm_client_t *hdl);

/**=========================================================================

FUNCTION pm_deregister

@brief  This function frees up resources that allocated during registration time.

@param [in] hdl        Client handle, returned via pm_register()

@dependencies
  None

@return
  0 on success , non-zero on failure.

@sideeffects

==========================================================================*/

int pm_deregister( pm_client_t hdl );


/****************************************************************************************
**powermgr 注册
****************************************************************************************/
void powermgr_str_register();

/****************************************************************************************
**powermgr 发送ACK
****************************************************************************************/
int powermgr_str_replyAck(int32_t sta);

#ifdef __cplusplus
}
#endif

#endif //_PM_LIFECYCLE_LISTENER_H_