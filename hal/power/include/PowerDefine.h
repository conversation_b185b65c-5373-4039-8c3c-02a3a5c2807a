/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/

#ifndef _POWER_MESSAGE_DEFINE_H_
#define _POWER_MESSAGE_DEFINE_H_

#include <map>
#include <cstddef>
#include <cstdint>
#include <stdint.h>

#include "com.autolink.power.pb.h"

using autolink::PowerEventId;

inline static int m_DoNotingSignal = -1;
template<typename ENUM>
inline constexpr int32_t toInt(ENUM const value) {
     return static_cast<int32_t>(value);
}
template<typename ENUM>
inline constexpr uint32_t toUInt(ENUM const value) {
     return static_cast<uint32_t>(value);
}

template<typename ENUM>
inline constexpr uint64_t toUInt64(ENUM const value) {
     return static_cast<uint64_t>(value);
}

//谨慎使用
template<typename ENUM>
inline constexpr uint8_t toUint8(ENUM const value) {
     return static_cast<uint8_t>(value);
}

enum MCUWakeUpReason : uint64_t {//供PowerEventId::PWR_MCU_POWER_ON_EVENT value值使用
     PWR_ON_COLD = 0, //GateWay通知冷启动开机
     STR_RESUME  = 1,//GateWay STR唤醒通知
     PWR_ON_RTC  = 2,//GateWay通知RTC唤醒
     PWR_ON_CAN  = 3,//GateWay通知CAN网络唤醒
};


enum PowerOnOffReason : uint64_t {
    PWR_ON_OFF_COLD = 0, //冷启动开机
    PWR_ON_OFF_FACRORY_RESET = 1, //android工程模式重启
    PWR_ON_OFF_CMD_REBOOT =2, //android ADB REBOOT请求MCU重启SOC
    PWR_ON_OFF_VOLTAGE_ABNORMAL = 3, //电压异常
    PWR_ON_OFF_HOST_HEARTBEAT_RESET = 4, //心跳重启
    PWR_ON_OFF_CAN_ACTIVE = 5, //ACC ON唤醒
    PWR_ON_OFF_STARTUP_RESET = 6, //开机15s内未收到SOC握手
    PWR_ON_OFF_POWERKEY_RESET = 7, //长按10s powerkey 重启
    PWR_ON_OFF_MCU_WDT_RESET = 8, //WDT重启
    PWR_ON_OFF_FAC_DIAG_RESET = 9,//诊断重启
    PWR_ON_OFF_IPC_NACK_RESET = 10,//IPC通讯无响应重启
    PWR_ON_OFF_KERNEL_INIT_FAIL_RESET = 11,//内核初始化失败重启
    PWR_ON_OFF_MCU_RTC_TIMEOUT = 12,//RTC超时
    PWR_ON_OFF_STR = 13,//STR
    PWR_ON_OFF_STR_TIMEOUT = 14,//STR超时
    PWR_ON_OFF_STD = 15,//STD
    PWR_ON_OFF_STD_TIMEOUT = 16,//STD超时
    PWR_ON_OFF_RESETEKEY = 17,//Reset key
    PWR_ON_OFF_VEHICLE_RESET = 18,//整车重启
    PWR_ON_OFF_TEMPERTURE_RECOVERY = 19,//温度恢复
    PWR_ON_OFF_SOC_RESCUE_RESET = 20,//android救援模式重启
    PWR_ON_OFF_SOC_RECOVERY_RESET = 21,//Soc恢复出厂设置重启
    PWR_ON_OFF_SOC_INVALID = 100,
};

enum PM_STATE : uint8_t {
    PM_RESUME           = 0,
    PM_SHUTDOWN_PREPARE = 1,
    PM_SUSPEND          = 2,
    PM_SUSPEND_COMPLETE = 3,
};


//Soc --> MCU 方向的信号信息
enum S2M_SIGNAL_NAME : int32_t {
    CMD_PWR_GET_POWERON_REASON = 0, //获取开机原因 MCU收到回复RVAR_PWR_POWERON_REASON
    SVAR_PWR_HEART_BEAT = 1,//设定心跳使能 MCU收到回复RVAR_PWR_HEART_BEAT(暂时不实现)
    CMD_PWR_HEART_BEAT = 2,//power心跳报文 MCU收到回复RVAR_PWR_HEART_BEAT_RES(暂时不实现)
    CMD_PWR_READY = 3,//power ready请求 MCU收到回复VAR_PWR_READY
    CMD_PWR_GET_VOLTAGE = 4,//获取电压请求 MCU收到回复RVAR_PWR_VOLTAGE
    CMD_PWR_GET_POWER_MODE = 5,//获取电源模式 MCU收到回复RVAR_PWR_POWER_MODE
    SVAR_PWR_ACK_POWER_MODE = 6,//Soc收到后需要回复RVAR_PWR_POWER_MODE
    SVAR_PWR_REQ_RESET = 7,//Soc向MCU请求复位自身
    SVAR_PWR_ILLEGALITY_SIGNAL_8 = 8,//不合法信号 纯粹为了排序对齐
    SVAR_PWR_ILLEGALITY_SIGNAL_9 = 9,//不合法信号 纯粹为了排序对齐
    SVAR_PWR_RESP_POWER_OFF = 10,//Soc收到RVAR_PWR_REQ_POWER_OFF时发起关机答复
    SVAR_PWR_REQ_POWER_OFF_NOW = 11,//Soc向MCU回复开始关机流程开始
    SVAR_PWR_REQ_NETWORK_KEEP = 12,//Soc请求保持CAN网络
    CMD_PWR_GET_MCU_LIFECYCLE = 13,//Soc请求获取MCU生命周期(暂时不实现)
    SVAR_PWR_SET_TEMPRUN_TIME = 14,//Soc向MCU设置tempRun模式时间 如10分钟模式(暂不实现)
    SVAR_PWR_REENTER_TEMPRUN_MODE = 15,//向MCU设置重新进入tempRun模式(暂不实现)
    SVAR_PWR_RESP_USER_POWER_OFF = 16,//用户POWER OFF答复 SOC不想重进TempRun模式时，通知MCU进入下一个电源状态(Standby模式) 暂不实施
    SVAR_PWR_ILLEGALITY_SIGNAL_17 = 17,//不合法信号 纯粹为了排序对齐
    SVAR_PWR_READY_RESP = 18,//Soc握手确认，收到RVAR_PWR_READY后回复
    SVAR_PWR_INVALID_SIGNAL = 19,//无效ID
};

typedef struct S_SignalConfig {
    PowerEventId        EventId;
	S2M_SIGNAL_NAME		signalName;
    bool                isSignalLegal;
    uint16_t			valueSize;
}S2MSignalConfig;

//Soc --> MCU 方向的信号信息
static const S2MSignalConfig m_S2MSignalConfigTable[] = {
    {PowerEventId::PWR_SOC_REQ_POWERON_REASON_EVENT,S2M_SIGNAL_NAME::CMD_PWR_GET_POWERON_REASON,true,0},
    {PowerEventId::PWR_MCU_RESP_POWERON_REASON_EVENT,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL,false,0},
    {PowerEventId::PWR_SOC_HEART_BEAT_SWITCH_EVENT,S2M_SIGNAL_NAME::SVAR_PWR_HEART_BEAT,true,1},
    {PowerEventId::PWR_MCU_HEART_BEAT_SWITCH_RESP_EVENT,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL,false,0},
    {PowerEventId::PWR_SOC_HEART_BEAT_EVENT,S2M_SIGNAL_NAME::CMD_PWR_HEART_BEAT,true,0},
    {PowerEventId::PWR_SOC_POWER_READY_EVENT,S2M_SIGNAL_NAME::CMD_PWR_READY,true,0},
    {PowerEventId::PWR_MCU_POWER_READY_RESP_EVENT,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL,false,0},
    {PowerEventId::PWR_SOC_POWER_READY_SUCC_EVENT,S2M_SIGNAL_NAME::SVAR_PWR_READY_RESP,true,0},
    {PowerEventId::PWR_SOC_REQ_VOLTAGE_EVENT,S2M_SIGNAL_NAME::CMD_PWR_GET_VOLTAGE,true,0},
    {PowerEventId::PWR_MCU_RESP_VOLTAGE_EVENT,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL,false,0},
    {PowerEventId::PWR_SOC_REQ_POWER_MODE_EVENT,S2M_SIGNAL_NAME::CMD_PWR_GET_POWER_MODE,true,0},
    {PowerEventId::PWR_MCU_RESP_POWER_MODE_EVENT,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL,false,0},
    {PowerEventId::PWR_SOC_POWER_MODE_REPLY_EVENT,S2M_SIGNAL_NAME::SVAR_PWR_ACK_POWER_MODE,true,1},
    {PowerEventId::PWR_SOC_REQ_RESET_SOC_EVENT,S2M_SIGNAL_NAME::SVAR_PWR_REQ_RESET,true,1},
    {PowerEventId::PWR_MCU_REQ_RESET_EVENT,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL,false,0},
    {PowerEventId::PWR_MCU_POWER_ON_EVENT,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL,false,0},
    {PowerEventId::PWR_SOC_RESP_POWER_OFF_EVENT,S2M_SIGNAL_NAME::SVAR_PWR_RESP_POWER_OFF,true,1},
    {PowerEventId::PWR_MCU_REQ_POWER_OFF_EVENT,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL,false,0},
    {PowerEventId::PWR_SOC_RESP_POWER_OFF_NOW_EVENT,S2M_SIGNAL_NAME::SVAR_PWR_REQ_POWER_OFF_NOW,true,1},
    {PowerEventId::PWR_SOC_REQ_NETWORK_KEEP_EVENT,S2M_SIGNAL_NAME::SVAR_PWR_REQ_NETWORK_KEEP,true,1},
    {PowerEventId::PWR_SOC_SET_TEMPRUN_TIME_EVENT,S2M_SIGNAL_NAME::SVAR_PWR_SET_TEMPRUN_TIME,true,2},
    {PowerEventId::PWR_SOC_REENTER_TEMPRUN_MODE_EVENT,S2M_SIGNAL_NAME::SVAR_PWR_REENTER_TEMPRUN_MODE,true,2},
    {PowerEventId::PWR_MCU_TEMPRUN_TIMEOUT_EVENT,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL,false,0},
    {PowerEventId::PWR_MCU_REPLY_HEART_BEAT_EVENT,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL,false,0}
};


//MCU --> Soc 方向的信号信息
enum M2S_SIGNAL_NAME : int32_t {
    RVAR_PWR_POWERON_REASON = 0,//返回开机原因
    RVAR_PWR_HEART_BEAT_SWITCH_RESP = 1,//设定心跳使能答复
    RVAR_PWR_HEART_BEAT_RES = 2,//power心跳报文答复
    RVAR_PWR_READY = 3,//power ready答复
    RVAR_PWR_VOLTAGE = 4,//电压通知
    RVAR_PWR_POWER_MODE = 5,//电源模式通知
    SVAR_PWR_ILLEGALITY_SIGNAL_6 = 6,//不合法信号 纯粹为了排序对齐
    SVAR_PWR_ILLEGALITY_SIGNAL_7 = 7,//不合法信号 纯粹为了排序对齐
    RVAR_PWR_RESET = 8,//MCU要求Soc复位
    RVAR_PWR_POWER_ON = 9,//MCU通知Soc已经开机 SOC在关机流程，且可打断状态，收到该通知时，需要当前终止关机流程
    RVAR_PWR_REQ_POWER_OFF = 10,//MCU要求Soc关机 Soc收到后回复SVAR_PWR_RESP_POWER_OFF
    SVAR_PWR_ILLEGALITY_SIGNAL_11 = 11,//不合法信号 纯粹为了排序对齐
    SVAR_PWR_ILLEGALITY_SIGNAL_12 = 12,//不合法信号 纯粹为了排序对齐
    RVAR_PWR_MCU_LIFECYCLE = 13,//MCU向Soc返回MCU生命周期
    SVAR_PWR_ILLEGALITY_SIGNAL_14 = 14,//不合法信号 纯粹为了排序对齐
    SVAR_PWR_ILLEGALITY_SIGNAL_15 = 15,//不合法信号 纯粹为了排序对齐
    SVAR_PWR_ILLEGALITY_SIGNAL_16 = 16,//不合法信号 纯粹为了排序对齐
    RVAR_PWR_TEMPRUN_TIMEOUT = 17,//临时模式超时通知 临时模式倒计时还剩10S时通知SOC
    RVAR_PWR_INVALID_SIGNAL =  18,//无效ID
};

typedef struct M_SignalConfig {
	M2S_SIGNAL_NAME		signalName;
    PowerEventId        EventId;
    bool                isSignalLegal;
    uint16_t			dataSize;
    bool                hasReplySignal;
    S2M_SIGNAL_NAME     SocReplySignal;
}M2SSignalConfig;
//MCU --> Soc 方向的信号信息
static const M2SSignalConfig m_M2SSignalConfigTable[] = {
    {M2S_SIGNAL_NAME::RVAR_PWR_POWERON_REASON,PowerEventId::PWR_MCU_RESP_POWERON_REASON_EVENT,true,2,false,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL},
    {M2S_SIGNAL_NAME::RVAR_PWR_HEART_BEAT_SWITCH_RESP,PowerEventId::PWR_MCU_HEART_BEAT_SWITCH_RESP_EVENT,true,2,false,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL},
    {M2S_SIGNAL_NAME::RVAR_PWR_HEART_BEAT_RES,PowerEventId::PWR_MCU_RESP_POWERON_REASON_EVENT,true,1,false,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL},
    {M2S_SIGNAL_NAME::RVAR_PWR_READY,PowerEventId::PWR_MCU_POWER_READY_RESP_EVENT,true,1,true,S2M_SIGNAL_NAME::SVAR_PWR_READY_RESP},
    {M2S_SIGNAL_NAME::RVAR_PWR_VOLTAGE,PowerEventId::PWR_MCU_RESP_VOLTAGE_EVENT,true,3,false,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL},
    {M2S_SIGNAL_NAME::RVAR_PWR_POWER_MODE,PowerEventId::PWR_MCU_RESP_POWER_MODE_EVENT,true,2,true,S2M_SIGNAL_NAME::SVAR_PWR_ACK_POWER_MODE},
    {M2S_SIGNAL_NAME::SVAR_PWR_ILLEGALITY_SIGNAL_6,PowerEventId::PWR_INVALID_EVENT,false,0,false,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL},
    {M2S_SIGNAL_NAME::SVAR_PWR_ILLEGALITY_SIGNAL_7,PowerEventId::PWR_INVALID_EVENT,false,0,false,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL},
    {M2S_SIGNAL_NAME::RVAR_PWR_RESET,PowerEventId::PWR_MCU_REQ_RESET_EVENT,true,2,false,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL},
    {M2S_SIGNAL_NAME::RVAR_PWR_POWER_ON,PowerEventId::PWR_MCU_POWER_ON_EVENT,true,2,false,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL},
    {M2S_SIGNAL_NAME::RVAR_PWR_REQ_POWER_OFF,PowerEventId::PWR_MCU_REQ_POWER_OFF_EVENT,true,2,true,S2M_SIGNAL_NAME::SVAR_PWR_RESP_POWER_OFF},
    {M2S_SIGNAL_NAME::SVAR_PWR_ILLEGALITY_SIGNAL_11,PowerEventId::PWR_INVALID_EVENT,false,0,false,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL},
    {M2S_SIGNAL_NAME::SVAR_PWR_ILLEGALITY_SIGNAL_12,PowerEventId::PWR_INVALID_EVENT,false,0,false,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL},
    {M2S_SIGNAL_NAME::RVAR_PWR_MCU_LIFECYCLE,PowerEventId::PWR_INVALID_EVENT,false,0,false,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL},
    {M2S_SIGNAL_NAME::SVAR_PWR_ILLEGALITY_SIGNAL_14,PowerEventId::PWR_INVALID_EVENT,false,0,false,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL},
    {M2S_SIGNAL_NAME::SVAR_PWR_ILLEGALITY_SIGNAL_15,PowerEventId::PWR_INVALID_EVENT,false,0,false,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL},
    {M2S_SIGNAL_NAME::SVAR_PWR_ILLEGALITY_SIGNAL_16,PowerEventId::PWR_INVALID_EVENT,false,0,false,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL},
    {M2S_SIGNAL_NAME::RVAR_PWR_TEMPRUN_TIMEOUT,PowerEventId::PWR_MCU_TEMPRUN_TIMEOUT_EVENT,true,1,false,S2M_SIGNAL_NAME::SVAR_PWR_INVALID_SIGNAL},
};

struct ILCMclientInfo{
    std::string clientID;
    uint32_t clientPID;
    uint32_t clientPriority;
};

struct PowerStateActionInfo
{
    bool prepareIsRunNext;
    bool suspendIsRunNext;
    bool suspendCompleteIsRunNext;

    PowerStateActionInfo()
        :prepareIsRunNext(false),
         suspendIsRunNext(false),
         suspendCompleteIsRunNext(false){
    }
};


#define SOC_READY_TIMEOUT (40 * 1000)

//inline constexpr uint16_t M2SSignalConfig_count = sizeof(m_M2SSignalConfigTable) / sizeof(M2SSignalConfig);
//inline constexpr uint16_t S2MSignalConfig_count = sizeof(m_S2MSignalConfigTable) / sizeof(S2MSignalConfig);

#endif //_POWER_MESSAGE_DEFINE_H_