/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef _POWER_SERVICE_FDBUS_H_
#define _POWER_SERVICE_FDBUS_H_

#include <iostream>

#include "PowerFDBusServer.h"
#include "PowerFdbusCallback.h"
#include "com.autolink.power.pb.h"

namespace powerservice
{
using autolink::PowerMode;
using autolink::PowerPropValue;
using autolink::PowerEventId;
using autolink::PowerMessageId;

class PowerFDBusServer;
class PowerServiceFdbus
{
public:
    PowerServiceFdbus();
    virtual ~PowerServiceFdbus();
    void init();
    void notifyPowerSessionEvent(uint32_t code,const PowerPropValue& value);
    void talkWithGateWayALSleepComplete();
    void notifyPowerMGRSTRState(uint32_t code);

private:
    PowerFDBusServer* m_PowerServer;
};

} // namespace powerservice

#endif