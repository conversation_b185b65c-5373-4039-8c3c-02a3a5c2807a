/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/

#ifndef _POWER_STATE_H_
#define _POWER_STATE_H_

#include <mutex>
#include <atomic>
#include <stdint.h>

#include "PowerDefine.h"


namespace powerservice
{

class PowerState
{

public:
    static PowerState* getInstance();
    void setPMStatus(PM_STATE state);
    PM_STATE getPMStatus();
    void setCurrentPowerMode(uint64_t mode);
    uint64_t getCurrentPowerMode();
    void setCurrentPowerOnReason(uint64_t reason);
    uint64_t getCurrentPowerOnReason();
    void setCurrentVoltage(uint64_t voltage);
    uint64_t getCurrentVoltage();
    bool setPowerOffSTRState(bool state);
    bool getPowerOffSTRState();
    void setAnimIsPlayed(bool is_played);
    bool getAnimIsPlayed();
    void setSocIsReady(bool is_ready);
    bool getSocIsReady();

    uint64_t getWaitForLASTRSleepSec();
    uint64_t getWaitForLANormalSleepSec();

    PowerStateActionInfo &PMStatusActionInfo();
    void ResetPMStatusActionInfo();


private:
    PowerState();
    virtual ~PowerState();

    bool mPowerOffSTRState;
    PM_STATE mPMStatus;
    uint64_t mPowerMode;
    uint64_t mPowerOnReason;
    uint64_t mVoltage;
    const uint64_t mALSTRSleepWaitSec = 35;//35秒
    const uint64_t mALNormalSleepWaitSec = 3;//3秒
    std::atomic<bool> mAnimISPlayed;     //false: playing  true:played
    std::atomic<bool> mSocISReady;    //false:no_ready  true:ready
    PowerStateActionInfo mPMStatusActionInfo;

};

} // namespace powerservice
#endif //_POWER_STATE_H_
