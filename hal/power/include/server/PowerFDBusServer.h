/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef _POWER_FDBUS_SERVER_H_
#define _POWER_FDBUS_SERVER_H_


#include <cstdint>
#include <map>
#include <mutex>
#include <fdbus/CBaseWorker.h>
#include <fdbus/CBaseJob.h>
#include <fdbus/CFdbBaseObject.h>

#include "PowerDefine.h"
#include "PowerState.h"
#include "com.autolink.power.pb.h"
#include "com.autolink.power.priv.pb.h"
#include "PowerFdbusCallback.h"
#include "Timer.hpp"

namespace powerservice
{
using namespace ipc::fdbus;
using namespace autolink;
using autolink::PowerPropValue;
using autolink::DispControlPropValue;

class PowerState;
class PowerFDBusServer : public CBaseServer
{
public:
    PowerFDBusServer(const char *name, CBaseWorker *worker = 0);
    virtual ~PowerFDBusServer();
    bool init();
    void notifyPowerSessionEvent(uint32_t code,const PowerPropValue& PropValue);
    void talkWithGateWayALSleepComplete();
    void notifyDispControlSessionEvent(uint32_t code,const DispControlPropValue& PropValue);
    void notifyPowerMGRSTRState(uint32_t code);

private:
    void onOnline(const CFdbOnlineInfo &info);
    void onOffline(const CFdbOnlineInfo &info);
    void onInvoke(CBaseJob::Ptr &msg_ref);
    void onSubscribe(CBaseJob::Ptr &msg_ref);
    void talkWithGateWayClient(uint64_t code, PowerPropValue PropValue);
    void talkWithAutoLinkClient(uint64_t code, PowerPropValue PropValue);
    void talkWithAutoLinkClientByALC(uint64_t code, PowerPropValue PropValue);
    void talkWithILCM(DispControlPropValue PropValue);
    void talkWithClientRegMsg(ILCMControlClientProp PropValue);
    void talkWithClientSuspendAckOkMsg(ILCMControlClientProp PropValue);
    void talkWithClientSuspendAckNokMsg(ILCMControlClientProp PropValue);
    void talkWithClientSuspendAckDelayMsg(ILCMAckDelay PropValue);
    void talkWithClientResumeAckOkMsg(ILCMControlClientProp PropValue);
    void talkWithClientResumeAckNokMsg(ILCMControlClientProp PropValue);
    void talkWithClientResumeAckDelayMsg(ILCMAckDelay PropValue);

    void startPowerOffTimer();
    void replyGateWayInALSleepComplete(uint32_t timerId,uint32_t time);
    static void *monitorPowerStatusThread(void* arg);
    void setResumeStatus();
    void stopPowerOffTimer();
    ILCMclientInfo convertToILCMclientInfo(ILCMControlClientProp PropValue);
    bool checkIsAllClientSuspendAckOk();
    void clearClientSuspendAckInfos();

private:
    std::mutex mClientInfoLock;
    std::map<std::string, ILCMclientInfo> mClientPropInfos; //注册的客户端信息
    std::list<std::string>  mClientSuspendAckInfos; //注册的客户端suspend应答状态信息
    qnx::Timer mSocReadyTimer;
};

}// namespace powerservice
#endif //_POWER_FDBUS_SERVER_H_