/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef _POWER_FDBUS_CALLBACK_H_
#define _POWER_FDBUS_CALLBACK_H_

class PowerFdbusCallback
{
public:
    PowerFdbusCallback() {};
    virtual ~PowerFdbusCallback() {};
    virtual void sendPowerSignalData(uint8_t const *data, uint16_t size) = 0;
};

#endif //