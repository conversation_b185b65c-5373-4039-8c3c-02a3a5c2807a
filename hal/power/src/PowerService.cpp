/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#include <cstdint>
#include <fdbus/CBaseWorker.h>
#include <fdbus/CBaseJob.h>

#include "powerControl.h"
#include "projectionConfig.h"
#include "PowerDefine.h"
#include "PowerService.h"
#include "PowerServiceManager.h"
#include "PowerLog.h"
#include "PowerTimer.h"



namespace powerservice
{
using namespace ipc::fdbus;

static CBaseWorker power_client_worker("power_down_thread", FDB_WORKER_DEFAULT, 3000);

// Class PowerService
PowerService::PowerService() {
    PowerTimer::getInstance()->init();
    m_PowerServiceFdbus = new PowerServiceFdbus();
    m_PowerServiceManager = new PowerServiceManager();
    if (hasALCPowerClient()) {
        LOG_I("hasALCPowerClient=%s\n",hasALCPowerClient() ? "true" : "false");
        m_PowerFDBusClient = new PowerFDBusClient();
    }
}

PowerService::~PowerService() {
    if (m_PowerServiceFdbus != nullptr) {
        delete m_PowerServiceFdbus;
        m_PowerServiceFdbus = nullptr;
    }
    if (m_PowerFDBusClient != nullptr) {
        delete m_PowerFDBusClient;
        m_PowerFDBusClient = nullptr;
    }
    if (m_PowerServiceManager != nullptr) {
        delete m_PowerServiceManager;
        m_PowerServiceManager = nullptr;
    }
}

PowerService* PowerService::getInstance() {
    //static PowerService instance;
    //return &instance;
    static pthread_mutex_t mtx = PTHREAD_MUTEX_INITIALIZER;
    static PowerService* instance;
    pthread_mutex_lock(&mtx);
    if (instance == nullptr) {
        instance = new PowerService();
    }
    pthread_mutex_unlock(&mtx);
    return instance;
}

void PowerService::init() {
    if (m_PowerServiceFdbus != nullptr) {
        m_PowerServiceFdbus->init();
    }
    if (m_PowerFDBusClient != nullptr) {
        m_PowerFDBusClient->init();
    }

    /* start worker thread */
    power_client_worker.start();
    LOG_I("PowerService init successfully \n");
}

//是否需要将收到的数据进行广播 根据业务需要走
bool PowerService::isNeedBroadcast(int32_t signalId) {
    bool result = false;
    switch(signalId) {
        case RVAR_PWR_POWER_MODE://mcu返回电源模式
        case RVAR_PWR_POWERON_REASON://mcu返回开机原因
        case RVAR_PWR_VOLTAGE://mcu返回当前电压
        case RVAR_PWR_RESET://mcu要求soc重启
        case RVAR_PWR_POWER_ON://mcu告知已经开机
        case RVAR_PWR_REQ_POWER_OFF://mcu要求soc开始走下电流程
        case RVAR_PWR_TEMPRUN_TIMEOUT://mcu通知soc临时模式超时
            result = true;
            break;
        default:
            break;
    }
    //LOG_D("result is %s:\n",result? "true" : "false");
    return result;
}

void PowerService::talkWithALC(uint8_t const *data,uint16_t size) {
    if (hasALCPowerClient()) {
        if (m_PowerServiceFdbus != nullptr) {
            m_PowerFDBusClient->sendPowerSignalToALC(data,size);
        } else {
            LOG_E("PowtalkWithALC,m_PowerServiceFdbus is nullptr.\n");
        }
    } else {
       LOG_E("talkWithALC,hasALCPowerClient=false.\n");
    }
}

void PowerService::talkWithGateWayALSleepComplete() {
    if (m_PowerServiceFdbus != nullptr) {
        m_PowerServiceFdbus->talkWithGateWayALSleepComplete();
    } else {
        LOG_E("talkWithGateWayALSleepComplete,m_PowerServiceFdbus is null.\n");
    }
}

void PowerService::notifyPowerSessionEvent(uint32_t code,const PowerPropValue& value,int32_t signalId) {
    if (m_PowerServiceFdbus != nullptr) {
        if (isNeedBroadcast(signalId)) {
            m_PowerServiceFdbus->notifyPowerSessionEvent(code,value);
        }
    } else {
        LOG_E("notifyPowerSessionEvent,m_PowerServiceFdbus is null\n");
    }
    //有些信号收到是要回复的
    if (hasALCPowerClient() && m_PowerFDBusClient != nullptr) {
        m_PowerFDBusClient->replySignalToALC(value,signalId);
    } else {
        LOG_E("m_PowerFDBusClient is null\n");
    }
}

void PowerService::notifyPowerMGRSTRState(uint32_t code) {
    if (m_PowerServiceFdbus != nullptr) {
        m_PowerServiceFdbus->notifyPowerMGRSTRState(code);
    } else {
        LOG_E("notifyPowerMGRSTRState,m_PowerServiceFdbus is null.\n");
    }
}

} // namespace Powerservice

