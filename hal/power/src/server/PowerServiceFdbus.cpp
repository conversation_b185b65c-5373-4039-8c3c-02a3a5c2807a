/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <fdbus/fdbus.h>
#include <fdbus/CFdbProtoMsgBuilder.h>

#include "PowerServiceFdbus.h"
#include "PowerDefine.h"
#include "PowerLog.h"

namespace powerservice
{

using namespace ipc::fdbus;
static CBaseWorker main_worker("power_fdbus_callback_thread");

// class PowerServiceFdbus
PowerServiceFdbus::PowerServiceFdbus() {
    m_PowerServer = nullptr;
}

PowerServiceFdbus::~PowerServiceFdbus() {
    if (m_PowerServer != nullptr) {
        delete m_PowerServer;
    }
    m_PowerServer = nullptr;
}

void PowerServiceFdbus::init() {
    std::string server_name("autolink_power_service");
    std::string url(FDB_URL_SVC);
    url += server_name;
    if(m_PowerServer == nullptr) {
        /* start fdbus context thread */
        CFdbContext::enableLogger(true);
        FDB_CONTEXT->start();
        CBaseWorker *worker_ptr = &main_worker;
        /* start worker thread */
        worker_ptr->start();
        /* create servers and bind the address: svc://service_name */
        server_name += "_server";
        m_PowerServer = new PowerFDBusServer(server_name.c_str(), worker_ptr);
    }
    m_PowerServer->bind(url.c_str());
    m_PowerServer->init();
    LOG_I("PowerServiceFdbus bind begin.\n");
}

void PowerServiceFdbus::notifyPowerSessionEvent(uint32_t code,const PowerPropValue& value) {
    if (m_PowerServer != nullptr) {
        m_PowerServer->notifyPowerSessionEvent(code,value);
    }else{
        LOG_D("m_PowerServer is null.\n");
    }
}

void PowerServiceFdbus::talkWithGateWayALSleepComplete() {
    if (m_PowerServer != nullptr) {
        m_PowerServer->talkWithGateWayALSleepComplete();
    }else{
        LOG_D("talkWithGateWayALSleepComplete,m_PowerServer is null.\n");
    }
}

void PowerServiceFdbus::notifyPowerMGRSTRState(uint32_t code) {
    if (m_PowerServer != nullptr) {
        m_PowerServer->notifyPowerMGRSTRState(code);
    }else{
        LOG_D("notifyPowerMGRSTRState,m_PowerServer is null.\n");
    }
}

} // namespace powerservice
