/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#include <cstdint>
#include <string>
#include <thread>

#include "com.autolink.power.pb.h"
#include "PowerClientSetJob.h"
#include <PowerLog.h>

namespace powerservice
{

PowerClientSetJob::PowerClientSetJob(uint8_t const *data,uint16_t size, ALCPowerClient* client) {
    m_ALCClinet = client;
    m_DataArr = new uint8_t[size];
    memcpy(m_DataArr, data, size);
    m_ArrSize = size;
}

void PowerClientSetJob::setPowerSignalData() {
    //LOG_I("setPowerSignalData\n");
    m_ALCClinet->sendMsg(REQ_MSG_ALC_TP_POWER, m_DataArr, m_ArrSize);
}

void PowerClientSetJob::run(Ptr &ref) {
    setPowerSignalData();
}
} // namespace powerservice
