/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#include <errno.h>
#include <stdio.h>
#include <functional>
#include <iostream>

#include "ALCClientType.h"
#include "ALCPowerClient.h"
#include "PowerClientSetJob.h"
#include "PowerFDBusClient.h"
#include "PowerMessageJob.h"
#include "PowerService.h"
#include "PowerLog.h"
#include "PowerTimer.h"
#include "powerControl.h"

#define _LOG_DEBUG_SWITCH_

namespace powerservice
{

static CBaseWorker comd_worker("power_up_thread", FDB_WORKER_DEFAULT, 3000);
static CBaseWorker set_worker("power_down_thread", FDB_WORKER_DEFAULT, 3000);
static uint32_t reSendTimes = 0;
bool PowerFDBusClient::mIsPowerOnReady = false;
uint32_t PowerFDBusClient::powerModeValue = toUInt(PowerMode::PWR_MODE_NONE);
PM_STATE PowerFDBusClient::mPMStatus = PM_STATE::PM_RESUME;

PowerFDBusClient *PowerFDBusClient::getInstance() {
    static PowerFDBusClient* instance;
    static pthread_mutex_t mtx = PTHREAD_MUTEX_INITIALIZER;
    pthread_mutex_lock(&mtx);
    if (instance == nullptr) {
        instance = new PowerFDBusClient();
    }
    pthread_mutex_unlock(&mtx);
    return instance;
}

PowerFDBusClient::PowerFDBusClient()
    : mComdClient(nullptr),
      isConnectComd(false) {
    //LOG_D("new PowerFDBusClient instance.\n");
    pthread_t thread_id;
    if(pthread_create(&thread_id, 0, PowerFDBusClient::moinitorLaPowerStatusThread, this) == EOK) {
        pthread_setname_np(thread_id, "PowerFDBusClientMonitor");
    } else {
        LOG_E("pthread_create PowerFDBusClientMonitor faild.\n");
    }
}

PowerFDBusClient::~PowerFDBusClient() {
    if (mComdClient != nullptr) {
        delete mComdClient;
        mComdClient = nullptr;
    }
}

void PowerFDBusClient::init() {
    if (mComdClient == nullptr) {
        mComdClient = new ALCPowerClient();
    }
    /*start comd broadcast worker thread*/
    comd_worker.start();
    set_worker.start();
    mComdClient->connectService(this);
    LOG_I("PowerFDBusClient connectService start.\n");
}

void PowerFDBusClient::sendPowerSignalToALC(uint8_t const *data,uint16_t size) {
    CBaseJob* job = new PowerClientSetJob(data,size,mComdClient);
    set_worker.sendAsync(job);
    #if 0
    if (isConnectComd) {
        int retry = 2;
        int ret = 0;
        while (retry--) {
            ret = mComdClient->sendMsg(REQ_MSG_ALC_TP_POWER, data, size);
            if (ret == ALCClientMessageState::Message_OK) {
                break;
            }
        }
    } else {
        LOG_E("PowerFDBusClient sendPowerSignalToALC error--not connect ALC \n");
    }
    #endif
}

void PowerFDBusClient::onConnect() {
    isConnectComd = true;
    startPowerReadyTimer();
    LOG_D("PowerFDBusClient onConnect done.\n");
}

void *PowerFDBusClient::moinitorLaPowerStatusThread(void* arg) {
    //LOG_I("moinitorLaPowerStatusThread start.");
    usleep(20000*1000);//等android开机完成
    while(1) {
    usleep(1000*1000);
        if(mPMStatus == PM_STATE::PM_RESUME) {
            LOG_I("PowerFDBusClient get LA power state.");
           if(request_gvm_power_state() == false) {//代表android休眠完成
                PowerFDBusClient::getInstance()->replyPowerOffInAndroidSleep(POWER_TIMER_POWER_OFF,10000);
            }
        }
    }
    return nullptr;
}

void PowerFDBusClient::setPMStatus(PM_STATE state) {
    mPMStatus = state;
}

void PowerFDBusClient::startPowerReadyTimer() {
    if (isConnectComd == true) {
        //LOG_I("startPowerReadyTimer.\n");
        if(!PowerTimer::getInstance()->isInitialized()) {
            PowerTimer::getInstance()->init();//确保初始化完成了
        }
        PowerTimer::getInstance()->startTimer(POWER_TIMER_POWER_READY, 
            std::bind(&PowerFDBusClient::onPowerReadyTimeOut, this, std::placeholders::_1, std::placeholders::_2), RESEND_INTERVA);
    } else {
        LOG_I("startPowerReadyTimer--isConnectComd=false.\n");
    }
}

void PowerFDBusClient::onPowerReadyTimeOut(uint32_t timerId,uint32_t time) {
    LOG_I("onPowerReadyTimeOut,timerId=%d,reSendTimes=%d,time=%d\n",timerId, reSendTimes,time);
    if (!isPowerOnReady() && reSendTimes < 50) {
        reSendTimes = reSendTimes + 1;
        sendPowerReadyToALC();
        startPowerReadyTimer();
    } else {
        startHeartBeatTimer();
        PowerTimer::getInstance()->stopTimer(POWER_TIMER_POWER_READY);
    }
}

void PowerFDBusClient::onDisconnect() {
    isConnectComd = false;
    LOG_E("PowerFDBusClient onDisconnect done.\n");
}

void PowerFDBusClient::onRevicesMsg(int32_t code, uint8_t const *data, uint16_t size) {
   if(code == NTF_MSG_ALC_TP_POWER && data != nullptr && size >= 1) {
        //LOG_I("PowerFDBusClient onRevicesMsg power Message.\n");
        CBaseJob* job = new PowerMessageJob(data, size);
        comd_worker.sendAsync(job);
   } else {
        LOG_E("PowerFDBusClient onRevicesMsg power Message code=%d,size=%d error \n", code, size);
    }
}

//通信协议定义 https://t83dfrspj4.feishu.cn/docx/DUn3dkB6go3vGHxjkSIc4Iu8nbf
void PowerFDBusClient::replySignalToALC(const PowerPropValue& PropValue,int32_t signalId) {
    uint8_t data[8] = {0};
    uint16_t size = 8;
    LOG_I("replySignalToALC signalId=%d\n",signalId);
    switch(signalId) {
        case RVAR_PWR_READY: {
            //根据协议收到 RVAR_PWR_READY 回复 SVAR_PWR_READY_RESP 进行确认
            #ifdef  _LOG_DEBUG_SWITCH_
            //LOG_I("replySignalToALC SVAR_PWR_READY_RESP to mcu.\n");
            #endif
            setPowerOnReady(true);
            if (m_M2SSignalConfigTable[signalId].hasReplySignal) {
                data[0] = {toUint8(m_M2SSignalConfigTable[signalId].SocReplySignal)};
                sendPowerSignalToALC(data,size);
            } else {
                LOG_I("hasReplySignal:false.");
            }
        }
            break;
        case RVAR_PWR_POWER_MODE:{//电源模式通知 Soc回复ACK
            #ifdef  _LOG_DEBUG_SWITCH_
            //LOG_I("replySignalToALC SVAR_PWR_ACK_POWER_MODE to mcu.\n");
            #endif
            std::array<uint8_t, 8> bytes = uint64_to_bytes(PropValue.value());
            data[0] = toUint8(toUint8(m_M2SSignalConfigTable[signalId].SocReplySignal));
            data[1] = bytes.at(0);
            setlocalPowerMode(toInt(bytes.at(0)));
            sendPowerSignalToALC(data,size);
        }
            break;
        case RVAR_PWR_REQ_POWER_OFF: {
            //MCU要求Soc关机 按照协议回复 SOC_SVAR_PWR_RESP_POWER_OFF
            //D[0]=toUint8(S2MSignalId::SOC_SVAR_PWR_RESP_POWER_OFF)
            //D[1]=15 //15秒
            data[0] = {toUint8(m_M2SSignalConfigTable[signalId].SocReplySignal)};
            if(PropValue.value() == PowerOnOffReason::PWR_ON_OFF_STR) {//STR下电开始
                data[1] = mALSTRSleepWaitSec;//等待Android端STR休眠完成
            } else {
                data[1] = mALNormalSleepWaitSec;//暂定3秒
            }
            #ifdef  _LOG_DEBUG_SWITCH_
            //LOG_I("reply SOC_SVAR_PWR_RESP_POWER_OFF to ALC.\n");
            #endif
            sendPowerSignalToALC(data,size);
            if(PropValue.value() == PowerOnOffReason::PWR_ON_OFF_STR) {//STR下电开始
                sendPowerOrSleepKey(1);//发送keycode_sleep通知android开始STR休眠 0:powerkey 1:sleepkey
            } else {
                startPowerOffTimer();
            }
        }
            break;
        case RVAR_PWR_HEART_BEAT_SWITCH_RESP://设定心跳使能答复
            break;
        case RVAR_PWR_POWERON_REASON: {//返回开机原因
            //LOG_I("Receive RVAR_PWR_POWERON_REASON from ALC.\n");
        }
            break;
        case RVAR_PWR_HEART_BEAT_RES://power心跳报文答复
            break;
        case RVAR_PWR_VOLTAGE://电压通知
            break;
        case RVAR_PWR_RESET://MCU要求Soc复位
             break;
        //MCU通知Soc已经开机 SOC在关机流程，且可打断状态，收到该通知时，需要当前终止关机流程
        case RVAR_PWR_POWER_ON: {
            stopPowerOffTimer();
            startPowerReadyTimer();//唤醒后重新握手
        }
            break;
        case RVAR_PWR_MCU_LIFECYCLE:
             LOG_I("Receive RVAR_PWR_MCU_LIFECYCLE.");
             break;
        case RVAR_PWR_TEMPRUN_TIMEOUT:{
            LOG_I("Receive RVAR_PWR_TEMPRUN_TIMEOUT.");
            break;
        }
        default:
            break;
    }
}

void PowerFDBusClient::startPowerOffTimer() {
    if(!PowerTimer::getInstance()->isInitialized()) {
        PowerTimer::getInstance()->init();//确保初始化完成了
    }
    PowerTimer::getInstance()->startTimer(POWER_TIMER_POWER_OFF,
        std::bind(&PowerFDBusClient::replyPowerOffInAndroidSleep, this, std::placeholders::_1, std::placeholders::_2), 3000);
}

void PowerFDBusClient::replyPowerOffInAndroidSleep(uint32_t timerId, uint64_t time) {
    LOG_I(" replyPowerOffInAndroidSleep,timerId=%d,time=%d\n",timerId,time);
    if (mPMStatus == PM_STATE::PM_SUSPEND) {//防抖
        LOG_I("PowerFDBusClient-mPMStatus=PM_SUSPEND.\n");
        return;
    }
    setPMStatus(PM_STATE::PM_SUSPEND);
    uint64_t sec = time / 1000;
    //soc回复mcu SVAR_PWR_RESP_POWER_OFF 之后，power hal同时通知android和qnx内部
    //逻辑处理之后还需要回复 SVAR_PWR_REQ_POWER_OFF_NOW 给mcu进行下一阶段的下电握手
    uint8_t signal = toUint8(S2M_SIGNAL_NAME::SVAR_PWR_REQ_POWER_OFF_NOW);
    uint8_t data[8] = {0};//{signal,3};
    data[0] = signal;
    data[1] = sec;
    sendPowerSignalToALC(data,8);
    stopHeartBeatTimer();
}

void PowerFDBusClient::stopPowerOffTimer() {
    //LOG_I("stopPowerOffTimer.\n");
    PowerTimer::getInstance()->stopTimer(POWER_TIMER_POWER_OFF);
}

void PowerFDBusClient::startHeartBeatTimer() {
    if (isPowerOnReady()) {
        //LOG_I("startHeartBeatTimer.\n");
        if(!PowerTimer::getInstance()->isInitialized()) {
            PowerTimer::getInstance()->init();//确保初始化完成了
        }
        PowerTimer::getInstance()->startTimer(POWER_TIMER_HEART_BAET,
            std::bind(&PowerFDBusClient::HeartBeatSendTimer, this, std::placeholders::_1, std::placeholders::_2), HEART_BEAT_INTERVAL);
    } else {
        LOG_I("startHeartBeatTimer but isPowerOnReady:false.\n");
    }
}

void PowerFDBusClient::stopHeartBeatTimer() {
    //LOG_I("stopHeartBeatTimer.");
    setPowerOnReady(false);
    PowerTimer::getInstance()->stopTimer(POWER_TIMER_HEART_BAET);
}

void PowerFDBusClient::HeartBeatSendTimer(uint32_t timerId, uint32_t time) {
    //LOG_I("HeartBeatSendTimer timerId=%d,time=%d\n",timerId,time);
    sendHeartBeatToALC();
    startHeartBeatTimer();
}

void PowerFDBusClient::sendPowerReadyToALC() {
    LOG_I("Reply power ready\n");
    uint8_t data[8] = {0};
    data[0] = 3;//S2M_SIGNAL_NAME::CMD_PWR_READY
    sendPowerSignalToALC(data,8);
}

void PowerFDBusClient::sendHeartBeatToALC() {
    uint8_t data[8] = {0};
    data[0] = 2;//S2M_SIGNAL_NAME::CMD_PWR_HEART_BEAT
    sendPowerSignalToALC(data,8);
}

void PowerFDBusClient::setPowerOnReady(bool set) {
    mIsPowerOnReady = set;
    LOG_I("setPowerOnReady=%s.\n",set ? "true":"false");
}

bool PowerFDBusClient::isPowerOnReady() {
    //LOG_I("mIsPowerOnReady is %s\n", mIsPowerOnReady ? "true":"false");
    return mIsPowerOnReady;
}

std::array<uint8_t, 8> PowerFDBusClient::uint64_to_bytes(uint64_t num) {
    std::array<uint8_t, 8> bytes;
    for (int i = 0; i < 8; ++i) {
        // 每次提取从低到高的字节，模拟小端序排列
        bytes[i] = static_cast<uint8_t>((num >> (i * 8)) & 0xFF);
    }
    return bytes;
}

uint32_t PowerFDBusClient::getlocalPowerMode() {
    return powerModeValue;
}

void PowerFDBusClient::setlocalPowerMode(uint32_t mode) {
    if (!(mode > toInt(PowerMode::PWR_MODE_NONE)
     && mode <= toInt(PowerMode::PWR_MODE_CHARGGING))) {
        LOG_E("Set powermode mode:%d is illegal!",mode);
        return;
    }
    if (powerModeValue == mode) {
        LOG_I("powermode is not changed,is %d",mode);
        return;
    }
    powerModeValue = mode;
    restartPowerReadyHandshakeIfNeed(mode);
    LOG_I("setlocalPowerMode mode is %d",mode);
}

void PowerFDBusClient::restartPowerReadyHandshakeIfNeed(uint32_t mode) {
    uint32_t prePowerMode = getlocalPowerMode();
    LOG_I("restartPowerReadyHandshakeIfNeed prePowerMode:%d,current mode:%d.\n",toInt(prePowerMode),mode);

    switch(mode) {
        case PowerMode::PWR_MODE_STR:
        case PowerMode::PWR_MODE_SLEEP:
        case PowerMode::PWR_MODE_ABNORMAL:
            stopHeartBeatTimer();
            break;
        default:
            break;
    }
    switch(prePowerMode) {
        case PowerMode::PWR_MODE_STR:
        case PowerMode::PWR_MODE_SLEEP:
        case PowerMode::PWR_MODE_ABNORMAL:
        case PowerMode::PWR_MODE_NONE:
                if (mode == PowerMode::PWR_MODE_CHARGGING
                    || mode == PowerMode::PWR_MODE_STANDBY
                    || mode == PowerMode::PWR_MODE_RUN
                    || mode == PowerMode::PWR_MODE_TEMP_RUN) {
                        //唤醒后重新握手
                        startPowerReadyTimer();
                    }
            break;
        default:
            break;
    }
}

} // namespace powerservice
