/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#include <cstdint>
#include <string>

#include "com.autolink.power.pb.h"
#include "ALCPowerClient.h"
#include "PowerService.h"
#include "PowerMessageJob.h"
#include "PowerLog.h"

//#define __LOG_DEBUG__
#define MAX_UINT64 UINT64_MAX  // 值为 18446744073709551615ULL

namespace powerservice
{
using namespace autolink;
 
PowerMessageJob::PowerMessageJob(uint8_t const *data,uint16_t size) {
    m_DataArr = new uint8_t[size];
    memcpy(m_DataArr, data, size);
    m_ArrSize = size;
    #ifdef __LOG_DEBUG__
    for(int i = 0; i < size; i ++) {
        LOG_D("DataArr[%d]=%d,size=%d",i,m_DataArr[i],size);
    }
    #endif
}

PowerMessageJob::~PowerMessageJob() {
    if (m_DataArr != nullptr) {
        //LOG_D("~PowerMessageJob().\n");
        delete m_DataArr;
        m_DataArr = nullptr;
    }
}

uint64_t PowerMessageJob::array_to_uint64_le(uint8_t const *data,uint16_t size) {
    uint64_t result = 0;

    if (size == 1) {
        LOG_I("data size==1, return 0");
        return 0;
    } else if (size == 2) {//data[0]为 PowerEventValue里的PowerEventId EventId
        LOG_I("data size==2, return data[1]:%d",data[1]);
        return static_cast<uint64_t>(data[1]);
    }
    if (size > 8) {
        LOG_E("data size error, size is:%d",size);
        return MAX_UINT64;
    }

    size_t dataSize = size - 1;
    uint8_t value[8] = {0};
    //请注意 这里是去掉data[0]的数据的，data[0]是命令 其他的是命令携带的数据
    for (size_t i = 1; i < size; i ++) {
        value[i-1] = data[i];
        //LOG_I("array_to_uint64_le--value[%d]",(i-1),value[i-1]);
    }

    for (size_t i = 0; i < dataSize; i ++) {
        // 将每个字节左移到对应的小端序位置，并合并到结果中
        //LOG_I("array_to_uint64_le, result:%d,value[%d]=%d",result,i,value[i]);
        result |= static_cast<uint64_t>((value[i]) << (i * 8));
    }
    //LOG_I("array_to_uint64_le, result=%d",result);
    return result;
}

bool PowerMessageJob::isMCUSignalID(int32_t signalId) {
    bool result = false;
    switch(signalId) {
        case RVAR_PWR_POWERON_REASON:
        case RVAR_PWR_HEART_BEAT_SWITCH_RESP:
        case RVAR_PWR_HEART_BEAT_RES:
        case RVAR_PWR_READY:
        case RVAR_PWR_VOLTAGE:
        case RVAR_PWR_POWER_MODE:
        case RVAR_PWR_RESET:
        case RVAR_PWR_POWER_ON:
        case RVAR_PWR_REQ_POWER_OFF:
        case RVAR_PWR_MCU_LIFECYCLE:
        case RVAR_PWR_TEMPRUN_TIMEOUT:
            result = true;
            break;
        default:
            break;
    }
    return result;
}

void PowerMessageJob::revicePowerMsgFromServer() {
    //检查是否合法协议
    if (!isMCUSignalID(toInt(m_DataArr[0]))) {
        LOG_E("The signalid=%d from mcu is illegal.",m_DataArr[0]);
        return;
    }
    int32_t signalId = toInt(m_DataArr[0]);

    uint64_t value = array_to_uint64_le(m_DataArr,m_ArrSize);
    if (value != MAX_UINT64) {
        PowerPropValue powerValue;
        PowerEventId eventid = m_M2SSignalConfigTable[signalId].EventId;
        powerValue.set_eventid(eventid);
        powerValue.set_value(value);
        LOG_E("revicePowerMsgFromServer--signalId:%d,value:%d",signalId,value);
        uint32_t code = PowerMessageId::MSG_POWERSERVICE_TO_AUTOLINK;
        PowerService::getInstance()->notifyPowerSessionEvent(code,powerValue,signalId);
    } else {
        LOG_E("m_M2SSignalConfigTable[%d].dataBufferValue == MAX_UINT64,it is error!!",signalId);
    }
}

void PowerMessageJob::run(Ptr &ref) {
    revicePowerMsgFromServer();
}

} // namespace powerservice
