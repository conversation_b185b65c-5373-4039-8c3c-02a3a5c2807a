/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/

#include <stdio.h>
#include <iostream>
#include <string.h>
#include <unistd.h>
#include <malloc.h>
#include <fdbus/fdbus.h>
#include <fdbus/cJSON/cJSON.h>
#include <fdbus/CFdbCJsonMsgBuilder.h>

#include <PowerLog.h>
#include "ALCClient.h"
#include "ALCClientType.h"
#include "ALCPowerClient.h"

namespace autolink
{
    using namespace std;

    class ALCPowerClientCallback : public IALCClientCallback
    {
    public:
        ALCPowerClientCallback(IALCPowerClientCallback *callback) {
            mClientCallback = callback;
        }
        virtual ~ALCPowerClientCallback() {
            //do nothing
        }
        virtual void onConnect() {
            if (mClientCallback != nullptr) {
                mClientCallback->onConnect();
            }
        }
        virtual void onDisconnect() {
            if (mClientCallback != nullptr) {
                mClientCallback->onDisconnect();
            }
        }
        virtual void onRevicesMsg(uint32_t code, uint8_t const *data, uint16_t size) {
            LOG_I("onRevicesMsg code=%d,size=%d\n", code, size);

            if (mClientCallback != nullptr && code == NTF_MSG_ALC_TP_POWER) {
                mClientCallback->onRevicesMsg(code, data, size);
            } else {
                LOG_D("mClientCallback is null.\n");
            }
        }

    private:
        IALCPowerClientCallback *mClientCallback;
    };

    ALCPowerClient::ALCPowerClient() : mPowerCallback(nullptr) {
        mALCClinet = new ALCClient();
    }

    ALCPowerClient::~ALCPowerClient() {
        LOG_E("~ALCPowerClient().\n");
        if (mALCClinet) {
            delete mALCClinet;
            mALCClinet = nullptr;
        }
        if (mPowerCallback) {
            delete mPowerCallback;
            mPowerCallback = nullptr;
        }
    }

    void ALCPowerClient::connectService(IALCPowerClientCallback *callback) {
        mPowerCallback = new ALCPowerClientCallback(callback);
        if (mALCClinet != nullptr) {
            LOG_I("Connect ALC server begin\n");
            std::vector<EMessageId> subList;
            subList.push_back(NTF_MSG_ALC_TP_POWER);
            mALCClinet->connectService(mPowerCallback, subList);
            subList.clear();
        }
    }

    void ALCPowerClient::disConnectService() {
        if (mALCClinet != nullptr) {
            mALCClinet->disConnectService();
        }
    }
    int ALCPowerClient::sendMsg(int16_t index, uint8_t const *data, uint16_t size) {
        int retry = 2;
        int ret = 0;
        if (mALCClinet) {
            while (retry--) {
                ret = mALCClinet->sendMsg(REQ_MSG_ALC_TP_POWER, data, size);
                if (ret == ALCClientMessageState::Message_OK) {
                    break;
                }
            }
            return ret;
        } else {
            LOG_E("ALCPowerClient mALCClinet is nullptr.\n");
        }
    }
} // namespace autolink