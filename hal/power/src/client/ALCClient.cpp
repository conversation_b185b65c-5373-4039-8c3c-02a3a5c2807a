/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/

#include <stdio.h>
#include <iostream>
#include <string.h>
#include <unistd.h>
#include <fdbus/fdbus.h>
#include <fdbus/cJSON/cJSON.h>
#include <fdbus/CFdbCJsonMsgBuilder.h>

#include "PowerLog.h"
#include "ALCClient.h"

#define FDBUS_INVOKE_TIMEOUT 1000 // unit ms
using namespace ipc::fdbus;
namespace autolink
{
    using namespace std;
    class ALCClientFdbus : public CBaseClient
    {
    public:
        ALCClientFdbus(const char *name, IALCClientCallback *callback, CBaseWorker *worker = 0)
            : CBaseClient(name, worker), mCallback(callback), mSessionId(0) {
            std::vector<EMessageId>().swap(mSubList);
            enableUDP(true);
            enableAysncRead(true);
            enableAysncWrite(true);
        }
        ~ALCClientFdbus() {
            std::vector<EMessageId>().swap(mSubList);
            disconnect();
        }

        void setSubscribeEvent(EMessageId id) {
            mSubList.push_back(id);
        }
        void subscribeEvent() {
            if (mIsOnline) {
                CFdbMsgSubscribeList subscribe_list;
                for (auto item : mSubList) {
                    addNotifyItem(subscribe_list, item);
                }
                subscribe(subscribe_list);
            }
        }

        int sendmsg(uint32_t code, uint8_t const *data, uint16_t size, uint32_t timeout, uint8_t retry) {
            ALCClientMessageState ret = ALCClientMessageState::Message_OK;
            while (retry--) {
                ret = sendmsg(code, data, size, timeout);
                if (ret != ALCClientMessageState::Message_Failed) {
                    break;
                }
            }
            return ret;
        }
        ALCClientMessageState sendmsg(uint32_t code, uint8_t const *data, uint16_t size, uint32_t timeout) {
            ALCClientMessageState result = ALCClientMessageState::Message_OK;
            if (mIsOnline) {
                CBaseJob::Ptr ref(new CBaseMessage(code));
                //LOG_D("ALCClientMessageState mSessionId:%d\n",mSessionId);
                bool ret = invoke(mSessionId, ref, data, size, timeout);
                if (!ret) {
                    LOG_E("invoke(%d, %d) = false...Done", code, size);
                    result = ALCClientMessageState::Message_Failed;
                    return result;
                }
                int32_t error_code = FdbMsgStatusCode::FDB_ST_OK;
                auto msg = castToMessage<CBaseMessage *>(ref);
                if (msg->isStatus()) {
                    std::string reason;
                    if (!msg->decodeStatus(error_code, reason)) {
                        error_code = FdbMsgStatusCode::FDB_ST_MSG_DECODE_FAIL;
                        LOG_E("reply decode fail!");
                    }
                }
                if (FdbMsgStatusCode::FDB_ST_OK == error_code) {
                    result = ALCClientMessageState::Message_OK;
                    LOG_D("invoke(%d, %d)...Success", code, size);
                } else if (FdbMsgStatusCode::FDB_ST_AUTO_REPLY_OK == error_code) {
                    result = ALCClientMessageState::Message_OK;
                    LOG_D("invoke(%d, %d)...Success", code, size);
                } else if (FdbMsgStatusCode::FDB_ST_TIMEOUT == error_code) {
                    result = ALCClientMessageState::Message_TimeOut;
                    LOG_E("invoke(%d, %d)...Timeout", code, size);
                } else {
                    result = ALCClientMessageState::Message_Failed;
                    LOG_E("invoke(%d, %d)...Error", code, size);
                }
            } else {
                LOG_E("server is offline!");
                result = ALCClientMessageState::Message_Failed;
            }
            return result;
        }

    protected:
        /* called when connected to the server */
        void onOnline(const CFdbOnlineInfo &info) {
            LOG_E("[ALC] onOnline sid=%d,FirstOrLast=%d\n", info.mSid, info.mFirstOrLast);
            mIsOnline = true;
            mSessionId = info.mSid;
            subscribeEvent();
            if (mCallback != nullptr) {
                mCallback->onConnect();
            }
        }

        /* called when disconnected from server */
        void onOffline(const CFdbOnlineInfo &info) {
            LOG_E("[ALC] onOffline sid=%d,FirstOrLast=%d\n", info.mSid, info.mFirstOrLast);
            if (mCallback != nullptr) {
                mCallback->onDisconnect();
            }
            mIsOnline = false;
        }

        /* called when events broadcasted from server is received */
        void onBroadcast(CBaseJob::Ptr &msg_ref) {
            //LOG_I("ALCClientFdbus::onBroadcast.\n");
            auto msg = castToMessage<CBaseMessage *>(msg_ref);
            uint8_t *buffer = msg->getPayloadBuffer();
            int count = msg->getPayloadSize();
            mCallback->onRevicesMsg(msg->code(), buffer, count);
        }

        void onReply(CBaseJob::Ptr &msg_ref) {
            LOG_I("onReply--\n");
        }

        /* check if something happen... */
        void onStatus(CBaseJob::Ptr &msg_ref, int32_t error_code, const char *description) {
            auto msg = castToMessage<CBaseMessage *>(msg_ref);
            if (msg->isSubscribe()) {
                LOG_E("subscribe is ok! sn=%d is received\n", msg->sn());
            } else {
                LOG_E("subscribe is error! sn=%d is received\n", msg->sn());
            }
            LOG_E("Reason: %s\n", description);
        }

    private:
        std::shared_ptr<IALCClientCallback> mCallback;
        bool mIsOnline = false;
        FdbSessionId_t mSessionId;
        std::vector<EMessageId> mSubList;
    };

    static ALCClientFdbus *zALCClient = nullptr;

    ALCClient::ALCClient() {
        //do nothing
    }

    ALCClient::~ALCClient() {
        if (zALCClient != nullptr) {
            delete zALCClient;
            zALCClient = nullptr;
        }
    }

    void ALCClient::connectService(IALCClientCallback *callback, std::vector<EMessageId> &list) {
        //LOG_I("%s line: %d \n", __FUNCTION__, __LINE__);
        std::string server_name("ALCom");
        std::string url(FDB_URL_SVC);
        url += server_name;
        if (zALCClient == nullptr) {
            CFdbContext::enableLogger(false);
            FDB_CONTEXT->start();
            CBaseWorker *worker_ptr = new CBaseWorker("ALCClientFdbus");
            worker_ptr->start();
            // server_name += std::string(__progname);

            zALCClient = new ALCClientFdbus("ALCom", callback, worker_ptr);
            if (zALCClient != nullptr) {
                for (auto it : list) {
                    //LOG_I("%s:setSubscribeEvent it=%d.\n",__FUNCTION__,it);
                    zALCClient->setSubscribeEvent(it);
                }
                zALCClient->enableReconnect(true);
                zALCClient->setOnlineChannelType(FDB_SEC_NO_CHECK);
                zALCClient->connect(url.c_str());
            }
        }
    }

    void ALCClient::disConnectService() {
        if (zALCClient != nullptr) {
            zALCClient->disconnect(FDB_INVALID_ID);
        }
    }

    void ALCClient::subScribe(EMessageId id) {
        if (zALCClient != nullptr) {
            zALCClient->setSubscribeEvent(id);
        }
    }

    int ALCClient::sendMsg(uint32_t code, uint8_t const *data, uint16_t size) {
        int ret = 0;
        if (zALCClient != nullptr) {
            // zALCClient->invoke(code, data, size);
            ret = zALCClient->sendmsg(code, data, size, FDBUS_INVOKE_TIMEOUT, 1);
        } else {
            ret = -1;
            LOG_E("ALCClient::%s zALCClient nullptr\n", __FUNCTION__);
        }
        return ret;
    }
} // namespace autolink