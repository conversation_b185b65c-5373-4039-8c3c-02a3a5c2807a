/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
//#ifndef AUTOLINK_POWERHAL_QNX
//#include <hidl/HidlSupport.h>
//#include <hidl/HidlTransportSupport.h>
//#endif //AUTOLINK_POWERHAL_QNX
#include <stdio.h>
#include <semaphore.h>
#include <string>
#include <sys/types.h>
#include <sys/resource.h>
#include <unistd.h>

#include "PowerLog.h"
#include "PowerService.h"
#include "PowerDefine.h"
#include "PowermgrLifecycleListener.h"

using namespace powerservice;

//#ifndef AUTOLINK_POWERHAL_QNX
//QNX不需要
//using android::hardware::configureRpcThreadpool;
//using android::hardware::joinRpcThreadpool;
//#endif //AUTOLINK_POWERHAL_QNX

int main(int argc, char **argv)
{
//#ifdef AUTOLINK_POWERHAL_QNX
    struct rlimit my_limits;
    my_limits.rlim_cur = 512 * 1024 * 1024; // 512MB
    my_limits.rlim_max = 512 * 1024 * 1024;
    power_slog2_init();
    LOG_W("main enter");
    if (-1 == setrlimit(RLIMIT_AS, &my_limits)) {
        LOG_E("setrlimit() failed! errno: %d", errno);
    }
    sem_t sem;
    sem_init(&sem, 0, 0);
    PowerService::getInstance()->init();
    powermgr_str_register();
    sem_wait(&sem);
    return 0;
//#else //AUTOLINK_POWERHAL_QNX
    //PowerService::getInstance()->init();
    //configureRpcThreadpool(5, true /*callerWillJoin*/);
	//joinRpcThreadpool();
	//return 0;
//#endif //AUTOLINK_POWERHAL_QNX
}