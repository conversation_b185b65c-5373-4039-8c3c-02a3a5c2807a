/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#include <fcntl.h>
#include <unistd.h>
#include <pthread.h>

#include "PowerLog.h"
#include "powerControl.h"

/**
 * 从QNX给android发送 KEYCODE_POWER 按键事件
 */
void sendPowerOrSleepKey(int key) {
    ssize_t bytes_written;
    const char* powerValue = "power";
    const char* SleepValue = "sleep";
    const int value_len = (key == 0) ? strlen(powerValue) : strlen(SleepValue);

    //打开设备节点
    int fd = open("/dev/qvm/la/powerkey", O_WRONLY);
    if (fd == -1) {
        LOG_E("Open /dev/qvm/la/powerkey fail!");
        return;
    }

    if (key == 0) {
        bytes_written = write(fd, powerValue, value_len);
    } else {
        bytes_written = write(fd, SleepValue, value_len);
    }
    LOG_I("sendPowerKey powerkey value size:%d",value_len);

    //错误处理
    if (bytes_written == -1) {
        LOG_E("write /dev/qvm/la/powerkey fail!");
        close(fd);
        return;
    } else if (bytes_written != value_len) {
        LOG_E("write /dev/qvm/la/powerkey fail,bytes_written:%d,value_len:%d",bytes_written,value_len);
        close(fd);
        return;
    }
    close(fd);
}
/** 
 * 获取文件节点/dev/pdbg/qvm/la/power_status的状态
 * 用于STR休眠唤醒过程中检测android是否休眠完成
 */
bool request_gvm_power_state() {
    int hGvmPwrSts = 0;
    int state = true;
    char buf[128] = {0};
    size_t n ;

    hGvmPwrSts = open ("/dev/pdbg/qvm/la/power_status", O_RDONLY ) ;
    if(hGvmPwrSts < 0) {
        LOG_I("qvm is shutdown, wait 1 second.");
            return false;
    }

    if (EOK != lseek(hGvmPwrSts, 0, SEEK_SET)) {
        LOG_E ("Failed to seek to start of file, err=%d", errno);
        return false;
    }

    if (-1 == (n = read (hGvmPwrSts, &buf, sizeof(buf)))){
        LOG_E ("Failed to read gvm power state n=%zu, err=%d", n , errno);
        close(hGvmPwrSts);
        return false;
    }

    if (atoi(buf)) {
        state = true ; //One or more vCPUs are running
    } else {
        state = false ; //All vCPUs are down
    }
    close(hGvmPwrSts);
    return state ;
}

/**
 *根据屏幕displayId来设置屏幕亮度
 *@param displayId:屏幕id
 *@param level:设置屏幕亮度的值 范围0~100
 *@return 0:DISPCTRL_SUCCESS -1:DISPCTRL_ERROR_INVALID_DISPLAY_ID -2:DISPCTRL_ERROR_INVALID_PARAMETER -3:DISPCTRL_ERROR_INVALID_PARAMETER
 */
 int power_dispctrl_set_display_brightness(int displayId, int level) {
#ifdef AUTOLINK_HAS_ALC_POWERCLIENT
     //待SBSP提供接口
     return 0;
#else
    if (level < 0 || level > 100) {
        return DISPCTRL_ERROR_UNSUPPORTED_OPERATION;
    }
    int res = dispctrl_set_display_backlight_brightness(displayId, level);
    LOG_I("Set display brightness level=%d in displayId=%d,res=%d",level,displayId,res);
    return res;
#endif // AUTOLINK_HAS_ALC_POWERCLIENT
 }

/**
 *根据屏幕displayId来获取屏幕亮度值 合法范围0~100
 *@param displayId:屏幕id
 *@return 0:DISPCTRL_SUCCESS -1:DISPCTRL_ERROR_INVALID_DISPLAY_ID -2:DISPCTRL_ERROR_INVALID_PARAMETER -3:DISPCTRL_ERROR_INVALID_PARAMETER
 */
 int power_dispctrl_get_display_backlight_brightness(int displayId) {
#ifdef AUTOLINK_HAS_ALC_POWERCLIENT
     //待SBSP提供接口
     return 0;
#else
    int level = dispctrl_get_display_backlight_brightness(displayId);
    //临时返回100 待BSP层提供接口
    LOG_I("Get display brightness level=%d in displayId=%d",level,displayId);
    return level;
#endif // AUTOLINK_HAS_ALC_POWERCLIENT
 }

/**
 *根据屏幕displayId来设置屏幕背光状态 合法范围0和1
 *@param displayId:屏幕id
 *@param powerstate:背光灭(0) 背光亮(1)
 *@return 0:DISPCTRL_SUCCESS -1:DISPCTRL_ERROR_INVALID_DISPLAY_ID -2:DISPCTRL_ERROR_INVALID_PARAMETER -3:DISPCTRL_ERROR_INVALID_PARAMETER
 */
 int power_dispctrl_set_display_powerstate(int displayId, int powerstate) {
#ifdef AUTOLINK_HAS_ALC_POWERCLIENT
     //待SBSP提供接口
     return 0;
#else
    int res = dispctrl_set_display_power_state(displayId,powerstate);
    LOG_I("Set display powerstate powerstate=%d in displayId=%d,res=%d",powerstate,displayId,res);
    return res;
#endif // AUTOLINK_HAS_ALC_POWERCLIENT
 }

/**
 *根据屏幕displayId来获取屏幕背光状态 返回结果合法范围0和1
 *@param displayId:屏幕id
 *@return 0:DISPCTRL_SUCCESS -1:DISPCTRL_ERROR_INVALID_DISPLAY_ID -2:DISPCTRL_ERROR_INVALID_PARAMETER -3:DISPCTRL_ERROR_INVALID_PARAMETER
 */
 int power_dispctrl_get_display_power_state(int displayId) {
#ifdef AUTOLINK_HAS_ALC_POWERCLIENT
     //待SBSP提供接口
     return 0;
#else
    int powerstate = dispctrl_get_display_power_state(displayId);
    LOG_I("Get display powerstate=%d in displayId=%d",powerstate,displayId);
    return powerstate;
#endif // AUTOLINK_HAS_ALC_POWERCLIENT
 }

/**
 *根据屏幕displayId来获取屏幕软件版本信息
 *@param displayId:屏幕id
 *@param version:版本信息 传入的参数地址容量至少5个字节 为了安全起见建议长度16字节
 *@return 0:DISPCTRL_SUCCESS -1:DISPCTRL_ERROR_INVALID_DISPLAY_ID -2:DISPCTRL_ERROR_INVALID_PARAMETER -3:DISPCTRL_ERROR_INVALID_PARAMETER
 */
 int power_dispctrl_get_display_software_version(int displayId,char* version, size_t versionSize) {
#ifdef AUTOLINK_HAS_ALC_POWERCLIENT
     //待SBSP提供接口
     return 0;
#else
    int res = dispctrl_get_display_hardware_version(displayId,version,versionSize);
    LOG_I("Get sw version message,res=%d",res);
    return res;
#endif // AUTOLINK_HAS_ALC_POWERCLIENT
 }

/**
 * *根据屏幕displayId来获取屏幕硬件版本信息
 *@param displayId:屏幕id
 *@param version:版本信息 传入的参数地址容量至少5个字节 为了安全起见建议长度16字节
 *@return 0:DISPCTRL_SUCCESS -1:DISPCTRL_ERROR_INVALID_DISPLAY_ID -2:DISPCTRL_ERROR_INVALID_PARAMETER -3:DISPCTRL_ERROR_INVALID_PARAMETER
 */
int power_dispctrl_get_display_hardware_version(int displayId, char* version, size_t versionSize) {
#ifdef AUTOLINK_HAS_ALC_POWERCLIENT
     //待SBSP提供接口
     return 0;
#else
    int res = dispctrl_get_display_hardware_version(displayId,version,versionSize);
    LOG_I("Get hw version message,res=%d",res);
    return res;
#endif // AUTOLINK_HAS_ALC_POWERCLIENT
}

/**
 * *根据屏幕displayId来获取屏幕温度
 *@param displayId:屏幕id
 *@return temp:屏幕温度
 *@return 0:DISPCTRL_SUCCESS -1:DISPCTRL_ERROR_INVALID_DISPLAY_ID -2:DISPCTRL_ERROR_INVALID_PARAMETER -3:DISPCTRL_ERROR_INVALID_PARAMETER
 */
 int power_dispctrl_get_display_temperature(int displayId) {
#ifdef AUTOLINK_HAS_ALC_POWERCLIENT
     //待SBSP提供接口
     return 0;
#else
     int temp = dispctrl_get_display_temperature(displayId);
     return temp;
#endif // AUTOLINK_HAS_ALC_POWERCLIENT
 }