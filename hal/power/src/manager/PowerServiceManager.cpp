/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#include <stdio.h>
#include <iostream>

#include "PowerDefine.h"
#include "PowerLog.h"
#include "PowerService.h"
#include "PowerServiceManager.h"

namespace powerservice
{
    PowerServiceManager::PowerServiceManager() {
        m_Mtx = PTHREAD_MUTEX_INITIALIZER;
    }
    PowerServiceManager::~PowerServiceManager() {
        pthread_mutex_destroy(&m_Mtx);
    }

    void PowerServiceManager::updatePowerSessionValue(const PowerPropValue& powerValue) {
        //PowerService::getInstance()->notifyPowerSessionEvent(powerValue);
    }

}//namespace powerservice