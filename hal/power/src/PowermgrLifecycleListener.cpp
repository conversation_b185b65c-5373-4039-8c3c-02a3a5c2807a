/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/

#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <pthread.h>
#include <sys/neutrino.h>
#include <fcntl.h>

//#include "PowerLog.h"
#include "powerControl.h"
#include "PowerService.h"
#include "projectionConfig.h"
#include "com.autolink.power.pb.h"
#include "PowermgrLifecycleListener.h"

#define PREPARE_PULSE   (_PULSE_CODE_MINAVAIL)
#define SUSPEND_PULSE   (_PULSE_CODE_MINAVAIL+1)
#define RESUME_PULSE    (_PULSE_CODE_MINAVAIL+2)
#define COMPLETE_PULSE  (_PULSE_CODE_MINAVAIL+3)
#define VOLT_NOK_PULSE  (_PULSE_CODE_MINAVAIL+4)
#define VOLT_OK_PULSE   (_PULSE_CODE_MINAVAIL+5)
#define BREAKOUT_PULSE  (_PULSE_CODE_MINAVAIL+10)

using powerservice::PowerService;
using autolink::PowerMGRMsgId;

struct _pm_client_s {
    int chid;
    int coid;
    int pm_fd;
    void *ctxt;
    struct pm_ops_s ops;
};

pm_state s_curStrSta = PM_STATE_MAX;
int32_t s_pm_fd = 0;

static int powermgr_str_suspendCallback(void* ctxt);
static int powermgr_str_resumeCallback(void* ctxt);
static int powermgr_str_prepareCallback(void* ctxt);
static int powermgr_str_completeCallback(void* ctxt);
static void* pm_register_client_thread(void *arg);

static int powermgr_str_suspendCallback (void* ctxt) {
    (void) ctxt;
    // qnx process
    LOG_I("powermgr_str_suspendCallback");
    powermgr_str_replyAck(0);
    if (powerservice::hasALCPowerClient()) {
        PowerService::getInstance()->notifyPowerMGRSTRState(PowerMGRMsgId::PM_STATE_SUSPEND_NOTICE);
    }

    return 0;
}

static int powermgr_str_resumeCallback (void* ctxt) {
    (void) ctxt;
    // qnx process
    LOG_I("powermgr_str_resumeCallback");
    powermgr_str_replyAck(0);
    if (powerservice::hasALCPowerClient()) {
        PowerService::getInstance()->notifyPowerMGRSTRState(PowerMGRMsgId::PM_STATE_RESUME_NOTICE);
    }

    return 0;
}

static int powermgr_str_prepareCallback (void* ctxt) {
    (void) ctxt;
    // qnx process
    LOG_I("powermgr_str_prepareCallback");
    if (powerservice::hasALCPowerClient()) {
        PowerService::getInstance()->notifyPowerMGRSTRState(PowerMGRMsgId::PM_STATE_SHUTDOWN_PREPARE_NOTICE);
    }

    return 0;
}

static int powermgr_str_completeCallback (void* ctxt) {
    (void) ctxt;
    // qnx process
    LOG_I("powermgr_str_completeCallback");
    if (powerservice::hasALCPowerClient()) {
        PowerService::getInstance()->notifyPowerMGRSTRState(PowerMGRMsgId::PM_STATE_RESUME_COMPLETE_NOTICE);
        power_dispctrl_set_display_powerstate(0,1);//点亮中控背光
    } else {
        power_dispctrl_set_display_powerstate(0,1);//点亮中控背光
    }

  return 0;
}

static void* pm_register_client_thread(void *arg) {
    int  ret = -1;
    struct _pulse pulse;
    struct _pm_client_s *client = (struct _pm_client_s*) arg;
    const struct pm_ops_s *ops = &client->ops;
    void* ctxt = client->ctxt;

    pthread_setname_np(pthread_self() ,"pm_client_handler");
    LOG_I("pm_register_client_thread");

    while(1) {
        ret = MsgReceivePulse(client->chid, &pulse, sizeof(pulse),NULL);
        if(ret != 0) {
            LOG_I("MsgReceivePulse Failed rc[%d]:%s",
                    errno,
                    strerror(errno));
            goto out;
        }

        struct pm_ack_s ack = {.rc = EOK, .state = PM_STATE_MAX};
        switch (pulse.code) {
            case PREPARE_PULSE: {
                //ack.state = PM_STATE_PREPARE;
                s_curStrSta = PM_STATE_PREPARE;
                if (ops->prepare)
                    ack.rc = ops->prepare(ctxt);
                break;
            }
            case SUSPEND_PULSE: {
               //ack.state = PM_STATE_SUSPEND;
                s_curStrSta = PM_STATE_SUSPEND;
                if (ops->suspend)
                    ack.rc = ops->suspend(ctxt);
                break;
            }
            case RESUME_PULSE: {
                //ack.state = PM_STATE_RESUME;
                s_curStrSta = PM_STATE_RESUME;
                if (ops->resume)
                    ack.rc = ops->resume(ctxt);
                break;
            }
            case COMPLETE_PULSE: {
                //ack.state = PM_STATE_COMPLETE;
                s_curStrSta = PM_STATE_COMPLETE;
                if (ops->complete)
                    ack.rc = ops->complete(ctxt);
                break;
            }
            case VOLT_NOK_PULSE: {
                //ack.state = PM_STATE_VOLT_NOK;
                s_curStrSta = PM_STATE_MAX;
                if (ops->volt_nok)
                    ack.rc = ops->volt_nok(ctxt);
                break;
            }
            case VOLT_OK_PULSE: {
                //ack.state = PM_STATE_VOLT_OK;
                s_curStrSta = PM_STATE_MAX;
                if (ops->volt_ok)
                    ack.rc = ops->volt_ok(ctxt);
                break;
            }
            case _PULSE_CODE_DISCONNECT: {
                s_curStrSta = PM_STATE_MAX;
                LOG_E("PM resource manager is down.\n");
                goto out;
            }
            case BREAKOUT_PULSE: {
                s_curStrSta = PM_STATE_MAX;
                LOG_E("Deregistering pm client.\n");
                goto out;
            }
            default:
                break;
        }
    }
out:
    (void) ConnectDetach (client->coid);
    (void) ChannelDestroy(client->chid);
    close (client->pm_fd);
    free (client);
    return NULL;
}

int pm_register(const char *name, enum pm_prio_level level, const struct pm_ops_s *ops ,
                int flags, void *ctxt, pm_client_t *hdl) {
    struct _pm_client_s *client ;

    if (!ops) {
        return -1;
    }
    if (hdl) {
        *hdl = NULL;
    }
    client = (struct _pm_client_s *)malloc(sizeof(struct _pm_client_s));
    if (!client) {
        return -1;
    }

    memset (client, 0x0, sizeof (*client));
    client->ops = *ops,
    client->ctxt = ctxt ;
    client->pm_fd = open("/dev/pm",O_RDWR | O_CLOEXEC);
    if (client->pm_fd == -1) {
        LOG_E("open(/dev/pm) failed, error: [%d]", errno);
        free (client);
        return -1;
    }
    s_pm_fd = client->pm_fd;

    client->chid = ChannelCreate(_NTO_CHF_UNBLOCK | _NTO_CHF_DISCONNECT);
    if (client->chid == -1) {
        LOG_E("ChannelCreate() failed error:[%d]", errno);
        close (client->pm_fd);
        free (client);
        return -1;
    }

    client->coid = ConnectAttach(0 , 0, client->chid,
            _NTO_SIDE_CHANNEL, _NTO_COF_CLOEXEC);
    if (client->coid == -1) {
        LOG_E("ChannelCreate() failed error:[%d]", errno);
        ChannelDestroy (client->chid );
        close (client->pm_fd);
        free (client);
        return -1;
    }

    struct pm_register_s request;
    INIT_PM_REGISTER_STRUCT(&request);

    strlcpy ( request.name, name, sizeof ( request.name ));
    request.priority = level;
    request.chid = client->chid;
    request.flags = flags;

    if (ops->prepare) {
        request.pulse_codes[PM_STATE_PREPARE] = PREPARE_PULSE;
    }
    if (ops->suspend) {
        request.pulse_codes[PM_STATE_SUSPEND] = SUSPEND_PULSE;
    }
    if (ops->resume) {
        request.pulse_codes[PM_STATE_RESUME]  = RESUME_PULSE;
    }
    if (ops->complete) {
        request.pulse_codes[PM_STATE_COMPLETE] = COMPLETE_PULSE;
    }
    if (ops->volt_nok) {
        request.pulse_codes[PM_STATE_VOLT_NOK] = VOLT_NOK_PULSE;
    }
    if (ops->volt_ok) {
        request.pulse_codes[PM_STATE_VOLT_OK] = VOLT_OK_PULSE;
    }

    int rc = devctl (client->pm_fd, DCMD_PM_REGISTER, &request,
            sizeof(struct pm_register_s), NULL);
    if(rc != EOK) {
        LOG_E("DCMD_PM_REGISTER failed rc=%d", rc);
        ConnectDetach (client->coid);
        ChannelDestroy (client->chid);
        close (client->pm_fd);
        free (client) ;
        return -1;
    }

    pthread_t tid;
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    rc = pthread_create(&tid, &attr, &pm_register_client_thread, client);
    if (rc != EOK) {
        LOG_E("pthread_create() failed, error:[%d]", errno);
        ConnectDetach(client->coid);
        ChannelDestroy(client->chid );
        close(client->pm_fd);
        free(client) ;
        return -1;
    }

    if (hdl) {
        *hdl = client;
    }

    pthread_attr_destroy(&attr);
    return EOK ;
}

int pm_deregister(pm_client_t hdl) {
    struct _pm_client_s *client = hdl;
    if (!client) {
        return -1;
    }
    return MsgSendPulse(client->coid, -1, BREAKOUT_PULSE, 0);
}

void powermgr_str_register() {
    struct pm_ops_s ops = {0};
    ops.suspend  = powermgr_str_suspendCallback;
    ops.resume   = powermgr_str_resumeCallback;
    ops.prepare  = powermgr_str_prepareCallback;
    ops.complete = powermgr_str_completeCallback;

    if(EOK != pm_register("al_cluster_powermgr", PM_PRIO_LEVEL_0, &ops, 0, NULL, NULL)) {
        LOG_E("pm_register() failed.\n");
    }
}

int powermgr_str_replyAck(int32_t sta) {
    if(s_curStrSta != PM_STATE_MAX) {
        struct pm_ack_s ack = {.rc = EOK, .state = PM_STATE_MAX};
        //ack.rc = sta;
        ack.rc = EOK;
        ack.state = s_curStrSta;
        s_curStrSta = PM_STATE_MAX;
        LOG_I("powermgr_str_replyAck rc=%d, state=%d", ack.rc, ack.state);
        (void)devctl(s_pm_fd, DCMD_PM_ACK, &ack , sizeof (ack), NULL);
    }
    return 0;
}
