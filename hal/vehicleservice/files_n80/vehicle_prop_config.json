{"propConfig": [{"propId": 4139, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 200000000}], "osTarget": 3}, {"propId": 4141, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4148, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 4149, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4150, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4151, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4199, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4203, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4322, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65534}], "osTarget": 3}, {"propId": 4323, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4546, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 63}], "osTarget": 3}, {"propId": 4547, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4548, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4554, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4555, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4556, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4557, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4558, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4559, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4560, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4561, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4562, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4563, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4567, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4568, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 63}], "osTarget": 3}, {"propId": 4569, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 63}], "osTarget": 3}, {"propId": 4577, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4578, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4579, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 63}], "osTarget": 3}, {"propId": 4908, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4909, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4910, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4911, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4912, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4913, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4914, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 63}], "osTarget": 3}, {"propId": 4915, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 63}], "osTarget": 3}, {"propId": 4916, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 63}], "osTarget": 3}, {"propId": 4917, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 63}], "osTarget": 3}, {"propId": 4918, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4919, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4920, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4921, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4922, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4923, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4924, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4927, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4930, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5268, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5275, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5286, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 200000000}], "osTarget": 3}, {"propId": 5287, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5288, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5289, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}]}