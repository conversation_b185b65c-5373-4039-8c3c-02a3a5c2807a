// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: autolink.alc.signal.pb.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_autolink_2ealc_2esignal_2epb_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_autolink_2ealc_2esignal_2epb_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019005 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_autolink_2ealc_2esignal_2epb_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_autolink_2ealc_2esignal_2epb_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_autolink_2ealc_2esignal_2epb_2eproto;
PROTOBUF_NAMESPACE_OPEN
PROTOBUF_NAMESPACE_CLOSE
namespace autolink {
namespace alc {
namespace signal {
namespace pb {

enum SignalIdSoc : int {
  VCU_GEARSIGVLD_214 = 4096,
  VCU_GEARSIG_214 = 4097,
  ESP_VEHSPD_318 = 4098,
  PDU_TPMS_589 = 4099,
  VCU_DRVGMILG_504 = 4102,
  YRS_LGTACCE_246 = 4363,
  ECC_ACSTS_373 = 4367,
  ECC_WINDSPDSTS_373 = 4369,
  ECC_BACKROWAIROUTLMODSTS_373 = 4370,
  ECC_DRVRTSETSTS_373 = 4371,
  ECC_PASSTSETSTS_373 = 4372,
  ECC_AUTOSTS_373 = 4374,
  ECC_SYNCSTS_373 = 4375,
  ECC_CIRCSTS_373 = 4377,
  ECC_PARTICLECONCVLD_373 = 4378,
  ECC_OUTDT_373 = 4379,
  ECC_OUTDTVLD_373 = 4380,
  ECC_MAXFRNTDEFRST_373 = 4381,
  ECC_HEATMNGTSYSFLT_373 = 4382,
  ECC_HEATMNGTFCTLIM_373 = 4383,
  ECC_DRVRAIROUTLMOD_373 = 4384,
  ECC_PASSAIROUTLMOD_373 = 4385,
  ECC_EGYSAVEMODSTS_373 = 4386,
  ICC_AUTOPOWER_OFFSWITCHSTATUS_37A = 4387,
  EHU_PETSMODBTN_37A = 4388,
  EHU_LEOUTLUPDWNMOTACTVCMD_37A = 4390,
  EHU_LEOUTLLERIMOTACTVCMD_37A = 4391,
  EHU_MIDLEOUTLUPDWNMOTACTVCMD_37A = 4393,
  EHU_MIDLEOUTLLERIMOTACTVCMD_37A = 4394,
  EHU_MIDRIOUTLUPDWNMOTACTVCMD_37A = 4396,
  EHU_MIDRIOUTLLERIMOTACTVCMD_37A = 4397,
  EHU_RIOUTLUPDWNMOTACTVCMD_37A = 4399,
  EHU_RIOUTLLERIMOTACTVCMD_37A = 4400,
  ECC_LEOUTLUPDWNMOTACTVSTS_378 = 4402,
  ECC_LEOUTLLERIMOTACTVSTS_378 = 4403,
  ECC_MIDLEOUTLUPDWNMOTACTVSTS_378 = 4405,
  ECC_MIDLEOUTLLERIMOTACTVSTS_378 = 4406,
  ECC_MIDRIOUTLUPDWNMOTACTVSTS_378 = 4408,
  ECC_MIDRIOUTLLERIMOTACTVSTS_378 = 4409,
  ECC_RIOUTLUPDWNMOTACTVSTS_378 = 4411,
  ECC_RIOUTLLERIMOTACTVSTS_378 = 4412,
  MCU_F_CRTROTDIR_150 = 4419,
  MCU_F_ALRMLAMP_FS_150 = 4423,
  PEPS_PWRMOD_333 = 4426,
  PEPS_PWRMODVLD_333 = 4427,
  PEPS_POLLINGSTS_333 = 4428,
  PEPS_KEYINCARRMN_37B = 4434,
  PEPS_SHIFTTOPNSTRTREQ_37B = 4436,
  PEPS_STEPBRKTOSTRTREQ_37B = 4437,
  PEPS_NOFOUNDLEGALKEY_37B = 4438,
  PEPS_EMGYSTRTPROMT_37B = 4441,
  PEPS_VCUAUTHENTFAILPROMT_37B = 4443,
  PEPS_KEYLOPWRPROMT_37B = 4444,
  PEPS_KEYOUTDCARPROMT_37B = 4445,
  PEPS_TIOUTPWROFFRMN_380 = 4453,
  PWC_CHRGSTS_524 = 4454,
  PWC_PHNFORGETREMDSTS_524 = 4455,
  PWC_MODLESWTSTS_524 = 4456,
  EHU_DRVRSEATTRACKMANREQ_533 = 4478,
  EHU_DRVRHEIMANREQ_533 = 4480,
  EHU_DRVRSEATBACKMANREQ_533 = 4481,
  EHU_PASSSEATTRACKMANREQ_533 = 4482,
  EHU_PASSSEATBACKMANREQ_533 = 4483,
  EHU_LUMBARUPD_533 = 4484,
  EHU_LUMBARDWN_533 = 4485,
  EHU_LUMBARFWD_533 = 4486,
  EHU_LUMBARBACKW_533 = 4487,
  EHU_AUTOPASSSEATHEATENA_533 = 4489,
  EHU_PASSLUMBARUPD_533 = 4494,
  EHU_PASSLUMBARDWN_533 = 4495,
  EHU_PASSLUMBARFWD_533 = 4496,
  EHU_PASSLUMBARBACKW_533 = 4497,
  EHU_LRLOCKWINCMD_533 = 4498,
  EHU_RRLOCKWINCMD_533 = 4499,
  EHU_SETMAXPOSNCMD_PLG_52 = 4501,
  EHU_TRACTNCMD_52 = 4502,
  ICC_DRLSWT_52 = 4503,
  ICC_LOCKAUTOCLSSUNSSWT_52 = 4504,
  EHU_VEHACCOUNTLOGINUIDSTS_90 = 4506,
  EHU_VEHACCOUNTLOGINUID_90 = 4507,
  EHU_SEATLOCNMEMOPERCMD_90 = 4508,
  EHU_DRVRSEATUIDSUBPOSN_90 = 4509,
  EHU_PASSSEATLOCNMEMOPERCMD_90 = 4510,
  EHU_PASSSEATUIDSUBPOSN_90 = 4511,
  EHU_FADERSET_44 = 4522,
  EHU_LERIBALSET_44 = 4524,
  EHU_MIDFRQAUDIOSET_44 = 4526,
  EHU_LOFRQAUDIOSET_44 = 4528,
  EHU_HIFRQAUDIOSET_44 = 4530,
  EHU_SOUNDSWITCH_44 = 4536,
  EHU_ARCFOXSOUNDMODESELECT_44 = 4537,
  EHU_FOLWMEHOMETISET_46 = 4538,
  EHU_INTRLAMPTISET_46 = 4539,
  EHU_MAIVOLSET_46 = 4542,
  EHU_MUTECTRL_46 = 4543,
  EHU_IESSMODREQ_46 = 4545,
  EHU_VSCMODREQ_46 = 4546,
  EHU_HFTVOLSET_46 = 4547,
  EHU_NAVVOLSET_46 = 4550,
  EHU_RTVOLSET_46 = 4552,
  EHU_VOLINCREASEWITHSPEED_46 = 4558,
  EHU_RIFRNTWINCTRL_4E = 4560,
  EHU_LEREWINCTRL_4E = 4561,
  EHU_RIREWINCTRL_4E = 4562,
  EHU_LEFRNTWINCTRL_4E = 4563,
  EHU_POSNLAMPCTRL_4E = 4567,
  EHU_LOBEAMCTRL_4E = 4569,
  EHU_REDEFRSTOPENREQ_4E = 4575,
  EHU_SUNSHADECTRLREQ_4E = 4576,
  EHU_CENTRLOCKCTRL_4E = 4577,
  EHU_MIRRCMD_4E = 4578,
  EHU_REMIRRAUTOFOLDSET_4E = 4579,
  EHU_RAINCLSDSUNROOFSET_4E = 4581,
  EHU_ARMEDCLSDWINSET_4E = 4582,
  EHU_OFFUNLCKSET_4E = 4583,
  EHU_DOORUNLOCKSET_4E = 4584,
  EHU_SETATMLAMPBRI_336 = 4587,
  EHU_ATMLAMPOPENCMD_336 = 4591,
  EHU_ALCCSTMSWT_336 = 4593,
  EHU_HDCSWTSIG_336 = 4594,
  EHU_ESPSWTSIG_336 = 4595,
  EHU_SLCSWT_336 = 4598,
  EHU_DRVRSEATHEATGREQ_528 = 4602,
  EHU_DRVRSEATVENTNREQ_528 = 4603,
  EHU_SEATWELFCTENAREQ_528 = 4606,
  EHU_PASSSEATWELFCTENAREQ_528 = 4607,
  EHU_SEATHEATLVAUTOREDUCEREQ_528 = 4609,
  EHU_PASSSEATHEATGREQ_528 = 4610,
  EHU_PASSSEATVENTNREQ_528 = 4611,
  EHU_LRSEATHEATREQ_528 = 4612,
  EHU_RRSEATHEATREQ_528 = 4614,
  EHU_CRTLANGUAGE_529 = 4617,
  EHU_BRIADJ_HUD_529 = 4619,
  EHU_STEERWHLPHNKEYBACKLI_529 = 4620,
  EHU_BACKGNDBRILVL_529 = 4621,
  EHU_BRIADJVAL_HUD_529 = 4624,
  EHU_OPENCMD_HUD_529 = 4625,
  EHU_SNOWMODSWT_HUD_529 = 4626,
  EHU_HEIADJ_HUD_529 = 4627,
  EHU_WIPRSRVPOSN_529 = 4630,
  EHU_PULLMODREQ_529 = 4635,
  EHU_USRPWROFFFB_529 = 4636,
  EHU_STEERWHLHEATGSW_529 = 4637,
  EHU_DRVRTSET_530 = 4640,
  EHU_PASSTSET_530 = 4641,
  EHU_ECCAUTOREQ_530 = 4643,
  EHU_DRVRSYNCREQ_530 = 4644,
  EHU_ACSWTREQ_530 = 4645,
  EHU_AIRVOLSET_530 = 4646,
  EHU_ECCINTEXTCIRCREQ_530 = 4647,
  EHU_AIRCLNSWTREQ_530 = 4648,
  EHU_MAXFRNTDEFRSTSET_530 = 4649,
  EHU_VSPCTRLCMD_530 = 4650,
  EHU_NAVROADTYP_530 = 4652,
  EHU_ECCEGYSAVEMODREQ_530 = 4656,
  EHU_BLOWWINBTN_530 = 4657,
  EHU_DRVGMILGDISPTYPSET_534 = 4659,
  EHU_UVCREQ_534 = 4660,
  EHU_DRVRBLOWFACEBTN_534 = 4663,
  EHU_PASSBLOWFACETBTN_534 = 4664,
  EHU_DRVRBLOWFOOTBTN_534 = 4665,
  EHU_PASSBLOWFOOTBTN_534 = 4666,
  EHU_BACKROWAIROUTLMODREQ_534 = 4667,
  EHU_ECCSYSSWTCMD_534 = 4669,
  EHU_DRVRBLOWMODREQ_534 = 4670,
  EHU_PASSBLOWMODREQ_534 = 4671,
  EHU_FLSEATMASMODCMD_534 = 4672,
  EHU_FLSEATMASGRADECMD_534 = 4673,
  EHU_FRSEATMASMODCMD_534 = 4674,
  EHU_FRSEATMASGRADECMD_534 = 4675,
  EHU_USRSETCHRGGUNANTITHFT_59C = 4683,
  EHU_CHRGDCHACTRLCMD_59C = 4692,
  EHU_SETACCHRGGUNUNLOCKSWT_59C = 4693,
  EHU_OPENCLOSECHRGPORT1REQ_59C = 4695,
  ICC_AUTOCOLORSWT_59C = 4697,
  ICC_EMISSTESTMODE_59C = 4699,
  ICC_IDLEMODE_59C = 4700,
  ICC_ENRGMOD_59C = 4702,
  ICC_REFSWITCHSTS_59C = 4703,
  ICC_CHRGMODUSRSET_59D = 4709,
  ICC_TURNLAMPMODSWT_59D = 4712,
  ICC_CONTRASTCOLORSWT4_59D = 4713,
  ICC_CONTRASTCOLORSWT5_59D = 4714,
  ICC_CONTRASTCOLORSWT6_59D = 4715,
  EHU_USRSETCHRGRMNMILG_610 = 4734,
  EHU_KL15KEEPREQ_610 = 4735,
  EHU_EGYCNSECLRFLG_610 = 4736,
  FAILRFUSAMPLECIRC_610 = 4739,
  EHU_POLLINGFCTOPENSTS_610 = 4740,
  EHU_CHRGINSULFCTREQ_610 = 4742,
  EHU_USRSETDISCHRGRMNMILG_610 = 4747,
  EHU_SETCHRGENDSOC_610 = 4748,
  ICC_ROADLMTSPD_63D = 4753,
  ICC_TIMETODEST_63D = 4754,
  ICC_MILETODEST_63D = 4755,
  VCU_RDYLAMP_214 = 4772,
  VCU_PWRBATTHVCNCTSTS_214 = 4773,
  VCU_DRVMODSHIFTMISOPER_214 = 4780,
  VCU_EXTREMEEGYSAVESWTENAFLG_358 = 4783,
  VCU_PULLMODENASIG_358 = 4785,
  VCU_PULLMODSIG_358 = 4786,
  VCU_DRVPWRLIMSTS_358 = 4789,
  VCU_EGYRECOVPWRLIMSTS_358 = 4790,
  VCU_EXTREMEEGYSAVEOPENSIG_358 = 4792,
  VCU_ONEPEDALKEEPDISPLAY_AB_AS_358 = 4794,
  VCU_VEHACSYCNSEEGY_579 = 4808,
  VCU_EGYRECOVEGY_579 = 4811,
  VCU_LONGTIHLTHSTOREPUSHINFO_605 = 4829,
  VCU_CHRGSTSTXT_605 = 4830,
  BMS_CHRGFLTPROMT_330 = 4833,
  BMS_PWRBATTRMNGCPSOC_330 = 4839,
  BMS_CELLMINTALRM_330 = 4840,
  BMS_PWRBATTTHERMRUNAWAYALRM_330 = 4841,
  ESP_WARNINGON_261 = 4849,
  ESP_BRKFLDALRM_261 = 4851,
  ESP_AVLINDCN_CST_261 = 4852,
  ESP_CTRLSTS_CST_261 = 4853,
  ESP_SYSSTS_EPB_268 = 4856,
  ESP_FLTINDCN_EPB_268 = 4857,
  ESP_ACTVNDCN_EPB_268 = 4858,
  EPB_WARNMSG01_268 = 4859,
  EPB_WARNMSG02_268 = 4860,
  EPB_WARNMSG04_268 = 4861,
  ESP_BRKPEDLSTS_318 = 4870,
  ESP_VEHSPDVLD_318 = 4871,
  ESP_SYSACTV_318 = 4872,
  ESP_LAMPSWTOFFINDCN_318 = 4873,
  ESP_FLTINDCN_EBD_318 = 4874,
  ESP_FLTINDCN_ABS_318 = 4875,
  ESP_FLTINDCN_TCS_318 = 4878,
  ESP_CTRLSTS_HDC_318 = 4879,
  ESP_AVLINDCN_HDC_318 = 4880,
  EPS_STEERWHLAGSIG_1C2 = 4883,
  EPS_STEERWHLAGSIGVLD_1C2 = 4884,
  BCM_WIPRINSRVPOSN_335 = 4888,
  BCM_INTLAMPTISETSTS_335 = 4889,
  BCM_WATERPOSNSNSRSWTSTS_335 = 4890,
  BCM_EXTLAMPSWTSTS_335 = 4894,
  BCM_RAINCLSSUNROOFSETSTS_335 = 4900,
  BCM_MIRRLOCKAUTOSETSTS_335 = 4901,
  BCM_DANGERALRMLAMPSWTSTS_335 = 4903,
  BCM_REDEFRSTHEATGCMD_335 = 4904,
  BCM_RVSLAMPOUTPCMD_335 = 4905,
  BCM_LETRUNLAMPOUTPCMD_335 = 4907,
  BCM_RITRUNLAMPOUTPCMD_335 = 4908,
  BCM_HIBEAMOUTPCMD_335 = 4911,
  BCM_LOBEAMOUTPCMD_335 = 4912,
  BCM_POSNLAMPOUTPCMD_335 = 4913,
  BCM_BRKLAMPOUTPCMD_335 = 4914,
  BCM_REFOGLAMPOUTPCMD_335 = 4915,
  BCM_FRNTWIPRSPD_335 = 4916,
  BCM_VEHAMBBRI_335 = 4918,
  BCM_FRNTHOODLIDSTS_343 = 4921,
  BCM_SUNROOFANTIPINCHSTS_343 = 4922,
  BCM_FRNTLEDOORLOCKSTS_343 = 4923,
  BCM_TRRELSSWTSTS_343 = 4924,
  BCM_LOCKALLDOORCMD_343 = 4925,
  BCM_LEFRNTDOORSTS_343 = 4926,
  BCM_RIFRNTDOORSTS_343 = 4927,
  BCM_TRSTS_343 = 4928,
  BCM_ANTITHFTSTS_343 = 4929,
  BCM_CENLOCKSWTSTS_343 = 4930,
  BCM_DOORUNLOCKSETFB_343 = 4931,
  BCM_RIREDOORSTS_343 = 4932,
  BCM_LEREDOORSTS_343 = 4933,
  BCM_LEFRNTWINSTS_343 = 4934,
  BCM_RIFRNTWINSTS_343 = 4935,
  BCM_LEREWINSTS_343 = 4936,
  BCM_RIREWINST_343 = 4937,
  BCM_FOLWMESETSTSFB_343 = 4938,
  BCM_DRVRBOORUNLCKOUTPCMD_343 = 4939,
  BCM_PASSDOORUNLCKOUTPCMD_343 = 4940,
  BCM_LEDRLOUTPCMD_343 = 4941,
  BCM_RIDRLOUTPCMD_343 = 4942,
  BCM_ARMEDCLSWINSETSTS_343 = 4943,
  BCM_OFFAUTOUNLCKSETSTS_343 = 4944,
  BCM_SUNROOFPOSNINFO_343 = 4945,
  BCM_SUNROOFOPENAR_343 = 4946,
  BCM_SUNROOFRUNNGSTS_343 = 4947,
  BCM_MIRRCMD_343 = 4948,
  BCM_APPLIANCECLSLVL_51E = 4949,
  DSMC_DRVRSEATTRACKSWTSTS_4F1 = 4952,
  DSMC_DRVRSEATHEIADJSWTSTS_4F1 = 4953,
  DSMC_DRVRSEATBACKADJSWTSTS_4F1 = 4954,
  DSMC_DRVRSEATWELFCTSETFB_4F1 = 4957,
  DSMC_REMIRRAUTODWNFLIPFB_4F1 = 4958,
  DSMC_RIMIRRRXDIECPOSN_4F3 = 4960,
  DSMC_RIMIRRRYDIRCPOSN_4F3 = 4961,
  DSMC_LEMIRRRXPOSN_4F3 = 4962,
  DSMC_LEMIRRRYPOSN_4F3 = 4963,
  DSMC_DRVRSEATTRACKPOSN_4F5 = 4968,
  DSMC_DRVRSEATHEIPOSN_4F5 = 4969,
  DSMC_DRVRSEATBACKPOSN_4F5 = 4970,
  DSMC_DRVRSEATMEMRECALLFB_62 = 4972,
  DSMC_DRVRSEATMEMDATAUPDFB_62 = 4973,
  DSMC_DRVRSEATHEATGSTS_518 = 4975,
  DSMC_DRVRSEATVENTNSTS_518 = 4976,
  DSMC_DRVRSEATTRACKADJSTS_518 = 4977,
  DSMC_DRVRSEATHEIADJSTS_518 = 4978,
  DSMC_DRVRSEATBACKADJSTS_518 = 4979,
  DSMC_LUMBARUPDSTS_518 = 4981,
  DSMC_LUMBARDWNSTS_518 = 4982,
  DSMC_LUMBARFWDSTS_518 = 4983,
  DSMC_LUMBARBACKWSTS_518 = 4984,
  DSMC_LRSEATHEATSTS_518 = 4986,
  DSMC_SEATHEATAUTODWNENASTS_518 = 4987,
  DSMC_REMIRRLERIFB_518 = 4988,
  DSMC_MASFL_SEATMASMOD_518 = 4990,
  DSMC_MASFL_SEATMASGRADESTS_518 = 4991,
  PLG_USRSETTRMAXHEIRESFB_64 = 4993,
  PLG_USRSETTRMAXHEI_471 = 4994,
  PLG_LETRPOSN_471 = 4995,
  PLG_SOUNDREMDNGREQ_EHU_471 = 4996,
  PLG_SYSFLTINDCN_471 = 4998,
  PLG_TRSWTSTSINDCN_471 = 4999,
  PLG_OPERMOD_471 = 5001,
  PLG_ANTIPINCHSTS_471 = 5002,
  PAS_STS_FPAS_574 = 5006,
  PAS_STS_RPAS_574 = 5008,
  PAS_SOUNDINDCN_F_576 = 5027,
  PAS_SOUNDINDCN_R_576 = 5028,
  EHU_INTEGTCRSSWT_526 = 5035,
  EHU_HMASET_526 = 5045,
  EHU_LIFESIGNMONITORSWT_4DF = 5058,
  ICM_TOTMILGVLD_ODO_531 = 5071,
  ICC_REMSENTRYMODSTS_531 = 5072,
  ICM_TOTMILG_ODO_531 = 5073,
  ICM_DISPVEHSPD_531 = 5074,
  ICM_DISPVEHSPDUNIT_531 = 5075,
  SDM_AIRBAGSYSALRMLAMPSTS_319 = 5082,
  SDM_SECUBLTALRMSTS_RL_319 = 5083,
  SDM_SECUBLTALRMSTS_RM_319 = 5084,
  SDM_SECUBLTALRMSTS_RR_319 = 5085,
  SDM_DRVERSECUBLTALRMSTS_319 = 5086,
  SDM_PASSSEATBLTBUCDSTS_319 = 5087,
  SDM_CLLSNSIG_319 = 5088,
  SDM_PASSSEATOCCSTS_319 = 5089,
  CIM_FRNTWIPRSWTSTS_310 = 5096,
  CIM_REWIPRSWTSTS_310 = 5098,
  VCU_ACCHRGELECTCLOCKSTSFBSIG_554 = 5099,
  VCU_CHRGDISCHRGCRTDISP_52C = 5104,
  VCU_CHRGDCHAPWRDISP_52C = 5105,
  VCU_ELECTCLOCKFLTPROMT_52D = 5106,
  BMS_CHRGOPRTGUIDEPROMT_51B = 5108,
  BMS_CHRGRLTDSTSPROMT_51B = 5109,
  BMS_CHRGDCHASTOPREASONPROMT_51B = 5110,
  ICC_NUMTRFCLMP_61F = 5160,
  ICC_NXTCHGTYPE_61F = 5163,
  ICC_MAPSTS_61F = 5164,
  ICC_FULLROADTRFCINFO_SEG_602 = 5170,
  ICC_FULLROADTRFCINFO_NUM_602 = 5171,
  ICC_FULLROADTRFCINFO_LEN_602 = 5172,
  ICC_FULLROADTRFCINFO_TI_602 = 5173,
  ICC_FULLROADTRFCINFO_STS_602 = 5174,
  ICC_TURNICON_620 = 5179,
  ICC_CRTROADTRFCINFO_NUM_620 = 5180,
  ICC_CRTROADTRFCINFO_MILG_620 = 5181,
  ICC_CRTROADTRFCINFO_TI_620 = 5182,
  ICC_CRTROADTRFCINFO_STS_620 = 5183,
  ICC_ESTIMTSOCPERC_60E = 5224,
  ICC_ESTIMTREMSOC_60E = 5225,
  ICC_RNG2NXTCHGTIME_64F = 5230,
  ICC_RNG2NXTCHGMILE_64F = 5231,
  ICC_TOTLMILERNG_61D = 5264,
  ICC_TOTLTIMERNG_61D = 5265,
  SLC_CRTCOLORR_337 = 5270,
  SLC_CRTCOLORG_337 = 5271,
  SLC_CRTCOLORB_337 = 5272,
  SLC_CRTBRI_337 = 5273,
  SLC_ALCCSTMSTS_337 = 5275,
  SLC_COURTESYFCTMODSTS_3EC = 5278,
  SLC_MUSICRHYTHMSTS_3EC = 5279,
  SLC_BRIBREATHSTS_3EC = 5280,
  SLC_VEHSPDRHYTHMSTS_3EC = 5282,
  SLC_DRVMDSTS_3EC = 5283,
  SLC_ACMODSTS_3EC = 5284,
  SLC_DOWSTS_3EC = 5285,
  SLC_BRI_3EC = 5286,
  SLC_COLOURBREATHSTS2_3EC = 5287,
  SLC_ALCSPCHSTS_3EC = 5289,
  SLC_ALCMOBCHAREMDSTS_3EC = 5290,
  SLC_LOGOLIGSTS_3EC = 5291,
  SLC_CONTRASTCOLORSTS1_3EC = 5292,
  SLC_CONTRASTCOLORSTS2_3EC = 5293,
  SLC_CONTRASTCOLORSTS3_3EC = 5294,
  ICC_LIGHTCRLSTSFB_320 = 5343,
  ICC_REFOGLAMPOUTPCMD_320 = 5344,
  ICM_MILGOFFS_ODO_532 = 5348,
  ICM_DISPTOTMILG_ODO_532 = 5349,
  ICM_MILGDATAVLD_ODO_532 = 5350,
  ICM_MILGRSTCNTR_ODO_532 = 5351,
  ICC_CTRL_CMD_532 = 5352,
  ICC_OPENPERCCMD_532 = 5353,
  EHU_RESVACCHRGSTRTTI_HR_614 = 5417,
  EHU_RESVACCHRGSTRTTI_MINS_614 = 5418,
  EHU_RESVACCHRGSTRTSET_614 = 5419,
  EHU_MONRESVACCHRGREPSTRTSET_614 = 5420,
  EHU_RESVACCHRGENDTI_HR_614 = 5427,
  EHU_RESVACCHRGENDTI_MINS_614 = 5428,
  EHU_RESVACCHRGENDSET_614 = 5429,
  EHU_MONRESVACCHRGREPENDSET_614 = 5430,
  VCU_REFRSHMODRESTRNTFCTCMD_357 = 5443,
  RESETFLG_TRIPFROMLASTCHARGE_357 = 5444,
  VCU_REMPWRBATTHEATGENDCMD_511 = 5446,
  VCU_REMBATTHEATGFAILREASON_511 = 5447,
  VCU_PETSMODFOBDREASON_511 = 5448,
  VCU_PETSMODWARN_511 = 5449,
  VCU_VEHCRTCHRGENDSOC_511 = 5451,
  ECC_INSDT_582 = 5453,
  ECC_PARTICLECONC_582 = 5454,
  VCU_ALRMLAMP_FS_105 = 5463,
  VCU_CRUISEFLTTIP_105 = 5464,
  VCU_DRVMODEXTNSIG_105 = 5465,
  VCU_CRUISESTS_105 = 5466,
  VCU_CRUISEAIMSPD_105 = 5467,
  VCU_DRVPWRLIMPERC_503 = 5468,
  VCU_EGYFBPWRLIMPERC_503 = 5469,
  VCU_MEMCHRGRMNMILGTHD_503 = 5470,
  VCU_USRHMIPROMT_503 = 5476,
  VCU_RESVCHRGSTSDISP_503 = 5477,
  VCU_INSNTEGYCNSEHR_503 = 5478,
  VCU_MONRPWRBATTTHERMRUNAWAYALRM_504 = 5479,
  VCU_LNCHCTRLTRIGRMN_504 = 5480,
  VCU_SHIFTOPERRMN_504 = 5481,
  VCU_MCUFSYSOVERTDISP_504 = 5484,
  VCU_MCURSYSOVERTDISP_504 = 5485,
  VCU_VEHSYSFLTLAMP_504 = 5486,
  VCU_RMNUSRCLSECCDISPCMD_504 = 5487,
  VCU_RMNUSRECCFCTLMTDISPCMD_504 = 5488,
  VCU_LNCHCTRLMODDIRMN_504 = 5489,
  VCU_DRVRNGVLD_504 = 5492,
  VCU_ACCUEEGYCNSE_505 = 5496,
  VCU_EGYRECOVFORBNFLG_505 = 5497,
  VCU_DRVPWRLIMINDCRLAMP_505 = 5498,
  VCU_BATTFLTINDCN_505 = 5501,
  VCU_CHRGINDCRLAMP_505 = 5502,
  VCU_CHRGDCHAGUNCNCTNINDCRLAMP_505 = 5504,
  VCU_CHRGGUNSTRT_505 = 5506,
  VCU_SOCLOCHRGRMN_50C = 5510,
  VCU_DRVGMILGDISPTYPCFM_50C = 5511,
  BMS_ALRMLAMP_FS_215 = 5518,
  BMS_CHRGCRATEDISP_240 = 5522,
  BMS_FBREMHEATGOPERSTS_363 = 5528,
  BMS_VEHEXTDCHASTS_363 = 5529,
  BMS_CHRGINSULFCTOPENSTS_363 = 5532,
  APA_FMEBSTS_2A0 = 5557,
  APA_RMEBSTS_2A0 = 5559,
  APA_STS_FPAS_558 = 5584,
  APA_STS_RPAS_558 = 5587,
  ESP_FLTINDCN_HHC_332 = 5667,
  ESP_ACTVINDCN_AVH_332 = 5668,
  ESP_SWTINDCN_AVH_332 = 5669,
  ESP_FLTINDCN_AVH_332 = 5670,
  EPS_FLTINDCN_470 = 5674,
  BCM_RLS_LIGHTSWTREQ_321 = 5675,
  BCM_SWH_STEERWHLHEATGSTS_321 = 5676,
  BCM_TIMEOUTPOWER_OFFFEEDBACK_321 = 5677,
  BCM_LIGHTREQREASON_RLS_321 = 5683,
  BCM_OFFUNLCKSETSTSFB_321 = 5684,
  BCM_SUNSHADERUNNGSTS_345 = 5688,
  BCM_SUNSHADEPOSNINFO_345 = 5689,
  BCM_LEPOSNLAMPFLT_539 = 5690,
  BCM_RIPOSNLAMPFLT_539 = 5691,
  BCM_LELOBEAMFLT_539 = 5692,
  BCM_RILOBEAMFLT_539 = 5693,
  BCM_LEHIBEAMFLT_539 = 5694,
  BCM_RIHIBEAMFLT_539 = 5695,
  BCM_LEDRLFLT_539 = 5696,
  BCM_RIDRLFLT_539 = 5697,
  BCM_LEFRNTFOGLAMPFLT_539 = 5698,
  BCM_RIFRNTFOGLAMPFLT_539 = 5699,
  BCM_REFOGLAMPFLT_539 = 5700,
  BCM_LOBRKLAMPFLT_539 = 5701,
  BCM_HIBRKLAMPFLT_539 = 5702,
  BCM_RVSLAMPFLT_539 = 5703,
  BCM_LETURNLAMPFLT_539 = 5704,
  BCM_RITURNLAMPFLT_539 = 5705,
  BCM_LICPLATELAMPFLT_539 = 5706,
  BCM_WINLOCKSWINPUT_539 = 5707,
  BCM_MILGDATAVLD_ODO_641 = 5708,
  BCM_MILGRSTCNTR_ODO_641 = 5709,
  BCM_TOTMILG_ODO_641 = 5710,
  BCM_MILGOFFS_ODO_641 = 5711,
  ADAS_HANDSOFFTAKEOVERREQ_32C = 5726,
  ADAS_AUDIOWARN_32C = 5734,
  MPC_OVERSPDWARN_SLA_32E = 5743,
  MPC_SPDLIMUNIT_SLA_32E = 5749,
  MPC_SPDLIM_SLA_32E = 5750,
  ADAS_WARN_FCW_33C = 5751,
  ADAS_STS_FCW_33C = 5753,
  ADAS_STS_AEB_33C = 5754,
  ADAS_TAKEOVERREQ_ACC_347 = 5773,
  ADAS_SPDLIM_ASL_347 = 5777,
  ADAS_SPDLIMSTS_ASL_347 = 5778,
  ADAS_ACC_OPERTXT_347 = 5779,
  MPC_STS_HMA_334 = 5802,
  MPC_WARNSIGN_334 = 5805,
  MPC_FOBDSIGN_334 = 5806,
  MPC_OVERTAKESIGN_334 = 5807,
  MPC_FRNTCAMBLI_334 = 5808,
  MPC_HMASETFB_334 = 5809,
  MPC_FRNTCAMFLT_334 = 5810,
  ADAS_LELINECOLOR_340 = 5818,
  ADAS_RILINECOLOR_340 = 5819,
  ADAS_INTEGTCRSSWTFB_340 = 5825,
  ADAS_FLTINDCR_340 = 5828,
  ADAS_INTECNFLTTXT_340 = 5830,
  CMRR_RL_STS_LCA_338 = 5833,
  CMRR_RL_LEWARN_DOW_338 = 5835,
  CMRR_RL_LEWARN_RCTA_338 = 5840,
  MPC_CMRR_FR_RIWARN_FCTA_243 = 5854,
  MPC_CMRR_FL_LEWARN_FCTA_243 = 5858,
  TBOX_RESVACCHRGOPENSTS_4F4 = 5887,
  TBOX_RESVACCHRGSTRTTI_HR_62E = 5892,
  TBOX_RESVACCHRGSTRTTI_MINS_62E = 5894,
  TBOX_MONRESVACCHRGREPSTRTSET_62E = 5895,
  TBOX_RESVACCHRGENDTI_HR_62E = 5902,
  TBOX_RESVACCHRGENDTI_MINS_62E = 5903,
  TBOX_RESVACCHRGENDSET_62E = 5904,
  TBOX_MONRESVACCHRGREPENDSET_62E = 5905,
  HUD_SWT_562 = 5912,
  HUD_CRTSYSSTS_562 = 5913,
  HUD_ILLADJ_562 = 5914,
  HUD_HEIADJ_562 = 5915,
  HUD_MODSWT_562 = 5916,
  HUD_SNOWMODSWTSTS_562 = 5917,
  HUD_CRTLANGUAGE_562 = 5918,
  TBOX_CRTTI_DAY_62F = 5919,
  TBOX_CRTTI_HR_62F = 5920,
  TBOX_CRTTI_MINS_62F = 5921,
  TBOX_CRTTI_YR_62F = 5922,
  TBOX_CRTTI_MTH_62F = 5923,
  TBOX_CRTTI_SEC_62F = 5924,
  MCU_R_ALRMLAMP_FS_151 = 5957,
  ESM_SPOILERMODSTS_30C = 5966,
  ESM_SPOILERMOVEMENTSTSFB_30C = 5968,
  ESM_SPOILERCRLSTSFB_30C = 5969,
  ESM_SPOILERWELCOMEFUNSETSTS_30C = 5970,
  ICC_EPSMODREQ_3C6 = 5981,
  ICC_BRAKEEGYRECOVINTENREQ_3C6 = 5982,
  ICC_AVHSWTSIG_3C6 = 5983,
  ICC_DRVGMODREQ_3C6 = 5984,
  ICC_AFONSTS_3C6 = 5985,
  ICC_AFCHANNELSET_3C6 = 5986,
  ICC_AFCONCENTRATION_3C6 = 5987,
  ICC_WASHCARSWT_3C6 = 5988,
  ICC_SPOILERMODSWT_3C6 = 5989,
  ICC_SPOILERWELCOMEFUNSWT_3C6 = 5990,
  ICC_WORMSTS_3C6 = 5991,
  ICC_ACCEMODREQ_3C6 = 5992,
  ICC_PARKCHARGEST_3C6 = 5994,
  AMP_BALSETSTS_49 = 6001,
  AMP_FADERSETSTS_49 = 6004,
  AMP_IESSMODSTS_49 = 6006,
  AMP_LOFRQAUDIOSETSTS_49 = 6007,
  AMP_MIDFRQAUDIOSETSTS_49 = 6009,
  AMP_HIFRQAUDIOSETSTS_49 = 6011,
  AMP_HFTVOLSETSTS_49 = 6013,
  AMP_NAVVOLSETSTS_49 = 6015,
  AMP_MAIVOLSETSTS_49 = 6016,
  ICC_LBADJUSTSET_3A8 = 6024,
  SLC_LBADJUSTSTS_3A7 = 6036,
  SLC_CONTRASTCOLORSTS6_3A7 = 6037,
  SLC_CONTRASTCOLORSTS4_3A7 = 6038,
  SLC_CONTRASTCOLORSTS_3A7 = 6039,
  SLC_AUTOCOLORSTS1_3A7 = 6043,
  ICC_SCRNBRILESET_327 = 6044,
  ICC_SCRNBRIAUTOSET_327 = 6045,
  ICC_HUMAISPCHSTS_327 = 6046,
  ICC_MUSICFRQ1_31B = 6047,
  ICC_MUSICFRQ2_31B = 6048,
  ICC_MUSICFRQ3_31B = 6049,
  ICC_MUSICFRQ4_31B = 6050,
  ICC_MUSICFRQ5_31B = 6051,
  ICC_MUSICFRQ6_31B = 6052,
  ICC_MUSICFRQ7_31B = 6053,
  ICC_MUSICFRQ8_31B = 6054,
  ICC_ALCBRIBREAMODSWT_3ED = 6056,
  ICC_ALCCLRBREAMODSWT2_3ED = 6058,
  ICC_ALCMUSICRHYMODSWT_3ED = 6059,
  ICC_ALCSPDRHYMODSWT_3ED = 6061,
  ICC_ALCSPCHSWT_3ED = 6062,
  ICC_ALCACMODSWT_3ED = 6063,
  ICC_ALCDOWMODSWT_3ED = 6064,
  ICC_ALCDRVMODRHYSWT_3ED = 6065,
  ICC_ALCMOBCHAREMDMODSWT_3ED = 6066,
  ICC_ALCWELMODSWT_3ED = 6067,
  ICC_VEHMDMESTS_3ED = 6068,
  ICC_APPLIANCECLSLVL_3ED = 6069,
  ICC_SCRNBRILESET_DUALAREA_3ED = 6070,
  ICC_AIRVETN_AL_SWT_3ED = 6071,
  ICC_ALCDUALAREAMODSWT_3ED = 6072,
  DSMC_PASSEATTRACKPOSN_3AA = 6080,
  DSMC_PASSEATBACKPOSN_3AA = 6082,
  DSMC_PASSSEATMEMRECALLFB_66 = 6084,
  DSMC_PASSSEATMEMDATAUPDFB_66 = 6085,
  DSMC_PASSSEATTRACKSWTSTS_328 = 6086,
  DSMC_PASSSEATBACKSWTSTS_328 = 6087,
  DSMC_PASSSEATHEATGSTS_512 = 6091,
  DSMC_PASSSEATVENTNSTS_512 = 6092,
  DSMC_PASSSEATTRACKADJSTS_512 = 6093,
  DSMC_PASSSEATBACKADJSTS_512 = 6094,
  DSMC_RRSEATHEATSTS_512 = 6095,
  DSMC_PASSLUMBARUPDSTS_512 = 6097,
  DSMC_PASSLUMBARDWNSTS_512 = 6098,
  DSMC_PASSLUMBARFWDSTS_512 = 6099,
  DSMC_PASSLUMBARBACKWSTS_512 = 6100,
  DSMC_SECROWSEATWELFCTSETFB_512 = 6103,
  DSMC_MASFR_SEATMASMOD_512 = 6105,
  DSMC_MASFR_SEATMASGRADESTS_512 = 6106,
  VCU_CHRGGUNANTITHFTOPENSTS_49C = 6116,
  VCU_EHUCHRGDCHAREQ_49C = 6118,
  VCU_CHRGDCHABTNREQ_49C = 6119,
  BMS_CHRGSTSDISP_49D = 6120,
  EPS_MODSTS_475 = 6121,
  BMS_BATTLOWTEMPIND_3C3 = 6122,
  BMS_RMNGCHRGTIDISPLY_29F = 6123,
  EHU_DRVRSEATTRACKPERCENTREQ_68 = 6124,
  EHU_DRVRSEATTILTPERCENTREQ_68 = 6125,
  EHU_DRVRHEIPERCENTREQ_68 = 6126,
  EHU_DRVRSEATBACKPERCENTREQ_68 = 6127,
  EHU_PASSSEATTRACKPERCENTREQ_68 = 6128,
  EHU_PASSSEATTILTPERCENTREQ_68 = 6129,
  EHU_PASSHEIPERCENTREQ_68 = 6130,
  EHU_PASSSEATBACKPERCENTREQ_68 = 6131,
  EHU_MIRRADJUP_3AC = 6132,
  EHU_MIRRADJDOWN_3AC = 6133,
  EHU_MIRRADJLEF_3AC = 6134,
  EHU_MIRRADJRI_3AC = 6135,
  EHU_LEREMIRRADJCMD_3AC = 6136,
  EHU_DRVRMIRRTURNDWNALLWD_3AC = 6137,
  ICC_EXHIBCARMODNOTICEFLAG_3AC = 6138,
  VCU_DRVMODSIG_102 = 6141,
  VCU_ACLRTIREQ_102 = 6143,
  VCU_SPORTMODACCTI_3A2 = 6144,
  VCU_COMFORTMODACCTI_3A2 = 6145,
  VCU_ECOMODACCTI_3A2 = 6146,
  VCU_ONEPEDALMODACCTI_3A2 = 6147,
  VCU_PERSONALMODACCTI_3A2 = 6148,
  VCU_DRVSTYLEFACTOR_3A2 = 6149,
  VCU_DRVSTYLE_3A2 = 6150,
  VCU_BRKEGYRECOVINTENSTS_3E8 = 6152,
  VCU_WORMSTS_3E8 = 6153,
  VCU_ACLRMODSTS_3E8 = 6154,
  VCU_CHRGPORTENAFLG_3AB = 6155,
  VCU_OPENCLSFLTINFODISP_3AB = 6156,
  VCU_VEHCURDISCHRGENDMILE_3AB = 6157,
  VCU_CHRGPORTDOORPOSST_3AB = 6158,
  ICM_PHNMSGSTS_587 = 6160,
  ICM_PHNMSGCALLINGTIMEH_587 = 6161,
  ICM_PHNMSGCALLINGTIMEM_587 = 6162,
  ICM_PHNMSGCALLINGTIMES_587 = 6163,
  ICC_MODSWT_HUD_3B3 = 6167,
  ICC_CMD_VOICECONTROLHUD_3B3 = 6168,
  ICC_TRIPARESETFLG_3B3 = 6170,
  ICC_TRIPBRESETFLG_3B3 = 6171,
  BCM_EEMINFOREQQUIESCENTCRT_3A9 = 6212,
  BCM_EEMFIMINSAVELEMOD_3A9 = 6213,
  BCM_VEVMMENUMVEHMDME_3A9 = 6214,
  VCU_CARWASHMODENA_50B = 6215,
  VCU_CARWASHMODSTS_50B = 6216,
  MFS_INCFOLWDST_514 = 6217,
  MFS_DECFOLWDST_514 = 6218,
  MFS_CUSTBTN_514 = 6219,
  MFS_PARKAID_514 = 6220,
  MFS_LEROLLUP_514 = 6221,
  MFS_LEROLLDWN_514 = 6222,
  MFS_LEROLLPRESS_514 = 6223,
  MFS_SRCSWTBTN_514 = 6224,
  MFS_VOICERCTCNBTN_514 = 6225,
  MFS_PREVSONGTUNESIG_514 = 6226,
  MFS_NEXTSONGTUNESIG_514 = 6227,
  MFS_RIROLLUP_514 = 6228,
  MFS_RIROLLDWN_514 = 6229,
  MFS_RIROLLPRESS_514 = 6230,
  MFS_STEERWHLHEATGINDCRLAMPSTS_514 = 6231,
  EUM_LECHILDLOCKSTS_309 = 6244,
  EUM_RICHILDLOCKSTS_309 = 6245,
  ECC_AFU_SWSTS_4FC = 6250,
  ECC_AFU_CHANNELSET_4FC = 6251,
  ECC_AFU_CONCENTRATION_4FC = 6252,
  ECC_AFU_CHANNELCHGSTS_4FC = 6254,
  ECC_AFU_CH1STS_4FC = 6255,
  ECC_AFU_CH2STS_4FC = 6256,
  ECC_AFU_CH3STS_4FC = 6257,
  ECC_AFU_CH1LEVSTS_4FC = 6258,
  ECC_AFU_CH2LEVSTS_4FC = 6259,
  ECC_AFU_CH3LEVSTS_4FC = 6260,
  ECC_AFU_CH1EXPIRATIONREMINDER_4FC = 6261,
  ECC_AFU_CH2EXPIRATIONREMINDER_4FC = 6262,
  ECC_AFU_CH3EXPIRATIONREMINDER_4FC = 6263,
  ICC_CSTSWTSIG_51A = 6267,
  ICC_PWROFFREQ_51A = 6273,
  ICC_PREHEATREQ_51A = 6274,
  VCU_SOKTSPLYINTERACTIVESTS_4DC = 6275,
  VCU_SOKTFUNOPERPROMT_EHU_4DC = 6277,
  VCU_EXHIBCARMODSIG_219 = 6280,
  ICC_SETACCHRGCURTLIMIT_37C = 6281,
  EHU_CTRLSTS_PWC_4E2 = 6293,
  BCM_BACKGNDBRILVL_3A3 = 6302,
  VCU_PETSMODREQFLG_3EE = 6310,
  VCU_PWRANTITHEFT_3EE = 6311,
  ICC_LOCKSOUNDPROMPTSWT_91 = 6313,
  ICC_LFWINCRL_91 = 6314,
  ICC_RFWINCRL_91 = 6315,
  ICC_LRWINCRL_91 = 6316,
  ICC_RRWINCRL_91 = 6317,
  ICC_OPENDOORLAMPLANGUAGE_91 = 6319,
  ICC_LECHILDLOCKSTS_91 = 6320,
  ICC_RICHILDLOCKSTS_91 = 6321,
  ICC_SMARTOPENTRUNK_91 = 6322,
  VCU_CHRGMODFUNSTS_3BD = 6323,
  APM_LRLOCKWINSTS_3E0 = 6324,
  APM_RRLOCKWINSTS_3E0 = 6325,
  APM_LFWINPOSFB_3E0 = 6326,
  APM_RFWINPOSFB_3E0 = 6327,
  APM_LRWINPOSFB_3E0 = 6328,
  APM_RRWINPOSFB_3E0 = 6329,
  VCU_CHRGSTOPSOCPLANVAL_51C = 6335,
  AVAP_SENTISNVTYSETSTS_4E7 = 6377,
  ICC_ALCCLRCSTMSET_BA = 6507,
  ICC_ALCADJ_COLOR1SET_69 = 6509,
  ICC_ALCADJ_COLOR2SET_69 = 6510,
  SLC_ALCSTS_510 = 6511,
  SLC_SCRNBRIAUTOSTS_510 = 6512,
  SLC_COLORCSTMSTS_510 = 6513,
  SLC_ALCCRTADJ_COLOR1_510 = 6516,
  SLC_ALCCRTADJ_COLOR2_510 = 6517,
  SLC_AIRVETN_AL_STS_510 = 6518,
  SLC_ALCDUALAREAMODSTS_510 = 6519,
  SLC_TURNLAMPMODSTS_510 = 6521,
  ECC_UVCSTS_3F7 = 6523,
  HUD_ARPARA1FB1_6B = 6527,
  HUD_ARPARA1FB2_6B = 6528,
  HUD_ARPARA1FB3_6B = 6529,
  HUD_ARPARA1FB4_6B = 6530,
  HUD_ARPARA1FB5_6B = 6531,
  HUD_ARPARA1FB6_6B = 6532,
  HUD_ARPARA1FB7_6B = 6533,
  HUD_ARPARA1FB8_6B = 6534,
  HUD_ARPARA1FB9_6B = 6535,
  HUD_ARPARA1FB10_6B = 6536,
  HUD_ARPARA1FB11_6B = 6537,
  HUD_ARPARA1FB12_6B = 6538,
  HUD_ARPARA1FB13_6B = 6539,
  HUD_ARPARA1FB14_6B = 6540,
  HUD_ARPARA1FB15_6B = 6541,
  HUD_ARPARA1FB16_6B = 6542,
  HUD_ARPARA2FB1_6B = 6543,
  HUD_ARPARA2FB2_6B = 6544,
  HUD_ARPARA2FB3_6B = 6545,
  HUD_ARPARA2FB4_6B = 6546,
  HUD_ARPARA2FB5_6B = 6547,
  HUD_ARPARA2FB6_6B = 6548,
  HUD_ARPARA2FB7_6B = 6549,
  HUD_ARPARA2FB8_6B = 6550,
  HUD_ARPARA2FB9_6B = 6551,
  HUD_ARPARA2FB10_6B = 6552,
  HUD_ARPARA2FB11_6B = 6553,
  HUD_ARPARA2FB12_6B = 6554,
  HUD_ARPARA2FB13_6B = 6555,
  HUD_ARPARA2FB14_6B = 6556,
  HUD_ARPARA2FB15_6B = 6557,
  HUD_ARPARA2FB16_6B = 6558,
  HUD_ARPARA3FB1_6C = 6559,
  HUD_ARPARA3FB2_6C = 6560,
  HUD_ARPARA3FB3_6C = 6561,
  HUD_ARPARA3FB4_6C = 6562,
  HUD_ARPARA3FB5_6C = 6563,
  HUD_ARPARA3FB6_6C = 6564,
  HUD_ARPARA3FB7_6C = 6565,
  HUD_ARPARA3FB8_6C = 6566,
  HUD_ARPARA3FB9_6C = 6567,
  HUD_ARPARA3FB10_6C = 6568,
  HUD_ARPARA3FB11_6C = 6569,
  HUD_ARPARA3FB12_6C = 6570,
  HUD_ARPARA3FB13_6C = 6571,
  HUD_ARPARA3FB14_6C = 6572,
  HUD_ARPARA3FB15_6C = 6573,
  HUD_ARPARA3FB16_6C = 6574,
  HUD_ARPARA4FB1_6C = 6575,
  HUD_ARPARA4FB2_6C = 6576,
  HUD_ARPARA4FB3_6C = 6577,
  HUD_ARPARA4FB4_6C = 6578,
  HUD_ARPARA4FB5_6C = 6579,
  HUD_ARPARA4FB6_6C = 6580,
  HUD_ARPARA4FB7_6C = 6581,
  HUD_ARPARA4FB8_6C = 6582,
  ICC_ARPARAFB1_49A = 6583,
  ICC_ARPARAFB2_49A = 6584,
  ICC_ARPARAFB3_49A = 6585,
  ICC_ARPARAFB4_49A = 6586,
  ICC_LOGOLIGSWT_49A = 6587,
  ICC_CONTRASTCOLORSWT1_49A = 6588,
  ICC_CONTRASTCOLORSWT2_49A = 6589,
  ICC_CONTRASTCOLORSWT3_49A = 6590,
  BMS_MAXLOADPWR_5BF = 6591,
  VCU_BATTHEATGMNGBSDMAP_616 = 6592,
  BCM_OPENDOORLAMPLANGUAGESWTSTS_311 = 6618,
  BCM_LOCKSOUNDPROMPTSWT_311 = 6619,
  BCM_SMARTOPENTRUNK_311 = 6620,
  RSM_SSMOVEMENT_311 = 6621,
  RSM_SSPOSPECR_311 = 6622,
  BCM_LOCKAUTOCLSSUNSSWTFB_311 = 6623,
  VCU_DRVGMILGDISPPERC_3B6 = 6761,
  VCU_DRRANGDISPERCENT_HIGPRECDISPLAYREQUIRE_3B6 = 6763,
  VCU_DRRANGDISPERCENT_HIGPREC_3B6 = 6764,
  VCU_EHUPWROFFENA_50E = 6769,
  CPD_CHILDCALLASWTSTS_3B2 = 6870,
  PDCU_EXHIBCARMODNOTICE_509 = 6946,
  PDCU_EXHIBCARMODTEXT_509 = 6947,
  VCU_EXHIBCARMODDISABLENOTICE_509 = 6948,
  BCM_EXHIBCARMODDISABLENOTICE_3DE = 6950,
  TBOX_EXHIBCARMODDISABLENOTICE_3FF = 6951,
  AVAP_SENTRYMODTI1VLD_547 = 6952,
  AVAP_SENTRYMODTI2VLD_547 = 6953,
  AVAP_SENTRYMODSTRTTI1_547 = 6954,
  AVAP_SENTRYMODDATE_547 = 6955,
  AVAP_SENTRYMODSTRTTI2_547 = 6956,
  AVAP_SENTRYMODENDTI1_547 = 6957,
  AVAP_SENTRYMODENDTI2_547 = 6958,
  AVAP_SENTRYMODSTSFB_549 = 6959,
  AVAP_SENTRYMODALRM_549 = 6963,
  ICC_SENTRYMODTI1VLD_61 = 6966,
  ICC_SENTRYMODTI2VLD_61 = 6967,
  ICC_SENTRYMODSTRTTI1_61 = 6968,
  ICC_SENTRYMODDATE_61 = 6969,
  ICC_SENTRYMODSTRTTI2_61 = 6970,
  ICC_SENTRYMODENDTI1_61 = 6971,
  ICC_SENTRYMODENDTI2_61 = 6972,
  ICC_SENTRYMODSW_63 = 6973,
  ICC_SENTRYALRMVIDEORXCFM_63 = 6977,
  ICC_SENTISNVTYSET_63 = 6978,
  TBOX_SENTRYMODSTRTTI1_B6 = 6981,
  TBOX_SENTRYMODDATE_B6 = 6982,
  TBOX_SENTRYMODSTRTTI2_B6 = 6983,
  TBOX_SENTRYMODENDTI1_B6 = 6984,
  TBOX_SENTRYMODENDTI2_B6 = 6985,
  TBOX_SENTRYMODSW_B7 = 6986,
  VCU_FSTEXTTIP_5A9 = 6987,
  PDCUFUELLEVELDISP_5A9 = 6988,
  PDCUFUMILGVLD_5A9 = 6989,
  PDCUFUMILGE_5A9 = 6990,
  BCM_DRLSWTSTS_51F = 6991,
  PDCUCHKENGLAMP_31F = 6992,
  PDCURNGLIM_31F = 6993,
  PDCUENRGMOD_3D0 = 6994,
  EMSOIL_PFAULT_3D0 = 6995,
  INDMODEGYFBDIS_3D0 = 6996,
  INDMODACCMODDIS_3D0 = 6997,
  INDMODSTEEMODDIS_3D0 = 6998,
  INDMODCREMODDIS_3D0 = 6999,
  PDCUPARKCHARGEST_3D0 = 7001,
  PDCUBMSTEMPLOWDRVLIMITHINT_3D0 = 7002,
  VCU_OBC_REMIND_LAMP_54B = 7009,
  EHU_CONTEXTUALMODE_59C = 7012,
  TBOXSENTISNVTYSET_B6 = 7023,
  ISGSYSTFLTDISP_21C = 7024,
  ISGMTRTTEMPFLTDISP_21C = 7025,
  ISGINVTEMPFLTDISP_21C = 7026,
  VCU_ABNORCHARGPORTALERT_3BC = 7033,
  ICC_100KMAVRGPWRCNSPTN_AB_542 = 7034,
  ICC_10KMAVRGPWRCNSPTN_AB_542 = 7035,
  ICC_100KMAVRGPWRCNSPTN_AS_542 = 7037,
  VCULASTCHRGTRIPAVRGPWRCNSPTN_AS_3B8 = 7038,
  VCU_TRIPAAVRGPWRCNSPTN_AS_3B8 = 7039,
  VCU_TRIPBAVRGPWRCNSPTN_AS_3B8 = 7040,
  VCU_100KMREMDRVGRNG_AB_AS_3B8 = 7043,
  VCU_PWRCNSPTNDIAG_AB_AS_3B8 = 7044,
  PDCUENRGMODREJHINT_AS_3B8 = 7059,
  VCU_PARKING_REMIND_PGEAR_AB_AS_3B8 = 7060,
  PDCUHYDOPERMOD_AS_3B8 = 7061,
  PDCUENRGMODFLTHINT_AS_3B8 = 7062,
  PDCUIDLEMODST_AS_3B8 = 7063,
  PDCUEMISSTESTMODST_AS_3B8 = 7066,
  PDCUENGSYSTWARN_AS_3B8 = 7067,
  PDCUEVST_AS_3B8 = 7068,
  PDCUREFSWITCHSTS_AS_3B8 = 7069,
  PDCUREFUNOTALLWD_AS_3B8 = 7070,
  PDCUFUFILRDOORRMN_AS_3B8 = 7071,
  PDCUFUTANKRELSPROGS_AS_3B8 = 7072,
  HDCU_WASHMODPROMPTSIG_AB_AS_3B8 = 7074,
  VCULASTCHRGTRIPAVRGPWRCNSPTN_AB_3B8 = 7075,
  VCU_TRIPAAVRGPWRCNSPTN_AB_3B8 = 7076,
  VCU_TRIPBAVRGPWRCNSPTN_AB_3B8 = 7077,
  VCU_10KMREMDRVGRNG_AB_3B8 = 7078,
  VCU_SUBTOLEGYCNSE_AB_AS_3B8 = 7079,
  VCU_ECCCNSEEGY_AS_56B = 7080,
  VCU_VEHDRVCNSEEGY_AS_56B = 7081,
  VCU_BHMCNSEEGY_AS_56B = 7082,
  VCU_VEHDRVCNSEEGY_AB_56B = 7091
};
bool SignalIdSoc_IsValid(int value);
constexpr SignalIdSoc SignalIdSoc_MIN = VCU_GEARSIGVLD_214;
constexpr SignalIdSoc SignalIdSoc_MAX = VCU_VEHDRVCNSEEGY_AB_56B;
constexpr int SignalIdSoc_ARRAYSIZE = SignalIdSoc_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SignalIdSoc_descriptor();
template<typename T>
inline const std::string& SignalIdSoc_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SignalIdSoc>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SignalIdSoc_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SignalIdSoc_descriptor(), enum_t_value);
}
inline bool SignalIdSoc_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SignalIdSoc* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SignalIdSoc>(
    SignalIdSoc_descriptor(), name, value);
}
// ===================================================================


// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace pb
}  // namespace signal
}  // namespace alc
}  // namespace autolink

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::autolink::alc::signal::pb::SignalIdSoc> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::autolink::alc::signal::pb::SignalIdSoc>() {
  return ::autolink::alc::signal::pb::SignalIdSoc_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_autolink_2ealc_2esignal_2epb_2eproto
