/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
syntax = "proto2";

package autolink.alc.signal.pb;

// the following SID can be subscribed
enum SignalIdSoc {
    VCU_GEARSIGVLD_214                                           =0x1000;
    VCU_GEARSIG_214                                              =0x1001;
    ESP_VEHSPD_318                                               =0x1002;
    PDU_TPMS_589                                                 =0x1003;
    VCU_DRVGMILG_504                                             =0x1006;
    YRS_LGTACCE_246                                              =0x110B;
    ECC_ACSTS_373                                                =0x110F;
    ECC_WINDSPDSTS_373                                           =0x1111;
    ECC_BACKROWAIROUTLMODSTS_373                                 =0x1112;
    ECC_DRVRTSETSTS_373                                          =0x1113;
    ECC_PASSTSETSTS_373                                          =0x1114;
    ECC_AUTOSTS_373                                              =0x1116;
    ECC_SYNCSTS_373                                              =0x1117;
    ECC_CIRCSTS_373                                              =0x1119;
    ECC_PARTICLECONCVLD_373                                      =0x111A;
    ECC_OUTDT_373                                                =0x111B;
    ECC_OUTDTVLD_373                                             =0x111C;
    ECC_MAXFRNTDEFRST_373                                        =0x111D;
    ECC_HEATMNGTSYSFLT_373                                       =0x111E;
    ECC_HEATMNGTFCTLIM_373                                       =0x111F;
    ECC_DRVRAIROUTLMOD_373                                       =0x1120;
    ECC_PASSAIROUTLMOD_373                                       =0x1121;
    ECC_EGYSAVEMODSTS_373                                        =0x1122;
    ICC_AUTOPOWER_OFFSWITCHSTATUS_37A                            =0x1123;
    EHU_PETSMODBTN_37A                                           =0x1124;
    EHU_LEOUTLUPDWNMOTACTVCMD_37A                                =0x1126;
    EHU_LEOUTLLERIMOTACTVCMD_37A                                 =0x1127;
    EHU_MIDLEOUTLUPDWNMOTACTVCMD_37A                             =0x1129;
    EHU_MIDLEOUTLLERIMOTACTVCMD_37A                              =0x112A;
    EHU_MIDRIOUTLUPDWNMOTACTVCMD_37A                             =0x112C;
    EHU_MIDRIOUTLLERIMOTACTVCMD_37A                              =0x112D;
    EHU_RIOUTLUPDWNMOTACTVCMD_37A                                =0x112F;
    EHU_RIOUTLLERIMOTACTVCMD_37A                                 =0x1130;
    ECC_LEOUTLUPDWNMOTACTVSTS_378                                =0x1132;
    ECC_LEOUTLLERIMOTACTVSTS_378                                 =0x1133;
    ECC_MIDLEOUTLUPDWNMOTACTVSTS_378                             =0x1135;
    ECC_MIDLEOUTLLERIMOTACTVSTS_378                              =0x1136;
    ECC_MIDRIOUTLUPDWNMOTACTVSTS_378                             =0x1138;
    ECC_MIDRIOUTLLERIMOTACTVSTS_378                              =0x1139;
    ECC_RIOUTLUPDWNMOTACTVSTS_378                                =0x113B;
    ECC_RIOUTLLERIMOTACTVSTS_378                                 =0x113C;
    MCU_F_CRTROTDIR_150                                          =0x1143;
    MCU_F_ALRMLAMP_FS_150                                        =0x1147;
    PEPS_PWRMOD_333                                              =0x114A;
    PEPS_PWRMODVLD_333                                           =0x114B;
    PEPS_POLLINGSTS_333                                          =0x114C;
    PEPS_KEYINCARRMN_37B                                         =0x1152;
    PEPS_SHIFTTOPNSTRTREQ_37B                                    =0x1154;
    PEPS_STEPBRKTOSTRTREQ_37B                                    =0x1155;
    PEPS_NOFOUNDLEGALKEY_37B                                     =0x1156;
    PEPS_EMGYSTRTPROMT_37B                                       =0x1159;
    PEPS_VCUAUTHENTFAILPROMT_37B                                 =0x115B;
    PEPS_KEYLOPWRPROMT_37B                                       =0x115C;
    PEPS_KEYOUTDCARPROMT_37B                                     =0x115D;
    PEPS_TIOUTPWROFFRMN_380                                      =0x1165;
    PWC_CHRGSTS_524                                              =0x1166;
    PWC_PHNFORGETREMDSTS_524                                     =0x1167;
    PWC_MODLESWTSTS_524                                          =0x1168;
    EHU_DRVRSEATTRACKMANREQ_533                                  =0x117E;
    EHU_DRVRHEIMANREQ_533                                        =0x1180;
    EHU_DRVRSEATBACKMANREQ_533                                   =0x1181;
    EHU_PASSSEATTRACKMANREQ_533                                  =0x1182;
    EHU_PASSSEATBACKMANREQ_533                                   =0x1183;
    EHU_LUMBARUPD_533                                            =0x1184;
    EHU_LUMBARDWN_533                                            =0x1185;
    EHU_LUMBARFWD_533                                            =0x1186;
    EHU_LUMBARBACKW_533                                          =0x1187;
    EHU_AUTOPASSSEATHEATENA_533                                  =0x1189;
    EHU_PASSLUMBARUPD_533                                        =0x118E;
    EHU_PASSLUMBARDWN_533                                        =0x118F;
    EHU_PASSLUMBARFWD_533                                        =0x1190;
    EHU_PASSLUMBARBACKW_533                                      =0x1191;
    EHU_LRLOCKWINCMD_533                                         =0x1192;
    EHU_RRLOCKWINCMD_533                                         =0x1193;
    EHU_SETMAXPOSNCMD_PLG_52                                     =0x1195;
    EHU_TRACTNCMD_52                                             =0x1196;
    ICC_DRLSWT_52                                                =0x1197;
    ICC_LOCKAUTOCLSSUNSSWT_52                                    =0x1198;
    EHU_VEHACCOUNTLOGINUIDSTS_90                                 =0x119A;
    EHU_VEHACCOUNTLOGINUID_90                                    =0x119B;
    EHU_SEATLOCNMEMOPERCMD_90                                    =0x119C;
    EHU_DRVRSEATUIDSUBPOSN_90                                    =0x119D;
    EHU_PASSSEATLOCNMEMOPERCMD_90                                =0x119E;
    EHU_PASSSEATUIDSUBPOSN_90                                    =0x119F;
    EHU_FADERSET_44                                              =0x11AA;
    EHU_LERIBALSET_44                                            =0x11AC;
    EHU_MIDFRQAUDIOSET_44                                        =0x11AE;
    EHU_LOFRQAUDIOSET_44                                         =0x11B0;
    EHU_HIFRQAUDIOSET_44                                         =0x11B2;
    EHU_SOUNDSWITCH_44                                           =0x11B8;
    EHU_ARCFOXSOUNDMODESELECT_44                                 =0x11B9;
    EHU_FOLWMEHOMETISET_46                                       =0x11BA;
    EHU_INTRLAMPTISET_46                                         =0x11BB;
    EHU_MAIVOLSET_46                                             =0x11BE;
    EHU_MUTECTRL_46                                              =0x11BF;
    EHU_IESSMODREQ_46                                            =0x11C1;
    EHU_VSCMODREQ_46                                             =0x11C2;
    EHU_HFTVOLSET_46                                             =0x11C3;
    EHU_NAVVOLSET_46                                             =0x11C6;
    EHU_RTVOLSET_46                                              =0x11C8;
    EHU_VOLINCREASEWITHSPEED_46                                  =0x11CE;
    EHU_RIFRNTWINCTRL_4E                                         =0x11D0;
    EHU_LEREWINCTRL_4E                                           =0x11D1;
    EHU_RIREWINCTRL_4E                                           =0x11D2;
    EHU_LEFRNTWINCTRL_4E                                         =0x11D3;
    EHU_POSNLAMPCTRL_4E                                          =0x11D7;
    EHU_LOBEAMCTRL_4E                                            =0x11D9;
    EHU_REDEFRSTOPENREQ_4E                                       =0x11DF;
    EHU_SUNSHADECTRLREQ_4E                                       =0x11E0;
    EHU_CENTRLOCKCTRL_4E                                         =0x11E1;
    EHU_MIRRCMD_4E                                               =0x11E2;
    EHU_REMIRRAUTOFOLDSET_4E                                     =0x11E3;
    EHU_RAINCLSDSUNROOFSET_4E                                    =0x11E5;
    EHU_ARMEDCLSDWINSET_4E                                       =0x11E6;
    EHU_OFFUNLCKSET_4E                                           =0x11E7;
    EHU_DOORUNLOCKSET_4E                                         =0x11E8;
    EHU_SETATMLAMPBRI_336                                        =0x11EB;
    EHU_ATMLAMPOPENCMD_336                                       =0x11EF;
    EHU_ALCCSTMSWT_336                                           =0x11F1;
    EHU_HDCSWTSIG_336                                            =0x11F2;
    EHU_ESPSWTSIG_336                                            =0x11F3;
    EHU_SLCSWT_336                                               =0x11F6;
    EHU_DRVRSEATHEATGREQ_528                                     =0x11FA;
    EHU_DRVRSEATVENTNREQ_528                                     =0x11FB;
    EHU_SEATWELFCTENAREQ_528                                     =0x11FE;
    EHU_PASSSEATWELFCTENAREQ_528                                 =0x11FF;
    EHU_SEATHEATLVAUTOREDUCEREQ_528                              =0x1201;
    EHU_PASSSEATHEATGREQ_528                                     =0x1202;
    EHU_PASSSEATVENTNREQ_528                                     =0x1203;
    EHU_LRSEATHEATREQ_528                                        =0x1204;
    EHU_RRSEATHEATREQ_528                                        =0x1206;
    EHU_CRTLANGUAGE_529                                          =0x1209;
    EHU_BRIADJ_HUD_529                                           =0x120B;
    EHU_STEERWHLPHNKEYBACKLI_529                                 =0x120C;
    EHU_BACKGNDBRILVL_529                                        =0x120D;
    EHU_BRIADJVAL_HUD_529                                        =0x1210;
    EHU_OPENCMD_HUD_529                                          =0x1211;
    EHU_SNOWMODSWT_HUD_529                                       =0x1212;
    EHU_HEIADJ_HUD_529                                           =0x1213;
    EHU_WIPRSRVPOSN_529                                          =0x1216;
    EHU_PULLMODREQ_529                                           =0x121B;
    EHU_USRPWROFFFB_529                                          =0x121C;
    EHU_STEERWHLHEATGSW_529                                      =0x121D;
    EHU_DRVRTSET_530                                             =0x1220;
    EHU_PASSTSET_530                                             =0x1221;
    EHU_ECCAUTOREQ_530                                           =0x1223;
    EHU_DRVRSYNCREQ_530                                          =0x1224;
    EHU_ACSWTREQ_530                                             =0x1225;
    EHU_AIRVOLSET_530                                            =0x1226;
    EHU_ECCINTEXTCIRCREQ_530                                     =0x1227;
    EHU_AIRCLNSWTREQ_530                                         =0x1228;
    EHU_MAXFRNTDEFRSTSET_530                                     =0x1229;
    EHU_VSPCTRLCMD_530                                           =0x122A;
    EHU_NAVROADTYP_530                                           =0x122C;
    EHU_ECCEGYSAVEMODREQ_530                                     =0x1230;
    EHU_BLOWWINBTN_530                                           =0x1231;
    EHU_DRVGMILGDISPTYPSET_534                                   =0x1233;
    EHU_UVCREQ_534                                               =0x1234;
    EHU_DRVRBLOWFACEBTN_534                                      =0x1237;
    EHU_PASSBLOWFACETBTN_534                                     =0x1238;
    EHU_DRVRBLOWFOOTBTN_534                                      =0x1239;
    EHU_PASSBLOWFOOTBTN_534                                      =0x123A;
    EHU_BACKROWAIROUTLMODREQ_534                                 =0x123B;
    EHU_ECCSYSSWTCMD_534                                         =0x123D;
    EHU_DRVRBLOWMODREQ_534                                       =0x123E;
    EHU_PASSBLOWMODREQ_534                                       =0x123F;
    EHU_FLSEATMASMODCMD_534                                      =0x1240;
    EHU_FLSEATMASGRADECMD_534                                    =0x1241;
    EHU_FRSEATMASMODCMD_534                                      =0x1242;
    EHU_FRSEATMASGRADECMD_534                                    =0x1243;
    EHU_USRSETCHRGGUNANTITHFT_59C                                =0x124B;
    EHU_CHRGDCHACTRLCMD_59C                                      =0x1254;
    EHU_SETACCHRGGUNUNLOCKSWT_59C                                =0x1255;
    EHU_OPENCLOSECHRGPORT1REQ_59C                                =0x1257;
    ICC_AUTOCOLORSWT_59C                                         =0x1259;
    ICC_EMISSTESTMODE_59C                                        =0x125B;
    ICC_IDLEMODE_59C                                             =0x125C;
    ICC_ENRGMOD_59C                                              =0x125E;
    ICC_REFSWITCHSTS_59C                                         =0x125F;
    ICC_CHRGMODUSRSET_59D                                        =0x1265;
    ICC_TURNLAMPMODSWT_59D                                       =0x1268;
    ICC_CONTRASTCOLORSWT4_59D                                    =0x1269;
    ICC_CONTRASTCOLORSWT5_59D                                    =0x126A;
    ICC_CONTRASTCOLORSWT6_59D                                    =0x126B;
    EHU_USRSETCHRGRMNMILG_610                                    =0x127E;
    EHU_KL15KEEPREQ_610                                          =0x127F;
    EHU_EGYCNSECLRFLG_610                                        =0x1280;
    FAILRFUSAMPLECIRC_610                                        =0x1283;
    EHU_POLLINGFCTOPENSTS_610                                    =0x1284;
    EHU_CHRGINSULFCTREQ_610                                      =0x1286;
    EHU_USRSETDISCHRGRMNMILG_610                                 =0x128B;
    EHU_SETCHRGENDSOC_610                                        =0x128C;
    ICC_ROADLMTSPD_63D                                           =0x1291;
    ICC_TIMETODEST_63D                                           =0x1292;
    ICC_MILETODEST_63D                                           =0x1293;
    VCU_RDYLAMP_214                                              =0x12A4;
    VCU_PWRBATTHVCNCTSTS_214                                     =0x12A5;
    VCU_DRVMODSHIFTMISOPER_214                                   =0x12AC;
    VCU_EXTREMEEGYSAVESWTENAFLG_358                              =0x12AF;
    VCU_PULLMODENASIG_358                                        =0x12B1;
    VCU_PULLMODSIG_358                                           =0x12B2;
    VCU_DRVPWRLIMSTS_358                                         =0x12B5;
    VCU_EGYRECOVPWRLIMSTS_358                                    =0x12B6;
    VCU_EXTREMEEGYSAVEOPENSIG_358                                =0x12B8;
    VCU_ONEPEDALKEEPDISPLAY_AB_AS_358                            =0x12BA;
    VCU_VEHACSYCNSEEGY_579                                       =0x12C8;
    VCU_EGYRECOVEGY_579                                          =0x12CB;
    VCU_LONGTIHLTHSTOREPUSHINFO_605                              =0x12DD;
    VCU_CHRGSTSTXT_605                                           =0x12DE;
    BMS_CHRGFLTPROMT_330                                         =0x12E1;
    BMS_PWRBATTRMNGCPSOC_330                                     =0x12E7;
    BMS_CELLMINTALRM_330                                         =0x12E8;
    BMS_PWRBATTTHERMRUNAWAYALRM_330                              =0x12E9;
    ESP_WARNINGON_261                                            =0x12F1;
    ESP_BRKFLDALRM_261                                           =0x12F3;
    ESP_AVLINDCN_CST_261                                         =0x12F4;
    ESP_CTRLSTS_CST_261                                          =0x12F5;
    ESP_SYSSTS_EPB_268                                           =0x12F8;
    ESP_FLTINDCN_EPB_268                                         =0x12F9;
    ESP_ACTVNDCN_EPB_268                                         =0x12FA;
    EPB_WARNMSG01_268                                            =0x12FB;
    EPB_WARNMSG02_268                                            =0x12FC;
    EPB_WARNMSG04_268                                            =0x12FD;
    ESP_BRKPEDLSTS_318                                           =0x1306;
    ESP_VEHSPDVLD_318                                            =0x1307;
    ESP_SYSACTV_318                                              =0x1308;
    ESP_LAMPSWTOFFINDCN_318                                      =0x1309;
    ESP_FLTINDCN_EBD_318                                         =0x130A;
    ESP_FLTINDCN_ABS_318                                         =0x130B;
    ESP_FLTINDCN_TCS_318                                         =0x130E;
    ESP_CTRLSTS_HDC_318                                          =0x130F;
    ESP_AVLINDCN_HDC_318                                         =0x1310;
    EPS_STEERWHLAGSIG_1C2                                        =0x1313;
    EPS_STEERWHLAGSIGVLD_1C2                                     =0x1314;
    BCM_WIPRINSRVPOSN_335                                        =0x1318;
    BCM_INTLAMPTISETSTS_335                                      =0x1319;
    BCM_WATERPOSNSNSRSWTSTS_335                                  =0x131A;
    BCM_EXTLAMPSWTSTS_335                                        =0x131E;
    BCM_RAINCLSSUNROOFSETSTS_335                                 =0x1324;
    BCM_MIRRLOCKAUTOSETSTS_335                                   =0x1325;
    BCM_DANGERALRMLAMPSWTSTS_335                                 =0x1327;
    BCM_REDEFRSTHEATGCMD_335                                     =0x1328;
    BCM_RVSLAMPOUTPCMD_335                                       =0x1329;
    BCM_LETRUNLAMPOUTPCMD_335                                    =0x132B;
    BCM_RITRUNLAMPOUTPCMD_335                                    =0x132C;
    BCM_HIBEAMOUTPCMD_335                                        =0x132F;
    BCM_LOBEAMOUTPCMD_335                                        =0x1330;
    BCM_POSNLAMPOUTPCMD_335                                      =0x1331;
    BCM_BRKLAMPOUTPCMD_335                                       =0x1332;
    BCM_REFOGLAMPOUTPCMD_335                                     =0x1333;
    BCM_FRNTWIPRSPD_335                                          =0x1334;
    BCM_VEHAMBBRI_335                                            =0x1336;
    BCM_FRNTHOODLIDSTS_343                                       =0x1339;
    BCM_SUNROOFANTIPINCHSTS_343                                  =0x133A;
    BCM_FRNTLEDOORLOCKSTS_343                                    =0x133B;
    BCM_TRRELSSWTSTS_343                                         =0x133C;
    BCM_LOCKALLDOORCMD_343                                       =0x133D;
    BCM_LEFRNTDOORSTS_343                                        =0x133E;
    BCM_RIFRNTDOORSTS_343                                        =0x133F;
    BCM_TRSTS_343                                                =0x1340;
    BCM_ANTITHFTSTS_343                                          =0x1341;
    BCM_CENLOCKSWTSTS_343                                        =0x1342;
    BCM_DOORUNLOCKSETFB_343                                      =0x1343;
    BCM_RIREDOORSTS_343                                          =0x1344;
    BCM_LEREDOORSTS_343                                          =0x1345;
    BCM_LEFRNTWINSTS_343                                         =0x1346;
    BCM_RIFRNTWINSTS_343                                         =0x1347;
    BCM_LEREWINSTS_343                                           =0x1348;
    BCM_RIREWINST_343                                            =0x1349;
    BCM_FOLWMESETSTSFB_343                                       =0x134A;
    BCM_DRVRBOORUNLCKOUTPCMD_343                                 =0x134B;
    BCM_PASSDOORUNLCKOUTPCMD_343                                 =0x134C;
    BCM_LEDRLOUTPCMD_343                                         =0x134D;
    BCM_RIDRLOUTPCMD_343                                         =0x134E;
    BCM_ARMEDCLSWINSETSTS_343                                    =0x134F;
    BCM_OFFAUTOUNLCKSETSTS_343                                   =0x1350;
    BCM_SUNROOFPOSNINFO_343                                      =0x1351;
    BCM_SUNROOFOPENAR_343                                        =0x1352;
    BCM_SUNROOFRUNNGSTS_343                                      =0x1353;
    BCM_MIRRCMD_343                                              =0x1354;
    BCM_APPLIANCECLSLVL_51E                                      =0x1355;
    DSMC_DRVRSEATTRACKSWTSTS_4F1                                 =0x1358;
    DSMC_DRVRSEATHEIADJSWTSTS_4F1                                =0x1359;
    DSMC_DRVRSEATBACKADJSWTSTS_4F1                               =0x135A;
    DSMC_DRVRSEATWELFCTSETFB_4F1                                 =0x135D;
    DSMC_REMIRRAUTODWNFLIPFB_4F1                                 =0x135E;
    DSMC_RIMIRRRXDIECPOSN_4F3                                    =0x1360;
    DSMC_RIMIRRRYDIRCPOSN_4F3                                    =0x1361;
    DSMC_LEMIRRRXPOSN_4F3                                        =0x1362;
    DSMC_LEMIRRRYPOSN_4F3                                        =0x1363;
    DSMC_DRVRSEATTRACKPOSN_4F5                                   =0x1368;
    DSMC_DRVRSEATHEIPOSN_4F5                                     =0x1369;
    DSMC_DRVRSEATBACKPOSN_4F5                                    =0x136A;
    DSMC_DRVRSEATMEMRECALLFB_62                                  =0x136C;
    DSMC_DRVRSEATMEMDATAUPDFB_62                                 =0x136D;
    DSMC_DRVRSEATHEATGSTS_518                                    =0x136F;
    DSMC_DRVRSEATVENTNSTS_518                                    =0x1370;
    DSMC_DRVRSEATTRACKADJSTS_518                                 =0x1371;
    DSMC_DRVRSEATHEIADJSTS_518                                   =0x1372;
    DSMC_DRVRSEATBACKADJSTS_518                                  =0x1373;
    DSMC_LUMBARUPDSTS_518                                        =0x1375;
    DSMC_LUMBARDWNSTS_518                                        =0x1376;
    DSMC_LUMBARFWDSTS_518                                        =0x1377;
    DSMC_LUMBARBACKWSTS_518                                      =0x1378;
    DSMC_LRSEATHEATSTS_518                                       =0x137A;
    DSMC_SEATHEATAUTODWNENASTS_518                               =0x137B;
    DSMC_REMIRRLERIFB_518                                        =0x137C;
    DSMC_MASFL_SEATMASMOD_518                                    =0x137E;
    DSMC_MASFL_SEATMASGRADESTS_518                               =0x137F;
    PLG_USRSETTRMAXHEIRESFB_64                                   =0x1381;
    PLG_USRSETTRMAXHEI_471                                       =0x1382;
    PLG_LETRPOSN_471                                             =0x1383;
    PLG_SOUNDREMDNGREQ_EHU_471                                   =0x1384;
    PLG_SYSFLTINDCN_471                                          =0x1386;
    PLG_TRSWTSTSINDCN_471                                        =0x1387;
    PLG_OPERMOD_471                                              =0x1389;
    PLG_ANTIPINCHSTS_471                                         =0x138A;
    PAS_STS_FPAS_574                                             =0x138E;
    PAS_STS_RPAS_574                                             =0x1390;
    PAS_SOUNDINDCN_F_576                                         =0x13A3;
    PAS_SOUNDINDCN_R_576                                         =0x13A4;
    EHU_INTEGTCRSSWT_526                                         =0x13AB;
    EHU_HMASET_526                                               =0x13B5;
    EHU_LIFESIGNMONITORSWT_4DF                                   =0x13C2;
    ICM_TOTMILGVLD_ODO_531                                       =0x13CF;
    ICC_REMSENTRYMODSTS_531                                      =0x13D0;
    ICM_TOTMILG_ODO_531                                          =0x13D1;
    ICM_DISPVEHSPD_531                                           =0x13D2;
    ICM_DISPVEHSPDUNIT_531                                       =0x13D3;
    SDM_AIRBAGSYSALRMLAMPSTS_319                                 =0x13DA;
    SDM_SECUBLTALRMSTS_RL_319                                    =0x13DB;
    SDM_SECUBLTALRMSTS_RM_319                                    =0x13DC;
    SDM_SECUBLTALRMSTS_RR_319                                    =0x13DD;
    SDM_DRVERSECUBLTALRMSTS_319                                  =0x13DE;
    SDM_PASSSEATBLTBUCDSTS_319                                   =0x13DF;
    SDM_CLLSNSIG_319                                             =0x13E0;
    SDM_PASSSEATOCCSTS_319                                       =0x13E1;
    CIM_FRNTWIPRSWTSTS_310                                       =0x13E8;
    CIM_REWIPRSWTSTS_310                                         =0x13EA;
    VCU_ACCHRGELECTCLOCKSTSFBSIG_554                             =0x13EB;
    VCU_CHRGDISCHRGCRTDISP_52C                                   =0x13F0;
    VCU_CHRGDCHAPWRDISP_52C                                      =0x13F1;
    VCU_ELECTCLOCKFLTPROMT_52D                                   =0x13F2;
    BMS_CHRGOPRTGUIDEPROMT_51B                                   =0x13F4;
    BMS_CHRGRLTDSTSPROMT_51B                                     =0x13F5;
    BMS_CHRGDCHASTOPREASONPROMT_51B                              =0x13F6;
    ICC_NUMTRFCLMP_61F                                           =0x1428;
    ICC_NXTCHGTYPE_61F                                           =0x142B;
    ICC_MAPSTS_61F                                               =0x142C;
    ICC_FULLROADTRFCINFO_SEG_602                                 =0x1432;
    ICC_FULLROADTRFCINFO_NUM_602                                 =0x1433;
    ICC_FULLROADTRFCINFO_LEN_602                                 =0x1434;
    ICC_FULLROADTRFCINFO_TI_602                                  =0x1435;
    ICC_FULLROADTRFCINFO_STS_602                                 =0x1436;
    ICC_TURNICON_620                                             =0x143B;
    ICC_CRTROADTRFCINFO_NUM_620                                  =0x143C;
    ICC_CRTROADTRFCINFO_MILG_620                                 =0x143D;
    ICC_CRTROADTRFCINFO_TI_620                                   =0x143E;
    ICC_CRTROADTRFCINFO_STS_620                                  =0x143F;
    ICC_ESTIMTSOCPERC_60E                                        =0x1468;
    ICC_ESTIMTREMSOC_60E                                         =0x1469;
    ICC_RNG2NXTCHGTIME_64F                                       =0x146E;
    ICC_RNG2NXTCHGMILE_64F                                       =0x146F;
    ICC_TOTLMILERNG_61D                                          =0x1490;
    ICC_TOTLTIMERNG_61D                                          =0x1491;
    SLC_CRTCOLORR_337                                            =0x1496;
    SLC_CRTCOLORG_337                                            =0x1497;
    SLC_CRTCOLORB_337                                            =0x1498;
    SLC_CRTBRI_337                                               =0x1499;
    SLC_ALCCSTMSTS_337                                           =0x149B;
    SLC_COURTESYFCTMODSTS_3EC                                    =0x149E;
    SLC_MUSICRHYTHMSTS_3EC                                       =0x149F;
    SLC_BRIBREATHSTS_3EC                                         =0x14A0;
    SLC_VEHSPDRHYTHMSTS_3EC                                      =0x14A2;
    SLC_DRVMDSTS_3EC                                             =0x14A3;
    SLC_ACMODSTS_3EC                                             =0x14A4;
    SLC_DOWSTS_3EC                                               =0x14A5;
    SLC_BRI_3EC                                                  =0x14A6;
    SLC_COLOURBREATHSTS2_3EC                                     =0x14A7;
    SLC_ALCSPCHSTS_3EC                                           =0x14A9;
    SLC_ALCMOBCHAREMDSTS_3EC                                     =0x14AA;
    SLC_LOGOLIGSTS_3EC                                           =0x14AB;
    SLC_CONTRASTCOLORSTS1_3EC                                    =0x14AC;
    SLC_CONTRASTCOLORSTS2_3EC                                    =0x14AD;
    SLC_CONTRASTCOLORSTS3_3EC                                    =0x14AE;
    ICC_LIGHTCRLSTSFB_320                                        =0x14DF;
    ICC_REFOGLAMPOUTPCMD_320                                     =0x14E0;
    ICM_MILGOFFS_ODO_532                                         =0x14E4;
    ICM_DISPTOTMILG_ODO_532                                      =0x14E5;
    ICM_MILGDATAVLD_ODO_532                                      =0x14E6;
    ICM_MILGRSTCNTR_ODO_532                                      =0x14E7;
    ICC_CTRL_CMD_532                                             =0x14E8;
    ICC_OPENPERCCMD_532                                          =0x14E9;
    EHU_RESVACCHRGSTRTTI_HR_614                                  =0x1529;
    EHU_RESVACCHRGSTRTTI_MINS_614                                =0x152A;
    EHU_RESVACCHRGSTRTSET_614                                    =0x152B;
    EHU_MONRESVACCHRGREPSTRTSET_614                              =0x152C;
    EHU_RESVACCHRGENDTI_HR_614                                   =0x1533;
    EHU_RESVACCHRGENDTI_MINS_614                                 =0x1534;
    EHU_RESVACCHRGENDSET_614                                     =0x1535;
    EHU_MONRESVACCHRGREPENDSET_614                               =0x1536;
    VCU_REFRSHMODRESTRNTFCTCMD_357                               =0x1543;
    RESETFLG_TRIPFROMLASTCHARGE_357                              =0x1544;
    VCU_REMPWRBATTHEATGENDCMD_511                                =0x1546;
    VCU_REMBATTHEATGFAILREASON_511                               =0x1547;
    VCU_PETSMODFOBDREASON_511                                    =0x1548;
    VCU_PETSMODWARN_511                                          =0x1549;
    VCU_VEHCRTCHRGENDSOC_511                                     =0x154B;
    ECC_INSDT_582                                                =0x154D;
    ECC_PARTICLECONC_582                                         =0x154E;
    VCU_ALRMLAMP_FS_105                                          =0x1557;
    VCU_CRUISEFLTTIP_105                                         =0x1558;
    VCU_DRVMODEXTNSIG_105                                        =0x1559;
    VCU_CRUISESTS_105                                            =0x155A;
    VCU_CRUISEAIMSPD_105                                         =0x155B;
    VCU_DRVPWRLIMPERC_503                                        =0x155C;
    VCU_EGYFBPWRLIMPERC_503                                      =0x155D;
    VCU_MEMCHRGRMNMILGTHD_503                                    =0x155E;
    VCU_USRHMIPROMT_503                                          =0x1564;
    VCU_RESVCHRGSTSDISP_503                                      =0x1565;
    VCU_INSNTEGYCNSEHR_503                                       =0x1566;
    VCU_MONRPWRBATTTHERMRUNAWAYALRM_504                          =0x1567;
    VCU_LNCHCTRLTRIGRMN_504                                      =0x1568;
    VCU_SHIFTOPERRMN_504                                         =0x1569;
    VCU_MCUFSYSOVERTDISP_504                                     =0x156C;
    VCU_MCURSYSOVERTDISP_504                                     =0x156D;
    VCU_VEHSYSFLTLAMP_504                                        =0x156E;
    VCU_RMNUSRCLSECCDISPCMD_504                                  =0x156F;
    VCU_RMNUSRECCFCTLMTDISPCMD_504                               =0x1570;
    VCU_LNCHCTRLMODDIRMN_504                                     =0x1571;
    VCU_DRVRNGVLD_504                                            =0x1574;
    VCU_ACCUEEGYCNSE_505                                         =0x1578;
    VCU_EGYRECOVFORBNFLG_505                                     =0x1579;
    VCU_DRVPWRLIMINDCRLAMP_505                                   =0x157A;
    VCU_BATTFLTINDCN_505                                         =0x157D;
    VCU_CHRGINDCRLAMP_505                                        =0x157E;
    VCU_CHRGDCHAGUNCNCTNINDCRLAMP_505                            =0x1580;
    VCU_CHRGGUNSTRT_505                                          =0x1582;
    VCU_SOCLOCHRGRMN_50C                                         =0x1586;
    VCU_DRVGMILGDISPTYPCFM_50C                                   =0x1587;
    BMS_ALRMLAMP_FS_215                                          =0x158E;
    BMS_CHRGCRATEDISP_240                                        =0x1592;
    BMS_FBREMHEATGOPERSTS_363                                    =0x1598;
    BMS_VEHEXTDCHASTS_363                                        =0x1599;
    BMS_CHRGINSULFCTOPENSTS_363                                  =0x159C;
    APA_FMEBSTS_2A0                                              =0x15B5;
    APA_RMEBSTS_2A0                                              =0x15B7;
    APA_STS_FPAS_558                                             =0x15D0;
    APA_STS_RPAS_558                                             =0x15D3;
    ESP_FLTINDCN_HHC_332                                         =0x1623;
    ESP_ACTVINDCN_AVH_332                                        =0x1624;
    ESP_SWTINDCN_AVH_332                                         =0x1625;
    ESP_FLTINDCN_AVH_332                                         =0x1626;
    EPS_FLTINDCN_470                                             =0x162A;
    BCM_RLS_LIGHTSWTREQ_321                                      =0x162B;
    BCM_SWH_STEERWHLHEATGSTS_321                                 =0x162C;
    BCM_TIMEOUTPOWER_OFFFEEDBACK_321                             =0x162D;
    BCM_LIGHTREQREASON_RLS_321                                   =0x1633;
    BCM_OFFUNLCKSETSTSFB_321                                     =0x1634;
    BCM_SUNSHADERUNNGSTS_345                                     =0x1638;
    BCM_SUNSHADEPOSNINFO_345                                     =0x1639;
    BCM_LEPOSNLAMPFLT_539                                        =0x163A;
    BCM_RIPOSNLAMPFLT_539                                        =0x163B;
    BCM_LELOBEAMFLT_539                                          =0x163C;
    BCM_RILOBEAMFLT_539                                          =0x163D;
    BCM_LEHIBEAMFLT_539                                          =0x163E;
    BCM_RIHIBEAMFLT_539                                          =0x163F;
    BCM_LEDRLFLT_539                                             =0x1640;
    BCM_RIDRLFLT_539                                             =0x1641;
    BCM_LEFRNTFOGLAMPFLT_539                                     =0x1642;
    BCM_RIFRNTFOGLAMPFLT_539                                     =0x1643;
    BCM_REFOGLAMPFLT_539                                         =0x1644;
    BCM_LOBRKLAMPFLT_539                                         =0x1645;
    BCM_HIBRKLAMPFLT_539                                         =0x1646;
    BCM_RVSLAMPFLT_539                                           =0x1647;
    BCM_LETURNLAMPFLT_539                                        =0x1648;
    BCM_RITURNLAMPFLT_539                                        =0x1649;
    BCM_LICPLATELAMPFLT_539                                      =0x164A;
    BCM_WINLOCKSWINPUT_539                                       =0x164B;
    BCM_MILGDATAVLD_ODO_641                                      =0x164C;
    BCM_MILGRSTCNTR_ODO_641                                      =0x164D;
    BCM_TOTMILG_ODO_641                                          =0x164E;
    BCM_MILGOFFS_ODO_641                                         =0x164F;
    ADAS_HANDSOFFTAKEOVERREQ_32C                                 =0x165E;
    ADAS_AUDIOWARN_32C                                           =0x1666;
    MPC_OVERSPDWARN_SLA_32E                                      =0x166F;
    MPC_SPDLIMUNIT_SLA_32E                                       =0x1675;
    MPC_SPDLIM_SLA_32E                                           =0x1676;
    ADAS_WARN_FCW_33C                                            =0x1677;
    ADAS_STS_FCW_33C                                             =0x1679;
    ADAS_STS_AEB_33C                                             =0x167A;
    ADAS_TAKEOVERREQ_ACC_347                                     =0x168D;
    ADAS_SPDLIM_ASL_347                                          =0x1691;
    ADAS_SPDLIMSTS_ASL_347                                       =0x1692;
    ADAS_ACC_OPERTXT_347                                         =0x1693;
    MPC_STS_HMA_334                                              =0x16AA;
    MPC_WARNSIGN_334                                             =0x16AD;
    MPC_FOBDSIGN_334                                             =0x16AE;
    MPC_OVERTAKESIGN_334                                         =0x16AF;
    MPC_FRNTCAMBLI_334                                           =0x16B0;
    MPC_HMASETFB_334                                             =0x16B1;
    MPC_FRNTCAMFLT_334                                           =0x16B2;
    ADAS_LELINECOLOR_340                                         =0x16BA;
    ADAS_RILINECOLOR_340                                         =0x16BB;
    ADAS_INTEGTCRSSWTFB_340                                      =0x16C1;
    ADAS_FLTINDCR_340                                            =0x16C4;
    ADAS_INTECNFLTTXT_340                                        =0x16C6;
    CMRR_RL_STS_LCA_338                                          =0x16C9;
    CMRR_RL_LEWARN_DOW_338                                       =0x16CB;
    CMRR_RL_LEWARN_RCTA_338                                      =0x16D0;
    MPC_CMRR_FR_RIWARN_FCTA_243                                  =0x16DE;
    MPC_CMRR_FL_LEWARN_FCTA_243                                  =0x16E2;
    TBOX_RESVACCHRGOPENSTS_4F4                                   =0x16FF;
    TBOX_RESVACCHRGSTRTTI_HR_62E                                 =0x1704;
    TBOX_RESVACCHRGSTRTTI_MINS_62E                               =0x1706;
    TBOX_MONRESVACCHRGREPSTRTSET_62E                             =0x1707;
    TBOX_RESVACCHRGENDTI_HR_62E                                  =0x170E;
    TBOX_RESVACCHRGENDTI_MINS_62E                                =0x170F;
    TBOX_RESVACCHRGENDSET_62E                                    =0x1710;
    TBOX_MONRESVACCHRGREPENDSET_62E                              =0x1711;
    HUD_SWT_562                                                  =0x1718;
    HUD_CRTSYSSTS_562                                            =0x1719;
    HUD_ILLADJ_562                                               =0x171A;
    HUD_HEIADJ_562                                               =0x171B;
    HUD_MODSWT_562                                               =0x171C;
    HUD_SNOWMODSWTSTS_562                                        =0x171D;
    HUD_CRTLANGUAGE_562                                          =0x171E;
    TBOX_CRTTI_DAY_62F                                           =0x171F;
    TBOX_CRTTI_HR_62F                                            =0x1720;
    TBOX_CRTTI_MINS_62F                                          =0x1721;
    TBOX_CRTTI_YR_62F                                            =0x1722;
    TBOX_CRTTI_MTH_62F                                           =0x1723;
    TBOX_CRTTI_SEC_62F                                           =0x1724;
    MCU_R_ALRMLAMP_FS_151                                        =0x1745;
    ESM_SPOILERMODSTS_30C                                        =0x174E;
    ESM_SPOILERMOVEMENTSTSFB_30C                                 =0x1750;
    ESM_SPOILERCRLSTSFB_30C                                      =0x1751;
    ESM_SPOILERWELCOMEFUNSETSTS_30C                              =0x1752;
    ICC_EPSMODREQ_3C6                                            =0x175D;
    ICC_BRAKEEGYRECOVINTENREQ_3C6                                =0x175E;
    ICC_AVHSWTSIG_3C6                                            =0x175F;
    ICC_DRVGMODREQ_3C6                                           =0x1760;
    ICC_AFONSTS_3C6                                              =0x1761;
    ICC_AFCHANNELSET_3C6                                         =0x1762;
    ICC_AFCONCENTRATION_3C6                                      =0x1763;
    ICC_WASHCARSWT_3C6                                           =0x1764;
    ICC_SPOILERMODSWT_3C6                                        =0x1765;
    ICC_SPOILERWELCOMEFUNSWT_3C6                                 =0x1766;
    ICC_WORMSTS_3C6                                              =0x1767;
    ICC_ACCEMODREQ_3C6                                           =0x1768;
    ICC_PARKCHARGEST_3C6                                         =0x176A;
    AMP_BALSETSTS_49                                             =0x1771;
    AMP_FADERSETSTS_49                                           =0x1774;
    AMP_IESSMODSTS_49                                            =0x1776;
    AMP_LOFRQAUDIOSETSTS_49                                      =0x1777;
    AMP_MIDFRQAUDIOSETSTS_49                                     =0x1779;
    AMP_HIFRQAUDIOSETSTS_49                                      =0x177B;
    AMP_HFTVOLSETSTS_49                                          =0x177D;
    AMP_NAVVOLSETSTS_49                                          =0x177F;
    AMP_MAIVOLSETSTS_49                                          =0x1780;
    ICC_LBADJUSTSET_3A8                                          =0x1788;
    SLC_LBADJUSTSTS_3A7                                          =0x1794;
    SLC_CONTRASTCOLORSTS6_3A7                                    =0x1795;
    SLC_CONTRASTCOLORSTS4_3A7                                    =0x1796;
    SLC_CONTRASTCOLORSTS_3A7                                     =0x1797;
    SLC_AUTOCOLORSTS1_3A7                                        =0x179B;
    ICC_SCRNBRILESET_327                                         =0x179C;
    ICC_SCRNBRIAUTOSET_327                                       =0x179D;
    ICC_HUMAISPCHSTS_327                                         =0x179E;
    ICC_MUSICFRQ1_31B                                            =0x179F;
    ICC_MUSICFRQ2_31B                                            =0x17A0;
    ICC_MUSICFRQ3_31B                                            =0x17A1;
    ICC_MUSICFRQ4_31B                                            =0x17A2;
    ICC_MUSICFRQ5_31B                                            =0x17A3;
    ICC_MUSICFRQ6_31B                                            =0x17A4;
    ICC_MUSICFRQ7_31B                                            =0x17A5;
    ICC_MUSICFRQ8_31B                                            =0x17A6;
    ICC_ALCBRIBREAMODSWT_3ED                                     =0x17A8;
    ICC_ALCCLRBREAMODSWT2_3ED                                    =0x17AA;
    ICC_ALCMUSICRHYMODSWT_3ED                                    =0x17AB;
    ICC_ALCSPDRHYMODSWT_3ED                                      =0x17AD;
    ICC_ALCSPCHSWT_3ED                                           =0x17AE;
    ICC_ALCACMODSWT_3ED                                          =0x17AF;
    ICC_ALCDOWMODSWT_3ED                                         =0x17B0;
    ICC_ALCDRVMODRHYSWT_3ED                                      =0x17B1;
    ICC_ALCMOBCHAREMDMODSWT_3ED                                  =0x17B2;
    ICC_ALCWELMODSWT_3ED                                         =0x17B3;
    ICC_VEHMDMESTS_3ED                                           =0x17B4;
    ICC_APPLIANCECLSLVL_3ED                                      =0x17B5;
    ICC_SCRNBRILESET_DUALAREA_3ED                                =0x17B6;
    ICC_AIRVETN_AL_SWT_3ED                                       =0x17B7;
    ICC_ALCDUALAREAMODSWT_3ED                                    =0x17B8;
    DSMC_PASSEATTRACKPOSN_3AA                                    =0x17C0;
    DSMC_PASSEATBACKPOSN_3AA                                     =0x17C2;
    DSMC_PASSSEATMEMRECALLFB_66                                  =0x17C4;
    DSMC_PASSSEATMEMDATAUPDFB_66                                 =0x17C5;
    DSMC_PASSSEATTRACKSWTSTS_328                                 =0x17C6;
    DSMC_PASSSEATBACKSWTSTS_328                                  =0x17C7;
    DSMC_PASSSEATHEATGSTS_512                                    =0x17CB;
    DSMC_PASSSEATVENTNSTS_512                                    =0x17CC;
    DSMC_PASSSEATTRACKADJSTS_512                                 =0x17CD;
    DSMC_PASSSEATBACKADJSTS_512                                  =0x17CE;
    DSMC_RRSEATHEATSTS_512                                       =0x17CF;
    DSMC_PASSLUMBARUPDSTS_512                                    =0x17D1;
    DSMC_PASSLUMBARDWNSTS_512                                    =0x17D2;
    DSMC_PASSLUMBARFWDSTS_512                                    =0x17D3;
    DSMC_PASSLUMBARBACKWSTS_512                                  =0x17D4;
    DSMC_SECROWSEATWELFCTSETFB_512                               =0x17D7;
    DSMC_MASFR_SEATMASMOD_512                                    =0x17D9;
    DSMC_MASFR_SEATMASGRADESTS_512                               =0x17DA;
    VCU_CHRGGUNANTITHFTOPENSTS_49C                               =0x17E4;
    VCU_EHUCHRGDCHAREQ_49C                                       =0x17E6;
    VCU_CHRGDCHABTNREQ_49C                                       =0x17E7;
    BMS_CHRGSTSDISP_49D                                          =0x17E8;
    EPS_MODSTS_475                                               =0x17E9;
    BMS_BATTLOWTEMPIND_3C3                                       =0x17EA;
    BMS_RMNGCHRGTIDISPLY_29F                                     =0x17EB;
    EHU_DRVRSEATTRACKPERCENTREQ_68                               =0x17EC;
    EHU_DRVRSEATTILTPERCENTREQ_68                                =0x17ED;
    EHU_DRVRHEIPERCENTREQ_68                                     =0x17EE;
    EHU_DRVRSEATBACKPERCENTREQ_68                                =0x17EF;
    EHU_PASSSEATTRACKPERCENTREQ_68                               =0x17F0;
    EHU_PASSSEATTILTPERCENTREQ_68                                =0x17F1;
    EHU_PASSHEIPERCENTREQ_68                                     =0x17F2;
    EHU_PASSSEATBACKPERCENTREQ_68                                =0x17F3;
    EHU_MIRRADJUP_3AC                                            =0x17F4;
    EHU_MIRRADJDOWN_3AC                                          =0x17F5;
    EHU_MIRRADJLEF_3AC                                           =0x17F6;
    EHU_MIRRADJRI_3AC                                            =0x17F7;
    EHU_LEREMIRRADJCMD_3AC                                       =0x17F8;
    EHU_DRVRMIRRTURNDWNALLWD_3AC                                 =0x17F9;
    ICC_EXHIBCARMODNOTICEFLAG_3AC                                =0x17FA;
    VCU_DRVMODSIG_102                                            =0x17FD;
    VCU_ACLRTIREQ_102                                            =0x17FF;
    VCU_SPORTMODACCTI_3A2                                        =0x1800;
    VCU_COMFORTMODACCTI_3A2                                      =0x1801;
    VCU_ECOMODACCTI_3A2                                          =0x1802;
    VCU_ONEPEDALMODACCTI_3A2                                     =0x1803;
    VCU_PERSONALMODACCTI_3A2                                     =0x1804;
    VCU_DRVSTYLEFACTOR_3A2                                       =0x1805;
    VCU_DRVSTYLE_3A2                                             =0x1806;
    VCU_BRKEGYRECOVINTENSTS_3E8                                  =0x1808;
    VCU_WORMSTS_3E8                                              =0x1809;
    VCU_ACLRMODSTS_3E8                                           =0x180A;
    VCU_CHRGPORTENAFLG_3AB                                       =0x180B;
    VCU_OPENCLSFLTINFODISP_3AB                                   =0x180C;
    VCU_VEHCURDISCHRGENDMILE_3AB                                 =0x180D;
    VCU_CHRGPORTDOORPOSST_3AB                                    =0x180E;
    ICM_PHNMSGSTS_587                                            =0x1810;
    ICM_PHNMSGCALLINGTIMEH_587                                   =0x1811;
    ICM_PHNMSGCALLINGTIMEM_587                                   =0x1812;
    ICM_PHNMSGCALLINGTIMES_587                                   =0x1813;
    ICC_MODSWT_HUD_3B3                                           =0x1817;
    ICC_CMD_VOICECONTROLHUD_3B3                                  =0x1818;
    ICC_TRIPARESETFLG_3B3                                        =0x181A;
    ICC_TRIPBRESETFLG_3B3                                        =0x181B;
    BCM_EEMINFOREQQUIESCENTCRT_3A9                               =0x1844;
    BCM_EEMFIMINSAVELEMOD_3A9                                    =0x1845;
    BCM_VEVMMENUMVEHMDME_3A9                                     =0x1846;
    VCU_CARWASHMODENA_50B                                        =0x1847;
    VCU_CARWASHMODSTS_50B                                        =0x1848;
    MFS_INCFOLWDST_514                                           =0x1849;
    MFS_DECFOLWDST_514                                           =0x184A;
    MFS_CUSTBTN_514                                              =0x184B;
    MFS_PARKAID_514                                              =0x184C;
    MFS_LEROLLUP_514                                             =0x184D;
    MFS_LEROLLDWN_514                                            =0x184E;
    MFS_LEROLLPRESS_514                                          =0x184F;
    MFS_SRCSWTBTN_514                                            =0x1850;
    MFS_VOICERCTCNBTN_514                                        =0x1851;
    MFS_PREVSONGTUNESIG_514                                      =0x1852;
    MFS_NEXTSONGTUNESIG_514                                      =0x1853;
    MFS_RIROLLUP_514                                             =0x1854;
    MFS_RIROLLDWN_514                                            =0x1855;
    MFS_RIROLLPRESS_514                                          =0x1856;
    MFS_STEERWHLHEATGINDCRLAMPSTS_514                            =0x1857;
    EUM_LECHILDLOCKSTS_309                                       =0x1864;
    EUM_RICHILDLOCKSTS_309                                       =0x1865;
    ECC_AFU_SWSTS_4FC                                            =0x186A;
    ECC_AFU_CHANNELSET_4FC                                       =0x186B;
    ECC_AFU_CONCENTRATION_4FC                                    =0x186C;
    ECC_AFU_CHANNELCHGSTS_4FC                                    =0x186E;
    ECC_AFU_CH1STS_4FC                                           =0x186F;
    ECC_AFU_CH2STS_4FC                                           =0x1870;
    ECC_AFU_CH3STS_4FC                                           =0x1871;
    ECC_AFU_CH1LEVSTS_4FC                                        =0x1872;
    ECC_AFU_CH2LEVSTS_4FC                                        =0x1873;
    ECC_AFU_CH3LEVSTS_4FC                                        =0x1874;
    ECC_AFU_CH1EXPIRATIONREMINDER_4FC                            =0x1875;
    ECC_AFU_CH2EXPIRATIONREMINDER_4FC                            =0x1876;
    ECC_AFU_CH3EXPIRATIONREMINDER_4FC                            =0x1877;
    ICC_CSTSWTSIG_51A                                            =0x187B;
    ICC_PWROFFREQ_51A                                            =0x1881;
    ICC_PREHEATREQ_51A                                           =0x1882;
    VCU_SOKTSPLYINTERACTIVESTS_4DC                               =0x1883;
    VCU_SOKTFUNOPERPROMT_EHU_4DC                                 =0x1885;
    VCU_EXHIBCARMODSIG_219                                       =0x1888;
    ICC_SETACCHRGCURTLIMIT_37C                                   =0x1889;
    EHU_CTRLSTS_PWC_4E2                                          =0x1895;
    BCM_BACKGNDBRILVL_3A3                                        =0x189E;
    VCU_PETSMODREQFLG_3EE                                        =0x18A6;
    VCU_PWRANTITHEFT_3EE                                         =0x18A7;
    ICC_LOCKSOUNDPROMPTSWT_91                                    =0x18A9;
    ICC_LFWINCRL_91                                              =0x18AA;
    ICC_RFWINCRL_91                                              =0x18AB;
    ICC_LRWINCRL_91                                              =0x18AC;
    ICC_RRWINCRL_91                                              =0x18AD;
    ICC_OPENDOORLAMPLANGUAGE_91                                  =0x18AF;
    ICC_LECHILDLOCKSTS_91                                        =0x18B0;
    ICC_RICHILDLOCKSTS_91                                        =0x18B1;
    ICC_SMARTOPENTRUNK_91                                        =0x18B2;
    VCU_CHRGMODFUNSTS_3BD                                        =0x18B3;
    APM_LRLOCKWINSTS_3E0                                         =0x18B4;
    APM_RRLOCKWINSTS_3E0                                         =0x18B5;
    APM_LFWINPOSFB_3E0                                           =0x18B6;
    APM_RFWINPOSFB_3E0                                           =0x18B7;
    APM_LRWINPOSFB_3E0                                           =0x18B8;
    APM_RRWINPOSFB_3E0                                           =0x18B9;
    VCU_CHRGSTOPSOCPLANVAL_51C                                   =0x18BF;
    AVAP_SENTISNVTYSETSTS_4E7                                    =0x18E9;
    ICC_ALCCLRCSTMSET_BA                                         =0x196B;
    ICC_ALCADJ_COLOR1SET_69                                      =0x196D;
    ICC_ALCADJ_COLOR2SET_69                                      =0x196E;
    SLC_ALCSTS_510                                               =0x196F;
    SLC_SCRNBRIAUTOSTS_510                                       =0x1970;
    SLC_COLORCSTMSTS_510                                         =0x1971;
    SLC_ALCCRTADJ_COLOR1_510                                     =0x1974;
    SLC_ALCCRTADJ_COLOR2_510                                     =0x1975;
    SLC_AIRVETN_AL_STS_510                                       =0x1976;
    SLC_ALCDUALAREAMODSTS_510                                    =0x1977;
    SLC_TURNLAMPMODSTS_510                                       =0x1979;
    ECC_UVCSTS_3F7                                               =0x197B;
    HUD_ARPARA1FB1_6B                                            =0x197F;
    HUD_ARPARA1FB2_6B                                            =0x1980;
    HUD_ARPARA1FB3_6B                                            =0x1981;
    HUD_ARPARA1FB4_6B                                            =0x1982;
    HUD_ARPARA1FB5_6B                                            =0x1983;
    HUD_ARPARA1FB6_6B                                            =0x1984;
    HUD_ARPARA1FB7_6B                                            =0x1985;
    HUD_ARPARA1FB8_6B                                            =0x1986;
    HUD_ARPARA1FB9_6B                                            =0x1987;
    HUD_ARPARA1FB10_6B                                           =0x1988;
    HUD_ARPARA1FB11_6B                                           =0x1989;
    HUD_ARPARA1FB12_6B                                           =0x198A;
    HUD_ARPARA1FB13_6B                                           =0x198B;
    HUD_ARPARA1FB14_6B                                           =0x198C;
    HUD_ARPARA1FB15_6B                                           =0x198D;
    HUD_ARPARA1FB16_6B                                           =0x198E;
    HUD_ARPARA2FB1_6B                                            =0x198F;
    HUD_ARPARA2FB2_6B                                            =0x1990;
    HUD_ARPARA2FB3_6B                                            =0x1991;
    HUD_ARPARA2FB4_6B                                            =0x1992;
    HUD_ARPARA2FB5_6B                                            =0x1993;
    HUD_ARPARA2FB6_6B                                            =0x1994;
    HUD_ARPARA2FB7_6B                                            =0x1995;
    HUD_ARPARA2FB8_6B                                            =0x1996;
    HUD_ARPARA2FB9_6B                                            =0x1997;
    HUD_ARPARA2FB10_6B                                           =0x1998;
    HUD_ARPARA2FB11_6B                                           =0x1999;
    HUD_ARPARA2FB12_6B                                           =0x199A;
    HUD_ARPARA2FB13_6B                                           =0x199B;
    HUD_ARPARA2FB14_6B                                           =0x199C;
    HUD_ARPARA2FB15_6B                                           =0x199D;
    HUD_ARPARA2FB16_6B                                           =0x199E;
    HUD_ARPARA3FB1_6C                                            =0x199F;
    HUD_ARPARA3FB2_6C                                            =0x19A0;
    HUD_ARPARA3FB3_6C                                            =0x19A1;
    HUD_ARPARA3FB4_6C                                            =0x19A2;
    HUD_ARPARA3FB5_6C                                            =0x19A3;
    HUD_ARPARA3FB6_6C                                            =0x19A4;
    HUD_ARPARA3FB7_6C                                            =0x19A5;
    HUD_ARPARA3FB8_6C                                            =0x19A6;
    HUD_ARPARA3FB9_6C                                            =0x19A7;
    HUD_ARPARA3FB10_6C                                           =0x19A8;
    HUD_ARPARA3FB11_6C                                           =0x19A9;
    HUD_ARPARA3FB12_6C                                           =0x19AA;
    HUD_ARPARA3FB13_6C                                           =0x19AB;
    HUD_ARPARA3FB14_6C                                           =0x19AC;
    HUD_ARPARA3FB15_6C                                           =0x19AD;
    HUD_ARPARA3FB16_6C                                           =0x19AE;
    HUD_ARPARA4FB1_6C                                            =0x19AF;
    HUD_ARPARA4FB2_6C                                            =0x19B0;
    HUD_ARPARA4FB3_6C                                            =0x19B1;
    HUD_ARPARA4FB4_6C                                            =0x19B2;
    HUD_ARPARA4FB5_6C                                            =0x19B3;
    HUD_ARPARA4FB6_6C                                            =0x19B4;
    HUD_ARPARA4FB7_6C                                            =0x19B5;
    HUD_ARPARA4FB8_6C                                            =0x19B6;
    ICC_ARPARAFB1_49A                                            =0x19B7;
    ICC_ARPARAFB2_49A                                            =0x19B8;
    ICC_ARPARAFB3_49A                                            =0x19B9;
    ICC_ARPARAFB4_49A                                            =0x19BA;
    ICC_LOGOLIGSWT_49A                                           =0x19BB;
    ICC_CONTRASTCOLORSWT1_49A                                    =0x19BC;
    ICC_CONTRASTCOLORSWT2_49A                                    =0x19BD;
    ICC_CONTRASTCOLORSWT3_49A                                    =0x19BE;
    BMS_MAXLOADPWR_5BF                                           =0x19BF;
    VCU_BATTHEATGMNGBSDMAP_616                                   =0x19C0;
    BCM_OPENDOORLAMPLANGUAGESWTSTS_311                           =0x19DA;
    BCM_LOCKSOUNDPROMPTSWT_311                                   =0x19DB;
    BCM_SMARTOPENTRUNK_311                                       =0x19DC;
    RSM_SSMOVEMENT_311                                           =0x19DD;
    RSM_SSPOSPECR_311                                            =0x19DE;
    BCM_LOCKAUTOCLSSUNSSWTFB_311                                 =0x19DF;
    VCU_DRVGMILGDISPPERC_3B6                                     =0x1A69;
    VCU_DRRANGDISPERCENT_HIGPRECDISPLAYREQUIRE_3B6               =0x1A6B;
    VCU_DRRANGDISPERCENT_HIGPREC_3B6                             =0x1A6C;
    VCU_EHUPWROFFENA_50E                                         =0x1A71;
    CPD_CHILDCALLASWTSTS_3B2                                     =0x1AD6;
    PDCU_EXHIBCARMODNOTICE_509                                   =0x1B22;
    PDCU_EXHIBCARMODTEXT_509                                     =0x1B23;
    VCU_EXHIBCARMODDISABLENOTICE_509                             =0x1B24;
    BCM_EXHIBCARMODDISABLENOTICE_3DE                             =0x1B26;
    TBOX_EXHIBCARMODDISABLENOTICE_3FF                            =0x1B27;
    AVAP_SENTRYMODTI1VLD_547                                     =0x1B28;
    AVAP_SENTRYMODTI2VLD_547                                     =0x1B29;
    AVAP_SENTRYMODSTRTTI1_547                                    =0x1B2A;
    AVAP_SENTRYMODDATE_547                                       =0x1B2B;
    AVAP_SENTRYMODSTRTTI2_547                                    =0x1B2C;
    AVAP_SENTRYMODENDTI1_547                                     =0x1B2D;
    AVAP_SENTRYMODENDTI2_547                                     =0x1B2E;
    AVAP_SENTRYMODSTSFB_549                                      =0x1B2F;
    AVAP_SENTRYMODALRM_549                                       =0x1B33;
    ICC_SENTRYMODTI1VLD_61                                       =0x1B36;
    ICC_SENTRYMODTI2VLD_61                                       =0x1B37;
    ICC_SENTRYMODSTRTTI1_61                                      =0x1B38;
    ICC_SENTRYMODDATE_61                                         =0x1B39;
    ICC_SENTRYMODSTRTTI2_61                                      =0x1B3A;
    ICC_SENTRYMODENDTI1_61                                       =0x1B3B;
    ICC_SENTRYMODENDTI2_61                                       =0x1B3C;
    ICC_SENTRYMODSW_63                                           =0x1B3D;
    ICC_SENTRYALRMVIDEORXCFM_63                                  =0x1B41;
    ICC_SENTISNVTYSET_63                                         =0x1B42;
    TBOX_SENTRYMODSTRTTI1_B6                                     =0x1B45;
    TBOX_SENTRYMODDATE_B6                                        =0x1B46;
    TBOX_SENTRYMODSTRTTI2_B6                                     =0x1B47;
    TBOX_SENTRYMODENDTI1_B6                                      =0x1B48;
    TBOX_SENTRYMODENDTI2_B6                                      =0x1B49;
    TBOX_SENTRYMODSW_B7                                          =0x1B4A;
    VCU_FSTEXTTIP_5A9                                            =0x1B4B;
    PDCUFUELLEVELDISP_5A9                                        =0x1B4C;
    PDCUFUMILGVLD_5A9                                            =0x1B4D;
    PDCUFUMILGE_5A9                                              =0x1B4E;
    BCM_DRLSWTSTS_51F                                            =0x1B4F;
    PDCUCHKENGLAMP_31F                                           =0x1B50;
    PDCURNGLIM_31F                                               =0x1B51;
    PDCUENRGMOD_3D0                                              =0x1B52;
    EMSOIL_PFAULT_3D0                                            =0x1B53;
    INDMODEGYFBDIS_3D0                                           =0x1B54;
    INDMODACCMODDIS_3D0                                          =0x1B55;
    INDMODSTEEMODDIS_3D0                                         =0x1B56;
    INDMODCREMODDIS_3D0                                          =0x1B57;
    PDCUPARKCHARGEST_3D0                                         =0x1B59;
    PDCUBMSTEMPLOWDRVLIMITHINT_3D0                               =0x1B5A;
    VCU_OBC_REMIND_LAMP_54B                                      =0x1B61;
    EHU_CONTEXTUALMODE_59C                                       =0x1B64;
    TBOXSENTISNVTYSET_B6                                         =0x1B6F;
    ISGSYSTFLTDISP_21C                                           =0x1B70;
    ISGMTRTTEMPFLTDISP_21C                                       =0x1B71;
    ISGINVTEMPFLTDISP_21C                                        =0x1B72;
    VCU_ABNORCHARGPORTALERT_3BC                                  =0x1B79;
    ICC_100KMAVRGPWRCNSPTN_AB_542                                =0x1B7A;
    ICC_10KMAVRGPWRCNSPTN_AB_542                                 =0x1B7B;
    ICC_100KMAVRGPWRCNSPTN_AS_542                                =0x1B7D;
    VCULASTCHRGTRIPAVRGPWRCNSPTN_AS_3B8                          =0x1B7E;
    VCU_TRIPAAVRGPWRCNSPTN_AS_3B8                                =0x1B7F;
    VCU_TRIPBAVRGPWRCNSPTN_AS_3B8                                =0x1B80;
    VCU_100KMREMDRVGRNG_AB_AS_3B8                                =0x1B83;
    VCU_PWRCNSPTNDIAG_AB_AS_3B8                                  =0x1B84;
    PDCUENRGMODREJHINT_AS_3B8                                    =0x1B93;
    VCU_PARKING_REMIND_PGEAR_AB_AS_3B8                           =0x1B94;
    PDCUHYDOPERMOD_AS_3B8                                        =0x1B95;
    PDCUENRGMODFLTHINT_AS_3B8                                    =0x1B96;
    PDCUIDLEMODST_AS_3B8                                         =0x1B97;
    PDCUEMISSTESTMODST_AS_3B8                                    =0x1B9A;
    PDCUENGSYSTWARN_AS_3B8                                       =0x1B9B;
    PDCUEVST_AS_3B8                                              =0x1B9C;
    PDCUREFSWITCHSTS_AS_3B8                                      =0x1B9D;
    PDCUREFUNOTALLWD_AS_3B8                                      =0x1B9E;
    PDCUFUFILRDOORRMN_AS_3B8                                     =0x1B9F;
    PDCUFUTANKRELSPROGS_AS_3B8                                   =0x1BA0;
    HDCU_WASHMODPROMPTSIG_AB_AS_3B8                              =0x1BA2;
    VCULASTCHRGTRIPAVRGPWRCNSPTN_AB_3B8                          =0x1BA3;
    VCU_TRIPAAVRGPWRCNSPTN_AB_3B8                                =0x1BA4;
    VCU_TRIPBAVRGPWRCNSPTN_AB_3B8                                =0x1BA5;
    VCU_10KMREMDRVGRNG_AB_3B8                                    =0x1BA6;
    VCU_SUBTOLEGYCNSE_AB_AS_3B8                                  =0x1BA7;
    VCU_ECCCNSEEGY_AS_56B                                        =0x1BA8;
    VCU_VEHDRVCNSEEGY_AS_56B                                     =0x1BA9;
    VCU_BHMCNSEEGY_AS_56B                                        =0x1BAA;
    VCU_VEHDRVCNSEEGY_AB_56B                                     =0x1BB3;

}
