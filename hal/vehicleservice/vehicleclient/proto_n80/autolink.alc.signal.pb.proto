/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
syntax = "proto2";

package autolink.alc.signal.pb;

// the following SID can be subscribed
enum SignalIdSoc {
    ICMDISPTOTDST_2E0                                            =0x102B;
    ICMDISPTOTDSTVLD_2E0                                         =0x102D;
    ODOOFFS_2E0                                                  =0x1034;
    ODOOFFSVLD_2E0                                               =0x1035;
    ODOTICKVAL_2E0                                               =0x1036;
    ODORSTCNTR_2E0                                               =0x1037;
    ICCEXTRLAMPSWT_321                                           =0x1067;
    REFOGLIREQ_321                                               =0x106B;
    VHLSPDODOCNT_185                                             =0x10E2;
    VHLSPDODOCNTVLD_185                                          =0x10E3;
    ICCACTSET3_377                                               =0x11C2;
    ICCACBLOWRLELSET2_377                                        =0x11C3;
    ICCACBLOWRLELSET3_377                                        =0x11C4;
    ICCACAIRDISTBNSET1_L_377                                     =0x11CA;
    ICCACAIRDISTBNSET2_377                                       =0x11CB;
    ICCACAIRDISTBNSET3_377                                       =0x11CC;
    ICCACONOFFSWT1_377                                           =0x11CD;
    ICCACONOFFSWT2_377                                           =0x11CE;
    ICCACONOFFSWT3_377                                           =0x11CF;
    ICCACACSWT_377                                               =0x11D0;
    ICCACAUTOSWT1_377                                            =0x11D1;
    ICCACDEFRSTSWT_377                                           =0x11D2;
    ICCACAIRRECIRCSWT_377                                        =0x11D3;
    ICCACBLOWERLESET1_377                                        =0x11D7;
    ICCACTSET1_L_377                                             =0x11D8;
    ICCACTSET2_377                                               =0x11D9;
    ICC_ECO_BTN_STS_377                                          =0x11E1;
    ICCACAIRDISTBNSET1_R_377                                     =0x11E2;
    ICCACTSET1_R_377                                             =0x11E3;
    ACONOFFSTSDISP1_271                                          =0x132C;
    ACONOFFSTSDISP2_271                                          =0x132D;
    ACONOFFSTSDISP3_271                                          =0x132E;
    ACBLOWRLELDISP1_271                                          =0x132F;
    ACBLOWRLELDISP3_271                                          =0x1330;
    ACBLOWRLELDISP5_271                                          =0x1331;
    ACLETDISP1_271                                               =0x1332;
    ACRITDISP1_271                                               =0x1333;
    ACLETDISP2_271                                               =0x1334;
    ACLETDISP3_271                                               =0x1335;
    ACLEAIRDISTBNDISP1_271                                       =0x1336;
    ACRIAIRDISTBNDISP1_271                                       =0x1337;
    ACLEAIRDISTBNDISP2_271                                       =0x1338;
    ACLEAIRDISTBNDISP3_271                                       =0x1339;
    ACDEFRSTDISP_271                                             =0x133A;
    ACRECIRCDISP_271                                             =0x133B;
    ACLEAUTODISP1_271                                            =0x133C;
    ACACDISP_271                                                 =0x133F;
    ACECODISP_271                                                =0x1342;
    BDCEXTRLAMPSTS_2D2                                           =0x1494;
    REFOGLAMPSWT_2D2                                             =0x149B;
    BCMODOTOTDST_2F1                                             =0x14A6;
    BCMODOOFFS_2F1                                               =0x14A7;
    BCMODOTOTDSTVLD_2F1                                          =0x14A8;
    BCMODORSTCNTR_2F1                                            =0x14A9;

}
