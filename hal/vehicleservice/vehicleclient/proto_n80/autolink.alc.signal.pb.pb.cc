// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: autolink.alc.signal.pb.proto

#include "autolink.alc.signal.pb.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace autolink {
namespace alc {
namespace signal {
namespace pb {
}  // namespace pb
}  // namespace signal
}  // namespace alc
}  // namespace autolink
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_autolink_2ealc_2esignal_2epb_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_autolink_2ealc_2esignal_2epb_2eproto = nullptr;
const uint32_t TableStruct_autolink_2ealc_2esignal_2epb_2eproto::offsets[1] = {};
static constexpr ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema* schemas = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::Message* const* file_default_instances = nullptr;

const char descriptor_table_protodef_autolink_2ealc_2esignal_2epb_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\034autolink.alc.signal.pb.proto\022\026autolink"
  ".alc.signal.pb*\272\n\n\013SignalIdSoc\022\026\n\021ICMDIS"
  "PTOTDST_2E0\020\253 \022\031\n\024ICMDISPTOTDSTVLD_2E0\020\255"
  " \022\020\n\013ODOOFFS_2E0\020\264 \022\023\n\016ODOOFFSVLD_2E0\020\265 "
  "\022\023\n\016ODOTICKVAL_2E0\020\266 \022\023\n\016ODORSTCNTR_2E0\020"
  "\267 \022\027\n\022ICCEXTRLAMPSWT_321\020\347 \022\023\n\016REFOGLIRE"
  "Q_321\020\353 \022\025\n\020VHLSPDODOCNT_185\020\342!\022\030\n\023VHLSP"
  "DODOCNTVLD_185\020\343!\022\023\n\016ICCACTSET3_377\020\302#\022\032"
  "\n\025ICCACBLOWRLELSET2_377\020\303#\022\032\n\025ICCACBLOWR"
  "LELSET3_377\020\304#\022\035\n\030ICCACAIRDISTBNSET1_L_3"
  "77\020\312#\022\033\n\026ICCACAIRDISTBNSET2_377\020\313#\022\033\n\026IC"
  "CACAIRDISTBNSET3_377\020\314#\022\027\n\022ICCACONOFFSWT"
  "1_377\020\315#\022\027\n\022ICCACONOFFSWT2_377\020\316#\022\027\n\022ICC"
  "ACONOFFSWT3_377\020\317#\022\023\n\016ICCACACSWT_377\020\320#\022"
  "\026\n\021ICCACAUTOSWT1_377\020\321#\022\027\n\022ICCACDEFRSTSW"
  "T_377\020\322#\022\032\n\025ICCACAIRRECIRCSWT_377\020\323#\022\032\n\025"
  "ICCACBLOWERLESET1_377\020\327#\022\025\n\020ICCACTSET1_L"
  "_377\020\330#\022\023\n\016ICCACTSET2_377\020\331#\022\030\n\023ICC_ECO_"
  "BTN_STS_377\020\341#\022\035\n\030ICCACAIRDISTBNSET1_R_3"
  "77\020\342#\022\025\n\020ICCACTSET1_R_377\020\343#\022\030\n\023ACONOFFS"
  "TSDISP1_271\020\254&\022\030\n\023ACONOFFSTSDISP2_271\020\255&"
  "\022\030\n\023ACONOFFSTSDISP3_271\020\256&\022\030\n\023ACBLOWRLEL"
  "DISP1_271\020\257&\022\030\n\023ACBLOWRLELDISP3_271\020\260&\022\030"
  "\n\023ACBLOWRLELDISP5_271\020\261&\022\023\n\016ACLETDISP1_2"
  "71\020\262&\022\023\n\016ACRITDISP1_271\020\263&\022\023\n\016ACLETDISP2"
  "_271\020\264&\022\023\n\016ACLETDISP3_271\020\265&\022\033\n\026ACLEAIRD"
  "ISTBNDISP1_271\020\266&\022\033\n\026ACRIAIRDISTBNDISP1_"
  "271\020\267&\022\033\n\026ACLEAIRDISTBNDISP2_271\020\270&\022\033\n\026A"
  "CLEAIRDISTBNDISP3_271\020\271&\022\025\n\020ACDEFRSTDISP"
  "_271\020\272&\022\025\n\020ACRECIRCDISP_271\020\273&\022\026\n\021ACLEAU"
  "TODISP1_271\020\274&\022\021\n\014ACACDISP_271\020\277&\022\022\n\rACE"
  "CODISP_271\020\302&\022\027\n\022BDCEXTRLAMPSTS_2D2\020\224)\022\025"
  "\n\020REFOGLAMPSWT_2D2\020\233)\022\025\n\020BCMODOTOTDST_2F"
  "1\020\246)\022\023\n\016BCMODOOFFS_2F1\020\247)\022\030\n\023BCMODOTOTDS"
  "TVLD_2F1\020\250)\022\026\n\021BCMODORSTCNTR_2F1\020\251)"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_autolink_2ealc_2esignal_2epb_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_autolink_2ealc_2esignal_2epb_2eproto = {
  false, false, 1395, descriptor_table_protodef_autolink_2ealc_2esignal_2epb_2eproto, "autolink.alc.signal.pb.proto", 
  &descriptor_table_autolink_2ealc_2esignal_2epb_2eproto_once, nullptr, 0, 0,
  schemas, file_default_instances, TableStruct_autolink_2ealc_2esignal_2epb_2eproto::offsets,
  nullptr, file_level_enum_descriptors_autolink_2ealc_2esignal_2epb_2eproto, file_level_service_descriptors_autolink_2ealc_2esignal_2epb_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_autolink_2ealc_2esignal_2epb_2eproto_getter() {
  return &descriptor_table_autolink_2ealc_2esignal_2epb_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_autolink_2ealc_2esignal_2epb_2eproto(&descriptor_table_autolink_2ealc_2esignal_2epb_2eproto);
namespace autolink {
namespace alc {
namespace signal {
namespace pb {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SignalIdSoc_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_autolink_2ealc_2esignal_2epb_2eproto);
  return file_level_enum_descriptors_autolink_2ealc_2esignal_2epb_2eproto[0];
}
bool SignalIdSoc_IsValid(int value) {
  switch (value) {
    case 4139:
    case 4141:
    case 4148:
    case 4149:
    case 4150:
    case 4151:
    case 4199:
    case 4203:
    case 4322:
    case 4323:
    case 4546:
    case 4547:
    case 4548:
    case 4554:
    case 4555:
    case 4556:
    case 4557:
    case 4558:
    case 4559:
    case 4560:
    case 4561:
    case 4562:
    case 4563:
    case 4567:
    case 4568:
    case 4569:
    case 4577:
    case 4578:
    case 4579:
    case 4908:
    case 4909:
    case 4910:
    case 4911:
    case 4912:
    case 4913:
    case 4914:
    case 4915:
    case 4916:
    case 4917:
    case 4918:
    case 4919:
    case 4920:
    case 4921:
    case 4922:
    case 4923:
    case 4924:
    case 4927:
    case 4930:
    case 5268:
    case 5275:
    case 5286:
    case 5287:
    case 5288:
    case 5289:
      return true;
    default:
      return false;
  }
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace pb
}  // namespace signal
}  // namespace alc
}  // namespace autolink
PROTOBUF_NAMESPACE_OPEN
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
