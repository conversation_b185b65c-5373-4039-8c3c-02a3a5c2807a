// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: autolink.alc.signal.pb.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_autolink_2ealc_2esignal_2epb_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_autolink_2ealc_2esignal_2epb_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019005 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_autolink_2ealc_2esignal_2epb_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_autolink_2ealc_2esignal_2epb_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_autolink_2ealc_2esignal_2epb_2eproto;
PROTOBUF_NAMESPACE_OPEN
PROTOBUF_NAMESPACE_CLOSE
namespace autolink {
namespace alc {
namespace signal {
namespace pb {

enum SignalIdSoc : int {
  ICMDISPTOTDST_2E0 = 4139,
  ICMDISPTOTDSTVLD_2E0 = 4141,
  ODOOFFS_2E0 = 4148,
  ODOOFFSVLD_2E0 = 4149,
  ODOTICKVAL_2E0 = 4150,
  ODORSTCNTR_2E0 = 4151,
  ICCEXTRLAMPSWT_321 = 4199,
  REFOGLIREQ_321 = 4203,
  VHLSPDODOCNT_185 = 4322,
  VHLSPDODOCNTVLD_185 = 4323,
  ICCACTSET3_377 = 4546,
  ICCACBLOWRLELSET2_377 = 4547,
  ICCACBLOWRLELSET3_377 = 4548,
  ICCACAIRDISTBNSET1_L_377 = 4554,
  ICCACAIRDISTBNSET2_377 = 4555,
  ICCACAIRDISTBNSET3_377 = 4556,
  ICCACONOFFSWT1_377 = 4557,
  ICCACONOFFSWT2_377 = 4558,
  ICCACONOFFSWT3_377 = 4559,
  ICCACACSWT_377 = 4560,
  ICCACAUTOSWT1_377 = 4561,
  ICCACDEFRSTSWT_377 = 4562,
  ICCACAIRRECIRCSWT_377 = 4563,
  ICCACBLOWERLESET1_377 = 4567,
  ICCACTSET1_L_377 = 4568,
  ICCACTSET2_377 = 4569,
  ICC_ECO_BTN_STS_377 = 4577,
  ICCACAIRDISTBNSET1_R_377 = 4578,
  ICCACTSET1_R_377 = 4579,
  ACONOFFSTSDISP1_271 = 4908,
  ACONOFFSTSDISP2_271 = 4909,
  ACONOFFSTSDISP3_271 = 4910,
  ACBLOWRLELDISP1_271 = 4911,
  ACBLOWRLELDISP3_271 = 4912,
  ACBLOWRLELDISP5_271 = 4913,
  ACLETDISP1_271 = 4914,
  ACRITDISP1_271 = 4915,
  ACLETDISP2_271 = 4916,
  ACLETDISP3_271 = 4917,
  ACLEAIRDISTBNDISP1_271 = 4918,
  ACRIAIRDISTBNDISP1_271 = 4919,
  ACLEAIRDISTBNDISP2_271 = 4920,
  ACLEAIRDISTBNDISP3_271 = 4921,
  ACDEFRSTDISP_271 = 4922,
  ACRECIRCDISP_271 = 4923,
  ACLEAUTODISP1_271 = 4924,
  ACACDISP_271 = 4927,
  ACECODISP_271 = 4930,
  BDCEXTRLAMPSTS_2D2 = 5268,
  REFOGLAMPSWT_2D2 = 5275,
  BCMODOTOTDST_2F1 = 5286,
  BCMODOOFFS_2F1 = 5287,
  BCMODOTOTDSTVLD_2F1 = 5288,
  BCMODORSTCNTR_2F1 = 5289
};
bool SignalIdSoc_IsValid(int value);
constexpr SignalIdSoc SignalIdSoc_MIN = ICMDISPTOTDST_2E0;
constexpr SignalIdSoc SignalIdSoc_MAX = BCMODORSTCNTR_2F1;
constexpr int SignalIdSoc_ARRAYSIZE = SignalIdSoc_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SignalIdSoc_descriptor();
template<typename T>
inline const std::string& SignalIdSoc_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SignalIdSoc>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SignalIdSoc_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SignalIdSoc_descriptor(), enum_t_value);
}
inline bool SignalIdSoc_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SignalIdSoc* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SignalIdSoc>(
    SignalIdSoc_descriptor(), name, value);
}
// ===================================================================


// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace pb
}  // namespace signal
}  // namespace alc
}  // namespace autolink

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::autolink::alc::signal::pb::SignalIdSoc> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::autolink::alc::signal::pb::SignalIdSoc>() {
  return ::autolink::alc::signal::pb::SignalIdSoc_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_autolink_2ealc_2esignal_2epb_2eproto
