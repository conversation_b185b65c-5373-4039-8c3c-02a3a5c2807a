#define LOG_TAG "ViClient::test"
#include <algorithm>
#include <vector>
#include <log.h>
#include "ViClient.h"
#include "com.autolink.vehicle.pb.h"
#include <unistd.h>
#include <iostream>
#include <chrono>
#include <thread>

#include <string_view>

using namespace std;
using namespace autolink;
std::mutex mutex_;
std::condition_variable condition_variable_;

class VehicleCallbackImpl : public ViCallback {
public:
    VehicleCallbackImpl()
        : _connected(false),
          _client(nullptr) {
        initPropList();
        _client = new ViClient(this);
    }

    virtual ~VehicleCallbackImpl() {
    }

    void onConnect() override {
        _connected = true;
        _client->subscribe((const int32_t*)propIdList.data(), (uint16_t)propIdList.size());
        LOG_TAG_INFO("connect success");
        std::unique_lock<std::mutex> Lock(mutex_);
        condition_variable_.notify_one();
    }

    void onDisconnect() override {
        _connected = false;
        LOG_TAG_INFO("disconnected");
    }

    void onBroadcast(int32_t code, ViClientPropValue &val) override {
        if (std::count(propIdList.begin(), propIdList.end(), code) > 0) {
            LOG_TAG_INFO("onRecvPropValue prop[%d]", code);
        }
        else {
            //不在订阅的列表里，舍弃
            return;
        }
    }

    /* IF */
    void ConnectService() {
        _client->connectService();
    }

    void DisConnectService() {
        _client->disConnectService();
    }

    void getProp(int propId) {
        ViClientPropValue result;
        LOG_TAG_INFO("getprop id[%d]", propId);

        _client->getProp(propId, 0, result);
        for (int i = 0; i < result.data().int32values_size(); i++) {
            int32_t value = result.data().int32values(i);
            LOG_TAG_INFO("getprop i = %d, value[%d]", i, value);
        }
    }

    void setInt32Prop(int32_t propId, int32_t areaId, int val) {
        bool ret = _client->setInt32Prop(propId, areaId, val);
        LOG_TAG_INFO("set int prop[%d] ret[%d]", propId, ret);
    }

    void setIntVecProp(int32_t propId, int32_t areaId, vector<int>& vec) {
        bool ret = _client->setInt32VecProp(propId, areaId, (const int32_t*)vec.data(), (uint16_t)vec.size());
        LOG_TAG_INFO("set Vector int prop[%d] ret[%d]", propId, ret);
    }

private:
    void initPropList() {
        propIdList.push_back(4096);
        propIdList.push_back(4099);
    }

    /* data */
    bool _connected;
    ViClient *_client{nullptr};
    std::vector<int32_t> propIdList;
};

void usage(int argc, char **argv) {

  printf("usage:\n");

  printf("%s -c xx -i yy -t [int|intvec|bytes] {prop id} {value}+\n", argv[0]);
  printf("-c {count}:             cycle number, default=1\n");
  printf(
      "-i {ms}:                send message interval in milisecond, "
      "default=1000\n");
  printf("%s -t [int|intvec|bytes|get] {prop id} {value}+\n", argv[0]);
  printf("-t [int|intvec|bytes|get]:  value type, default=int\n\n");
  printf("Example: %s -c 1 -i 100 -t int 0x1000  1\n", argv[0]);
  printf("Example: %s -c 1 -i 100 -t intvec 0x1001  1 0x2 0x300\n", argv[0]);
  printf("Example: %s -c 1 -i 100 -t bytes 0x1002  100088ff5533\n\n", argv[0]);
  printf("Example: %s -t get 0x1002 \n\n\n", argv[0]);
}

bool setData(VehicleCallbackImpl* pVehicle,  char* type, int32_t propId,
                                    const std::vector<std::string>& values) {
    LOG_TAG_INFO("%s: begin2", __func__);
    if(strcmp(type, "int") == 0) {
        int val = std::stoi(values[0]);
        pVehicle->setInt32Prop(propId, 0, val);
    }
    else if(strcmp(type, "intvec") == 0) {
        vector<int> dataVec;
        int val = 0;
        for (size_t i = 0; i < values.size(); i++) {
            val = std::stoi(values[i]);
            dataVec.push_back(val);
        }
        pVehicle->setIntVecProp(propId, 0, dataVec);
    }
    else if(strcmp(type, "bytes") == 0) {
        LOG_TAG_INFO("%s: not support", __func__);

    }
    else {
        LOG_TAG_ERROR("invald data type");
        return false;
    }
    return true;
}

int main(int argc, char *argv[]) {
    LOG_TAG_INFO("vehicle test start");
    VehicleCallbackImpl* pVehicle = new VehicleCallbackImpl();
    pVehicle->ConnectService();

    int opt;
    char *type = "int";
    int32_t propId = 0;
    bool isGetProp = false;
    int interval = 1000;
    int count = 1;

    while ((opt = getopt(argc, argv, "c:i:t:")) != -1) {
        switch (opt) {
        case 'c':
            printf("count=%d\n", std::stoi(optarg));
            count = std::stoi(optarg);
            break;
        case 'i':
            printf("interval=%d\n", std::stoi(optarg));
            interval = std::stoi(optarg);
            break;
        case 't':
            printf("type=%s\n", optarg);
            type = optarg;
            break;
        case '?':
            printf("unkonwn option: %c\n", optopt);
            break;
        default:
            break;
        }
    }

    if(optind >= argc) {
        cout << "invalid arguments." << endl;
        usage(argc, argv);
        return 0;
    }

    if (strcmp(type, "get") == 0) {
        isGetProp = true;
    }
    // 处理非选项参数
    int32_t prop_id = std::stoi(argv[optind], nullptr, 16);
    std::unique_lock<std::mutex> Lock(mutex_);
    condition_variable_.wait(Lock);

    if (isGetProp) {
        pVehicle->getProp(prop_id);
    }
    else {
        std::vector<std::string> values;
        for (int i = optind + 1; i < argc; i++) {
            values.emplace_back(argv[i]);
        }
        printf("prop id: 0x%04x values: ", prop_id);
        for(size_t i = 0; i < values.size(); i++) {
            printf("[%d]=%s ", i, values[i].c_str());
            if((i+1)%10 == 0) {
                putchar('\n');
            }
        }
        putchar('\n');
        while (count--)
        {
            bool ret = setData(pVehicle, type, prop_id, values);
            if(!ret) {
                printf("%s: set data for 0x%04x failed.", __func__, prop_id);
            }
            if (interval != 0) {
                std::this_thread::sleep_for(std::chrono::milliseconds(interval));
            }
        }
    }
    LOG_TAG_INFO("vehicle test end");
    return 0;
}

