/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#define LOG_TAG "[ViClient]"
#include "ViClient.h"

#include <semaphore.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

#include <iostream>
#include <log.h>

#include "VehicleClientFdbus.h"
#include "autolink.alc.signal.pb.pb.h"

using autolink::vehicle::RawValue;
using autolink::vehicle::ValueType;

using namespace autolink::alc::signal::pb;

struct ViClientPrivate {
    VehicleClientFdbus* m_VehicleClientFdbus{nullptr};
};

const google::protobuf::EnumDescriptor *GetSignalIdSocDescr() {
    static const google::protobuf::EnumDescriptor *descrSignalIdSoc = SignalIdSoc_descriptor();
    return descrSignalIdSoc;
}

ViClient::ViClient(ViCallback* callback)
    : p_(new ViClientPrivate()) {
    p_->m_VehicleClientFdbus = new VehicleClientFdbus(callback);

    if (nullptr == p_->m_VehicleClientFdbus) {
        throw std::runtime_error("no ipc client");
    }
}

ViClient::~ViClient() {
    if (nullptr != p_) {
        if (nullptr != p_->m_VehicleClientFdbus) {
            delete p_->m_VehicleClientFdbus;
        }

        delete p_;
    }
}

void ViClient::connectService()
{
    (p_->m_VehicleClientFdbus)->connectService();
}
void ViClient::disConnectService()
{
    (p_->m_VehicleClientFdbus)->disConnectService();
}

bool ViClient::getProp(int32_t prop, int32_t areaId, ViClientPropValue& result) {
    LOG_TAG_DEBUG("getProp(propId = 0x%04x areaId = 0x%04x)  \n", prop, areaId);
    return (p_->m_VehicleClientFdbus)->getPropValue(prop, areaId, result);
}

bool ViClient::getAllProp(ViClientPropValues& result) {
    return (p_->m_VehicleClientFdbus)->getAllPropValue(result);
}

bool ViClient::subscribe(const int32_t* propList, uint16_t size) {
    return (p_->m_VehicleClientFdbus)->subscribePropList(propList, size);
}

bool ViClient::unsubscribe(const int32_t* propList, uint16_t size) {
    return (p_->m_VehicleClientFdbus)->unsubscribePropList(propList, size);
}

bool ViClient::subscribeContinousProp(int32_t prop, int32_t sample) {
    return true;
}

bool ViClient::unsubscribeContinousProp(int32_t prop) {
    return true;
}

bool ViClient::getPropConfig(int32_t msgId, ViClientPropConfigs& result) {
    return (p_->m_VehicleClientFdbus)->getPropConfig(msgId, result);
}

bool ViClient::setInt32Prop(int32_t prop, int32_t areaId, int32_t val) {
    ViClientPropValue propVal_set;
    propVal_set.set_propid(prop);
    propVal_set.set_areaid(areaId);
    propVal_set.set_valuetype(ValueType::INT32);
    propVal_set.mutable_data()->add_int32values(val);
    return (p_->m_VehicleClientFdbus)->setPropValue(propVal_set);
}

bool ViClient::setInt32VecProp(int32_t prop, int32_t areaId, const int32_t* val, uint16_t size) {
    ViClientPropValue propVal_set;
    propVal_set.set_propid(prop);
    propVal_set.set_areaid(areaId);
    propVal_set.set_valuetype(ValueType::INT32_VEC);
    RawValue* rawVal = propVal_set.mutable_data();
    for (int i = 0; i < size; i++) {
        rawVal->add_int32values(val[i]);
    }
    return (p_->m_VehicleClientFdbus)->setPropValue(propVal_set);
}

bool ViClient::setStringProp(int32_t prop, int32_t areaId, const char* val) {
    ViClientPropValue propVal_set;
    propVal_set.set_propid(prop);
    propVal_set.set_areaid(areaId);
    propVal_set.set_valuetype(ValueType::STRING);
    RawValue* rawVal = propVal_set.mutable_data();
    rawVal->set_stringvalues(val);
    return (p_->m_VehicleClientFdbus)->setPropValue(propVal_set);
}

bool ViClient::setBoolProp(int32_t prop, int32_t areaId, bool val) {
    ViClientPropValue propVal_set;
    propVal_set.set_propid(prop);
    propVal_set.set_areaid(areaId);
    propVal_set.set_valuetype(ValueType::BOOL);
    RawValue* rawVal = propVal_set.mutable_data();
    rawVal->add_int32values(int32_t(val));
    return (p_->m_VehicleClientFdbus)->setPropValue(propVal_set);
}

bool ViClient::setInt64Prop(int32_t prop, int32_t areaId, int64_t val) {
    ViClientPropValue propVal_set;
    propVal_set.set_propid(prop);
    propVal_set.set_areaid(areaId);
    propVal_set.set_valuetype(ValueType::INT64);
    RawValue* rawVal = propVal_set.mutable_data();
    rawVal->add_int64values(val);
    return (p_->m_VehicleClientFdbus)->setPropValue(propVal_set);
}

bool ViClient::setInt64VecProp(int32_t prop, int32_t areaId, const int64_t* val, uint16_t size) {
    ViClientPropValue propVal_set;
    propVal_set.set_propid(prop);
    propVal_set.set_areaid(areaId);
    propVal_set.set_valuetype(ValueType::INT64_VEC);
    RawValue* rawVal = propVal_set.mutable_data();
    for (int i = 0; i < size; i++) {
        rawVal->add_int64values(val[i]);
    }
    return (p_->m_VehicleClientFdbus)->setPropValue(propVal_set);
}

bool ViClient::setFloatProp(int32_t prop, int32_t areaId, float val) {
    ViClientPropValue propVal_set;
    propVal_set.set_propid(prop);
    propVal_set.set_areaid(areaId);
    propVal_set.set_valuetype(ValueType::FLOAT);
    RawValue* rawVal = propVal_set.mutable_data();
    rawVal->add_floatvalues(val);
    return (p_->m_VehicleClientFdbus)->setPropValue(propVal_set);
}

bool ViClient::setFloatVecProp(int32_t prop, int32_t areaId, const float* val, uint16_t size) {
    ViClientPropValue propVal_set;
    propVal_set.set_propid(prop);
    propVal_set.set_areaid(areaId);
    propVal_set.set_valuetype(ValueType::FLOAT_VEC);
    RawValue* rawVal = propVal_set.mutable_data();
    for (int i = 0; i < size; i++) {
        rawVal->add_floatvalues(val[i]);
    }
    return (p_->m_VehicleClientFdbus)->setPropValue(propVal_set);
}

bool ViClient::setBytesProp(int32_t prop, int32_t areaId, const uint8_t* val, uint16_t size) {
    ViClientPropValue propVal_set;
    propVal_set.set_propid(prop);
    propVal_set.set_areaid(areaId);
    propVal_set.set_valuetype(ValueType::BYTES);
    RawValue* rawVal = propVal_set.mutable_data();
    rawVal->set_bytesvalues(val, size);
    return (p_->m_VehicleClientFdbus)->setPropValue(propVal_set);
}

int ViClient::SignalName2Id(const char *inSignalName) {
    const google::protobuf::EnumValueDescriptor *descri =
        GetSignalIdSocDescr()->FindValueByName(inSignalName);
    if (nullptr != descri) {
        return (descri->number());
    } else {
        return -1;
    }
}

std::string ViClient::SignalId2Name(int inUnGroupedSignalID) {
    const google::protobuf::EnumValueDescriptor *descri =
        GetSignalIdSocDescr()->FindValueByNumber((SignalIdSoc)inUnGroupedSignalID);
    if (nullptr != descri) {
        return (descri->name());
    } else {
        return "";
    }
}
