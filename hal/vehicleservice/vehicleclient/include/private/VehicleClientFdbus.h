/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/

#ifndef IPC_CLIENT_H
#define IPC_CLIENT_H

#include "com.autolink.vehicle.pb.h"

using autolink::vehicle::ViClientPropValue;
using autolink::vehicle::ViClientPropConfigs;
using autolink::vehicle::ViClientPropValues;
class VehicleClient;
class ViCallback;

class VehicleClientFdbus
{
public:
    VehicleClientFdbus(ViCallback* callback);
    virtual ~VehicleClientFdbus();
    VehicleClientFdbus(const VehicleClientFdbus&) = delete;
    VehicleClientFdbus& operator=(const VehicleClientFdbus&) = delete;

    void connectService();
    void disConnectService();

    bool subscribePropList(const int32_t* propList, uint16_t size);
    bool unsubscribePropList(const int32_t* propList, uint16_t size);

    bool getPropValue(int32_t prop, int32_t areaId, ViClientPropValue& result);
    bool getAllPropValue(ViClientPropValues& result);
    bool getPropConfig(int32_t msgId, ViClientPropConfigs& result);

    bool setPropValue(const ViClientPropValue& propVal);

private:
    VehicleClient* m_VehicleClient;
    ViCallback* m_Callback;
};



#endif
