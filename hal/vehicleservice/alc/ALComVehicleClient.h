/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef _ALCOM_VEHICLE_CLIENT_H
#define _ALCOM_VEHICLE_CLIENT_H

#define ALCOM_VEHICLE_MSG_OPTION_SIZE (1)
#define ALCOM_VEHICLE_MSG_RESERVE_SIZE (1)
#define ALCOM_VEHICLE_MESSAGE_LEN_SIZE (2)
#define ALCOM_VEHICLE_MESSAGE_TP_SIZE (2)
#define ALCOM_VEHICLE_MESSAGE_HEAD_SIZE (ALCOM_VEHICLE_MESSAGE_LEN_SIZE + ALCOM_VEHICLE_MESSAGE_TP_SIZE)

#define ALCOM_VEHICLE_SIGNAL_HEAD_SIZE (2)
#define ALCOM_VEHICLE_SIGNAL_HEAD_INDEX (0)
#define ALCOM_VEHICLE_SIGNAL_COUNT_SIZE (2)
#define ALCOM_VEHICLE_SIGNAL_COUNT_INDEX (2)
#define ALCOM_VEHICLE_SIGNAL_OPTION_SIZE (2)
#define ALCOM_VEHICLE_SIGNAL_OPTION_INDEX (4)
#define ALCOM_VEHICLE_SIGNAL_1_STATUS_SIZE (1)
#define ALCOM_VEHICLE_SIGNAL_2_STATUS_SIZE (1)
#define ALCOM_VEHICLE_SIGNAL_NAME_SIZE (2)
#define ALCOM_VEHICLE_SIGNAL_NAME_INDEX (6)
#define ALCOM_VEHICLE_SIGNAL_DATA_INDEX (8)

#define ALCOM_VEHICLE_BYTE_SIZE (8)

#define ALCOM_VEHICLE_NG (0)
#define ALCOM_VEHICLE_OK (1)

#include <stdint.h>
#include "ALComClient.h"

enum class VehicleType {
    Vehicle_Message = 0x00,
    Vehicle_WarningIDX1 = 0x01,
    Vehicle_WarningIDX2 = 0x02,
};

namespace autolink {
class IALComVehicleClientCallback {
public:
    IALComVehicleClientCallback() = default;
    virtual ~IALComVehicleClientCallback() = default;
    virtual void onConnect() = 0;
    virtual void onDisconnect() = 0;
    virtual void onRevicesMsg(MsgList& msgList) = 0;
};

class ALComClient;
class IALComClientCallback;

class ALComVehicleClient {
public:
    ALComVehicleClient();
    virtual ~ALComVehicleClient();
    void connectService(IALComVehicleClientCallback *callback);
    void disConnectService();
    //@return ALComClientMessageState
    int sendMsg(MsgList& list);

private:
    ALComClient *mALcomClinet;
    IALComClientCallback *mVehicleCallback;
};
}  // namespace autolink

#endif  //_ALCOM_VEHICLE_CLIENT_H
