/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#define LOG_TAG "VS::ALComClient"

#include <log.h>
#include <stdio.h>
#include <iostream>
#include <string.h>
#include <unistd.h>
#include <fdbus/fdbus.h>
#include <fdbus/cJSON/cJSON.h>
#include <fdbus/CFdbCJsonMsgBuilder.h>
#include <fdbus/CFdbProtoMsgBuilder.h>
#include "ALComClient.h"
#define FDBUS_INVOKE_TIMEOUT 1000 // unit ms
extern char *__progname;
using namespace ipc::fdbus;
namespace autolink
{
    using namespace std;

    class ALComClientFdbus : public CBaseClient
    {
    public:
        ALComClientFdbus(const char *name, IALComClientCallback *callback, CBaseWorker *worker = 0)
            : CBaseClient(name, worker), mCallback(callback), mSessionId(0)
        {
            std::vector<EMessageId>().swap(mSubList);
            enableUDP(true);
            enableAysncRead(true);
            enableAysncWrite(true);
        }
        ~ALComClientFdbus()
        {
            std::vector<EMessageId>().swap(mSubList);
            disconnect();
        }

        void setSubscribeEvent(EMessageId id)
        {
            mSubList.push_back(id);
        }
        void subscribeEvent()
        {
            if (mIsOnline)
            {
                CFdbMsgSubscribeList subscribe_list;
                for (auto item : mSubList)
                {
                    addNotifyItem(subscribe_list, item);
                }
                subscribe(subscribe_list);
            }
        }

        int sendmsg(uint32_t code,  MsgList& list, uint32_t timeout, uint8_t retry)
        {
            ALComClientMessageState ret = ALComClientMessageState::Message_OK;
            while (retry--)
            {
                ret = sendmsg(code, list, timeout);
                if (ret != ALComClientMessageState::Message_Failed)
                {
                    break;
                }
            }
            return ret;
        }

        ALComClientMessageState sendmsg(uint32_t code, MsgList& list, uint32_t timeout)
        {
            ALComClientMessageState result = ALComClientMessageState::Message_OK;

            if (mIsOnline)
            {
                CFdbProtoMsgBuilder builder(list);
                CBaseJob::Ptr ref(new CBaseMessage(code));
                bool ret = invoke(ref, builder, timeout);
                if (!ret)
                {
                    LOG_TAG_ERROR("invoke(%d) = false...Done", code);
                    result = ALComClientMessageState::Message_Failed;
                    return result;
                }
                int32_t error_code = FdbMsgStatusCode::FDB_ST_OK;
                auto msg = castToMessage<CBaseMessage *>(ref);
                if (msg->isStatus())
                {
                    std::string reason;
                    if (!msg->decodeStatus(error_code, reason))
                    {
                        error_code = FdbMsgStatusCode::FDB_ST_MSG_DECODE_FAIL;
                        LOG_TAG_ERROR("reply decode fail!");
                    }
                }
                if (FdbMsgStatusCode::FDB_ST_OK == error_code)
                {
                    result = ALComClientMessageState::Message_OK;
                    //LOG_TAG_DEBUG("invoke(%d)...Success", code);
                }
                else if (FdbMsgStatusCode::FDB_ST_AUTO_REPLY_OK == error_code)
                {
                    result = ALComClientMessageState::Message_OK;
                    //LOG_TAG_DEBUG("invoke(%d])...Success", code);
                }
                else if (FdbMsgStatusCode::FDB_ST_TIMEOUT == error_code)
                {
                    result = ALComClientMessageState::Message_TimeOut;
                    LOG_TAG_ERROR("invoke(%d)...Timeout", code);
                }
                else
                {
                    result = ALComClientMessageState::Message_Failed;
                    LOG_TAG_ERROR("invoke(%d)...Error", code);
                }
            }
            else
            {
                LOG_TAG_ERROR("server is offline!");
                result = ALComClientMessageState::Message_Failed;
            }
            return result;
        }

    protected:
        /* called when connected to the server */
        void onOnline(const CFdbOnlineInfo &info)
        {
            LOG_TAG_ERROR("[ALCOMC]on connect sid:%d,FirstOrLast:%d\n", info.mSid, info.mFirstOrLast);
            mIsOnline = true;
            mSessionId = info.mSid;
            subscribeEvent();
            if (mCallback != nullptr)
                mCallback->onConnect();
        }

        /* called when disconnected from server */
        void onOffline(const CFdbOnlineInfo &info)
        {
            LOG_TAG_ERROR("[ALCOMC]on offline sid:%d,FirstOrLast:%d\n", info.mSid, info.mFirstOrLast);
            if (mCallback != nullptr)
                mCallback->onDisconnect();
            mIsOnline = false;
        }

        /* called when events broadcasted from server is received */
        void onBroadcast(CBaseJob::Ptr &msg_ref)
        {
            MsgList msgList;
            auto msg = castToMessage<CBaseMessage *>(msg_ref);

            if (msg->code() == NTF_MSG_ALCOM_TP_VEHICLE) {
                CFdbProtoMsgParser parser(msgList);
                if (!msg->deserialize(parser)) {
                    LOG_TAG_ERROR("parser msg failed\n");
                    msg->status(msg_ref, FdbMsgStatusCode::FDB_ST_MSG_DECODE_FAIL, "Fail to decode msgList!");
                    return;
                }
                mCallback->onRevicesMsg(msgList);
            }
        }

        /* check if something happen... */
        void onStatus(CBaseJob::Ptr &msg_ref, int32_t error_code, const char *description)
        {
            auto msg = castToMessage<CBaseMessage *>(msg_ref);
            if (msg->isSubscribe())
            {
                LOG_TAG_ERROR("subscribe is ok! sn: %d is received.\n", msg->sn());
            }
            else
            {
                LOG_TAG_ERROR("subscribe is error! sn: %d is received.\n", msg->sn());
            }
            LOG_TAG_ERROR("Reason: %s\n", description);
        }

    private:
        std::shared_ptr<IALComClientCallback> mCallback;
        bool mIsOnline = false;
        FdbSessionId_t mSessionId;
        std::vector<EMessageId> mSubList;
    };

    static ALComClientFdbus *alComClient = nullptr;

    ALComClient::ALComClient()
    {
    }

    ALComClient::~ALComClient()
    {
        if (alComClient != nullptr)
        {
            delete alComClient;
        }
        alComClient = nullptr;
    }

    void ALComClient::connectService(IALComClientCallback *callback, std::vector<EMessageId> &list)
    {
        LOG_TAG_DEBUG("%s line: %d \n", __FUNCTION__, __LINE__);
        std::string server_name("ALCom");
        std::string url(FDB_URL_SVC);
        url += server_name;
        if (alComClient == nullptr)
        {
            CFdbContext::enableLogger(false);
            FDB_CONTEXT->start();
            CBaseWorker *worker_ptr = new CBaseWorker("ALComClientFdbus");
            worker_ptr->start();
            // server_name += std::string(__progname);

            alComClient = new ALComClientFdbus(__progname, callback, worker_ptr);
            if (alComClient != nullptr)
            {
                for (auto it : list)
                {
                    alComClient->setSubscribeEvent(it);
                }
                alComClient->enableReconnect(true);
                alComClient->setOnlineChannelType(FDB_SEC_NO_CHECK);
                alComClient->connect(url.c_str());
                LOG_TAG_DEBUG("%s connect, url: %s \n", __FUNCTION__, url.c_str());
            }
        }
    }

    void ALComClient::disConnectService()
    {
        if (alComClient != nullptr)
        {
            alComClient->disconnect(FDB_INVALID_ID);
        }
    }

    void ALComClient::subScribe(EMessageId id)
    {
        if (alComClient != nullptr)
        {
            alComClient->setSubscribeEvent(id);
        }
    }

    int ALComClient::sendMsg(uint32_t code, MsgList& list)
    {
        int ret = 0;
        if (alComClient != nullptr)
        {
            // alComClient->invoke(code, data, size);
            ret = alComClient->sendmsg(code, list, FDBUS_INVOKE_TIMEOUT, 1);
        }
        else
        {
            ret = -1;
            LOG_TAG_ERROR("ALComClient::%s alComClient nullptr\n", __FUNCTION__);
        }
        return ret;
    }
} // namespace autolink
