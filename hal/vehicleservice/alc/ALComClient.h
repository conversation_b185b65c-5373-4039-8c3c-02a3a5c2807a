/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef _ALCOM_CLIENT_H
#define _ALCOM_CLIENT_H
#include <iostream>
#include <list>
#include <string>
#include "ALComClientType.h"
#include "autolink.platform.vehicle.pb.h"

using namespace autolink::platform::vehicle::pb;

namespace autolink
{
    class IALComClientCallback
    {
    public:
        IALComClientCallback() = default;
        virtual ~IALComClientCallback() = default;
        virtual void onConnect() = 0;
        virtual void onDisconnect() = 0;
        virtual void onRevicesMsg(MsgList& msgList) = 0;
    };
    class ALComClient
    {
    public:
        ALComClient();
        virtual ~ALComClient();
        void connectService(IALComClientCallback *callback, std::vector<EMessageId> &list);
        void disConnectService();
        void subScribe(EMessageId id);
        int sendMsg(uint32_t code, MsgList& list);
    };
} // namespace autolink

#endif //_ALCOM_CLIENT_H
