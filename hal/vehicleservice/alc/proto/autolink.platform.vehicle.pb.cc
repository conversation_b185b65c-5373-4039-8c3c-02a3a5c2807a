// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: autolink.platform.vehicle.proto

#include "autolink.platform.vehicle.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace autolink {
namespace platform {
namespace vehicle {
namespace pb {
constexpr MsgSignal::MsgSignal(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : string_value_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , bytes_value_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , bool_value_(false)
  , uint32_value_(0u)
  , uint64_value_(uint64_t{0u})
  , sint64_value_(int64_t{0})
  , sint32_value_(0)
  , signal_id_(4426)

  , value_type_(1)
{}
struct MsgSignalDefaultTypeInternal {
  constexpr MsgSignalDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MsgSignalDefaultTypeInternal() {}
  union {
    MsgSignal _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MsgSignalDefaultTypeInternal _MsgSignal_default_instance_;
constexpr MsgSignalSp::MsgSignalSp(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : signal_(nullptr)
  , state_(0)
{}
struct MsgSignalSpDefaultTypeInternal {
  constexpr MsgSignalSpDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MsgSignalSpDefaultTypeInternal() {}
  union {
    MsgSignalSp _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MsgSignalSpDefaultTypeInternal _MsgSignalSp_default_instance_;
constexpr MsgPdu::MsgPdu(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : signals_()
  , state_(0)

  , pdu_id_(4099)
{}
struct MsgPduDefaultTypeInternal {
  constexpr MsgPduDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MsgPduDefaultTypeInternal() {}
  union {
    MsgPdu _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MsgPduDefaultTypeInternal _MsgPdu_default_instance_;
constexpr MsgList::MsgList(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : pdus_()
  , signals_(){}
struct MsgListDefaultTypeInternal {
  constexpr MsgListDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MsgListDefaultTypeInternal() {}
  union {
    MsgList _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MsgListDefaultTypeInternal _MsgList_default_instance_;
}  // namespace pb
}  // namespace vehicle
}  // namespace platform
}  // namespace autolink
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_autolink_2eplatform_2evehicle_2eproto[4];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_autolink_2eplatform_2evehicle_2eproto[4];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_autolink_2eplatform_2evehicle_2eproto = nullptr;

const uint32_t TableStruct_autolink_2eplatform_2evehicle_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, signal_id_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, value_type_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, string_value_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, bool_value_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, uint32_value_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, sint32_value_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, uint64_value_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, sint64_value_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, bytes_value_),
  7,
  8,
  0,
  2,
  3,
  6,
  4,
  5,
  1,
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignalSp, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignalSp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignalSp, state_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignalSp, signal_),
  1,
  0,
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgPdu, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgPdu, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgPdu, pdu_id_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgPdu, state_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgPdu, signals_),
  1,
  0,
  ~0u,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgList, pdus_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgList, signals_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, 15, -1, sizeof(::autolink::platform::vehicle::pb::MsgSignal)},
  { 24, 32, -1, sizeof(::autolink::platform::vehicle::pb::MsgSignalSp)},
  { 34, 43, -1, sizeof(::autolink::platform::vehicle::pb::MsgPdu)},
  { 46, -1, -1, sizeof(::autolink::platform::vehicle::pb::MsgList)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::autolink::platform::vehicle::pb::_MsgSignal_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::autolink::platform::vehicle::pb::_MsgSignalSp_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::autolink::platform::vehicle::pb::_MsgPdu_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::autolink::platform::vehicle::pb::_MsgList_default_instance_),
};

const char descriptor_table_protodef_autolink_2eplatform_2evehicle_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\037autolink.platform.vehicle.proto\022\034autol"
  "ink.platform.vehicle.pb\"\240\002\n\tMsgSignal\0229\n"
  "\tsignal_id\030\001 \002(\0162&.autolink.platform.veh"
  "icle.pb.SignalId\022A\n\nvalue_type\030\002 \002(\0162-.a"
  "utolink.platform.vehicle.pb.SignalValueT"
  "ype\022\024\n\014string_value\030\003 \001(\t\022\022\n\nbool_value\030"
  "\004 \001(\010\022\024\n\014uint32_value\030\005 \001(\r\022\024\n\014sint32_va"
  "lue\030\006 \001(\021\022\024\n\014uint64_value\030\007 \001(\004\022\024\n\014sint6"
  "4_value\030\010 \001(\022\022\023\n\013bytes_value\030\t \001(\014\"}\n\013Ms"
  "gSignalSp\0225\n\005state\030\001 \002(\0162&.autolink.plat"
  "form.vehicle.pb.PduState\0227\n\006signal\030\002 \002(\013"
  "2\'.autolink.platform.vehicle.pb.MsgSigna"
  "l\"\256\001\n\006MsgPdu\0223\n\006pdu_id\030\001 \002(\0162#.autolink."
  "platform.vehicle.pb.PduId\0225\n\005state\030\002 \002(\016"
  "2&.autolink.platform.vehicle.pb.PduState"
  "\0228\n\007signals\030\003 \003(\0132\'.autolink.platform.ve"
  "hicle.pb.MsgSignal\"y\n\007MsgList\0222\n\004pdus\030\001 "
  "\003(\0132$.autolink.platform.vehicle.pb.MsgPd"
  "u\022:\n\007signals\030\002 \003(\0132).autolink.platform.v"
  "ehicle.pb.MsgSignalSp*1\n\010PduState\022\013\n\007DEF"
  "AULT\020\000\022\014\n\010INACTIVE\020\001\022\n\n\006ACTIVE\020\002*\276\001\n\017Sig"
  "nalValueType\022\013\n\007TYPE_U8\020\001\022\014\n\010TYPE_U16\020\002\022"
  "\017\n\013TYPE_UINT32\020\003\022\020\n\014TYPE_BOOLEAN\020\004\022\013\n\007TY"
  "PE_S8\020\005\022\014\n\010TYPE_S16\020\006\022\017\n\013TYPE_SINT32\020\007\022\017"
  "\n\013TYPE_UINT64\020\010\022\017\n\013TYPE_SINT64\020\t\022\017\n\013TYPE"
  "_STRING\020\n\022\016\n\nTYPE_BYTES\020\013*,\n\005PduId\022\023\n\016PD"
  "U_TPMS_0x589\020\203 \022\016\n\nPDU_ID_MAX\020\001*\273\224\002\n\010Sig"
  "nalId\022\"\n\035SIGNAL_PEPS_0x333_PEPS_PwrMod\020\312"
  "\"\022!\n\034SIGNAL_ICC_0x46_EHU_MuteCtrl\020\277#\022\'\n\""
  "SIGNAL_ICC_0x610_EHU_EgyCnseClrFlg\020\200%\022!\n"
  "\034SIGNAL_VCU_0x214_VCU_RdyLamp\020\244%\022$\n\037SIGN"
  "AL_VCU_0x214_VCU_GearSigVld\020\200 \022!\n\034SIGNAL"
  "_VCU_0x214_VCU_GearSig\020\201 \022*\n%SIGNAL_BMS_"
  "0x330_BMS_PwrBattRmngCpSOC\020\347%\022#\n\036SIGNAL_"
  "ESP_0x318_ESP_VehSpdVld\020\207&\022 \n\033SIGNAL_ESP"
  "_0x318_ESP_VehSpd\020\202 \022(\n#SIGNAL_ICC_0x531"
  "_ICM_TotMilgVld_ODO\020\317\'\022%\n SIGNAL_ICC_0x5"
  "31_ICM_TotMilg_ODO\020\321\'\022$\n\037SIGNAL_ICC_0x53"
  "1_ICM_DispVehSpd\020\322\'\022&\n!SIGNAL_ICC_0x532_"
  "ICM_MilgOffs_ODO\020\344)\022)\n$SIGNAL_ICC_0x532_"
  "ICM_DispTotMilg_ODO\020\345)\022)\n$SIGNAL_ICC_0x5"
  "32_ICM_MilgDataVld_ODO\020\346)\022)\n$SIGNAL_ICC_"
  "0x532_ICM_MilgRstCntr_ODO\020\347)\0221\n,SIGNAL_V"
  "CU_0x357_ResetFlg_TripfromLastCharge\020\304*\022"
  "\'\n\"SIGNAL_VCU_0x503_VCU_DrvPwrLimPerc\020\334*"
  "\022)\n$SIGNAL_VCU_0x503_VCU_EgyFbPwrLimPerc"
  "\020\335*\022(\n#SIGNAL_VCU_0x503_VCU_InsntEgyCnse"
  "Hr\020\346*\022\"\n\035SIGNAL_VCU_0x504_VCU_DrvgMilg\020\206"
  " \022#\n\036SIGNAL_VCU_0x504_VCU_DrvRngVld\020\364*\0223"
  "\n.SIGNAL_VCU_0x505_VCU_ChrgDchaGunCnctnI"
  "ndcrLamp\020\200+\022,\n\'SIGNAL_VCU_0x50C_VCU_Drvg"
  "MilgDispTypCfm\020\207+\022)\n$SIGNAL_BCM_0x641_BC"
  "M_MilgDataVld_ODO\020\314,\022)\n$SIGNAL_BCM_0x641"
  "_BCM_MilgRstCntr_ODO\020\315,\022%\n SIGNAL_BCM_0x"
  "641_BCM_TotMilg_ODO\020\316,\022&\n!SIGNAL_BCM_0x6"
  "41_BCM_MilgOffs_ODO\020\317,\022\'\n\"SIGNAL_ICC_0x3"
  "B3_ICC_TripAResetFlg\020\2320\022\'\n\"SIGNAL_ICC_0x"
  "3B3_ICC_TripBResetFlg\020\2330\022%\n SIGNAL_MFS_0"
  "x514_MFS_LeRollPress\020\3170\022*\n%SIGNAL_VCU_0x"
  "3B6_VCU_DrvgMilgDispPerc\020\3514\022\'\n\"SIGNAL_VC"
  "U_0x5A9_PdcuFuelLevelDisp\020\3146\022#\n\036SIGNAL_V"
  "CU_0x5A9_PdcuFuMilgVld\020\3156\022!\n\034SIGNAL_VCU_"
  "0x5A9_PdcuFuMilge\020\3166\022/\n*SIGNAL_ICC_0x542"
  "_ICC_100kmAvrgPwrCnsptn_AB\020\3726\022.\n)SIGNAL_"
  "ICC_0x542_ICC_10kmAvrgPwrCnsptn_AB\020\3736\0225\n"
  "0SIGNAL_VCU_0x3B8_VCULastChrgTripAvrgPwr"
  "Cnsptn_AS\020\3766\022/\n*SIGNAL_VCU_0x3B8_VCU_Tri"
  "pAAvrgPwrCnsptn_AS\020\3776\022/\n*SIGNAL_VCU_0x3B"
  "8_VCU_TripBAvrgPwrCnsptn_AS\020\2007\022/\n*SIGNAL"
  "_VCU_0x3B8_VCU_100kmRemDrvgRng_AB_AS\020\2037\022"
  "!\n\034SIGNAL_VCU_0x3B8_PdcuEvSt_AS\020\2347\0225\n0SI"
  "GNAL_VCU_0x3B8_VCULastChrgTripAvrgPwrCns"
  "ptn_AB\020\2437\022/\n*SIGNAL_VCU_0x3B8_VCU_TripAA"
  "vrgPwrCnsptn_AB\020\2447\022/\n*SIGNAL_VCU_0x3B8_V"
  "CU_TripBAvrgPwrCnsptn_AB\020\2457\022+\n&SIGNAL_VC"
  "U_0x3B8_VCU_10kmRemDrvgRng_AB\020\2467\022-\n(SIGN"
  "AL_VCU_0x3B8_VCU_SubtolEgyCnse_AB_AS\020\2477\022"
  "%\n\037SIGNAL_TPMS_0x589_TPMS_TirePosn\020\200\200\002\022*"
  "\n$SIGNAL_TPMS_0x589_TPMS_SngTireSigSts\020\201"
  "\200\002\022#\n\035SIGNAL_TPMS_0x589_TPMS_SysSts\020\202\200\002\022"
  ".\n(SIGNAL_TPMS_0x589_TPMS_SngTireLeakage"
  "Sts\020\203\200\002\022(\n\"SIGNAL_TPMS_0x589_TPMS_SngTir"
  "ePSts\020\204\200\002\022(\n\"SIGNAL_TPMS_0x589_TPMS_Tire"
  "TStsFlg\020\205\200\002\0223\n-SIGNAL_TPMS_0x589_TPMS_Sn"
  "gTireSnsrBattVoltSts\020\206\200\002\022%\n\037SIGNAL_TPMS_"
  "0x589_TPMS_SngTireT\020\207\200\002\022%\n\037SIGNAL_TPMS_0"
  "x589_TPMS_SngTireP\020\210\200\002\022\'\n!SIGNAL_TPMS_0x"
  "589_TPMS_SysDataSts\020\211\200\002\022)\n#SIGNAL_TPMS_0"
  "x589_TPMS_SnsrMatchSts\020\212\200\002\022!\n\034SIGNAL_YRS"
  "_0x246_YRS_LgtAcce\020\213\"\022\037\n\032SIGNAL_ECC_0x37"
  "3_ECC_ACSts\020\217\"\022$\n\037SIGNAL_ECC_0x373_ECC_W"
  "indSpdSts\020\221\"\022.\n)SIGNAL_ECC_0x373_ECC_Bac"
  "kRowAirOutlModSts\020\222\"\022%\n SIGNAL_ECC_0x373"
  "_ECC_DrvrTSetSts\020\223\"\022%\n SIGNAL_ECC_0x373_"
  "ECC_PassTSetSts\020\224\"\022!\n\034SIGNAL_ECC_0x373_E"
  "CC_AUTOSts\020\226\"\022!\n\034SIGNAL_ECC_0x373_ECC_SY"
  "NCSts\020\227\"\022!\n\034SIGNAL_ECC_0x373_ECC_CircSts"
  "\020\231\"\022)\n$SIGNAL_ECC_0x373_ECC_ParticleConc"
  "Vld\020\232\"\022\037\n\032SIGNAL_ECC_0x373_ECC_OutdT\020\233\"\022"
  "\"\n\035SIGNAL_ECC_0x373_ECC_OutdTVld\020\234\"\022\'\n\"S"
  "IGNAL_ECC_0x373_ECC_MaxFrntDefrst\020\235\"\022(\n#"
  "SIGNAL_ECC_0x373_ECC_HeatMngtSysFlt\020\236\"\022("
  "\n#SIGNAL_ECC_0x373_ECC_HeatMngtFctLim\020\237\""
  "\022(\n#SIGNAL_ECC_0x373_ECC_DrvrAirOutlMod\020"
  "\240\"\022(\n#SIGNAL_ECC_0x373_ECC_PassAirOutlMo"
  "d\020\241\"\022\'\n\"SIGNAL_ECC_0x373_ECC_EgySaveModS"
  "ts\020\242\"\0223\n.SIGNAL_ICC_0x37A_ICC_AutoPower_"
  "offSwitchStatus\020\243\"\022$\n\037SIGNAL_ICC_0x37A_E"
  "HU_PetsModBtn\020\244\"\022/\n*SIGNAL_ICC_0x37A_EHU"
  "_LeOutlUpDwnMotActvCmd\020\246\"\022.\n)SIGNAL_ICC_"
  "0x37A_EHU_LeOutlLeRiMotActvCmd\020\247\"\0222\n-SIG"
  "NAL_ICC_0x37A_EHU_MidLeOutlUpDwnMotActvC"
  "md\020\251\"\0221\n,SIGNAL_ICC_0x37A_EHU_MidLeOutlL"
  "eRiMotActvCmd\020\252\"\0222\n-SIGNAL_ICC_0x37A_EHU"
  "_MidRiOutlUpDwnMotActvCmd\020\254\"\0221\n,SIGNAL_I"
  "CC_0x37A_EHU_MidRiOutlLeRiMotActvCmd\020\255\"\022"
  "/\n*SIGNAL_ICC_0x37A_EHU_RiOutlUpDwnMotAc"
  "tvCmd\020\257\"\022.\n)SIGNAL_ICC_0x37A_EHU_RiOutlL"
  "eRiMotActvCmd\020\260\"\022/\n*SIGNAL_ECC_0x378_ECC"
  "_LeOutlUpDwnMotActvSts\020\262\"\022.\n)SIGNAL_ECC_"
  "0x378_ECC_LeOutlLeRiMotActvSts\020\263\"\0222\n-SIG"
  "NAL_ECC_0x378_ECC_MidLeOutlUpDwnMotActvS"
  "ts\020\265\"\0221\n,SIGNAL_ECC_0x378_ECC_MidLeOutlL"
  "eRiMotActvSts\020\266\"\0222\n-SIGNAL_ECC_0x378_ECC"
  "_MidRiOutlUpDwnMotActvSts\020\270\"\0221\n,SIGNAL_E"
  "CC_0x378_ECC_MidRiOutlLeRiMotActvSts\020\271\"\022"
  "/\n*SIGNAL_ECC_0x378_ECC_RiOutlUpDwnMotAc"
  "tvSts\020\273\"\022.\n)SIGNAL_ECC_0x378_ECC_RiOutlL"
  "eRiMotActvSts\020\274\"\022%\n SIGNAL_PEPS_0x333_PE"
  "PS_PwrModVld\020\313\"\022&\n!SIGNAL_PEPS_0x333_PEP"
  "S_PollingSts\020\314\"\022\'\n\"SIGNAL_PEPS_0x37B_PEP"
  "S_KeyInCarRmn\020\322\"\022,\n\'SIGNAL_PEPS_0x37B_PE"
  "PS_ShiftToPNStrtReq\020\324\"\022,\n\'SIGNAL_PEPS_0x"
  "37B_PEPS_StepBrkToStrtReq\020\325\"\022+\n&SIGNAL_P"
  "EPS_0x37B_PEPS_NoFoundLegalKey\020\326\"\022)\n$SIG"
  "NAL_PEPS_0x37B_PEPS_EmgyStrtPromt\020\331\"\022/\n*"
  "SIGNAL_PEPS_0x37B_PEPS_VCUAuthentFailPro"
  "mt\020\333\"\022)\n$SIGNAL_PEPS_0x37B_PEPS_KeyLoPwr"
  "Promt\020\334\"\022+\n&SIGNAL_PEPS_0x37B_PEPS_KeyOu"
  "tdCarPromt\020\335\"\022*\n%SIGNAL_PEPS_0x380_PEPS_"
  "TiOutPwrOffRmn\020\345\"\022!\n\034SIGNAL_PWC_0x524_PW"
  "C_ChrgSts\020\346\"\022%\n SIGNAL_PWC_0x524_PWC_Mod"
  "leSwtSts\020\350\"\022-\n(SIGNAL_ICC_0x533_EHU_Drvr"
  "SeatTrackManReq\020\376\"\022\'\n\"SIGNAL_ICC_0x533_E"
  "HU_DrvrHeiManReq\020\200#\022,\n\'SIGNAL_ICC_0x533_"
  "EHU_DrvrSeatBackManReq\020\201#\022-\n(SIGNAL_ICC_"
  "0x533_EHU_PassSeatTrackManReq\020\202#\022,\n\'SIGN"
  "AL_ICC_0x533_EHU_PassSeatBackManReq\020\203#\022#"
  "\n\036SIGNAL_ICC_0x533_EHU_LumbarUpd\020\204#\022#\n\036S"
  "IGNAL_ICC_0x533_EHU_LumbarDwn\020\205#\022#\n\036SIGN"
  "AL_ICC_0x533_EHU_LumbarFwd\020\206#\022%\n SIGNAL_"
  "ICC_0x533_EHU_LumbarBackw\020\207#\022-\n(SIGNAL_I"
  "CC_0x533_EHU_AutoPassSeatHeatEna\020\211#\022\'\n\"S"
  "IGNAL_ICC_0x533_EHU_PassLumbarUpd\020\216#\022\'\n\""
  "SIGNAL_ICC_0x533_EHU_PassLumbarDwn\020\217#\022\'\n"
  "\"SIGNAL_ICC_0x533_EHU_PassLumbarFwd\020\220#\022)"
  "\n$SIGNAL_ICC_0x533_EHU_PassLumbarBackw\020\221"
  "#\022&\n!SIGNAL_ICC_0x533_EHU_LRLockWinCmd\020\222"
  "#\022&\n!SIGNAL_ICC_0x533_EHU_RRLockWinCmd\020\223"
  "#\022*\n%SIGNAL_ICC_0x52_EHU_SetMaxPosnCmd_P"
  "LG\020\225#\022\"\n\035SIGNAL_ICC_0x52_EHU_TrActnCmd\020\226"
  "#\022\037\n\032SIGNAL_ICC_0x52_ICC_DRLSwt\020\227#\022+\n&SI"
  "GNAL_ICC_0x52_ICC_LockAutoClsSunSSwt\020\230#\022"
  ".\n)SIGNAL_ICC_0x90_EHU_VehAccountLoginUI"
  "DSts\020\232#\022+\n&SIGNAL_ICC_0x90_EHU_VehAccoun"
  "tLoginUID\020\233#\022+\n&SIGNAL_ICC_0x90_EHU_Seat"
  "LocnMemOperCmd\020\234#\022+\n&SIGNAL_ICC_0x90_EHU"
  "_DrvrSeatUidSubPosn\020\235#\022/\n*SIGNAL_ICC_0x9"
  "0_EHU_PassSeatLocnMemOperCmd\020\236#\022+\n&SIGNA"
  "L_ICC_0x90_EHU_PassSeatUidSubPosn\020\237#\022!\n\034"
  "SIGNAL_ICC_0x44_EHU_FaderSet\020\252#\022#\n\036SIGNA"
  "L_ICC_0x44_EHU_LeRiBalSet\020\254#\022\'\n\"SIGNAL_I"
  "CC_0x44_EHU_MidFrqAudioSet\020\256#\022&\n!SIGNAL_"
  "ICC_0x44_EHU_LoFrqAudioSet\020\260#\022&\n!SIGNAL_"
  "ICC_0x44_EHU_HiFrqAudioSet\020\262#\022$\n\037SIGNAL_"
  "ICC_0x44_EHU_SoundSwitch\020\270#\022.\n)SIGNAL_IC"
  "C_0x44_EHU_ARCFOXSoundModeSelect\020\271#\022(\n#S"
  "IGNAL_ICC_0x46_EHU_FolwMeHomeTiSet\020\272#\022&\n"
  "!SIGNAL_ICC_0x46_EHU_IntrLampTiSet\020\273#\022\"\n"
  "\035SIGNAL_ICC_0x46_EHU_MaiVolSet\020\276#\022#\n\036SIG"
  "NAL_ICC_0x46_EHU_IESSModReq\020\301#\022\"\n\035SIGNAL"
  "_ICC_0x46_EHU_VSCModReq\020\302#\022\"\n\035SIGNAL_ICC"
  "_0x46_EHU_HFTVolSet\020\303#\022\"\n\035SIGNAL_ICC_0x4"
  "6_EHU_NavVolSet\020\306#\022!\n\034SIGNAL_ICC_0x46_EH"
  "U_RTVolSet\020\310#\022-\n(SIGNAL_ICC_0x46_EHU_Vol"
  "IncreaseWithSpeed\020\316#\022&\n!SIGNAL_ICC_0x4E_"
  "EHU_RiFrntWinCtrl\020\320#\022$\n\037SIGNAL_ICC_0x4E_"
  "EHU_LeReWinCtrl\020\321#\022$\n\037SIGNAL_ICC_0x4E_EH"
  "U_RiReWinCtrl\020\322#\022&\n!SIGNAL_ICC_0x4E_EHU_"
  "LeFrntWinCtrl\020\323#\022%\n SIGNAL_ICC_0x4E_EHU_"
  "PosnLampCtrl\020\327#\022#\n\036SIGNAL_ICC_0x4E_EHU_L"
  "oBeamCtrl\020\331#\022(\n#SIGNAL_ICC_0x4E_EHU_ReDe"
  "frstOpenReq\020\337#\022(\n#SIGNAL_ICC_0x4E_EHU_Su"
  "nshadeCtrlReq\020\340#\022&\n!SIGNAL_ICC_0x4E_EHU_"
  "CentrLockCtrl\020\341#\022 \n\033SIGNAL_ICC_0x4E_EHU_"
  "MirrCmd\020\342#\022*\n%SIGNAL_ICC_0x4E_EHU_ReMirr"
  "AutoFoldSet\020\343#\022+\n&SIGNAL_ICC_0x4E_EHU_Ra"
  "inClsdSunroofSet\020\345#\022(\n#SIGNAL_ICC_0x4E_E"
  "HU_ArmedClsdWinSet\020\346#\022$\n\037SIGNAL_ICC_0x4E"
  "_EHU_OffUnlckSet\020\347#\022&\n!SIGNAL_ICC_0x4E_E"
  "HU_DoorUnlockSet\020\350#\022\'\n\"SIGNAL_ICC_0x336_"
  "EHU_SetAtmLampBri\020\353#\022(\n#SIGNAL_ICC_0x336"
  "_EHU_AtmLampOpenCmd\020\357#\022$\n\037SIGNAL_ICC_0x3"
  "36_EHU_AlcCstmSwt\020\361#\022#\n\036SIGNAL_ICC_0x336"
  "_EHU_HDCSwtSig\020\362#\022#\n\036SIGNAL_ICC_0x336_EH"
  "U_ESPSwtSig\020\363#\022 \n\033SIGNAL_ICC_0x336_EHU_S"
  "LCSwt\020\366#\022*\n%SIGNAL_ICC_0x528_EHU_DrvrSea"
  "tHeatgReq\020\372#\022*\n%SIGNAL_ICC_0x528_EHU_Drv"
  "rSeatVentnReq\020\373#\022*\n%SIGNAL_ICC_0x528_EHU"
  "_SeatWelFctEnaReq\020\376#\022.\n)SIGNAL_ICC_0x528"
  "_EHU_PassSeatWelFctEnaReq\020\377#\0221\n,SIGNAL_I"
  "CC_0x528_EHU_SeatHeatLvAutoReduceReq\020\201$\022"
  "*\n%SIGNAL_ICC_0x528_EHU_PassSeatHeatgReq"
  "\020\202$\022*\n%SIGNAL_ICC_0x528_EHU_PassSeatVent"
  "nReq\020\203$\022\'\n\"SIGNAL_ICC_0x528_EHU_LRSeatHe"
  "atReq\020\204$\022\'\n\"SIGNAL_ICC_0x528_EHU_RRSeatH"
  "eatReq\020\206$\022%\n SIGNAL_ICC_0x529_EHU_CrtLan"
  "guage\020\211$\022$\n\037SIGNAL_ICC_0x529_EHU_BriAdj_"
  "HUD\020\213$\022.\n)SIGNAL_ICC_0x529_EHU_SteerWhlP"
  "hnKeyBackLi\020\214$\022\'\n\"SIGNAL_ICC_0x529_EHU_B"
  "ackgndBriLvl\020\215$\022\'\n\"SIGNAL_ICC_0x529_EHU_"
  "BriAdjVal_HUD\020\220$\022%\n SIGNAL_ICC_0x529_EHU"
  "_OpenCmd_HUD\020\221$\022(\n#SIGNAL_ICC_0x529_EHU_"
  "SnowModSwt_HUD\020\222$\022$\n\037SIGNAL_ICC_0x529_EH"
  "U_HeiAdj_HUD\020\223$\022%\n SIGNAL_ICC_0x529_EHU_"
  "WiprSrvPosn\020\226$\022$\n\037SIGNAL_ICC_0x529_EHU_P"
  "ullModReq\020\233$\022%\n SIGNAL_ICC_0x529_EHU_Usr"
  "PwrOffFb\020\234$\022)\n$SIGNAL_ICC_0x529_EHU_Stee"
  "rWhlHeatgSw\020\235$\022\"\n\035SIGNAL_ICC_0x530_EHU_D"
  "rvrTSet\020\240$\022\"\n\035SIGNAL_ICC_0x530_EHU_PassT"
  "Set\020\241$\022$\n\037SIGNAL_ICC_0x530_EHU_ECCAUTORe"
  "q\020\243$\022%\n SIGNAL_ICC_0x530_EHU_DrvrSYNCReq"
  "\020\244$\022\"\n\035SIGNAL_ICC_0x530_EHU_ACSwtReq\020\245$\022"
  "#\n\036SIGNAL_ICC_0x530_EHU_AirVolSet\020\246$\022*\n%"
  "SIGNAL_ICC_0x530_EHU_ECCIntExtCircReq\020\247$"
  "\022&\n!SIGNAL_ICC_0x530_EHU_AirClnSwtReq\020\250$"
  "\022*\n%SIGNAL_ICC_0x530_EHU_MaxFrntDefrstSe"
  "t\020\251$\022$\n\037SIGNAL_ICC_0x530_EHU_VSPCtrlCmd\020"
  "\252$\022*\n%SIGNAL_ICC_0x530_EHU_ECCEgySaveMod"
  "Req\020\260$\022$\n\037SIGNAL_ICC_0x530_EHU_BlowWinBt"
  "n\020\261$\022,\n\'SIGNAL_ICC_0x534_EHU_DrvgMilgDis"
  "pTypSet\020\263$\022 \n\033SIGNAL_ICC_0x534_EHU_UVCRe"
  "q\020\264$\022)\n$SIGNAL_ICC_0x534_EHU_DrvrBlowFac"
  "eBtn\020\267$\022*\n%SIGNAL_ICC_0x534_EHU_PassBlow"
  "FacetBtn\020\270$\022)\n$SIGNAL_ICC_0x534_EHU_Drvr"
  "BlowFootBtn\020\271$\022)\n$SIGNAL_ICC_0x534_EHU_P"
  "assBlowFootBtn\020\272$\022.\n)SIGNAL_ICC_0x534_EH"
  "U_BackRowAirOutlModReq\020\273$\022&\n!SIGNAL_ICC_"
  "0x534_EHU_ECCSysSwtCmd\020\275$\022(\n#SIGNAL_ICC_"
  "0x534_EHU_DrvrBlowModReq\020\276$\022(\n#SIGNAL_IC"
  "C_0x534_EHU_PassBlowModReq\020\277$\022)\n$SIGNAL_"
  "ICC_0x534_EHU_FLSeatMasModCmd\020\300$\022+\n&SIGN"
  "AL_ICC_0x534_EHU_FLSeatMasGradeCmd\020\301$\022)\n"
  "$SIGNAL_ICC_0x534_EHU_FRSeatMasModCmd\020\302$"
  "\022+\n&SIGNAL_ICC_0x534_EHU_FRSeatMasGradeC"
  "md\020\303$\022/\n*SIGNAL_ICC_0x59C_EHU_UsrSetChrg"
  "GunAntithft\020\313$\022)\n$SIGNAL_ICC_0x59C_EHU_C"
  "hrgDchaCtrlCmd\020\324$\022/\n*SIGNAL_ICC_0x59C_EH"
  "U_SetACChrgGunUnLockSwt\020\325$\022/\n*SIGNAL_ICC"
  "_0x59C_EHU_OpenCloseChrgPort1Req\020\327$\022&\n!S"
  "IGNAL_ICC_0x59C_ICC_AutoColorSwt\020\331$\022\'\n\"S"
  "IGNAL_ICC_0x59C_ICC_EmissTestMode\020\333$\022\"\n\035"
  "SIGNAL_ICC_0x59C_ICC_IdleMode\020\334$\022!\n\034SIGN"
  "AL_ICC_0x59C_ICC_EnrgMod\020\336$\022&\n!SIGNAL_IC"
  "C_0x59C_ICC_RefSwitchSts\020\337$\022\'\n\"SIGNAL_IC"
  "C_0x59D_ICC_ChrgModUsrSet\020\345$\022(\n#SIGNAL_I"
  "CC_0x59D_ICC_TurnlampModSwt\020\350$\022+\n&SIGNAL"
  "_ICC_0x59D_ICC_ContrastColorSwt4\020\351$\022+\n&S"
  "IGNAL_ICC_0x59D_ICC_ContrastColorSwt5\020\352$"
  "\022+\n&SIGNAL_ICC_0x59D_ICC_ContrastColorSw"
  "t6\020\353$\022+\n&SIGNAL_ICC_0x610_EHU_UsrSetChrg"
  "RmnMilg\020\376$\022%\n SIGNAL_ICC_0x610_EHU_KL15K"
  "eepReq\020\377$\022+\n&SIGNAL_ICC_0x610_EHU_Pollin"
  "gFctOpenSts\020\204%\022)\n$SIGNAL_ICC_0x610_EHU_C"
  "hrgInsulFctReq\020\206%\022.\n)SIGNAL_ICC_0x610_EH"
  "U_UsrSetDischrgRmnMilg\020\213%\022\'\n\"SIGNAL_ICC_"
  "0x610_EHU_SetChrgEndSOC\020\214%\022*\n%SIGNAL_VCU"
  "_0x214_VCU_PwrBattHVCnctSts\020\245%\022,\n\'SIGNAL"
  "_VCU_0x214_VCU_DrvModShiftMisoper\020\254%\0221\n,"
  "SIGNAL_VCU_0x358_VCU_ExtremeEgySaveSwtEn"
  "aFlg\020\257%\022\'\n\"SIGNAL_VCU_0x358_VCU_PullModE"
  "naSig\020\261%\022$\n\037SIGNAL_VCU_0x358_VCU_PullMod"
  "Sig\020\262%\022&\n!SIGNAL_VCU_0x358_VCU_DrvPwrLim"
  "Sts\020\265%\022+\n&SIGNAL_VCU_0x358_VCU_EgyRecovP"
  "wrLimSts\020\266%\022/\n*SIGNAL_VCU_0x358_VCU_Extr"
  "emeEgySaveOpenSig\020\270%\0223\n.SIGNAL_VCU_0x358"
  "_VCU_OnePedalKeepDisplay_AB_AS\020\272%\022(\n#SIG"
  "NAL_VCU_0x579_VCU_VehAcsyCnseEgy\020\310%\022%\n S"
  "IGNAL_VCU_0x579_VCU_EgyRecovEgy\020\313%\0221\n,SI"
  "GNAL_VCU_0x605_VCU_LongTiHlthStorePushIn"
  "fo\020\335%\022$\n\037SIGNAL_VCU_0x605_VCU_ChrgStsTxt"
  "\020\336%\022&\n!SIGNAL_BMS_0x330_BMS_ChrgFltPromt"
  "\020\341%\022&\n!SIGNAL_BMS_0x330_BMS_CellMinTAlrm"
  "\020\350%\0221\n,SIGNAL_BMS_0x330_BMS_PwrBattTherm"
  "RunawayAlrm\020\351%\022#\n\036SIGNAL_ESP_0x261_ESP_W"
  "arningOn\020\361%\022$\n\037SIGNAL_ESP_0x261_ESP_BrkF"
  "ldAlrm\020\363%\022&\n!SIGNAL_ESP_0x261_ESP_AvlInd"
  "cn_CST\020\364%\022%\n SIGNAL_ESP_0x261_ESP_CtrlSt"
  "s_CST\020\365%\022$\n\037SIGNAL_ESP_0x268_ESP_SysSts_"
  "EPB\020\370%\022&\n!SIGNAL_ESP_0x268_ESP_FltIndcn_"
  "EPB\020\371%\022&\n!SIGNAL_ESP_0x268_ESP_Actvndcn_"
  "EPB\020\372%\022#\n\036SIGNAL_ESP_0x268_EPB_WarnMsg01"
  "\020\373%\022#\n\036SIGNAL_ESP_0x268_EPB_WarnMsg02\020\374%"
  "\022#\n\036SIGNAL_ESP_0x268_EPB_WarnMsg04\020\375%\022$\n"
  "\037SIGNAL_ESP_0x318_ESP_BrkPedlSts\020\206&\022!\n\034S"
  "IGNAL_ESP_0x318_ESP_SysActv\020\210&\022)\n$SIGNAL"
  "_ESP_0x318_ESP_LampSwtOffIndcn\020\211&\022&\n!SIG"
  "NAL_ESP_0x318_ESP_FltIndcn_EBD\020\212&\022&\n!SIG"
  "NAL_ESP_0x318_ESP_FltIndcn_ABS\020\213&\022&\n!SIG"
  "NAL_ESP_0x318_ESP_FltIndcn_TCS\020\216&\022%\n SIG"
  "NAL_ESP_0x318_ESP_CtrlSts_HDC\020\217&\022&\n!SIGN"
  "AL_ESP_0x318_ESP_AvlIndcn_HDC\020\220&\022\'\n\"SIGN"
  "AL_EPS_0x1C2_EPS_SteerWhlAgSig\020\223&\022*\n%SIG"
  "NAL_EPS_0x1C2_EPS_SteerWhlAgSigVld\020\224&\022\'\n"
  "\"SIGNAL_BCM_0x335_BCM_WiprInSrvPosn\020\230&\022)"
  "\n$SIGNAL_BCM_0x335_BCM_IntLampTiSetSts\020\231"
  "&\022-\n(SIGNAL_BCM_0x335_BCM_WaterPosnSnsrS"
  "wtSts\020\232&\022\'\n\"SIGNAL_BCM_0x335_BCM_ExtLamp"
  "SwtSts\020\236&\022.\n)SIGNAL_BCM_0x335_BCM_RainCl"
  "sSunroofSetSts\020\244&\022,\n\'SIGNAL_BCM_0x335_BC"
  "M_MirrLockAutoSetSts\020\245&\022.\n)SIGNAL_BCM_0x"
  "335_BCM_DangerAlrmLampSwtSts\020\247&\022*\n%SIGNA"
  "L_BCM_0x335_BCM_ReDefrstHeatgCmd\020\250&\022+\n&S"
  "IGNAL_BCM_0x335_BCM_LeTrunLampOutpCmd\020\253&"
  "\022+\n&SIGNAL_BCM_0x335_BCM_RiTrunLampOutpC"
  "md\020\254&\022\'\n\"SIGNAL_BCM_0x335_BCM_HiBeamOutp"
  "Cmd\020\257&\022\'\n\"SIGNAL_BCM_0x335_BCM_LoBeamOut"
  "pCmd\020\260&\022)\n$SIGNAL_BCM_0x335_BCM_PosnLamp"
  "OutpCmd\020\261&\022*\n%SIGNAL_BCM_0x335_BCM_ReFog"
  "LampOutpCmd\020\263&\022%\n SIGNAL_BCM_0x335_BCM_F"
  "rntWiprSpd\020\264&\022#\n\036SIGNAL_BCM_0x335_BCM_Ve"
  "hAmbBri\020\266&\022(\n#SIGNAL_BCM_0x343_BCM_FrntH"
  "oodLidSts\020\271&\022-\n(SIGNAL_BCM_0x343_BCM_Sun"
  "roofAntipinchSts\020\272&\022+\n&SIGNAL_BCM_0x343_"
  "BCM_FrntLeDoorLockSts\020\273&\022&\n!SIGNAL_BCM_0"
  "x343_BCM_TrRelsSwtSts\020\274&\022(\n#SIGNAL_BCM_0"
  "x343_BCM_LockAllDoorCmd\020\275&\022\'\n\"SIGNAL_BCM"
  "_0x343_BCM_LeFrntDoorSts\020\276&\022\'\n\"SIGNAL_BC"
  "M_0x343_BCM_RiFrntDoorSts\020\277&\022\037\n\032SIGNAL_B"
  "CM_0x343_BCM_TrSts\020\300&\022%\n SIGNAL_BCM_0x34"
  "3_BCM_AntithftSts\020\301&\022\'\n\"SIGNAL_BCM_0x343"
  "_BCM_CenLockSwtSts\020\302&\022)\n$SIGNAL_BCM_0x34"
  "3_BCM_DoorUnlockSetFb\020\303&\022%\n SIGNAL_BCM_0"
  "x343_BCM_RiReDoorSts\020\304&\022%\n SIGNAL_BCM_0x"
  "343_BCM_LeReDoorSts\020\305&\022&\n!SIGNAL_BCM_0x3"
  "43_BCM_LeFrntWinSts\020\306&\022&\n!SIGNAL_BCM_0x3"
  "43_BCM_RiFrntWinSts\020\307&\022$\n\037SIGNAL_BCM_0x3"
  "43_BCM_LeReWinSts\020\310&\022#\n\036SIGNAL_BCM_0x343"
  "_BCM_RiReWinSt\020\311&\022(\n#SIGNAL_BCM_0x343_BC"
  "M_FolwMeSetStsFb\020\312&\022.\n)SIGNAL_BCM_0x343_"
  "BCM_DrvrBoorUnlckOutpCmd\020\313&\022.\n)SIGNAL_BC"
  "M_0x343_BCM_PassDoorUnlckOutpCmd\020\314&\022&\n!S"
  "IGNAL_BCM_0x343_BCM_LeDRLOutpCmd\020\315&\022&\n!S"
  "IGNAL_BCM_0x343_BCM_RiDRLOutpCmd\020\316&\022+\n&S"
  "IGNAL_BCM_0x343_BCM_ArmedClsWinSetSts\020\317&"
  "\022,\n\'SIGNAL_BCM_0x343_BCM_OffAutoUnlckSet"
  "Sts\020\320&\022)\n$SIGNAL_BCM_0x343_BCM_SunroofPo"
  "snInfo\020\321&\022\'\n\"SIGNAL_BCM_0x343_BCM_Sunroo"
  "fOpenAr\020\322&\022)\n$SIGNAL_BCM_0x343_BCM_Sunro"
  "ofRunngSts\020\323&\022!\n\034SIGNAL_BCM_0x343_BCM_Mi"
  "rrCmd\020\324&\022)\n$SIGNAL_BCM_0x51E_BCM_Applian"
  "ceClsLvl\020\325&\022/\n*SIGNAL_DSMC_0x4F1_DSMC_Dr"
  "vrSeatTrackSwtSts\020\330&\0220\n+SIGNAL_DSMC_0x4F"
  "1_DSMC_DrvrSeatHeiAdjSwtSts\020\331&\0221\n,SIGNAL"
  "_DSMC_0x4F1_DSMC_DrvrSeatBackAdjSwtSts\020\332"
  "&\022/\n*SIGNAL_DSMC_0x4F1_DSMC_DrvrSeatWelF"
  "ctSetFb\020\335&\022/\n*SIGNAL_DSMC_0x4F1_DSMC_ReM"
  "irrAutoDwnFlipFb\020\336&\022,\n\'SIGNAL_DSMC_0x4F3"
  "_DSMC_RiMirrrXDiecPosn\020\340&\022,\n\'SIGNAL_DSMC"
  "_0x4F3_DSMC_RiMirrrYDircPosn\020\341&\022(\n#SIGNA"
  "L_DSMC_0x4F3_DSMC_LeMirrrXPosn\020\342&\022(\n#SIG"
  "NAL_DSMC_0x4F3_DSMC_LeMirrrYPosn\020\343&\022-\n(S"
  "IGNAL_DSMC_0x4F5_DSMC_DrvrSeatTrackPosn\020"
  "\350&\022+\n&SIGNAL_DSMC_0x4F5_DSMC_DrvrSeatHei"
  "Posn\020\351&\022,\n\'SIGNAL_DSMC_0x4F5_DSMC_DrvrSe"
  "atBackPosn\020\352&\022.\n)SIGNAL_DSMC_0x62_DSMC_D"
  "rvrSeatMemRecallFb\020\354&\022/\n*SIGNAL_DSMC_0x6"
  "2_DSMC_DrvrSeatMemDataUpdFb\020\355&\022,\n\'SIGNAL"
  "_DSMC_0x518_DSMC_DrvrSeatHeatgSts\020\357&\022,\n\'"
  "SIGNAL_DSMC_0x518_DSMC_DrvrSeatVentnSts\020"
  "\360&\022/\n*SIGNAL_DSMC_0x518_DSMC_DrvrSeatTra"
  "ckAdjSts\020\361&\022-\n(SIGNAL_DSMC_0x518_DSMC_Dr"
  "vrSeatHeiAdjSts\020\362&\022.\n)SIGNAL_DSMC_0x518_"
  "DSMC_DrvrSeatBackAdjSts\020\363&\022(\n#SIGNAL_DSM"
  "C_0x518_DSMC_LumbarUpdSts\020\365&\022(\n#SIGNAL_D"
  "SMC_0x518_DSMC_LumbarDwnSts\020\366&\022(\n#SIGNAL"
  "_DSMC_0x518_DSMC_LumbarFwdSts\020\367&\022*\n%SIGN"
  "AL_DSMC_0x518_DSMC_LumbarBackwSts\020\370&\022)\n$"
  "SIGNAL_DSMC_0x518_DSMC_LRSeatHeatSts\020\372&\022"
  "1\n,SIGNAL_DSMC_0x518_DSMC_SeatHeatAutoDw"
  "nEnaSts\020\373&\022(\n#SIGNAL_DSMC_0x518_DSMC_ReM"
  "irrLeRiFb\020\374&\022,\n\'SIGNAL_DSMC_0x518_DSMC_M"
  "ASFL_SeatMasMod\020\376&\0221\n,SIGNAL_DSMC_0x518_"
  "DSMC_MASFL_SeatMasGradeSts\020\377&\022,\n\'SIGNAL_"
  "PLG_0x64_PLG_UsrSetTrMaxHeiResFb\020\201\'\022(\n#S"
  "IGNAL_PLG_0x471_PLG_UsrSetTrMaxHei\020\202\'\022\"\n"
  "\035SIGNAL_PLG_0x471_PLG_LeTrPosn\020\203\'\022,\n\'SIG"
  "NAL_PLG_0x471_PLG_SoundRemdngReq_EHU\020\204\'\022"
  "%\n SIGNAL_PLG_0x471_PLG_SysFltIndcn\020\206\'\022\'"
  "\n\"SIGNAL_PLG_0x471_PLG_TrSwtStsIndcn\020\207\'\022"
  "!\n\034SIGNAL_PLG_0x471_PLG_OperMod\020\211\'\022&\n!SI"
  "GNAL_PLG_0x471_PLG_AntipinchSts\020\212\'\022\"\n\035SI"
  "GNAL_PAS_0x574_PAS_Sts_FPAS\020\216\'\022\"\n\035SIGNAL"
  "_PAS_0x574_PAS_Sts_RPAS\020\220\'\022&\n!SIGNAL_PAS"
  "_0x576_PAS_SoundIndcn_F\020\243\'\022&\n!SIGNAL_PAS"
  "_0x576_PAS_SoundIndcn_R\020\244\'\022&\n!SIGNAL_ICC"
  "_0x526_EHU_IntegtCrsSwt\020\253\'\022 \n\033SIGNAL_ICC"
  "_0x526_EHU_HMASet\020\265\'\022,\n\'SIGNAL_ICC_0x4DF"
  "_EHU_LifeSignMonitorSwt\020\302\'\022)\n$SIGNAL_ICC"
  "_0x531_ICC_RemSentryModSts\020\320\'\022(\n#SIGNAL_"
  "ICC_0x531_ICM_DispVehSpdUnit\020\323\'\022.\n)SIGNA"
  "L_SDM_0x319_SDM_AirBagSysAlrmLampSts\020\332\'\022"
  "+\n&SIGNAL_SDM_0x319_SDM_SecuBltAlrmSts_R"
  "L\020\333\'\022+\n&SIGNAL_SDM_0x319_SDM_SecuBltAlrm"
  "Sts_RM\020\334\'\022+\n&SIGNAL_SDM_0x319_SDM_SecuBl"
  "tAlrmSts_RR\020\335\'\022-\n(SIGNAL_SDM_0x319_SDM_D"
  "rverSecuBltAlrmSts\020\336\'\022,\n\'SIGNAL_SDM_0x31"
  "9_SDM_PassSeatBltBucdSts\020\337\'\022\"\n\035SIGNAL_SD"
  "M_0x319_SDM_CllsnSig\020\340\'\022(\n#SIGNAL_SDM_0x"
  "319_SDM_PassSeatOccSts\020\341\'\022(\n#SIGNAL_CIM_"
  "0x310_CIM_FrntWiprSwtSts\020\350\'\022&\n!SIGNAL_CI"
  "M_0x310_CIM_ReWiprSwtSts\020\352\'\0222\n-SIGNAL_VC"
  "U_0x554_VCU_ACChrgElectcLockStsFbSig\020\353\'\022"
  ",\n\'SIGNAL_VCU_0x52C_VCU_ChrgDischrgCrtDi"
  "sp\020\360\'\022)\n$SIGNAL_VCU_0x52C_VCU_ChrgDchaPw"
  "rDisp\020\361\'\022,\n\'SIGNAL_VCU_0x52D_VCU_ElectcL"
  "ockFltPromt\020\362\'\022,\n\'SIGNAL_BMS_0x51B_BMS_C"
  "hrgOprtGuidePromt\020\364\'\022*\n%SIGNAL_BMS_0x51B"
  "_BMS_ChrgRltdStsPromt\020\365\'\0221\n,SIGNAL_BMS_0"
  "x51B_BMS_ChrgDchaStopReasonPromt\020\366\'\022#\n\036S"
  "IGNAL_SLC_0x337_SLC_CrtColorR\020\226)\022#\n\036SIGN"
  "AL_SLC_0x337_SLC_CrtColorG\020\227)\022#\n\036SIGNAL_"
  "SLC_0x337_SLC_CrtColorB\020\230)\022 \n\033SIGNAL_SLC"
  "_0x337_SLC_CrtBri\020\231)\022$\n\037SIGNAL_SLC_0x337"
  "_SLC_AlcCstmSts\020\233)\022+\n&SIGNAL_SLC_0x3EC_S"
  "LC_CourtesyFctModSts\020\236)\022(\n#SIGNAL_SLC_0x"
  "3EC_SLC_MusicRhythmSts\020\237)\022&\n!SIGNAL_SLC_"
  "0x3EC_SLC_BriBreathSts\020\240)\022)\n$SIGNAL_SLC_"
  "0x3EC_SLC_VehSpdRhythmSts\020\242)\022\"\n\035SIGNAL_S"
  "LC_0x3EC_SLC_DrvMdSts\020\243)\022\"\n\035SIGNAL_SLC_0"
  "x3EC_SLC_AcModSts\020\244)\022 \n\033SIGNAL_SLC_0x3EC"
  "_SLC_DOWSts\020\245)\022\035\n\030SIGNAL_SLC_0x3EC_SLC_B"
  "ri\020\246)\022*\n%SIGNAL_SLC_0x3EC_SLC_ColourBrea"
  "thSts2\020\247)\022$\n\037SIGNAL_SLC_0x3EC_SLC_AlcSpc"
  "hSts\020\251)\022*\n%SIGNAL_SLC_0x3EC_SLC_AlcMobCh"
  "aRemdSts\020\252)\022$\n\037SIGNAL_SLC_0x3EC_SLC_LOGO"
  "LigSts\020\253)\022+\n&SIGNAL_SLC_0x3EC_SLC_Contra"
  "stColorSts1\020\254)\022+\n&SIGNAL_SLC_0x3EC_SLC_C"
  "ontrastColorSts2\020\255)\022+\n&SIGNAL_SLC_0x3EC_"
  "SLC_ContrastColorSts3\020\256)\022\'\n\"SIGNAL_ICC_0"
  "x320_ICC_LightCrlStsFB\020\337)\022*\n%SIGNAL_ICC_"
  "0x320_ICC_ReFogLampOutpCmd\020\340)\022\"\n\035SIGNAL_"
  "ICC_0x532_ICC_Ctrl_Cmd\020\350)\022%\n SIGNAL_ICC_"
  "0x532_ICC_OpenPercCmd\020\351)\022-\n(SIGNAL_ICC_0"
  "x614_EHU_ResvACChrgStrtTi_Hr\020\251*\022/\n*SIGNA"
  "L_ICC_0x614_EHU_ResvACChrgStrtTi_Mins\020\252*"
  "\022+\n&SIGNAL_ICC_0x614_EHU_ResvACChrgStrtS"
  "et\020\253*\0221\n,SIGNAL_ICC_0x614_EHU_MonResvACC"
  "hrgRepStrtSet\020\254*\022,\n\'SIGNAL_ICC_0x614_EHU"
  "_ResvACChrgEndTi_Hr\020\263*\022.\n)SIGNAL_ICC_0x6"
  "14_EHU_ResvACChrgEndTi_Mins\020\264*\022*\n%SIGNAL"
  "_ICC_0x614_EHU_ResvACChrgEndSet\020\265*\0220\n+SI"
  "GNAL_ICC_0x614_EHU_MonResvACChrgRepEndSe"
  "t\020\266*\0220\n+SIGNAL_VCU_0x357_VCU_RefrshModRe"
  "strntFctCmd\020\303*\022/\n*SIGNAL_VCU_0x511_VCU_R"
  "emPwrBattHeatgEndCmd\020\306*\0220\n+SIGNAL_VCU_0x"
  "511_VCU_RemBattHeatgFailReason\020\307*\022+\n&SIG"
  "NAL_VCU_0x511_VCU_PetsModFobdReason\020\310*\022%"
  "\n SIGNAL_VCU_0x511_VCU_PetsModWarn\020\311*\022*\n"
  "%SIGNAL_VCU_0x511_VCU_VehCrtChrgEndSOC\020\313"
  "*\022\037\n\032SIGNAL_ECC_0x582_ECC_InsdT\020\315*\022&\n!SI"
  "GNAL_ECC_0x582_ECC_ParticleConc\020\316*\022%\n SI"
  "GNAL_VCU_0x105_VCU_AlrmLamp_FS\020\327*\022&\n!SIG"
  "NAL_VCU_0x105_VCU_CruiseFltTip\020\330*\022\'\n\"SIG"
  "NAL_VCU_0x105_VCU_DrvModExtnSig\020\331*\022#\n\036SI"
  "GNAL_VCU_0x105_VCU_CruiseSts\020\332*\022&\n!SIGNA"
  "L_VCU_0x105_VCU_CruiseAimSpd\020\333*\022+\n&SIGNA"
  "L_VCU_0x503_VCU_MemChrgRmnMilgThd\020\336*\022%\n "
  "SIGNAL_VCU_0x503_VCU_UsrHMIPromt\020\344*\022)\n$S"
  "IGNAL_VCU_0x503_VCU_ResvChrgStsDisp\020\345*\0225"
  "\n0SIGNAL_VCU_0x504_VCU_MonrPwrBattThermR"
  "unawayAlrm\020\347*\022)\n$SIGNAL_VCU_0x504_VCU_Ln"
  "chCtrlTrigRmn\020\350*\022&\n!SIGNAL_VCU_0x504_VCU"
  "_ShiftOperRmn\020\351*\022*\n%SIGNAL_VCU_0x504_VCU"
  "_MCUFSysOverTDisp\020\354*\022*\n%SIGNAL_VCU_0x504"
  "_VCU_MCURSysOverTDisp\020\355*\022\'\n\"SIGNAL_VCU_0"
  "x504_VCU_VehSysFltLamp\020\356*\022-\n(SIGNAL_VCU_"
  "0x504_VCU_RmnUsrClsECCDispCmd\020\357*\0220\n+SIGN"
  "AL_VCU_0x504_VCU_RmnUsrECCFctLmtDispCmd\020"
  "\360*\022*\n%SIGNAL_VCU_0x504_VCU_LnchCtrlModDi"
  "Rmn\020\361*\022*\n%SIGNAL_VCU_0x505_VCU_EgyRecovF"
  "orbnFlg\020\371*\022,\n\'SIGNAL_VCU_0x505_VCU_DrvPw"
  "rLimIndcrLamp\020\372*\022&\n!SIGNAL_VCU_0x505_VCU"
  "_BattFltIndcn\020\375*\022\'\n\"SIGNAL_VCU_0x505_VCU"
  "_ChrgIndcrLamp\020\376*\022%\n SIGNAL_VCU_0x505_VC"
  "U_ChrgGunStrt\020\202+\022&\n!SIGNAL_VCU_0x50C_VCU"
  "_SOCLoChrgRmn\020\206+\022%\n SIGNAL_BMS_0x215_BMS"
  "_AlrmLamp_FS\020\216+\022\'\n\"SIGNAL_BMS_0x240_BMS_"
  "ChrgCRateDisp\020\222+\022+\n&SIGNAL_BMS_0x363_BMS"
  "_FbRemHeatgOperSts\020\230+\022\'\n\"SIGNAL_BMS_0x36"
  "3_BMS_VehExtDchaSts\020\231+\022-\n(SIGNAL_BMS_0x3"
  "63_BMS_ChrgInsulFctOpenSts\020\234+\022!\n\034SIGNAL_"
  "APA_0x2A0_APA_FMEBSts\020\265+\022!\n\034SIGNAL_APA_0"
  "x2A0_APA_RMEBSts\020\267+\022\"\n\035SIGNAL_APA_0x558_"
  "APA_Sts_FPAS\020\320+\022\"\n\035SIGNAL_APA_0x558_APA_"
  "Sts_RPAS\020\323+\022&\n!SIGNAL_ESP_0x332_ESP_FltI"
  "ndcn_HHC\020\243,\022\'\n\"SIGNAL_ESP_0x332_ESP_Actv"
  "Indcn_AVH\020\244,\022&\n!SIGNAL_ESP_0x332_ESP_Swt"
  "Indcn_AVH\020\245,\022&\n!SIGNAL_ESP_0x332_ESP_Flt"
  "Indcn_AVH\020\246,\022\"\n\035SIGNAL_EPS_0x470_EPS_Flt"
  "Indcn\020\252,\022)\n$SIGNAL_BCM_0x321_BCM_RLS_LIg"
  "htSwtReq\020\253,\022.\n)SIGNAL_BCM_0x321_BCM_SWH_"
  "SteerWhlHeatgSts\020\254,\0222\n-SIGNAL_BCM_0x321_"
  "BCM_TimeoutPower_OffFeedback\020\255,\022,\n\'SIGNA"
  "L_BCM_0x321_BCM_LightReqReason_RLS\020\263,\022*\n"
  "%SIGNAL_BCM_0x321_BCM_OffUnlckSetStsFb\020\264"
  ",\022*\n%SIGNAL_BCM_0x345_BCM_SunshadeRunngS"
  "ts\020\270,\022*\n%SIGNAL_BCM_0x345_BCM_SunshadePo"
  "snInfo\020\271,\022\'\n\"SIGNAL_BCM_0x539_BCM_LePosn"
  "LampFlt\020\272,\022\'\n\"SIGNAL_BCM_0x539_BCM_RiPos"
  "nLampFlt\020\273,\022%\n SIGNAL_BCM_0x539_BCM_LeLo"
  "BeamFlt\020\274,\022%\n SIGNAL_BCM_0x539_BCM_RiLoB"
  "eamFlt\020\275,\022%\n SIGNAL_BCM_0x539_BCM_LeHiBe"
  "amFlt\020\276,\022%\n SIGNAL_BCM_0x539_BCM_RiHiBea"
  "mFlt\020\277,\022\"\n\035SIGNAL_BCM_0x539_BCM_LeDRLFlt"
  "\020\300,\022\"\n\035SIGNAL_BCM_0x539_BCM_RiDRLFlt\020\301,\022"
  "*\n%SIGNAL_BCM_0x539_BCM_LeFrntFogLampFlt"
  "\020\302,\022*\n%SIGNAL_BCM_0x539_BCM_RiFrntFogLam"
  "pFlt\020\303,\022&\n!SIGNAL_BCM_0x539_BCM_ReFogLam"
  "pFlt\020\304,\022&\n!SIGNAL_BCM_0x539_BCM_LoBrkLam"
  "pFlt\020\305,\022&\n!SIGNAL_BCM_0x539_BCM_HiBrkLam"
  "pFlt\020\306,\022$\n\037SIGNAL_BCM_0x539_BCM_RvsLampF"
  "lt\020\307,\022\'\n\"SIGNAL_BCM_0x539_BCM_LeTurnLamp"
  "Flt\020\310,\022\'\n\"SIGNAL_BCM_0x539_BCM_RiTurnLam"
  "pFlt\020\311,\022)\n$SIGNAL_BCM_0x539_BCM_LicPlate"
  "LampFlt\020\312,\022(\n#SIGNAL_BCM_0x539_BCM_WinLo"
  "ckSwInput\020\313,\022.\n)SIGNAL_MPC_0x32C_ADAS_Ha"
  "ndsOffTakeOverReq\020\336,\022$\n\037SIGNAL_MPC_0x32C"
  "_ADAS_AudioWarn\020\346,\022)\n$SIGNAL_MPC_0x32E_M"
  "PC_OverSpdWarn_SLA\020\357,\022(\n#SIGNAL_MPC_0x32"
  "E_MPC_SpdLimUnit_SLA\020\365,\022$\n\037SIGNAL_MPC_0x"
  "32E_MPC_SpdLim_SLA\020\366,\022#\n\036SIGNAL_MPC_0x33"
  "C_ADAS_Warn_FCW\020\367,\022\"\n\035SIGNAL_MPC_0x33C_A"
  "DAS_Sts_FCW\020\371,\022\"\n\035SIGNAL_MPC_0x33C_ADAS_"
  "Sts_AEB\020\372,\022*\n%SIGNAL_MPC_0x347_ADAS_Take"
  "OverReq_ACC\020\215-\022%\n SIGNAL_MPC_0x347_ADAS_"
  "SpdLim_ASL\020\221-\022(\n#SIGNAL_MPC_0x347_ADAS_S"
  "pdLimSts_ASL\020\222-\022&\n!SIGNAL_MPC_0x347_ADAS"
  "_ACC_OperTxt\020\223-\022!\n\034SIGNAL_MPC_0x334_MPC_"
  "Sts_HMA\020\252-\022\"\n\035SIGNAL_MPC_0x334_MPC_WarnS"
  "ign\020\255-\022\"\n\035SIGNAL_MPC_0x334_MPC_FobdSign\020"
  "\256-\022&\n!SIGNAL_MPC_0x334_MPC_OverTakeSign\020"
  "\257-\022$\n\037SIGNAL_MPC_0x334_MPC_FrntCamBli\020\260-"
  "\022\"\n\035SIGNAL_MPC_0x334_MPC_HMASetFb\020\261-\022$\n\037"
  "SIGNAL_MPC_0x334_MPC_FrntCamFlt\020\262-\022&\n!SI"
  "GNAL_MPC_0x340_ADAS_LeLineColor\020\272-\022&\n!SI"
  "GNAL_MPC_0x340_ADAS_RiLineColor\020\273-\022)\n$SI"
  "GNAL_MPC_0x340_ADAS_IntegtCrsSwtFb\020\301-\022#\n"
  "\036SIGNAL_MPC_0x340_ADAS_FltIndcr\020\304-\022\'\n\"SI"
  "GNAL_MPC_0x340_ADAS_IntecnFltTxt\020\306-\022)\n$S"
  "IGNAL_CMRR_RL_0x338_CMRR_RL_Sts_LCA\020\311-\022,"
  "\n\'SIGNAL_CMRR_RL_0x338_CMRR_RL_LeWarn_DO"
  "W\020\313-\022-\n(SIGNAL_CMRR_RL_0x338_CMRR_RL_LeW"
  "arn_RCTA\020\320-\022-\n(SIGNAL_MPC_0x243_MPC_CMRR"
  "_FR_RiWarn_FCTA\020\336-\022-\n(SIGNAL_MPC_0x243_M"
  "PC_CMRR_FL_LeWarn_FCTA\020\342-\022-\n(SIGNAL_TBOX"
  "_0x4F4_TBOX_ResvACChrgOpenSts\020\377-\022/\n*SIGN"
  "AL_TBOX_0x62E_TBOX_ResvACChrgStrtTi_Hr\020\204"
  ".\0221\n,SIGNAL_TBOX_0x62E_TBOX_ResvACChrgSt"
  "rtTi_Mins\020\206.\0223\n.SIGNAL_TBOX_0x62E_TBOX_M"
  "onResvACChrgRepStrtSet\020\207.\022.\n)SIGNAL_TBOX"
  "_0x62E_TBOX_ResvACChrgEndTi_Hr\020\216.\0220\n+SIG"
  "NAL_TBOX_0x62E_TBOX_ResvACChrgEndTi_Mins"
  "\020\217.\022,\n\'SIGNAL_TBOX_0x62E_TBOX_ResvACChrg"
  "EndSet\020\220.\0222\n-SIGNAL_TBOX_0x62E_TBOX_MonR"
  "esvACChrgRepEndSet\020\221.\022\035\n\030SIGNAL_HUD_0x56"
  "2_HUD_Swt\020\230.\022#\n\036SIGNAL_HUD_0x562_HUD_Crt"
  "SysSts\020\231.\022 \n\033SIGNAL_HUD_0x562_HUD_ILLAdj"
  "\020\232.\022 \n\033SIGNAL_HUD_0x562_HUD_HeiAdj\020\233.\022 \n"
  "\033SIGNAL_HUD_0x562_HUD_ModSwt\020\234.\022\'\n\"SIGNA"
  "L_HUD_0x562_HUD_SnowModSwtSts\020\235.\022%\n SIGN"
  "AL_HUD_0x562_HUD_CrtLanguage\020\236.\022%\n SIGNA"
  "L_TBOX_0x62F_TBOX_CrtTi_Day\020\237.\022$\n\037SIGNAL"
  "_TBOX_0x62F_TBOX_CrtTi_Hr\020\240.\022&\n!SIGNAL_T"
  "BOX_0x62F_TBOX_CrtTi_Mins\020\241.\022$\n\037SIGNAL_T"
  "BOX_0x62F_TBOX_CrtTi_Yr\020\242.\022%\n SIGNAL_TBO"
  "X_0x62F_TBOX_CrtTi_Mth\020\243.\022%\n SIGNAL_TBOX"
  "_0x62F_TBOX_CrtTi_Sec\020\244.\022)\n$SIGNAL_MCU_R"
  "_0x151_MCU_R_AlrmLamp_FS\020\305.\022\'\n\"SIGNAL_ES"
  "M_0x30C_ESM_SpoilerModSts\020\316.\022.\n)SIGNAL_E"
  "SM_0x30C_ESM_SpoilerMovementStsFB\020\320.\022)\n$"
  "SIGNAL_ESM_0x30C_ESM_SpoilerCrlStsFB\020\321.\022"
  "1\n,SIGNAL_ESM_0x30C_ESM_SpoilerWelcomeFu"
  "nSetSts\020\322.\022#\n\036SIGNAL_ICC_0x3C6_ICC_EPSMo"
  "dReq\020\335.\022/\n*SIGNAL_ICC_0x3C6_ICC_BrakeEgy"
  "RecovIntenReq\020\336.\022#\n\036SIGNAL_ICC_0x3C6_ICC"
  "_AVHSwtSig\020\337.\022$\n\037SIGNAL_ICC_0x3C6_ICC_Dr"
  "vgModReq\020\340.\022!\n\034SIGNAL_ICC_0x3C6_ICC_AFON"
  "Sts\020\341.\022&\n!SIGNAL_ICC_0x3C6_ICC_AFChannel"
  "Set\020\342.\022)\n$SIGNAL_ICC_0x3C6_ICC_AFConcent"
  "ration\020\343.\022$\n\037SIGNAL_ICC_0x3C6_ICC_WashCa"
  "rSwt\020\344.\022\'\n\"SIGNAL_ICC_0x3C6_ICC_SpoilerM"
  "odSwt\020\345.\022.\n)SIGNAL_ICC_0x3C6_ICC_Spoiler"
  "WelcomeFunSwt\020\346.\022!\n\034SIGNAL_ICC_0x3C6_ICC"
  "_WormSts\020\347.\022$\n\037SIGNAL_ICC_0x3C6_ICC_Acce"
  "ModReq\020\350.\022&\n!SIGNAL_ICC_0x3C6_ICC_ParkCh"
  "argeSt\020\352.\022\"\n\035SIGNAL_AMP_0x49_AMP_BalSetS"
  "ts\020\361.\022$\n\037SIGNAL_AMP_0x49_AMP_FaderSetSts"
  "\020\364.\022#\n\036SIGNAL_AMP_0x49_AMP_IESSModSts\020\366."
  "\022)\n$SIGNAL_AMP_0x49_AMP_LoFrqAudioSetSts"
  "\020\367.\022*\n%SIGNAL_AMP_0x49_AMP_MidFrqAudioSe"
  "tSts\020\371.\022)\n$SIGNAL_AMP_0x49_AMP_HiFrqAudi"
  "oSetSts\020\373.\022%\n SIGNAL_AMP_0x49_AMP_HFTVol"
  "SetSts\020\375.\022%\n SIGNAL_AMP_0x49_AMP_NavVolS"
  "etSts\020\377.\022%\n SIGNAL_AMP_0x49_AMP_MaiVolSe"
  "tSts\020\200/\022%\n SIGNAL_ICC_0x3A8_ICC_LBAdjust"
  "Set\020\210/\022%\n SIGNAL_SLC_0x3A7_SLC_LBAdjustS"
  "ts\020\224/\022+\n&SIGNAL_SLC_0x3A7_SLC_ContrastCo"
  "lorSts6\020\225/\022+\n&SIGNAL_SLC_0x3A7_SLC_Contr"
  "astColorSts4\020\226/\022*\n%SIGNAL_SLC_0x3A7_SLC_"
  "ContrastColorSts\020\227/\022\'\n\"SIGNAL_SLC_0x3A7_"
  "SLC_AutoColorSts1\020\233/\022&\n!SIGNAL_ICC_0x327"
  "_ICC_ScrnBriLeSet\020\234/\022(\n#SIGNAL_ICC_0x327"
  "_ICC_ScrnBriAutoSet\020\235/\022&\n!SIGNAL_ICC_0x3"
  "27_ICC_HumAiSpchSts\020\236/\022#\n\036SIGNAL_ICC_0x3"
  "1B_ICC_MusicFrq1\020\237/\022#\n\036SIGNAL_ICC_0x31B_"
  "ICC_MusicFrq2\020\240/\022#\n\036SIGNAL_ICC_0x31B_ICC"
  "_MusicFrq3\020\241/\022#\n\036SIGNAL_ICC_0x31B_ICC_Mu"
  "sicFrq4\020\242/\022#\n\036SIGNAL_ICC_0x31B_ICC_Music"
  "Frq5\020\243/\022#\n\036SIGNAL_ICC_0x31B_ICC_MusicFrq"
  "6\020\244/\022#\n\036SIGNAL_ICC_0x31B_ICC_MusicFrq7\020\245"
  "/\022#\n\036SIGNAL_ICC_0x31B_ICC_MusicFrq8\020\246/\022*"
  "\n%SIGNAL_ICC_0x3ED_ICC_AlcBriBreaModSwt\020"
  "\250/\022+\n&SIGNAL_ICC_0x3ED_ICC_AlcClrBreaMod"
  "Swt2\020\252/\022+\n&SIGNAL_ICC_0x3ED_ICC_AlcMusic"
  "RhyModSwt\020\253/\022)\n$SIGNAL_ICC_0x3ED_ICC_Alc"
  "SpdRhyModSwt\020\255/\022$\n\037SIGNAL_ICC_0x3ED_ICC_"
  "AlcSpchSwt\020\256/\022%\n SIGNAL_ICC_0x3ED_ICC_Al"
  "cAcModSwt\020\257/\022&\n!SIGNAL_ICC_0x3ED_ICC_Alc"
  "DOWModSwt\020\260/\022)\n$SIGNAL_ICC_0x3ED_ICC_Alc"
  "DrvModRhySwt\020\261/\022-\n(SIGNAL_ICC_0x3ED_ICC_"
  "AlcMobChaRemdModSwt\020\262/\022&\n!SIGNAL_ICC_0x3"
  "ED_ICC_AlcWelModSwt\020\263/\022$\n\037SIGNAL_ICC_0x3"
  "ED_ICC_VehMdMeSts\020\264/\022)\n$SIGNAL_ICC_0x3ED"
  "_ICC_ApplianceClsLvl\020\265/\022/\n*SIGNAL_ICC_0x"
  "3ED_ICC_ScrnBriLeSet_DualArea\020\266/\022(\n#SIGN"
  "AL_ICC_0x3ED_ICC_Airvetn_AL_Swt\020\267/\022+\n&SI"
  "GNAL_ICC_0x3ED_ICC_AlcDualAreaModSwt\020\270/\022"
  ",\n\'SIGNAL_DSMC_0x3AA_DSMC_PasSeatTrackPo"
  "sn\020\300/\022+\n&SIGNAL_DSMC_0x3AA_DSMC_PasSeatB"
  "ackPosn\020\302/\022.\n)SIGNAL_DSMC_0x66_DSMC_Pass"
  "SeatMemRecallFb\020\304/\022/\n*SIGNAL_DSMC_0x66_D"
  "SMC_PassSeatMemDataUpdFb\020\305/\022/\n*SIGNAL_DS"
  "MC_0x328_DSMC_PassSeatTrackSwtSts\020\306/\022.\n)"
  "SIGNAL_DSMC_0x328_DSMC_PassSeatBackSwtSt"
  "s\020\307/\022,\n\'SIGNAL_DSMC_0x512_DSMC_PassSeatH"
  "eatgSts\020\313/\022,\n\'SIGNAL_DSMC_0x512_DSMC_Pas"
  "sSeatVentnSts\020\314/\022/\n*SIGNAL_DSMC_0x512_DS"
  "MC_PassSeatTrackAdjSts\020\315/\022.\n)SIGNAL_DSMC"
  "_0x512_DSMC_PassSeatBackAdjSts\020\316/\022)\n$SIG"
  "NAL_DSMC_0x512_DSMC_RRSeatHeatSts\020\317/\022,\n\'"
  "SIGNAL_DSMC_0x512_DSMC_PassLumbarUpdSts\020"
  "\321/\022,\n\'SIGNAL_DSMC_0x512_DSMC_PassLumbarD"
  "wnSts\020\322/\022,\n\'SIGNAL_DSMC_0x512_DSMC_PassL"
  "umbarFwdSts\020\323/\022.\n)SIGNAL_DSMC_0x512_DSMC"
  "_PassLumbarBackwSts\020\324/\0221\n,SIGNAL_DSMC_0x"
  "512_DSMC_SecRowSeatWelFctSetFb\020\327/\022,\n\'SIG"
  "NAL_DSMC_0x512_DSMC_MASFR_SeatMasMod\020\331/\022"
  "1\n,SIGNAL_DSMC_0x512_DSMC_MASFR_SeatMasG"
  "radeSts\020\332/\0220\n+SIGNAL_VCU_0x49C_VCU_ChrgG"
  "unAntithftOpenSts\020\344/\022(\n#SIGNAL_VCU_0x49C"
  "_VCU_EHUChrgDchaReq\020\346/\022(\n#SIGNAL_VCU_0x4"
  "9C_VCU_ChrgDchaBtnReq\020\347/\022%\n SIGNAL_BMS_0"
  "x49D_BMS_ChrgStsDisp\020\350/\022 \n\033SIGNAL_EPS_0x"
  "475_EPS_ModSts\020\351/\022(\n#SIGNAL_BMS_0x3C3_BM"
  "S_BattLowTempInd\020\352/\022*\n%SIGNAL_BMS_0x29F_"
  "BMS_RmngChrgTiDisply\020\353/\0220\n+SIGNAL_ICC_0x"
  "68_EHU_DrvrSeatTrackPercentReq\020\354/\022/\n*SIG"
  "NAL_ICC_0x68_EHU_DrvrSeatTiltPercentReq\020"
  "\355/\022*\n%SIGNAL_ICC_0x68_EHU_DrvrHeiPercent"
  "Req\020\356/\022/\n*SIGNAL_ICC_0x68_EHU_DrvrSeatBa"
  "ckPercentReq\020\357/\0220\n+SIGNAL_ICC_0x68_EHU_P"
  "assSeatTrackPercentReq\020\360/\022/\n*SIGNAL_ICC_"
  "0x68_EHU_PassSeatTiltPercentReq\020\361/\022*\n%SI"
  "GNAL_ICC_0x68_EHU_PassHeiPercentReq\020\362/\022/"
  "\n*SIGNAL_ICC_0x68_EHU_PassSeatBackPercen"
  "tReq\020\363/\022#\n\036SIGNAL_ICC_0x3AC_EHU_MirrAdjU"
  "p\020\364/\022%\n SIGNAL_ICC_0x3AC_EHU_MirrAdjDown"
  "\020\365/\022$\n\037SIGNAL_ICC_0x3AC_EHU_MirrAdjLef\020\366"
  "/\022#\n\036SIGNAL_ICC_0x3AC_EHU_MirrAdjRi\020\367/\022("
  "\n#SIGNAL_ICC_0x3AC_EHU_LeReMirrAdjCmd\020\370/"
  "\022.\n)SIGNAL_ICC_0x3AC_EHU_DrvrMirrTurnDwn"
  "Allwd\020\371/\022/\n*SIGNAL_ICC_0x3AC_ICC_ExhibCa"
  "rModNoticeFlag\020\372/\022#\n\036SIGNAL_VCU_0x102_VC"
  "U_DrvModSig\020\375/\022#\n\036SIGNAL_VCU_0x102_VCU_A"
  "clrTiReq\020\377/\022\'\n\"SIGNAL_VCU_0x3A2_VCU_Spor"
  "tModAccTi\020\2000\022)\n$SIGNAL_VCU_0x3A2_VCU_Com"
  "fortModAccTi\020\2010\022%\n SIGNAL_VCU_0x3A2_VCU_"
  "EcoModAccTi\020\2020\022*\n%SIGNAL_VCU_0x3A2_VCU_O"
  "nePedalModAccTi\020\2030\022*\n%SIGNAL_VCU_0x3A2_V"
  "CU_PersonalModAccTi\020\2040\022(\n#SIGNAL_VCU_0x3"
  "A2_VCU_DrvStyleFactor\020\2050\022\"\n\035SIGNAL_VCU_0"
  "x3A2_VCU_DrvStyle\020\2060\022-\n(SIGNAL_VCU_0x3E8"
  "_VCU_BrkEgyRecovIntenSts\020\2100\022!\n\034SIGNAL_VC"
  "U_0x3E8_VCU_WormSts\020\2110\022$\n\037SIGNAL_VCU_0x3"
  "E8_VCU_AclrModSts\020\2120\022(\n#SIGNAL_VCU_0x3AB"
  "_VCU_ChrgPortEnaFlg\020\2130\022,\n\'SIGNAL_VCU_0x3"
  "AB_VCU_OpenClsFltInfoDisp\020\2140\022.\n)SIGNAL_V"
  "CU_0x3AB_VCU_VehCurDischrgEndMile\020\2150\022+\n&"
  "SIGNAL_VCU_0x3AB_VCU_ChrgPortDoorPosSt\020\216"
  "0\022#\n\036SIGNAL_ICC_0x587_ICM_PhnMsgSts\020\2200\022,"
  "\n\'SIGNAL_ICC_0x587_ICM_PhnMsgCallingTime"
  "H\020\2210\022,\n\'SIGNAL_ICC_0x587_ICM_PhnMsgCalli"
  "ngTimeM\020\2220\022,\n\'SIGNAL_ICC_0x587_ICM_PhnMs"
  "gCallingTimeS\020\2230\022$\n\037SIGNAL_ICC_0x3B3_ICC"
  "_ModSwt_HUD\020\2270\022-\n(SIGNAL_ICC_0x3B3_ICC_C"
  "md_VoiceControlHUD\020\2300\0220\n+SIGNAL_BCM_0x3A"
  "9_BCM_EEMInfoReqQuiescentCrt\020\3040\022+\n&SIGNA"
  "L_BCM_0x3A9_BCM_EEMFiMinSavEleMod\020\3050\022*\n%"
  "SIGNAL_BCM_0x3A9_BCM_VeVMMEnumVehMdMe\020\3060"
  "\022\'\n\"SIGNAL_VCU_0x50B_VCU_CarWashModEna\020\307"
  "0\022\'\n\"SIGNAL_VCU_0x50B_VCU_CarWashModSts\020"
  "\3100\022$\n\037SIGNAL_MFS_0x514_MFS_IncFolwDst\020\3110"
  "\022$\n\037SIGNAL_MFS_0x514_MFS_DecFolwDst\020\3120\022!"
  "\n\034SIGNAL_MFS_0x514_MFS_CustBtn\020\3130\022!\n\034SIG"
  "NAL_MFS_0x514_MFS_ParkAid\020\3140\022\"\n\035SIGNAL_M"
  "FS_0x514_MFS_LeRollUp\020\3150\022#\n\036SIGNAL_MFS_0"
  "x514_MFS_LeRollDwn\020\3160\022#\n\036SIGNAL_MFS_0x51"
  "4_MFS_SrcSwtBtn\020\3200\022\'\n\"SIGNAL_MFS_0x514_M"
  "FS_VoiceRctcnBtn\020\3210\022)\n$SIGNAL_MFS_0x514_"
  "MFS_PrevSongTuneSig\020\3220\022)\n$SIGNAL_MFS_0x5"
  "14_MFS_NextSongTuneSig\020\3230\022\"\n\035SIGNAL_MFS_"
  "0x514_MFS_RiRollUp\020\3240\022#\n\036SIGNAL_MFS_0x51"
  "4_MFS_RiRollDwn\020\3250\022%\n SIGNAL_MFS_0x514_M"
  "FS_RiRollPress\020\3260\0223\n.SIGNAL_MFS_0x514_MF"
  "S_SteerWhlHeatgIndcrLampSts\020\3270\022(\n#SIGNAL"
  "_EUM_0x309_EUM_LeChildLockSts\020\3440\022(\n#SIGN"
  "AL_EUM_0x309_EUM_RiChildLockSts\020\3450\022#\n\036SI"
  "GNAL_ECC_0x4FC_ECC_AFU_SwSts\020\3520\022(\n#SIGNA"
  "L_ECC_0x4FC_ECC_AFU_ChannelSet\020\3530\022+\n&SIG"
  "NAL_ECC_0x4FC_ECC_AFU_Concentration\020\3540\022+"
  "\n&SIGNAL_ECC_0x4FC_ECC_AFU_ChannelChgSts"
  "\020\3560\022$\n\037SIGNAL_ECC_0x4FC_ECC_AFU_Ch1Sts\020\357"
  "0\022$\n\037SIGNAL_ECC_0x4FC_ECC_AFU_Ch2Sts\020\3600\022"
  "$\n\037SIGNAL_ECC_0x4FC_ECC_AFU_Ch3Sts\020\3610\022\'\n"
  "\"SIGNAL_ECC_0x4FC_ECC_AFU_Ch1LevSts\020\3620\022\'"
  "\n\"SIGNAL_ECC_0x4FC_ECC_AFU_Ch2LevSts\020\3630\022"
  "\'\n\"SIGNAL_ECC_0x4FC_ECC_AFU_Ch3LevSts\020\3640"
  "\0223\n.SIGNAL_ECC_0x4FC_ECC_AFU_Ch1Expirati"
  "onReminder\020\3650\0223\n.SIGNAL_ECC_0x4FC_ECC_AF"
  "U_Ch2ExpirationReminder\020\3660\0223\n.SIGNAL_ECC"
  "_0x4FC_ECC_AFU_Ch3ExpirationReminder\020\3670\022"
  "#\n\036SIGNAL_ICC_0x51A_ICC_CSTSwtSig\020\3730\022#\n\036"
  "SIGNAL_ICC_0x51A_ICC_PwrOffReq\020\2011\022$\n\037SIG"
  "NAL_ICC_0x51A_ICC_PreHeatReq\020\2021\0220\n+SIGNA"
  "L_VCU_0x4DC_VCU_SoktSplyInteractiveSts\020\203"
  "1\022.\n)SIGNAL_VCU_0x4DC_VCU_SoktFunOperPro"
  "mt_EHU\020\2051\022(\n#SIGNAL_VCU_0x219_VCU_ExhibC"
  "arModSig\020\2101\022,\n\'SIGNAL_ICC_0x37C_ICC_SetA"
  "CChrgCurtLimit\020\2111\022%\n SIGNAL_ICC_0x4E2_EH"
  "U_CtrlSts_PWC\020\2251\022\'\n\"SIGNAL_BCM_0x3A3_BCM"
  "_BackGndBriLvl\020\2361\022\'\n\"SIGNAL_VCU_0x3EE_VC"
  "U_PetsModReqFlg\020\2461\022&\n!SIGNAL_VCU_0x3EE_V"
  "CU_PwrAntiTheft\020\2471\022+\n&SIGNAL_ICC_0x91_IC"
  "C_LocksoundpromptSwt\020\2511\022!\n\034SIGNAL_ICC_0x"
  "91_ICC_LFWinCrl\020\2521\022!\n\034SIGNAL_ICC_0x91_IC"
  "C_RFWinCrl\020\2531\022!\n\034SIGNAL_ICC_0x91_ICC_LRW"
  "inCrl\020\2541\022!\n\034SIGNAL_ICC_0x91_ICC_RRWinCrl"
  "\020\2551\022-\n(SIGNAL_ICC_0x91_ICC_OpenDoorLampl"
  "anguage\020\2571\022\'\n\"SIGNAL_ICC_0x91_ICC_LeChil"
  "dLockSts\020\2601\022\'\n\"SIGNAL_ICC_0x91_ICC_RiChi"
  "ldLockSts\020\2611\022\'\n\"SIGNAL_ICC_0x91_ICC_Smar"
  "tOpenTrunk\020\2621\022\'\n\"SIGNAL_VCU_0x3BD_VCU_Ch"
  "rgModFunSts\020\2631\022&\n!SIGNAL_APM_0x3E0_APM_L"
  "RLockWinSts\020\2641\022&\n!SIGNAL_APM_0x3E0_APM_R"
  "RLockWinSts\020\2651\022$\n\037SIGNAL_APM_0x3E0_APM_L"
  "FWinPosFB\020\2661\022$\n\037SIGNAL_APM_0x3E0_APM_RFW"
  "inPosFB\020\2671\022$\n\037SIGNAL_APM_0x3E0_APM_LRWin"
  "PosFB\020\2701\022$\n\037SIGNAL_APM_0x3E0_APM_RRWinPo"
  "sFB\020\2711\022,\n\'SIGNAL_VCU_0x51C_VCU_ChrgStopS"
  "OCPlanVal\020\2771\022&\n!SIGNAL_ICC_0xBA_ICC_AlcC"
  "lrCstmSet\020\3532\022)\n$SIGNAL_ICC_0x69_ICC_ALcA"
  "dj_Color1Set\020\3552\022)\n$SIGNAL_ICC_0x69_ICC_A"
  "LcAdj_Color2Set\020\3562\022 \n\033SIGNAL_SLC_0x510_S"
  "LC_AlcSts\020\3572\022(\n#SIGNAL_SLC_0x510_SLC_Scr"
  "nBriAutoSts\020\3602\022&\n!SIGNAL_SLC_0x510_SLC_C"
  "olorCstmSts\020\3612\022*\n%SIGNAL_SLC_0x510_SLC_A"
  "LcCrtAdj_Color1\020\3642\022*\n%SIGNAL_SLC_0x510_S"
  "LC_ALcCrtAdj_Color2\020\3652\022(\n#SIGNAL_SLC_0x5"
  "10_SLC_Airvetn_AL_Sts\020\3662\022+\n&SIGNAL_SLC_0"
  "x510_SLC_AlcDualAreaModSts\020\3672\022(\n#SIGNAL_"
  "SLC_0x510_SLC_TurnlampModSts\020\3712\022 \n\033SIGNA"
  "L_ECC_0x3F7_ECC_UVCSts\020\3732\022#\n\036SIGNAL_HUD_"
  "0x6B_HUD_ARPara1Fb1\020\3772\022#\n\036SIGNAL_HUD_0x6"
  "B_HUD_ARPara1Fb2\020\2003\022#\n\036SIGNAL_HUD_0x6B_H"
  "UD_ARPara1Fb3\020\2013\022#\n\036SIGNAL_HUD_0x6B_HUD_"
  "ARPara1Fb4\020\2023\022#\n\036SIGNAL_HUD_0x6B_HUD_ARP"
  "ara1Fb5\020\2033\022#\n\036SIGNAL_HUD_0x6B_HUD_ARPara"
  "1Fb6\020\2043\022#\n\036SIGNAL_HUD_0x6B_HUD_ARPara1Fb"
  "7\020\2053\022#\n\036SIGNAL_HUD_0x6B_HUD_ARPara1Fb8\020\206"
  "3\022#\n\036SIGNAL_HUD_0x6B_HUD_ARPara1Fb9\020\2073\022$"
  "\n\037SIGNAL_HUD_0x6B_HUD_ARPara1Fb10\020\2103\022$\n\037"
  "SIGNAL_HUD_0x6B_HUD_ARPara1Fb11\020\2113\022$\n\037SI"
  "GNAL_HUD_0x6B_HUD_ARPara1Fb12\020\2123\022$\n\037SIGN"
  "AL_HUD_0x6B_HUD_ARPara1Fb13\020\2133\022$\n\037SIGNAL"
  "_HUD_0x6B_HUD_ARPara1Fb14\020\2143\022$\n\037SIGNAL_H"
  "UD_0x6B_HUD_ARPara1Fb15\020\2153\022$\n\037SIGNAL_HUD"
  "_0x6B_HUD_ARPara1Fb16\020\2163\022#\n\036SIGNAL_HUD_0"
  "x6B_HUD_ARPara2Fb1\020\2173\022#\n\036SIGNAL_HUD_0x6B"
  "_HUD_ARPara2Fb2\020\2203\022#\n\036SIGNAL_HUD_0x6B_HU"
  "D_ARPara2Fb3\020\2213\022#\n\036SIGNAL_HUD_0x6B_HUD_A"
  "RPara2Fb4\020\2223\022#\n\036SIGNAL_HUD_0x6B_HUD_ARPa"
  "ra2Fb5\020\2233\022#\n\036SIGNAL_HUD_0x6B_HUD_ARPara2"
  "Fb6\020\2243\022#\n\036SIGNAL_HUD_0x6B_HUD_ARPara2Fb7"
  "\020\2253\022#\n\036SIGNAL_HUD_0x6B_HUD_ARPara2Fb8\020\2263"
  "\022#\n\036SIGNAL_HUD_0x6B_HUD_ARPara2Fb9\020\2273\022$\n"
  "\037SIGNAL_HUD_0x6B_HUD_ARPara2Fb10\020\2303\022$\n\037S"
  "IGNAL_HUD_0x6B_HUD_ARPara2Fb11\020\2313\022$\n\037SIG"
  "NAL_HUD_0x6B_HUD_ARPara2Fb12\020\2323\022$\n\037SIGNA"
  "L_HUD_0x6B_HUD_ARPara2Fb13\020\2333\022$\n\037SIGNAL_"
  "HUD_0x6B_HUD_ARPara2Fb14\020\2343\022$\n\037SIGNAL_HU"
  "D_0x6B_HUD_ARPara2Fb15\020\2353\022$\n\037SIGNAL_HUD_"
  "0x6B_HUD_ARPara2Fb16\020\2363\022#\n\036SIGNAL_HUD_0x"
  "6C_HUD_ARPara3Fb1\020\2373\022#\n\036SIGNAL_HUD_0x6C_"
  "HUD_ARPara3Fb2\020\2403\022#\n\036SIGNAL_HUD_0x6C_HUD"
  "_ARPara3Fb3\020\2413\022#\n\036SIGNAL_HUD_0x6C_HUD_AR"
  "Para3Fb4\020\2423\022#\n\036SIGNAL_HUD_0x6C_HUD_ARPar"
  "a3Fb5\020\2433\022#\n\036SIGNAL_HUD_0x6C_HUD_ARPara3F"
  "b6\020\2443\022#\n\036SIGNAL_HUD_0x6C_HUD_ARPara3Fb7\020"
  "\2453\022#\n\036SIGNAL_HUD_0x6C_HUD_ARPara3Fb8\020\2463\022"
  "#\n\036SIGNAL_HUD_0x6C_HUD_ARPara3Fb9\020\2473\022$\n\037"
  "SIGNAL_HUD_0x6C_HUD_ARPara3Fb10\020\2503\022$\n\037SI"
  "GNAL_HUD_0x6C_HUD_ARPara3Fb11\020\2513\022$\n\037SIGN"
  "AL_HUD_0x6C_HUD_ARPara3Fb12\020\2523\022$\n\037SIGNAL"
  "_HUD_0x6C_HUD_ARPara3Fb13\020\2533\022$\n\037SIGNAL_H"
  "UD_0x6C_HUD_ARPara3Fb14\020\2543\022$\n\037SIGNAL_HUD"
  "_0x6C_HUD_ARPara3Fb15\020\2553\022$\n\037SIGNAL_HUD_0"
  "x6C_HUD_ARPara3Fb16\020\2563\022#\n\036SIGNAL_HUD_0x6"
  "C_HUD_ARPara4Fb1\020\2573\022#\n\036SIGNAL_HUD_0x6C_H"
  "UD_ARPara4Fb2\020\2603\022#\n\036SIGNAL_HUD_0x6C_HUD_"
  "ARPara4Fb3\020\2613\022#\n\036SIGNAL_HUD_0x6C_HUD_ARP"
  "ara4Fb4\020\2623\022#\n\036SIGNAL_HUD_0x6C_HUD_ARPara"
  "4Fb5\020\2633\022#\n\036SIGNAL_HUD_0x6C_HUD_ARPara4Fb"
  "6\020\2643\022#\n\036SIGNAL_HUD_0x6C_HUD_ARPara4Fb7\020\265"
  "3\022#\n\036SIGNAL_HUD_0x6C_HUD_ARPara4Fb8\020\2663\022#"
  "\n\036SIGNAL_ICC_0x49A_ICC_ARParaFb1\020\2673\022#\n\036S"
  "IGNAL_ICC_0x49A_ICC_ARParaFb2\020\2703\022#\n\036SIGN"
  "AL_ICC_0x49A_ICC_ARParaFb3\020\2713\022#\n\036SIGNAL_"
  "ICC_0x49A_ICC_ARParaFb4\020\2723\022$\n\037SIGNAL_ICC"
  "_0x49A_ICC_LOGOLigSwt\020\2733\022+\n&SIGNAL_ICC_0"
  "x49A_ICC_ContrastColorSwt1\020\2743\022+\n&SIGNAL_"
  "ICC_0x49A_ICC_ContrastColorSwt2\020\2753\022+\n&SI"
  "GNAL_ICC_0x49A_ICC_ContrastColorSwt3\020\2763\022"
  "$\n\037SIGNAL_BMS_0x5BF_BMS_MaxLoadPwr\020\2773\0224\n"
  "/SIGNAL_BCM_0x311_BCM_OpenDoorLamplangua"
  "geSwtSts\020\3323\022,\n\'SIGNAL_BCM_0x311_BCM_Lock"
  "soundpromptSwt\020\3333\022(\n#SIGNAL_BCM_0x311_BC"
  "M_SmartOpenTrunk\020\3343\022$\n\037SIGNAL_BCM_0x311_"
  "RSM_SsMovement\020\3353\022#\n\036SIGNAL_BCM_0x311_RS"
  "M_SSPosPecr\020\3363\022.\n)SIGNAL_BCM_0x311_BCM_L"
  "ockAutoClsSunSSwtFb\020\3373\022@\n;SIGNAL_VCU_0x3"
  "B6_VCU_DrRangDisPercent_HigPrecDisplayRe"
  "quire\020\3534\0222\n-SIGNAL_VCU_0x3B6_VCU_DrRangD"
  "isPercent_HigPrec\020\3544\022&\n!SIGNAL_VCU_0x50E"
  "_VCU_EHUPwrOffEna\020\3614\022*\n%SIGNAL_CPD_0x3B2"
  "_CPD_ChildCallASwtSts\020\3265\022,\n\'SIGNAL_VCU_0"
  "x509_PDCU_ExhibCarModNotice\020\2426\022*\n%SIGNAL"
  "_VCU_0x509_PDCU_ExhibCarmodText\020\2436\0222\n-SI"
  "GNAL_VCU_0x509_VCU_ExhibCarModDisableNot"
  "ice\020\2446\0222\n-SIGNAL_BCM_0x3DE_BCM_ExhibCarM"
  "odDisableNotice\020\2466\0224\n/SIGNAL_TBOX_0x3FF_"
  "TBOX_ExhibCarModDisableNotice\020\2476\022*\n%SIGN"
  "AL_AVAP_0x549_AVAP_SentryModStsFb\020\2576\022(\n#"
  "SIGNAL_ICC_0x61_ICC_SentryModTi1Vld\020\2666\022("
  "\n#SIGNAL_ICC_0x61_ICC_SentryModTi2Vld\020\2676"
  "\022)\n$SIGNAL_ICC_0x61_ICC_SentryModStrtTi1"
  "\020\2706\022)\n$SIGNAL_ICC_0x61_ICC_SentryModStrt"
  "Ti2\020\2726\022(\n#SIGNAL_ICC_0x61_ICC_SentryModE"
  "ndTi1\020\2736\022(\n#SIGNAL_ICC_0x61_ICC_SentryMo"
  "dEndTi2\020\2746\022$\n\037SIGNAL_ICC_0x63_ICC_Sentry"
  "ModSw\020\2756\022&\n!SIGNAL_ICC_0x63_ICC_SentiSnv"
  "tySet\020\3026\022#\n\036SIGNAL_VCU_0x5A9_VCU_FsTextT"
  "ip\020\3136\022#\n\036SIGNAL_BCM_0x51F_BCM_DRLSwtSts\020"
  "\3176\022%\n SIGNAL_HDCU_0x31F_PdcuChkEngLamp\020\320"
  "6\022!\n\034SIGNAL_HDCU_0x31F_PdcuRngLim\020\3216\022\"\n\035"
  "SIGNAL_PDCU_0x3D0_PdcuEnrgMod\020\3226\022$\n\037SIGN"
  "AL_PDCU_0x3D0_EmsOil_Pfault\020\3236\022%\n SIGNAL"
  "_PDCU_0x3D0_IndModEgyFbDis\020\3246\022&\n!SIGNAL_"
  "PDCU_0x3D0_IndModAccModDis\020\3256\022\'\n\"SIGNAL_"
  "PDCU_0x3D0_IndModSteeModDis\020\3266\022&\n!SIGNAL"
  "_PDCU_0x3D0_IndModCreModDis\020\3276\022\'\n\"SIGNAL"
  "_PDCU_0x3D0_PdcuParkChargeSt\020\3316\022*\n%SIGNA"
  "L_HDCU_0x54B_VCU_OBC_REMIND_LAMP\020\3416\022&\n!S"
  "IGNAL_TBOX_0xB7_TBOX_SentryModSw\020\3126\022/\n*S"
  "IGNAL_ICC_0x542_ICC_100kmAvrgPwrCnsptn_A"
  "S\020\3756\022-\n(SIGNAL_VCU_0x3B8_VCU_PwrCnsptnDi"
  "ag_AB_AS\020\2047\022+\n&SIGNAL_VCU_0x3B8_PdcuEnrg"
  "ModRejHint_AS\020\2237\022\'\n\"SIGNAL_VCU_0x3B8_Pdc"
  "uHydOperMod_AS\020\2257\022&\n!SIGNAL_VCU_0x3B8_Pd"
  "cuIdleModSt_AS\020\2277\022+\n&SIGNAL_VCU_0x3B8_Pd"
  "cuEmissTestModSt_AS\020\2327\022)\n$SIGNAL_VCU_0x3"
  "B8_PdcuRefSwitchSts_AS\020\2357\022)\n$SIGNAL_VCU_"
  "0x3B8_PdcuRefuNotAllwd_AS\020\2367\022*\n%SIGNAL_V"
  "CU_0x3B8_PdcuFuFilrDoorRmn_AS\020\2377\022,\n\'SIGN"
  "AL_VCU_0x3B8_PdcuFuTankRelsProgs_AS\020\2407\0221"
  "\n,SIGNAL_VCU_0x3B8_HDCU_WashModPromptSig"
  "_AB_AS\020\2427\022*\n%SIGNAL_VCU_0x56B_VCU_VehDrv"
  "CnseEgy_AS\020\2517\022\'\n\"SIGNAL_VCU_0x56B_VCU_BH"
  "MCnseEgy_AS\020\2527\022*\n%SIGNAL_VCU_0x56B_VCU_V"
  "ehDrvCnseEgy_AB\020\2637\022\'\n\"SIGNAL_MCU_F_0x150"
  "_MCU_F_CrtRotDir\020\303\"\022)\n$SIGNAL_MCU_F_0x15"
  "0_MCU_F_AlrmLamp_FS\020\307\""
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_autolink_2eplatform_2evehicle_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_autolink_2eplatform_2evehicle_2eproto = {
  false, false, 36462, descriptor_table_protodef_autolink_2eplatform_2evehicle_2eproto, "autolink.platform.vehicle.proto", 
  &descriptor_table_autolink_2eplatform_2evehicle_2eproto_once, nullptr, 0, 4,
  schemas, file_default_instances, TableStruct_autolink_2eplatform_2evehicle_2eproto::offsets,
  file_level_metadata_autolink_2eplatform_2evehicle_2eproto, file_level_enum_descriptors_autolink_2eplatform_2evehicle_2eproto, file_level_service_descriptors_autolink_2eplatform_2evehicle_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_autolink_2eplatform_2evehicle_2eproto_getter() {
  return &descriptor_table_autolink_2eplatform_2evehicle_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_autolink_2eplatform_2evehicle_2eproto(&descriptor_table_autolink_2eplatform_2evehicle_2eproto);
namespace autolink {
namespace platform {
namespace vehicle {
namespace pb {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PduState_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_autolink_2eplatform_2evehicle_2eproto);
  return file_level_enum_descriptors_autolink_2eplatform_2evehicle_2eproto[0];
}
bool PduState_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SignalValueType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_autolink_2eplatform_2evehicle_2eproto);
  return file_level_enum_descriptors_autolink_2eplatform_2evehicle_2eproto[1];
}
bool SignalValueType_IsValid(int value) {
  switch (value) {
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PduId_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_autolink_2eplatform_2evehicle_2eproto);
  return file_level_enum_descriptors_autolink_2eplatform_2evehicle_2eproto[2];
}
bool PduId_IsValid(int value) {
  switch (value) {
    case 1:
    case 4099:
      return true;
    default:
      return true;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SignalId_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_autolink_2eplatform_2evehicle_2eproto);
  return file_level_enum_descriptors_autolink_2eplatform_2evehicle_2eproto[3];
}
bool SignalId_IsValid(int value) {
  switch (value) {
    case 4096:
    case 4097:
    case 4098:
    case 4102:
    case 4363:
    case 4367:
    case 4369:
    case 4370:
    case 4371:
    case 4372:
    case 4374:
    case 4375:
    case 4377:
    case 4378:
    case 4379:
    case 4380:
    case 4381:
    case 4382:
    case 4383:
    case 4384:
    case 4385:
    case 4386:
    case 4387:
    case 4388:
    case 4390:
    case 4391:
    case 4393:
    case 4394:
    case 4396:
    case 4397:
    case 4399:
    case 4400:
    case 4402:
    case 4403:
    case 4405:
    case 4406:
    case 4408:
    case 4409:
    case 4411:
    case 4412:
    case 4419:
    case 4423:
    case 4426:
    case 4427:
    case 4428:
    case 4434:
    case 4436:
    case 4437:
    case 4438:
    case 4441:
    case 4443:
    case 4444:
    case 4445:
    case 4453:
    case 4454:
    case 4456:
    case 4478:
    case 4480:
    case 4481:
    case 4482:
    case 4483:
    case 4484:
    case 4485:
    case 4486:
    case 4487:
    case 4489:
    case 4494:
    case 4495:
    case 4496:
    case 4497:
    case 4498:
    case 4499:
    case 4501:
    case 4502:
    case 4503:
    case 4504:
    case 4506:
    case 4507:
    case 4508:
    case 4509:
    case 4510:
    case 4511:
    case 4522:
    case 4524:
    case 4526:
    case 4528:
    case 4530:
    case 4536:
    case 4537:
    case 4538:
    case 4539:
    case 4542:
    case 4543:
    case 4545:
    case 4546:
    case 4547:
    case 4550:
    case 4552:
    case 4558:
    case 4560:
    case 4561:
    case 4562:
    case 4563:
    case 4567:
    case 4569:
    case 4575:
    case 4576:
    case 4577:
    case 4578:
    case 4579:
    case 4581:
    case 4582:
    case 4583:
    case 4584:
    case 4587:
    case 4591:
    case 4593:
    case 4594:
    case 4595:
    case 4598:
    case 4602:
    case 4603:
    case 4606:
    case 4607:
    case 4609:
    case 4610:
    case 4611:
    case 4612:
    case 4614:
    case 4617:
    case 4619:
    case 4620:
    case 4621:
    case 4624:
    case 4625:
    case 4626:
    case 4627:
    case 4630:
    case 4635:
    case 4636:
    case 4637:
    case 4640:
    case 4641:
    case 4643:
    case 4644:
    case 4645:
    case 4646:
    case 4647:
    case 4648:
    case 4649:
    case 4650:
    case 4656:
    case 4657:
    case 4659:
    case 4660:
    case 4663:
    case 4664:
    case 4665:
    case 4666:
    case 4667:
    case 4669:
    case 4670:
    case 4671:
    case 4672:
    case 4673:
    case 4674:
    case 4675:
    case 4683:
    case 4692:
    case 4693:
    case 4695:
    case 4697:
    case 4699:
    case 4700:
    case 4702:
    case 4703:
    case 4709:
    case 4712:
    case 4713:
    case 4714:
    case 4715:
    case 4734:
    case 4735:
    case 4736:
    case 4740:
    case 4742:
    case 4747:
    case 4748:
    case 4772:
    case 4773:
    case 4780:
    case 4783:
    case 4785:
    case 4786:
    case 4789:
    case 4790:
    case 4792:
    case 4794:
    case 4808:
    case 4811:
    case 4829:
    case 4830:
    case 4833:
    case 4839:
    case 4840:
    case 4841:
    case 4849:
    case 4851:
    case 4852:
    case 4853:
    case 4856:
    case 4857:
    case 4858:
    case 4859:
    case 4860:
    case 4861:
    case 4870:
    case 4871:
    case 4872:
    case 4873:
    case 4874:
    case 4875:
    case 4878:
    case 4879:
    case 4880:
    case 4883:
    case 4884:
    case 4888:
    case 4889:
    case 4890:
    case 4894:
    case 4900:
    case 4901:
    case 4903:
    case 4904:
    case 4907:
    case 4908:
    case 4911:
    case 4912:
    case 4913:
    case 4915:
    case 4916:
    case 4918:
    case 4921:
    case 4922:
    case 4923:
    case 4924:
    case 4925:
    case 4926:
    case 4927:
    case 4928:
    case 4929:
    case 4930:
    case 4931:
    case 4932:
    case 4933:
    case 4934:
    case 4935:
    case 4936:
    case 4937:
    case 4938:
    case 4939:
    case 4940:
    case 4941:
    case 4942:
    case 4943:
    case 4944:
    case 4945:
    case 4946:
    case 4947:
    case 4948:
    case 4949:
    case 4952:
    case 4953:
    case 4954:
    case 4957:
    case 4958:
    case 4960:
    case 4961:
    case 4962:
    case 4963:
    case 4968:
    case 4969:
    case 4970:
    case 4972:
    case 4973:
    case 4975:
    case 4976:
    case 4977:
    case 4978:
    case 4979:
    case 4981:
    case 4982:
    case 4983:
    case 4984:
    case 4986:
    case 4987:
    case 4988:
    case 4990:
    case 4991:
    case 4993:
    case 4994:
    case 4995:
    case 4996:
    case 4998:
    case 4999:
    case 5001:
    case 5002:
    case 5006:
    case 5008:
    case 5027:
    case 5028:
    case 5035:
    case 5045:
    case 5058:
    case 5071:
    case 5072:
    case 5073:
    case 5074:
    case 5075:
    case 5082:
    case 5083:
    case 5084:
    case 5085:
    case 5086:
    case 5087:
    case 5088:
    case 5089:
    case 5096:
    case 5098:
    case 5099:
    case 5104:
    case 5105:
    case 5106:
    case 5108:
    case 5109:
    case 5110:
    case 5270:
    case 5271:
    case 5272:
    case 5273:
    case 5275:
    case 5278:
    case 5279:
    case 5280:
    case 5282:
    case 5283:
    case 5284:
    case 5285:
    case 5286:
    case 5287:
    case 5289:
    case 5290:
    case 5291:
    case 5292:
    case 5293:
    case 5294:
    case 5343:
    case 5344:
    case 5348:
    case 5349:
    case 5350:
    case 5351:
    case 5352:
    case 5353:
    case 5417:
    case 5418:
    case 5419:
    case 5420:
    case 5427:
    case 5428:
    case 5429:
    case 5430:
    case 5443:
    case 5444:
    case 5446:
    case 5447:
    case 5448:
    case 5449:
    case 5451:
    case 5453:
    case 5454:
    case 5463:
    case 5464:
    case 5465:
    case 5466:
    case 5467:
    case 5468:
    case 5469:
    case 5470:
    case 5476:
    case 5477:
    case 5478:
    case 5479:
    case 5480:
    case 5481:
    case 5484:
    case 5485:
    case 5486:
    case 5487:
    case 5488:
    case 5489:
    case 5492:
    case 5497:
    case 5498:
    case 5501:
    case 5502:
    case 5504:
    case 5506:
    case 5510:
    case 5511:
    case 5518:
    case 5522:
    case 5528:
    case 5529:
    case 5532:
    case 5557:
    case 5559:
    case 5584:
    case 5587:
    case 5667:
    case 5668:
    case 5669:
    case 5670:
    case 5674:
    case 5675:
    case 5676:
    case 5677:
    case 5683:
    case 5684:
    case 5688:
    case 5689:
    case 5690:
    case 5691:
    case 5692:
    case 5693:
    case 5694:
    case 5695:
    case 5696:
    case 5697:
    case 5698:
    case 5699:
    case 5700:
    case 5701:
    case 5702:
    case 5703:
    case 5704:
    case 5705:
    case 5706:
    case 5707:
    case 5708:
    case 5709:
    case 5710:
    case 5711:
    case 5726:
    case 5734:
    case 5743:
    case 5749:
    case 5750:
    case 5751:
    case 5753:
    case 5754:
    case 5773:
    case 5777:
    case 5778:
    case 5779:
    case 5802:
    case 5805:
    case 5806:
    case 5807:
    case 5808:
    case 5809:
    case 5810:
    case 5818:
    case 5819:
    case 5825:
    case 5828:
    case 5830:
    case 5833:
    case 5835:
    case 5840:
    case 5854:
    case 5858:
    case 5887:
    case 5892:
    case 5894:
    case 5895:
    case 5902:
    case 5903:
    case 5904:
    case 5905:
    case 5912:
    case 5913:
    case 5914:
    case 5915:
    case 5916:
    case 5917:
    case 5918:
    case 5919:
    case 5920:
    case 5921:
    case 5922:
    case 5923:
    case 5924:
    case 5957:
    case 5966:
    case 5968:
    case 5969:
    case 5970:
    case 5981:
    case 5982:
    case 5983:
    case 5984:
    case 5985:
    case 5986:
    case 5987:
    case 5988:
    case 5989:
    case 5990:
    case 5991:
    case 5992:
    case 5994:
    case 6001:
    case 6004:
    case 6006:
    case 6007:
    case 6009:
    case 6011:
    case 6013:
    case 6015:
    case 6016:
    case 6024:
    case 6036:
    case 6037:
    case 6038:
    case 6039:
    case 6043:
    case 6044:
    case 6045:
    case 6046:
    case 6047:
    case 6048:
    case 6049:
    case 6050:
    case 6051:
    case 6052:
    case 6053:
    case 6054:
    case 6056:
    case 6058:
    case 6059:
    case 6061:
    case 6062:
    case 6063:
    case 6064:
    case 6065:
    case 6066:
    case 6067:
    case 6068:
    case 6069:
    case 6070:
    case 6071:
    case 6072:
    case 6080:
    case 6082:
    case 6084:
    case 6085:
    case 6086:
    case 6087:
    case 6091:
    case 6092:
    case 6093:
    case 6094:
    case 6095:
    case 6097:
    case 6098:
    case 6099:
    case 6100:
    case 6103:
    case 6105:
    case 6106:
    case 6116:
    case 6118:
    case 6119:
    case 6120:
    case 6121:
    case 6122:
    case 6123:
    case 6124:
    case 6125:
    case 6126:
    case 6127:
    case 6128:
    case 6129:
    case 6130:
    case 6131:
    case 6132:
    case 6133:
    case 6134:
    case 6135:
    case 6136:
    case 6137:
    case 6138:
    case 6141:
    case 6143:
    case 6144:
    case 6145:
    case 6146:
    case 6147:
    case 6148:
    case 6149:
    case 6150:
    case 6152:
    case 6153:
    case 6154:
    case 6155:
    case 6156:
    case 6157:
    case 6158:
    case 6160:
    case 6161:
    case 6162:
    case 6163:
    case 6167:
    case 6168:
    case 6170:
    case 6171:
    case 6212:
    case 6213:
    case 6214:
    case 6215:
    case 6216:
    case 6217:
    case 6218:
    case 6219:
    case 6220:
    case 6221:
    case 6222:
    case 6223:
    case 6224:
    case 6225:
    case 6226:
    case 6227:
    case 6228:
    case 6229:
    case 6230:
    case 6231:
    case 6244:
    case 6245:
    case 6250:
    case 6251:
    case 6252:
    case 6254:
    case 6255:
    case 6256:
    case 6257:
    case 6258:
    case 6259:
    case 6260:
    case 6261:
    case 6262:
    case 6263:
    case 6267:
    case 6273:
    case 6274:
    case 6275:
    case 6277:
    case 6280:
    case 6281:
    case 6293:
    case 6302:
    case 6310:
    case 6311:
    case 6313:
    case 6314:
    case 6315:
    case 6316:
    case 6317:
    case 6319:
    case 6320:
    case 6321:
    case 6322:
    case 6323:
    case 6324:
    case 6325:
    case 6326:
    case 6327:
    case 6328:
    case 6329:
    case 6335:
    case 6507:
    case 6509:
    case 6510:
    case 6511:
    case 6512:
    case 6513:
    case 6516:
    case 6517:
    case 6518:
    case 6519:
    case 6521:
    case 6523:
    case 6527:
    case 6528:
    case 6529:
    case 6530:
    case 6531:
    case 6532:
    case 6533:
    case 6534:
    case 6535:
    case 6536:
    case 6537:
    case 6538:
    case 6539:
    case 6540:
    case 6541:
    case 6542:
    case 6543:
    case 6544:
    case 6545:
    case 6546:
    case 6547:
    case 6548:
    case 6549:
    case 6550:
    case 6551:
    case 6552:
    case 6553:
    case 6554:
    case 6555:
    case 6556:
    case 6557:
    case 6558:
    case 6559:
    case 6560:
    case 6561:
    case 6562:
    case 6563:
    case 6564:
    case 6565:
    case 6566:
    case 6567:
    case 6568:
    case 6569:
    case 6570:
    case 6571:
    case 6572:
    case 6573:
    case 6574:
    case 6575:
    case 6576:
    case 6577:
    case 6578:
    case 6579:
    case 6580:
    case 6581:
    case 6582:
    case 6583:
    case 6584:
    case 6585:
    case 6586:
    case 6587:
    case 6588:
    case 6589:
    case 6590:
    case 6591:
    case 6618:
    case 6619:
    case 6620:
    case 6621:
    case 6622:
    case 6623:
    case 6761:
    case 6763:
    case 6764:
    case 6769:
    case 6870:
    case 6946:
    case 6947:
    case 6948:
    case 6950:
    case 6951:
    case 6959:
    case 6966:
    case 6967:
    case 6968:
    case 6970:
    case 6971:
    case 6972:
    case 6973:
    case 6978:
    case 6986:
    case 6987:
    case 6988:
    case 6989:
    case 6990:
    case 6991:
    case 6992:
    case 6993:
    case 6994:
    case 6995:
    case 6996:
    case 6997:
    case 6998:
    case 6999:
    case 7001:
    case 7009:
    case 7034:
    case 7035:
    case 7037:
    case 7038:
    case 7039:
    case 7040:
    case 7043:
    case 7044:
    case 7059:
    case 7061:
    case 7063:
    case 7066:
    case 7068:
    case 7069:
    case 7070:
    case 7071:
    case 7072:
    case 7074:
    case 7075:
    case 7076:
    case 7077:
    case 7078:
    case 7079:
    case 7081:
    case 7082:
    case 7091:
    case 32768:
    case 32769:
    case 32770:
    case 32771:
    case 32772:
    case 32773:
    case 32774:
    case 32775:
    case 32776:
    case 32777:
    case 32778:
      return true;
    default:
      return true;
  }
}


// ===================================================================

class MsgSignal::_Internal {
 public:
  using HasBits = decltype(std::declval<MsgSignal>()._has_bits_);
  static void set_has_signal_id(HasBits* has_bits) {
    (*has_bits)[0] |= 128u;
  }
  static void set_has_value_type(HasBits* has_bits) {
    (*has_bits)[0] |= 256u;
  }
  static void set_has_string_value(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_bool_value(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_uint32_value(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_sint32_value(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
  static void set_has_uint64_value(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_sint64_value(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static void set_has_bytes_value(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000180) ^ 0x00000180) != 0;
  }
};

MsgSignal::MsgSignal(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:autolink.platform.vehicle.pb.MsgSignal)
}
MsgSignal::MsgSignal(const MsgSignal& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  string_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    string_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_string_value()) {
    string_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_string_value(), 
      GetArenaForAllocation());
  }
  bytes_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    bytes_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_bytes_value()) {
    bytes_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_bytes_value(), 
      GetArenaForAllocation());
  }
  ::memcpy(&bool_value_, &from.bool_value_,
    static_cast<size_t>(reinterpret_cast<char*>(&value_type_) -
    reinterpret_cast<char*>(&bool_value_)) + sizeof(value_type_));
  // @@protoc_insertion_point(copy_constructor:autolink.platform.vehicle.pb.MsgSignal)
}

inline void MsgSignal::SharedCtor() {
string_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  string_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
bytes_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  bytes_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&bool_value_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&sint32_value_) -
    reinterpret_cast<char*>(&bool_value_)) + sizeof(sint32_value_));
signal_id_ = 4426;
value_type_ = 1;
}

MsgSignal::~MsgSignal() {
  // @@protoc_insertion_point(destructor:autolink.platform.vehicle.pb.MsgSignal)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MsgSignal::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  string_value_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  bytes_value_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void MsgSignal::ArenaDtor(void* object) {
  MsgSignal* _this = reinterpret_cast< MsgSignal* >(object);
  (void)_this;
}
void MsgSignal::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MsgSignal::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MsgSignal::Clear() {
// @@protoc_insertion_point(message_clear_start:autolink.platform.vehicle.pb.MsgSignal)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      string_value_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      bytes_value_.ClearNonDefaultToEmpty();
    }
  }
  if (cached_has_bits & 0x000000fcu) {
    ::memset(&bool_value_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&sint32_value_) -
        reinterpret_cast<char*>(&bool_value_)) + sizeof(sint32_value_));
    signal_id_ = 4426;
  }
  value_type_ = 1;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MsgSignal::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required .autolink.platform.vehicle.pb.SignalId signal_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::autolink::platform::vehicle::pb::SignalId_IsValid(val))) {
            _internal_set_signal_id(static_cast<::autolink::platform::vehicle::pb::SignalId>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // required .autolink.platform.vehicle.pb.SignalValueType value_type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::autolink::platform::vehicle::pb::SignalValueType_IsValid(val))) {
            _internal_set_value_type(static_cast<::autolink::platform::vehicle::pb::SignalValueType>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(2, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // optional string string_value = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_string_value();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "autolink.platform.vehicle.pb.MsgSignal.string_value");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bool bool_value = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _Internal::set_has_bool_value(&has_bits);
          bool_value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional uint32 uint32_value = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          _Internal::set_has_uint32_value(&has_bits);
          uint32_value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional sint32 sint32_value = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          _Internal::set_has_sint32_value(&has_bits);
          sint32_value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarintZigZag32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional uint64 uint64_value = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          _Internal::set_has_uint64_value(&has_bits);
          uint64_value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional sint64 sint64_value = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          _Internal::set_has_sint64_value(&has_bits);
          sint64_value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarintZigZag64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bytes bytes_value = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          auto str = _internal_mutable_bytes_value();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MsgSignal::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:autolink.platform.vehicle.pb.MsgSignal)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .autolink.platform.vehicle.pb.SignalId signal_id = 1;
  if (cached_has_bits & 0x00000080u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_signal_id(), target);
  }

  // required .autolink.platform.vehicle.pb.SignalValueType value_type = 2;
  if (cached_has_bits & 0x00000100u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_value_type(), target);
  }

  // optional string string_value = 3;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_string_value().data(), static_cast<int>(this->_internal_string_value().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "autolink.platform.vehicle.pb.MsgSignal.string_value");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_string_value(), target);
  }

  // optional bool bool_value = 4;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_bool_value(), target);
  }

  // optional uint32 uint32_value = 5;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_uint32_value(), target);
  }

  // optional sint32 sint32_value = 6;
  if (cached_has_bits & 0x00000040u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteSInt32ToArray(6, this->_internal_sint32_value(), target);
  }

  // optional uint64 uint64_value = 7;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(7, this->_internal_uint64_value(), target);
  }

  // optional sint64 sint64_value = 8;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteSInt64ToArray(8, this->_internal_sint64_value(), target);
  }

  // optional bytes bytes_value = 9;
  if (cached_has_bits & 0x00000002u) {
    target = stream->WriteBytesMaybeAliased(
        9, this->_internal_bytes_value(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:autolink.platform.vehicle.pb.MsgSignal)
  return target;
}

size_t MsgSignal::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:autolink.platform.vehicle.pb.MsgSignal)
  size_t total_size = 0;

  if (_internal_has_signal_id()) {
    // required .autolink.platform.vehicle.pb.SignalId signal_id = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_signal_id());
  }

  if (_internal_has_value_type()) {
    // required .autolink.platform.vehicle.pb.SignalValueType value_type = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_value_type());
  }

  return total_size;
}
size_t MsgSignal::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:autolink.platform.vehicle.pb.MsgSignal)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000180) ^ 0x00000180) == 0) {  // All required fields are present.
    // required .autolink.platform.vehicle.pb.SignalId signal_id = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_signal_id());

    // required .autolink.platform.vehicle.pb.SignalValueType value_type = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_value_type());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000007fu) {
    // optional string string_value = 3;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_string_value());
    }

    // optional bytes bytes_value = 9;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
          this->_internal_bytes_value());
    }

    // optional bool bool_value = 4;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 + 1;
    }

    // optional uint32 uint32_value = 5;
    if (cached_has_bits & 0x00000008u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_uint32_value());
    }

    // optional uint64 uint64_value = 7;
    if (cached_has_bits & 0x00000010u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_uint64_value());
    }

    // optional sint64 sint64_value = 8;
    if (cached_has_bits & 0x00000020u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SInt64SizePlusOne(this->_internal_sint64_value());
    }

    // optional sint32 sint32_value = 6;
    if (cached_has_bits & 0x00000040u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SInt32SizePlusOne(this->_internal_sint32_value());
    }

  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MsgSignal::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MsgSignal::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MsgSignal::GetClassData() const { return &_class_data_; }

void MsgSignal::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MsgSignal *>(to)->MergeFrom(
      static_cast<const MsgSignal &>(from));
}


void MsgSignal::MergeFrom(const MsgSignal& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:autolink.platform.vehicle.pb.MsgSignal)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_string_value(from._internal_string_value());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_set_bytes_value(from._internal_bytes_value());
    }
    if (cached_has_bits & 0x00000004u) {
      bool_value_ = from.bool_value_;
    }
    if (cached_has_bits & 0x00000008u) {
      uint32_value_ = from.uint32_value_;
    }
    if (cached_has_bits & 0x00000010u) {
      uint64_value_ = from.uint64_value_;
    }
    if (cached_has_bits & 0x00000020u) {
      sint64_value_ = from.sint64_value_;
    }
    if (cached_has_bits & 0x00000040u) {
      sint32_value_ = from.sint32_value_;
    }
    if (cached_has_bits & 0x00000080u) {
      signal_id_ = from.signal_id_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  if (cached_has_bits & 0x00000100u) {
    _internal_set_value_type(from._internal_value_type());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MsgSignal::CopyFrom(const MsgSignal& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:autolink.platform.vehicle.pb.MsgSignal)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MsgSignal::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void MsgSignal::InternalSwap(MsgSignal* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &string_value_, lhs_arena,
      &other->string_value_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &bytes_value_, lhs_arena,
      &other->bytes_value_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MsgSignal, sint32_value_)
      + sizeof(MsgSignal::sint32_value_)
      - PROTOBUF_FIELD_OFFSET(MsgSignal, bool_value_)>(
          reinterpret_cast<char*>(&bool_value_),
          reinterpret_cast<char*>(&other->bool_value_));
  swap(signal_id_, other->signal_id_);
  swap(value_type_, other->value_type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata MsgSignal::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_autolink_2eplatform_2evehicle_2eproto_getter, &descriptor_table_autolink_2eplatform_2evehicle_2eproto_once,
      file_level_metadata_autolink_2eplatform_2evehicle_2eproto[0]);
}

// ===================================================================

class MsgSignalSp::_Internal {
 public:
  using HasBits = decltype(std::declval<MsgSignalSp>()._has_bits_);
  static void set_has_state(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static const ::autolink::platform::vehicle::pb::MsgSignal& signal(const MsgSignalSp* msg);
  static void set_has_signal(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000003) ^ 0x00000003) != 0;
  }
};

const ::autolink::platform::vehicle::pb::MsgSignal&
MsgSignalSp::_Internal::signal(const MsgSignalSp* msg) {
  return *msg->signal_;
}
MsgSignalSp::MsgSignalSp(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:autolink.platform.vehicle.pb.MsgSignalSp)
}
MsgSignalSp::MsgSignalSp(const MsgSignalSp& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_signal()) {
    signal_ = new ::autolink::platform::vehicle::pb::MsgSignal(*from.signal_);
  } else {
    signal_ = nullptr;
  }
  state_ = from.state_;
  // @@protoc_insertion_point(copy_constructor:autolink.platform.vehicle.pb.MsgSignalSp)
}

inline void MsgSignalSp::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&signal_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&state_) -
    reinterpret_cast<char*>(&signal_)) + sizeof(state_));
}

MsgSignalSp::~MsgSignalSp() {
  // @@protoc_insertion_point(destructor:autolink.platform.vehicle.pb.MsgSignalSp)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MsgSignalSp::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete signal_;
}

void MsgSignalSp::ArenaDtor(void* object) {
  MsgSignalSp* _this = reinterpret_cast< MsgSignalSp* >(object);
  (void)_this;
}
void MsgSignalSp::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MsgSignalSp::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MsgSignalSp::Clear() {
// @@protoc_insertion_point(message_clear_start:autolink.platform.vehicle.pb.MsgSignalSp)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(signal_ != nullptr);
    signal_->Clear();
  }
  state_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MsgSignalSp::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required .autolink.platform.vehicle.pb.PduState state = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::autolink::platform::vehicle::pb::PduState_IsValid(val))) {
            _internal_set_state(static_cast<::autolink::platform::vehicle::pb::PduState>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // required .autolink.platform.vehicle.pb.MsgSignal signal = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_signal(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MsgSignalSp::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:autolink.platform.vehicle.pb.MsgSignalSp)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .autolink.platform.vehicle.pb.PduState state = 1;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_state(), target);
  }

  // required .autolink.platform.vehicle.pb.MsgSignal signal = 2;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::signal(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:autolink.platform.vehicle.pb.MsgSignalSp)
  return target;
}

size_t MsgSignalSp::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:autolink.platform.vehicle.pb.MsgSignalSp)
  size_t total_size = 0;

  if (_internal_has_signal()) {
    // required .autolink.platform.vehicle.pb.MsgSignal signal = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *signal_);
  }

  if (_internal_has_state()) {
    // required .autolink.platform.vehicle.pb.PduState state = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());
  }

  return total_size;
}
size_t MsgSignalSp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:autolink.platform.vehicle.pb.MsgSignalSp)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000003) ^ 0x00000003) == 0) {  // All required fields are present.
    // required .autolink.platform.vehicle.pb.MsgSignal signal = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *signal_);

    // required .autolink.platform.vehicle.pb.PduState state = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MsgSignalSp::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MsgSignalSp::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MsgSignalSp::GetClassData() const { return &_class_data_; }

void MsgSignalSp::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MsgSignalSp *>(to)->MergeFrom(
      static_cast<const MsgSignalSp &>(from));
}


void MsgSignalSp::MergeFrom(const MsgSignalSp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:autolink.platform.vehicle.pb.MsgSignalSp)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_mutable_signal()->::autolink::platform::vehicle::pb::MsgSignal::MergeFrom(from._internal_signal());
    }
    if (cached_has_bits & 0x00000002u) {
      state_ = from.state_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MsgSignalSp::CopyFrom(const MsgSignalSp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:autolink.platform.vehicle.pb.MsgSignalSp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MsgSignalSp::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (_internal_has_signal()) {
    if (!signal_->IsInitialized()) return false;
  }
  return true;
}

void MsgSignalSp::InternalSwap(MsgSignalSp* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MsgSignalSp, state_)
      + sizeof(MsgSignalSp::state_)
      - PROTOBUF_FIELD_OFFSET(MsgSignalSp, signal_)>(
          reinterpret_cast<char*>(&signal_),
          reinterpret_cast<char*>(&other->signal_));
}

::PROTOBUF_NAMESPACE_ID::Metadata MsgSignalSp::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_autolink_2eplatform_2evehicle_2eproto_getter, &descriptor_table_autolink_2eplatform_2evehicle_2eproto_once,
      file_level_metadata_autolink_2eplatform_2evehicle_2eproto[1]);
}

// ===================================================================

class MsgPdu::_Internal {
 public:
  using HasBits = decltype(std::declval<MsgPdu>()._has_bits_);
  static void set_has_pdu_id(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_state(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000003) ^ 0x00000003) != 0;
  }
};

MsgPdu::MsgPdu(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  signals_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:autolink.platform.vehicle.pb.MsgPdu)
}
MsgPdu::MsgPdu(const MsgPdu& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_),
      signals_(from.signals_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&state_, &from.state_,
    static_cast<size_t>(reinterpret_cast<char*>(&pdu_id_) -
    reinterpret_cast<char*>(&state_)) + sizeof(pdu_id_));
  // @@protoc_insertion_point(copy_constructor:autolink.platform.vehicle.pb.MsgPdu)
}

inline void MsgPdu::SharedCtor() {
state_ = 0;
pdu_id_ = 4099;
}

MsgPdu::~MsgPdu() {
  // @@protoc_insertion_point(destructor:autolink.platform.vehicle.pb.MsgPdu)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MsgPdu::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void MsgPdu::ArenaDtor(void* object) {
  MsgPdu* _this = reinterpret_cast< MsgPdu* >(object);
  (void)_this;
}
void MsgPdu::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MsgPdu::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MsgPdu::Clear() {
// @@protoc_insertion_point(message_clear_start:autolink.platform.vehicle.pb.MsgPdu)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  signals_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    state_ = 0;
    pdu_id_ = 4099;
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MsgPdu::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required .autolink.platform.vehicle.pb.PduId pdu_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::autolink::platform::vehicle::pb::PduId_IsValid(val))) {
            _internal_set_pdu_id(static_cast<::autolink::platform::vehicle::pb::PduId>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // required .autolink.platform.vehicle.pb.PduState state = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::autolink::platform::vehicle::pb::PduState_IsValid(val))) {
            _internal_set_state(static_cast<::autolink::platform::vehicle::pb::PduState>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(2, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // repeated .autolink.platform.vehicle.pb.MsgSignal signals = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_signals(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MsgPdu::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:autolink.platform.vehicle.pb.MsgPdu)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .autolink.platform.vehicle.pb.PduId pdu_id = 1;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_pdu_id(), target);
  }

  // required .autolink.platform.vehicle.pb.PduState state = 2;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_state(), target);
  }

  // repeated .autolink.platform.vehicle.pb.MsgSignal signals = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_signals_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, this->_internal_signals(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:autolink.platform.vehicle.pb.MsgPdu)
  return target;
}

size_t MsgPdu::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:autolink.platform.vehicle.pb.MsgPdu)
  size_t total_size = 0;

  if (_internal_has_state()) {
    // required .autolink.platform.vehicle.pb.PduState state = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());
  }

  if (_internal_has_pdu_id()) {
    // required .autolink.platform.vehicle.pb.PduId pdu_id = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_pdu_id());
  }

  return total_size;
}
size_t MsgPdu::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:autolink.platform.vehicle.pb.MsgPdu)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000003) ^ 0x00000003) == 0) {  // All required fields are present.
    // required .autolink.platform.vehicle.pb.PduState state = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());

    // required .autolink.platform.vehicle.pb.PduId pdu_id = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_pdu_id());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .autolink.platform.vehicle.pb.MsgSignal signals = 3;
  total_size += 1UL * this->_internal_signals_size();
  for (const auto& msg : this->signals_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MsgPdu::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MsgPdu::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MsgPdu::GetClassData() const { return &_class_data_; }

void MsgPdu::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MsgPdu *>(to)->MergeFrom(
      static_cast<const MsgPdu &>(from));
}


void MsgPdu::MergeFrom(const MsgPdu& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:autolink.platform.vehicle.pb.MsgPdu)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  signals_.MergeFrom(from.signals_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      state_ = from.state_;
    }
    if (cached_has_bits & 0x00000002u) {
      pdu_id_ = from.pdu_id_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MsgPdu::CopyFrom(const MsgPdu& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:autolink.platform.vehicle.pb.MsgPdu)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MsgPdu::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (!::PROTOBUF_NAMESPACE_ID::internal::AllAreInitialized(signals_))
    return false;
  return true;
}

void MsgPdu::InternalSwap(MsgPdu* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  signals_.InternalSwap(&other->signals_);
  swap(state_, other->state_);
  swap(pdu_id_, other->pdu_id_);
}

::PROTOBUF_NAMESPACE_ID::Metadata MsgPdu::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_autolink_2eplatform_2evehicle_2eproto_getter, &descriptor_table_autolink_2eplatform_2evehicle_2eproto_once,
      file_level_metadata_autolink_2eplatform_2evehicle_2eproto[2]);
}

// ===================================================================

class MsgList::_Internal {
 public:
};

MsgList::MsgList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  pdus_(arena),
  signals_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:autolink.platform.vehicle.pb.MsgList)
}
MsgList::MsgList(const MsgList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      pdus_(from.pdus_),
      signals_(from.signals_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:autolink.platform.vehicle.pb.MsgList)
}

inline void MsgList::SharedCtor() {
}

MsgList::~MsgList() {
  // @@protoc_insertion_point(destructor:autolink.platform.vehicle.pb.MsgList)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MsgList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void MsgList::ArenaDtor(void* object) {
  MsgList* _this = reinterpret_cast< MsgList* >(object);
  (void)_this;
}
void MsgList::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MsgList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MsgList::Clear() {
// @@protoc_insertion_point(message_clear_start:autolink.platform.vehicle.pb.MsgList)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  pdus_.Clear();
  signals_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MsgList::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .autolink.platform.vehicle.pb.MsgPdu pdus = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_pdus(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .autolink.platform.vehicle.pb.MsgSignalSp signals = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_signals(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MsgList::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:autolink.platform.vehicle.pb.MsgList)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .autolink.platform.vehicle.pb.MsgPdu pdus = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_pdus_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_pdus(i), target, stream);
  }

  // repeated .autolink.platform.vehicle.pb.MsgSignalSp signals = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_signals_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_signals(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:autolink.platform.vehicle.pb.MsgList)
  return target;
}

size_t MsgList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:autolink.platform.vehicle.pb.MsgList)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .autolink.platform.vehicle.pb.MsgPdu pdus = 1;
  total_size += 1UL * this->_internal_pdus_size();
  for (const auto& msg : this->pdus_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .autolink.platform.vehicle.pb.MsgSignalSp signals = 2;
  total_size += 1UL * this->_internal_signals_size();
  for (const auto& msg : this->signals_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MsgList::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MsgList::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MsgList::GetClassData() const { return &_class_data_; }

void MsgList::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MsgList *>(to)->MergeFrom(
      static_cast<const MsgList &>(from));
}


void MsgList::MergeFrom(const MsgList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:autolink.platform.vehicle.pb.MsgList)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  pdus_.MergeFrom(from.pdus_);
  signals_.MergeFrom(from.signals_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MsgList::CopyFrom(const MsgList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:autolink.platform.vehicle.pb.MsgList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MsgList::IsInitialized() const {
  if (!::PROTOBUF_NAMESPACE_ID::internal::AllAreInitialized(pdus_))
    return false;
  if (!::PROTOBUF_NAMESPACE_ID::internal::AllAreInitialized(signals_))
    return false;
  return true;
}

void MsgList::InternalSwap(MsgList* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  pdus_.InternalSwap(&other->pdus_);
  signals_.InternalSwap(&other->signals_);
}

::PROTOBUF_NAMESPACE_ID::Metadata MsgList::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_autolink_2eplatform_2evehicle_2eproto_getter, &descriptor_table_autolink_2eplatform_2evehicle_2eproto_once,
      file_level_metadata_autolink_2eplatform_2evehicle_2eproto[3]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace pb
}  // namespace vehicle
}  // namespace platform
}  // namespace autolink
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::autolink::platform::vehicle::pb::MsgSignal* Arena::CreateMaybeMessage< ::autolink::platform::vehicle::pb::MsgSignal >(Arena* arena) {
  return Arena::CreateMessageInternal< ::autolink::platform::vehicle::pb::MsgSignal >(arena);
}
template<> PROTOBUF_NOINLINE ::autolink::platform::vehicle::pb::MsgSignalSp* Arena::CreateMaybeMessage< ::autolink::platform::vehicle::pb::MsgSignalSp >(Arena* arena) {
  return Arena::CreateMessageInternal< ::autolink::platform::vehicle::pb::MsgSignalSp >(arena);
}
template<> PROTOBUF_NOINLINE ::autolink::platform::vehicle::pb::MsgPdu* Arena::CreateMaybeMessage< ::autolink::platform::vehicle::pb::MsgPdu >(Arena* arena) {
  return Arena::CreateMessageInternal< ::autolink::platform::vehicle::pb::MsgPdu >(arena);
}
template<> PROTOBUF_NOINLINE ::autolink::platform::vehicle::pb::MsgList* Arena::CreateMaybeMessage< ::autolink::platform::vehicle::pb::MsgList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::autolink::platform::vehicle::pb::MsgList >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
