// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: autolink.platform.vehicle.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_autolink_2eplatform_2evehicle_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_autolink_2eplatform_2evehicle_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019005 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_autolink_2eplatform_2evehicle_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_autolink_2eplatform_2evehicle_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[4]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_autolink_2eplatform_2evehicle_2eproto;
namespace autolink {
namespace platform {
namespace vehicle {
namespace pb {
class MsgList;
struct MsgListDefaultTypeInternal;
extern MsgListDefaultTypeInternal _MsgList_default_instance_;
class MsgPdu;
struct MsgPduDefaultTypeInternal;
extern MsgPduDefaultTypeInternal _MsgPdu_default_instance_;
class MsgSignal;
struct MsgSignalDefaultTypeInternal;
extern MsgSignalDefaultTypeInternal _MsgSignal_default_instance_;
class MsgSignalSp;
struct MsgSignalSpDefaultTypeInternal;
extern MsgSignalSpDefaultTypeInternal _MsgSignalSp_default_instance_;
}  // namespace pb
}  // namespace vehicle
}  // namespace platform
}  // namespace autolink
PROTOBUF_NAMESPACE_OPEN
template<> ::autolink::platform::vehicle::pb::MsgList* Arena::CreateMaybeMessage<::autolink::platform::vehicle::pb::MsgList>(Arena*);
template<> ::autolink::platform::vehicle::pb::MsgPdu* Arena::CreateMaybeMessage<::autolink::platform::vehicle::pb::MsgPdu>(Arena*);
template<> ::autolink::platform::vehicle::pb::MsgSignal* Arena::CreateMaybeMessage<::autolink::platform::vehicle::pb::MsgSignal>(Arena*);
template<> ::autolink::platform::vehicle::pb::MsgSignalSp* Arena::CreateMaybeMessage<::autolink::platform::vehicle::pb::MsgSignalSp>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace autolink {
namespace platform {
namespace vehicle {
namespace pb {

enum PduState : int {
  DEFAULT = 0,
  INACTIVE = 1,
  ACTIVE = 2
};
bool PduState_IsValid(int value);
constexpr PduState PduState_MIN = DEFAULT;
constexpr PduState PduState_MAX = ACTIVE;
constexpr int PduState_ARRAYSIZE = PduState_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PduState_descriptor();
template<typename T>
inline const std::string& PduState_Name(T enum_t_value) {
  static_assert(::std::is_same<T, PduState>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function PduState_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    PduState_descriptor(), enum_t_value);
}
inline bool PduState_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, PduState* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<PduState>(
    PduState_descriptor(), name, value);
}
enum SignalValueType : int {
  TYPE_U8 = 1,
  TYPE_U16 = 2,
  TYPE_UINT32 = 3,
  TYPE_BOOLEAN = 4,
  TYPE_S8 = 5,
  TYPE_S16 = 6,
  TYPE_SINT32 = 7,
  TYPE_UINT64 = 8,
  TYPE_SINT64 = 9,
  TYPE_STRING = 10,
  TYPE_BYTES = 11
};
bool SignalValueType_IsValid(int value);
constexpr SignalValueType SignalValueType_MIN = TYPE_U8;
constexpr SignalValueType SignalValueType_MAX = TYPE_BYTES;
constexpr int SignalValueType_ARRAYSIZE = SignalValueType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SignalValueType_descriptor();
template<typename T>
inline const std::string& SignalValueType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SignalValueType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SignalValueType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SignalValueType_descriptor(), enum_t_value);
}
inline bool SignalValueType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SignalValueType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SignalValueType>(
    SignalValueType_descriptor(), name, value);
}
enum PduId : int {
  PDU_TPMS_0x589 = 4099,
  PDU_ID_MAX = 1
};
bool PduId_IsValid(int value);
constexpr PduId PduId_MIN = PDU_ID_MAX;
constexpr PduId PduId_MAX = PDU_TPMS_0x589;
constexpr int PduId_ARRAYSIZE = PduId_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PduId_descriptor();
template<typename T>
inline const std::string& PduId_Name(T enum_t_value) {
  static_assert(::std::is_same<T, PduId>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function PduId_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    PduId_descriptor(), enum_t_value);
}
inline bool PduId_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, PduId* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<PduId>(
    PduId_descriptor(), name, value);
}
enum SignalId : int {
  SIGNAL_PEPS_0x333_PEPS_PwrMod = 4426,
  SIGNAL_ICC_0x46_EHU_MuteCtrl = 4543,
  SIGNAL_ICC_0x610_EHU_EgyCnseClrFlg = 4736,
  SIGNAL_VCU_0x214_VCU_RdyLamp = 4772,
  SIGNAL_VCU_0x214_VCU_GearSigVld = 4096,
  SIGNAL_VCU_0x214_VCU_GearSig = 4097,
  SIGNAL_BMS_0x330_BMS_PwrBattRmngCpSOC = 4839,
  SIGNAL_ESP_0x318_ESP_VehSpdVld = 4871,
  SIGNAL_ESP_0x318_ESP_VehSpd = 4098,
  SIGNAL_ICC_0x531_ICM_TotMilgVld_ODO = 5071,
  SIGNAL_ICC_0x531_ICM_TotMilg_ODO = 5073,
  SIGNAL_ICC_0x531_ICM_DispVehSpd = 5074,
  SIGNAL_ICC_0x532_ICM_MilgOffs_ODO = 5348,
  SIGNAL_ICC_0x532_ICM_DispTotMilg_ODO = 5349,
  SIGNAL_ICC_0x532_ICM_MilgDataVld_ODO = 5350,
  SIGNAL_ICC_0x532_ICM_MilgRstCntr_ODO = 5351,
  SIGNAL_VCU_0x357_ResetFlg_TripfromLastCharge = 5444,
  SIGNAL_VCU_0x503_VCU_DrvPwrLimPerc = 5468,
  SIGNAL_VCU_0x503_VCU_EgyFbPwrLimPerc = 5469,
  SIGNAL_VCU_0x503_VCU_InsntEgyCnseHr = 5478,
  SIGNAL_VCU_0x504_VCU_DrvgMilg = 4102,
  SIGNAL_VCU_0x504_VCU_DrvRngVld = 5492,
  SIGNAL_VCU_0x505_VCU_ChrgDchaGunCnctnIndcrLamp = 5504,
  SIGNAL_VCU_0x50C_VCU_DrvgMilgDispTypCfm = 5511,
  SIGNAL_BCM_0x641_BCM_MilgDataVld_ODO = 5708,
  SIGNAL_BCM_0x641_BCM_MilgRstCntr_ODO = 5709,
  SIGNAL_BCM_0x641_BCM_TotMilg_ODO = 5710,
  SIGNAL_BCM_0x641_BCM_MilgOffs_ODO = 5711,
  SIGNAL_ICC_0x3B3_ICC_TripAResetFlg = 6170,
  SIGNAL_ICC_0x3B3_ICC_TripBResetFlg = 6171,
  SIGNAL_MFS_0x514_MFS_LeRollPress = 6223,
  SIGNAL_VCU_0x3B6_VCU_DrvgMilgDispPerc = 6761,
  SIGNAL_VCU_0x5A9_PdcuFuelLevelDisp = 6988,
  SIGNAL_VCU_0x5A9_PdcuFuMilgVld = 6989,
  SIGNAL_VCU_0x5A9_PdcuFuMilge = 6990,
  SIGNAL_ICC_0x542_ICC_100kmAvrgPwrCnsptn_AB = 7034,
  SIGNAL_ICC_0x542_ICC_10kmAvrgPwrCnsptn_AB = 7035,
  SIGNAL_VCU_0x3B8_VCULastChrgTripAvrgPwrCnsptn_AS = 7038,
  SIGNAL_VCU_0x3B8_VCU_TripAAvrgPwrCnsptn_AS = 7039,
  SIGNAL_VCU_0x3B8_VCU_TripBAvrgPwrCnsptn_AS = 7040,
  SIGNAL_VCU_0x3B8_VCU_100kmRemDrvgRng_AB_AS = 7043,
  SIGNAL_VCU_0x3B8_PdcuEvSt_AS = 7068,
  SIGNAL_VCU_0x3B8_VCULastChrgTripAvrgPwrCnsptn_AB = 7075,
  SIGNAL_VCU_0x3B8_VCU_TripAAvrgPwrCnsptn_AB = 7076,
  SIGNAL_VCU_0x3B8_VCU_TripBAvrgPwrCnsptn_AB = 7077,
  SIGNAL_VCU_0x3B8_VCU_10kmRemDrvgRng_AB = 7078,
  SIGNAL_VCU_0x3B8_VCU_SubtolEgyCnse_AB_AS = 7079,
  SIGNAL_TPMS_0x589_TPMS_TirePosn = 32768,
  SIGNAL_TPMS_0x589_TPMS_SngTireSigSts = 32769,
  SIGNAL_TPMS_0x589_TPMS_SysSts = 32770,
  SIGNAL_TPMS_0x589_TPMS_SngTireLeakageSts = 32771,
  SIGNAL_TPMS_0x589_TPMS_SngTirePSts = 32772,
  SIGNAL_TPMS_0x589_TPMS_TireTStsFlg = 32773,
  SIGNAL_TPMS_0x589_TPMS_SngTireSnsrBattVoltSts = 32774,
  SIGNAL_TPMS_0x589_TPMS_SngTireT = 32775,
  SIGNAL_TPMS_0x589_TPMS_SngTireP = 32776,
  SIGNAL_TPMS_0x589_TPMS_SysDataSts = 32777,
  SIGNAL_TPMS_0x589_TPMS_SnsrMatchSts = 32778,
  SIGNAL_YRS_0x246_YRS_LgtAcce = 4363,
  SIGNAL_ECC_0x373_ECC_ACSts = 4367,
  SIGNAL_ECC_0x373_ECC_WindSpdSts = 4369,
  SIGNAL_ECC_0x373_ECC_BackRowAirOutlModSts = 4370,
  SIGNAL_ECC_0x373_ECC_DrvrTSetSts = 4371,
  SIGNAL_ECC_0x373_ECC_PassTSetSts = 4372,
  SIGNAL_ECC_0x373_ECC_AUTOSts = 4374,
  SIGNAL_ECC_0x373_ECC_SYNCSts = 4375,
  SIGNAL_ECC_0x373_ECC_CircSts = 4377,
  SIGNAL_ECC_0x373_ECC_ParticleConcVld = 4378,
  SIGNAL_ECC_0x373_ECC_OutdT = 4379,
  SIGNAL_ECC_0x373_ECC_OutdTVld = 4380,
  SIGNAL_ECC_0x373_ECC_MaxFrntDefrst = 4381,
  SIGNAL_ECC_0x373_ECC_HeatMngtSysFlt = 4382,
  SIGNAL_ECC_0x373_ECC_HeatMngtFctLim = 4383,
  SIGNAL_ECC_0x373_ECC_DrvrAirOutlMod = 4384,
  SIGNAL_ECC_0x373_ECC_PassAirOutlMod = 4385,
  SIGNAL_ECC_0x373_ECC_EgySaveModSts = 4386,
  SIGNAL_ICC_0x37A_ICC_AutoPower_offSwitchStatus = 4387,
  SIGNAL_ICC_0x37A_EHU_PetsModBtn = 4388,
  SIGNAL_ICC_0x37A_EHU_LeOutlUpDwnMotActvCmd = 4390,
  SIGNAL_ICC_0x37A_EHU_LeOutlLeRiMotActvCmd = 4391,
  SIGNAL_ICC_0x37A_EHU_MidLeOutlUpDwnMotActvCmd = 4393,
  SIGNAL_ICC_0x37A_EHU_MidLeOutlLeRiMotActvCmd = 4394,
  SIGNAL_ICC_0x37A_EHU_MidRiOutlUpDwnMotActvCmd = 4396,
  SIGNAL_ICC_0x37A_EHU_MidRiOutlLeRiMotActvCmd = 4397,
  SIGNAL_ICC_0x37A_EHU_RiOutlUpDwnMotActvCmd = 4399,
  SIGNAL_ICC_0x37A_EHU_RiOutlLeRiMotActvCmd = 4400,
  SIGNAL_ECC_0x378_ECC_LeOutlUpDwnMotActvSts = 4402,
  SIGNAL_ECC_0x378_ECC_LeOutlLeRiMotActvSts = 4403,
  SIGNAL_ECC_0x378_ECC_MidLeOutlUpDwnMotActvSts = 4405,
  SIGNAL_ECC_0x378_ECC_MidLeOutlLeRiMotActvSts = 4406,
  SIGNAL_ECC_0x378_ECC_MidRiOutlUpDwnMotActvSts = 4408,
  SIGNAL_ECC_0x378_ECC_MidRiOutlLeRiMotActvSts = 4409,
  SIGNAL_ECC_0x378_ECC_RiOutlUpDwnMotActvSts = 4411,
  SIGNAL_ECC_0x378_ECC_RiOutlLeRiMotActvSts = 4412,
  SIGNAL_PEPS_0x333_PEPS_PwrModVld = 4427,
  SIGNAL_PEPS_0x333_PEPS_PollingSts = 4428,
  SIGNAL_PEPS_0x37B_PEPS_KeyInCarRmn = 4434,
  SIGNAL_PEPS_0x37B_PEPS_ShiftToPNStrtReq = 4436,
  SIGNAL_PEPS_0x37B_PEPS_StepBrkToStrtReq = 4437,
  SIGNAL_PEPS_0x37B_PEPS_NoFoundLegalKey = 4438,
  SIGNAL_PEPS_0x37B_PEPS_EmgyStrtPromt = 4441,
  SIGNAL_PEPS_0x37B_PEPS_VCUAuthentFailPromt = 4443,
  SIGNAL_PEPS_0x37B_PEPS_KeyLoPwrPromt = 4444,
  SIGNAL_PEPS_0x37B_PEPS_KeyOutdCarPromt = 4445,
  SIGNAL_PEPS_0x380_PEPS_TiOutPwrOffRmn = 4453,
  SIGNAL_PWC_0x524_PWC_ChrgSts = 4454,
  SIGNAL_PWC_0x524_PWC_ModleSwtSts = 4456,
  SIGNAL_ICC_0x533_EHU_DrvrSeatTrackManReq = 4478,
  SIGNAL_ICC_0x533_EHU_DrvrHeiManReq = 4480,
  SIGNAL_ICC_0x533_EHU_DrvrSeatBackManReq = 4481,
  SIGNAL_ICC_0x533_EHU_PassSeatTrackManReq = 4482,
  SIGNAL_ICC_0x533_EHU_PassSeatBackManReq = 4483,
  SIGNAL_ICC_0x533_EHU_LumbarUpd = 4484,
  SIGNAL_ICC_0x533_EHU_LumbarDwn = 4485,
  SIGNAL_ICC_0x533_EHU_LumbarFwd = 4486,
  SIGNAL_ICC_0x533_EHU_LumbarBackw = 4487,
  SIGNAL_ICC_0x533_EHU_AutoPassSeatHeatEna = 4489,
  SIGNAL_ICC_0x533_EHU_PassLumbarUpd = 4494,
  SIGNAL_ICC_0x533_EHU_PassLumbarDwn = 4495,
  SIGNAL_ICC_0x533_EHU_PassLumbarFwd = 4496,
  SIGNAL_ICC_0x533_EHU_PassLumbarBackw = 4497,
  SIGNAL_ICC_0x533_EHU_LRLockWinCmd = 4498,
  SIGNAL_ICC_0x533_EHU_RRLockWinCmd = 4499,
  SIGNAL_ICC_0x52_EHU_SetMaxPosnCmd_PLG = 4501,
  SIGNAL_ICC_0x52_EHU_TrActnCmd = 4502,
  SIGNAL_ICC_0x52_ICC_DRLSwt = 4503,
  SIGNAL_ICC_0x52_ICC_LockAutoClsSunSSwt = 4504,
  SIGNAL_ICC_0x90_EHU_VehAccountLoginUIDSts = 4506,
  SIGNAL_ICC_0x90_EHU_VehAccountLoginUID = 4507,
  SIGNAL_ICC_0x90_EHU_SeatLocnMemOperCmd = 4508,
  SIGNAL_ICC_0x90_EHU_DrvrSeatUidSubPosn = 4509,
  SIGNAL_ICC_0x90_EHU_PassSeatLocnMemOperCmd = 4510,
  SIGNAL_ICC_0x90_EHU_PassSeatUidSubPosn = 4511,
  SIGNAL_ICC_0x44_EHU_FaderSet = 4522,
  SIGNAL_ICC_0x44_EHU_LeRiBalSet = 4524,
  SIGNAL_ICC_0x44_EHU_MidFrqAudioSet = 4526,
  SIGNAL_ICC_0x44_EHU_LoFrqAudioSet = 4528,
  SIGNAL_ICC_0x44_EHU_HiFrqAudioSet = 4530,
  SIGNAL_ICC_0x44_EHU_SoundSwitch = 4536,
  SIGNAL_ICC_0x44_EHU_ARCFOXSoundModeSelect = 4537,
  SIGNAL_ICC_0x46_EHU_FolwMeHomeTiSet = 4538,
  SIGNAL_ICC_0x46_EHU_IntrLampTiSet = 4539,
  SIGNAL_ICC_0x46_EHU_MaiVolSet = 4542,
  SIGNAL_ICC_0x46_EHU_IESSModReq = 4545,
  SIGNAL_ICC_0x46_EHU_VSCModReq = 4546,
  SIGNAL_ICC_0x46_EHU_HFTVolSet = 4547,
  SIGNAL_ICC_0x46_EHU_NavVolSet = 4550,
  SIGNAL_ICC_0x46_EHU_RTVolSet = 4552,
  SIGNAL_ICC_0x46_EHU_VolIncreaseWithSpeed = 4558,
  SIGNAL_ICC_0x4E_EHU_RiFrntWinCtrl = 4560,
  SIGNAL_ICC_0x4E_EHU_LeReWinCtrl = 4561,
  SIGNAL_ICC_0x4E_EHU_RiReWinCtrl = 4562,
  SIGNAL_ICC_0x4E_EHU_LeFrntWinCtrl = 4563,
  SIGNAL_ICC_0x4E_EHU_PosnLampCtrl = 4567,
  SIGNAL_ICC_0x4E_EHU_LoBeamCtrl = 4569,
  SIGNAL_ICC_0x4E_EHU_ReDefrstOpenReq = 4575,
  SIGNAL_ICC_0x4E_EHU_SunshadeCtrlReq = 4576,
  SIGNAL_ICC_0x4E_EHU_CentrLockCtrl = 4577,
  SIGNAL_ICC_0x4E_EHU_MirrCmd = 4578,
  SIGNAL_ICC_0x4E_EHU_ReMirrAutoFoldSet = 4579,
  SIGNAL_ICC_0x4E_EHU_RainClsdSunroofSet = 4581,
  SIGNAL_ICC_0x4E_EHU_ArmedClsdWinSet = 4582,
  SIGNAL_ICC_0x4E_EHU_OffUnlckSet = 4583,
  SIGNAL_ICC_0x4E_EHU_DoorUnlockSet = 4584,
  SIGNAL_ICC_0x336_EHU_SetAtmLampBri = 4587,
  SIGNAL_ICC_0x336_EHU_AtmLampOpenCmd = 4591,
  SIGNAL_ICC_0x336_EHU_AlcCstmSwt = 4593,
  SIGNAL_ICC_0x336_EHU_HDCSwtSig = 4594,
  SIGNAL_ICC_0x336_EHU_ESPSwtSig = 4595,
  SIGNAL_ICC_0x336_EHU_SLCSwt = 4598,
  SIGNAL_ICC_0x528_EHU_DrvrSeatHeatgReq = 4602,
  SIGNAL_ICC_0x528_EHU_DrvrSeatVentnReq = 4603,
  SIGNAL_ICC_0x528_EHU_SeatWelFctEnaReq = 4606,
  SIGNAL_ICC_0x528_EHU_PassSeatWelFctEnaReq = 4607,
  SIGNAL_ICC_0x528_EHU_SeatHeatLvAutoReduceReq = 4609,
  SIGNAL_ICC_0x528_EHU_PassSeatHeatgReq = 4610,
  SIGNAL_ICC_0x528_EHU_PassSeatVentnReq = 4611,
  SIGNAL_ICC_0x528_EHU_LRSeatHeatReq = 4612,
  SIGNAL_ICC_0x528_EHU_RRSeatHeatReq = 4614,
  SIGNAL_ICC_0x529_EHU_CrtLanguage = 4617,
  SIGNAL_ICC_0x529_EHU_BriAdj_HUD = 4619,
  SIGNAL_ICC_0x529_EHU_SteerWhlPhnKeyBackLi = 4620,
  SIGNAL_ICC_0x529_EHU_BackgndBriLvl = 4621,
  SIGNAL_ICC_0x529_EHU_BriAdjVal_HUD = 4624,
  SIGNAL_ICC_0x529_EHU_OpenCmd_HUD = 4625,
  SIGNAL_ICC_0x529_EHU_SnowModSwt_HUD = 4626,
  SIGNAL_ICC_0x529_EHU_HeiAdj_HUD = 4627,
  SIGNAL_ICC_0x529_EHU_WiprSrvPosn = 4630,
  SIGNAL_ICC_0x529_EHU_PullModReq = 4635,
  SIGNAL_ICC_0x529_EHU_UsrPwrOffFb = 4636,
  SIGNAL_ICC_0x529_EHU_SteerWhlHeatgSw = 4637,
  SIGNAL_ICC_0x530_EHU_DrvrTSet = 4640,
  SIGNAL_ICC_0x530_EHU_PassTSet = 4641,
  SIGNAL_ICC_0x530_EHU_ECCAUTOReq = 4643,
  SIGNAL_ICC_0x530_EHU_DrvrSYNCReq = 4644,
  SIGNAL_ICC_0x530_EHU_ACSwtReq = 4645,
  SIGNAL_ICC_0x530_EHU_AirVolSet = 4646,
  SIGNAL_ICC_0x530_EHU_ECCIntExtCircReq = 4647,
  SIGNAL_ICC_0x530_EHU_AirClnSwtReq = 4648,
  SIGNAL_ICC_0x530_EHU_MaxFrntDefrstSet = 4649,
  SIGNAL_ICC_0x530_EHU_VSPCtrlCmd = 4650,
  SIGNAL_ICC_0x530_EHU_ECCEgySaveModReq = 4656,
  SIGNAL_ICC_0x530_EHU_BlowWinBtn = 4657,
  SIGNAL_ICC_0x534_EHU_DrvgMilgDispTypSet = 4659,
  SIGNAL_ICC_0x534_EHU_UVCReq = 4660,
  SIGNAL_ICC_0x534_EHU_DrvrBlowFaceBtn = 4663,
  SIGNAL_ICC_0x534_EHU_PassBlowFacetBtn = 4664,
  SIGNAL_ICC_0x534_EHU_DrvrBlowFootBtn = 4665,
  SIGNAL_ICC_0x534_EHU_PassBlowFootBtn = 4666,
  SIGNAL_ICC_0x534_EHU_BackRowAirOutlModReq = 4667,
  SIGNAL_ICC_0x534_EHU_ECCSysSwtCmd = 4669,
  SIGNAL_ICC_0x534_EHU_DrvrBlowModReq = 4670,
  SIGNAL_ICC_0x534_EHU_PassBlowModReq = 4671,
  SIGNAL_ICC_0x534_EHU_FLSeatMasModCmd = 4672,
  SIGNAL_ICC_0x534_EHU_FLSeatMasGradeCmd = 4673,
  SIGNAL_ICC_0x534_EHU_FRSeatMasModCmd = 4674,
  SIGNAL_ICC_0x534_EHU_FRSeatMasGradeCmd = 4675,
  SIGNAL_ICC_0x59C_EHU_UsrSetChrgGunAntithft = 4683,
  SIGNAL_ICC_0x59C_EHU_ChrgDchaCtrlCmd = 4692,
  SIGNAL_ICC_0x59C_EHU_SetACChrgGunUnLockSwt = 4693,
  SIGNAL_ICC_0x59C_EHU_OpenCloseChrgPort1Req = 4695,
  SIGNAL_ICC_0x59C_ICC_AutoColorSwt = 4697,
  SIGNAL_ICC_0x59C_ICC_EmissTestMode = 4699,
  SIGNAL_ICC_0x59C_ICC_IdleMode = 4700,
  SIGNAL_ICC_0x59C_ICC_EnrgMod = 4702,
  SIGNAL_ICC_0x59C_ICC_RefSwitchSts = 4703,
  SIGNAL_ICC_0x59D_ICC_ChrgModUsrSet = 4709,
  SIGNAL_ICC_0x59D_ICC_TurnlampModSwt = 4712,
  SIGNAL_ICC_0x59D_ICC_ContrastColorSwt4 = 4713,
  SIGNAL_ICC_0x59D_ICC_ContrastColorSwt5 = 4714,
  SIGNAL_ICC_0x59D_ICC_ContrastColorSwt6 = 4715,
  SIGNAL_ICC_0x610_EHU_UsrSetChrgRmnMilg = 4734,
  SIGNAL_ICC_0x610_EHU_KL15KeepReq = 4735,
  SIGNAL_ICC_0x610_EHU_PollingFctOpenSts = 4740,
  SIGNAL_ICC_0x610_EHU_ChrgInsulFctReq = 4742,
  SIGNAL_ICC_0x610_EHU_UsrSetDischrgRmnMilg = 4747,
  SIGNAL_ICC_0x610_EHU_SetChrgEndSOC = 4748,
  SIGNAL_VCU_0x214_VCU_PwrBattHVCnctSts = 4773,
  SIGNAL_VCU_0x214_VCU_DrvModShiftMisoper = 4780,
  SIGNAL_VCU_0x358_VCU_ExtremeEgySaveSwtEnaFlg = 4783,
  SIGNAL_VCU_0x358_VCU_PullModEnaSig = 4785,
  SIGNAL_VCU_0x358_VCU_PullModSig = 4786,
  SIGNAL_VCU_0x358_VCU_DrvPwrLimSts = 4789,
  SIGNAL_VCU_0x358_VCU_EgyRecovPwrLimSts = 4790,
  SIGNAL_VCU_0x358_VCU_ExtremeEgySaveOpenSig = 4792,
  SIGNAL_VCU_0x358_VCU_OnePedalKeepDisplay_AB_AS = 4794,
  SIGNAL_VCU_0x579_VCU_VehAcsyCnseEgy = 4808,
  SIGNAL_VCU_0x579_VCU_EgyRecovEgy = 4811,
  SIGNAL_VCU_0x605_VCU_LongTiHlthStorePushInfo = 4829,
  SIGNAL_VCU_0x605_VCU_ChrgStsTxt = 4830,
  SIGNAL_BMS_0x330_BMS_ChrgFltPromt = 4833,
  SIGNAL_BMS_0x330_BMS_CellMinTAlrm = 4840,
  SIGNAL_BMS_0x330_BMS_PwrBattThermRunawayAlrm = 4841,
  SIGNAL_ESP_0x261_ESP_WarningOn = 4849,
  SIGNAL_ESP_0x261_ESP_BrkFldAlrm = 4851,
  SIGNAL_ESP_0x261_ESP_AvlIndcn_CST = 4852,
  SIGNAL_ESP_0x261_ESP_CtrlSts_CST = 4853,
  SIGNAL_ESP_0x268_ESP_SysSts_EPB = 4856,
  SIGNAL_ESP_0x268_ESP_FltIndcn_EPB = 4857,
  SIGNAL_ESP_0x268_ESP_Actvndcn_EPB = 4858,
  SIGNAL_ESP_0x268_EPB_WarnMsg01 = 4859,
  SIGNAL_ESP_0x268_EPB_WarnMsg02 = 4860,
  SIGNAL_ESP_0x268_EPB_WarnMsg04 = 4861,
  SIGNAL_ESP_0x318_ESP_BrkPedlSts = 4870,
  SIGNAL_ESP_0x318_ESP_SysActv = 4872,
  SIGNAL_ESP_0x318_ESP_LampSwtOffIndcn = 4873,
  SIGNAL_ESP_0x318_ESP_FltIndcn_EBD = 4874,
  SIGNAL_ESP_0x318_ESP_FltIndcn_ABS = 4875,
  SIGNAL_ESP_0x318_ESP_FltIndcn_TCS = 4878,
  SIGNAL_ESP_0x318_ESP_CtrlSts_HDC = 4879,
  SIGNAL_ESP_0x318_ESP_AvlIndcn_HDC = 4880,
  SIGNAL_EPS_0x1C2_EPS_SteerWhlAgSig = 4883,
  SIGNAL_EPS_0x1C2_EPS_SteerWhlAgSigVld = 4884,
  SIGNAL_BCM_0x335_BCM_WiprInSrvPosn = 4888,
  SIGNAL_BCM_0x335_BCM_IntLampTiSetSts = 4889,
  SIGNAL_BCM_0x335_BCM_WaterPosnSnsrSwtSts = 4890,
  SIGNAL_BCM_0x335_BCM_ExtLampSwtSts = 4894,
  SIGNAL_BCM_0x335_BCM_RainClsSunroofSetSts = 4900,
  SIGNAL_BCM_0x335_BCM_MirrLockAutoSetSts = 4901,
  SIGNAL_BCM_0x335_BCM_DangerAlrmLampSwtSts = 4903,
  SIGNAL_BCM_0x335_BCM_ReDefrstHeatgCmd = 4904,
  SIGNAL_BCM_0x335_BCM_LeTrunLampOutpCmd = 4907,
  SIGNAL_BCM_0x335_BCM_RiTrunLampOutpCmd = 4908,
  SIGNAL_BCM_0x335_BCM_HiBeamOutpCmd = 4911,
  SIGNAL_BCM_0x335_BCM_LoBeamOutpCmd = 4912,
  SIGNAL_BCM_0x335_BCM_PosnLampOutpCmd = 4913,
  SIGNAL_BCM_0x335_BCM_ReFogLampOutpCmd = 4915,
  SIGNAL_BCM_0x335_BCM_FrntWiprSpd = 4916,
  SIGNAL_BCM_0x335_BCM_VehAmbBri = 4918,
  SIGNAL_BCM_0x343_BCM_FrntHoodLidSts = 4921,
  SIGNAL_BCM_0x343_BCM_SunroofAntipinchSts = 4922,
  SIGNAL_BCM_0x343_BCM_FrntLeDoorLockSts = 4923,
  SIGNAL_BCM_0x343_BCM_TrRelsSwtSts = 4924,
  SIGNAL_BCM_0x343_BCM_LockAllDoorCmd = 4925,
  SIGNAL_BCM_0x343_BCM_LeFrntDoorSts = 4926,
  SIGNAL_BCM_0x343_BCM_RiFrntDoorSts = 4927,
  SIGNAL_BCM_0x343_BCM_TrSts = 4928,
  SIGNAL_BCM_0x343_BCM_AntithftSts = 4929,
  SIGNAL_BCM_0x343_BCM_CenLockSwtSts = 4930,
  SIGNAL_BCM_0x343_BCM_DoorUnlockSetFb = 4931,
  SIGNAL_BCM_0x343_BCM_RiReDoorSts = 4932,
  SIGNAL_BCM_0x343_BCM_LeReDoorSts = 4933,
  SIGNAL_BCM_0x343_BCM_LeFrntWinSts = 4934,
  SIGNAL_BCM_0x343_BCM_RiFrntWinSts = 4935,
  SIGNAL_BCM_0x343_BCM_LeReWinSts = 4936,
  SIGNAL_BCM_0x343_BCM_RiReWinSt = 4937,
  SIGNAL_BCM_0x343_BCM_FolwMeSetStsFb = 4938,
  SIGNAL_BCM_0x343_BCM_DrvrBoorUnlckOutpCmd = 4939,
  SIGNAL_BCM_0x343_BCM_PassDoorUnlckOutpCmd = 4940,
  SIGNAL_BCM_0x343_BCM_LeDRLOutpCmd = 4941,
  SIGNAL_BCM_0x343_BCM_RiDRLOutpCmd = 4942,
  SIGNAL_BCM_0x343_BCM_ArmedClsWinSetSts = 4943,
  SIGNAL_BCM_0x343_BCM_OffAutoUnlckSetSts = 4944,
  SIGNAL_BCM_0x343_BCM_SunroofPosnInfo = 4945,
  SIGNAL_BCM_0x343_BCM_SunroofOpenAr = 4946,
  SIGNAL_BCM_0x343_BCM_SunroofRunngSts = 4947,
  SIGNAL_BCM_0x343_BCM_MirrCmd = 4948,
  SIGNAL_BCM_0x51E_BCM_ApplianceClsLvl = 4949,
  SIGNAL_DSMC_0x4F1_DSMC_DrvrSeatTrackSwtSts = 4952,
  SIGNAL_DSMC_0x4F1_DSMC_DrvrSeatHeiAdjSwtSts = 4953,
  SIGNAL_DSMC_0x4F1_DSMC_DrvrSeatBackAdjSwtSts = 4954,
  SIGNAL_DSMC_0x4F1_DSMC_DrvrSeatWelFctSetFb = 4957,
  SIGNAL_DSMC_0x4F1_DSMC_ReMirrAutoDwnFlipFb = 4958,
  SIGNAL_DSMC_0x4F3_DSMC_RiMirrrXDiecPosn = 4960,
  SIGNAL_DSMC_0x4F3_DSMC_RiMirrrYDircPosn = 4961,
  SIGNAL_DSMC_0x4F3_DSMC_LeMirrrXPosn = 4962,
  SIGNAL_DSMC_0x4F3_DSMC_LeMirrrYPosn = 4963,
  SIGNAL_DSMC_0x4F5_DSMC_DrvrSeatTrackPosn = 4968,
  SIGNAL_DSMC_0x4F5_DSMC_DrvrSeatHeiPosn = 4969,
  SIGNAL_DSMC_0x4F5_DSMC_DrvrSeatBackPosn = 4970,
  SIGNAL_DSMC_0x62_DSMC_DrvrSeatMemRecallFb = 4972,
  SIGNAL_DSMC_0x62_DSMC_DrvrSeatMemDataUpdFb = 4973,
  SIGNAL_DSMC_0x518_DSMC_DrvrSeatHeatgSts = 4975,
  SIGNAL_DSMC_0x518_DSMC_DrvrSeatVentnSts = 4976,
  SIGNAL_DSMC_0x518_DSMC_DrvrSeatTrackAdjSts = 4977,
  SIGNAL_DSMC_0x518_DSMC_DrvrSeatHeiAdjSts = 4978,
  SIGNAL_DSMC_0x518_DSMC_DrvrSeatBackAdjSts = 4979,
  SIGNAL_DSMC_0x518_DSMC_LumbarUpdSts = 4981,
  SIGNAL_DSMC_0x518_DSMC_LumbarDwnSts = 4982,
  SIGNAL_DSMC_0x518_DSMC_LumbarFwdSts = 4983,
  SIGNAL_DSMC_0x518_DSMC_LumbarBackwSts = 4984,
  SIGNAL_DSMC_0x518_DSMC_LRSeatHeatSts = 4986,
  SIGNAL_DSMC_0x518_DSMC_SeatHeatAutoDwnEnaSts = 4987,
  SIGNAL_DSMC_0x518_DSMC_ReMirrLeRiFb = 4988,
  SIGNAL_DSMC_0x518_DSMC_MASFL_SeatMasMod = 4990,
  SIGNAL_DSMC_0x518_DSMC_MASFL_SeatMasGradeSts = 4991,
  SIGNAL_PLG_0x64_PLG_UsrSetTrMaxHeiResFb = 4993,
  SIGNAL_PLG_0x471_PLG_UsrSetTrMaxHei = 4994,
  SIGNAL_PLG_0x471_PLG_LeTrPosn = 4995,
  SIGNAL_PLG_0x471_PLG_SoundRemdngReq_EHU = 4996,
  SIGNAL_PLG_0x471_PLG_SysFltIndcn = 4998,
  SIGNAL_PLG_0x471_PLG_TrSwtStsIndcn = 4999,
  SIGNAL_PLG_0x471_PLG_OperMod = 5001,
  SIGNAL_PLG_0x471_PLG_AntipinchSts = 5002,
  SIGNAL_PAS_0x574_PAS_Sts_FPAS = 5006,
  SIGNAL_PAS_0x574_PAS_Sts_RPAS = 5008,
  SIGNAL_PAS_0x576_PAS_SoundIndcn_F = 5027,
  SIGNAL_PAS_0x576_PAS_SoundIndcn_R = 5028,
  SIGNAL_ICC_0x526_EHU_IntegtCrsSwt = 5035,
  SIGNAL_ICC_0x526_EHU_HMASet = 5045,
  SIGNAL_ICC_0x4DF_EHU_LifeSignMonitorSwt = 5058,
  SIGNAL_ICC_0x531_ICC_RemSentryModSts = 5072,
  SIGNAL_ICC_0x531_ICM_DispVehSpdUnit = 5075,
  SIGNAL_SDM_0x319_SDM_AirBagSysAlrmLampSts = 5082,
  SIGNAL_SDM_0x319_SDM_SecuBltAlrmSts_RL = 5083,
  SIGNAL_SDM_0x319_SDM_SecuBltAlrmSts_RM = 5084,
  SIGNAL_SDM_0x319_SDM_SecuBltAlrmSts_RR = 5085,
  SIGNAL_SDM_0x319_SDM_DrverSecuBltAlrmSts = 5086,
  SIGNAL_SDM_0x319_SDM_PassSeatBltBucdSts = 5087,
  SIGNAL_SDM_0x319_SDM_CllsnSig = 5088,
  SIGNAL_SDM_0x319_SDM_PassSeatOccSts = 5089,
  SIGNAL_CIM_0x310_CIM_FrntWiprSwtSts = 5096,
  SIGNAL_CIM_0x310_CIM_ReWiprSwtSts = 5098,
  SIGNAL_VCU_0x554_VCU_ACChrgElectcLockStsFbSig = 5099,
  SIGNAL_VCU_0x52C_VCU_ChrgDischrgCrtDisp = 5104,
  SIGNAL_VCU_0x52C_VCU_ChrgDchaPwrDisp = 5105,
  SIGNAL_VCU_0x52D_VCU_ElectcLockFltPromt = 5106,
  SIGNAL_BMS_0x51B_BMS_ChrgOprtGuidePromt = 5108,
  SIGNAL_BMS_0x51B_BMS_ChrgRltdStsPromt = 5109,
  SIGNAL_BMS_0x51B_BMS_ChrgDchaStopReasonPromt = 5110,
  SIGNAL_SLC_0x337_SLC_CrtColorR = 5270,
  SIGNAL_SLC_0x337_SLC_CrtColorG = 5271,
  SIGNAL_SLC_0x337_SLC_CrtColorB = 5272,
  SIGNAL_SLC_0x337_SLC_CrtBri = 5273,
  SIGNAL_SLC_0x337_SLC_AlcCstmSts = 5275,
  SIGNAL_SLC_0x3EC_SLC_CourtesyFctModSts = 5278,
  SIGNAL_SLC_0x3EC_SLC_MusicRhythmSts = 5279,
  SIGNAL_SLC_0x3EC_SLC_BriBreathSts = 5280,
  SIGNAL_SLC_0x3EC_SLC_VehSpdRhythmSts = 5282,
  SIGNAL_SLC_0x3EC_SLC_DrvMdSts = 5283,
  SIGNAL_SLC_0x3EC_SLC_AcModSts = 5284,
  SIGNAL_SLC_0x3EC_SLC_DOWSts = 5285,
  SIGNAL_SLC_0x3EC_SLC_Bri = 5286,
  SIGNAL_SLC_0x3EC_SLC_ColourBreathSts2 = 5287,
  SIGNAL_SLC_0x3EC_SLC_AlcSpchSts = 5289,
  SIGNAL_SLC_0x3EC_SLC_AlcMobChaRemdSts = 5290,
  SIGNAL_SLC_0x3EC_SLC_LOGOLigSts = 5291,
  SIGNAL_SLC_0x3EC_SLC_ContrastColorSts1 = 5292,
  SIGNAL_SLC_0x3EC_SLC_ContrastColorSts2 = 5293,
  SIGNAL_SLC_0x3EC_SLC_ContrastColorSts3 = 5294,
  SIGNAL_ICC_0x320_ICC_LightCrlStsFB = 5343,
  SIGNAL_ICC_0x320_ICC_ReFogLampOutpCmd = 5344,
  SIGNAL_ICC_0x532_ICC_Ctrl_Cmd = 5352,
  SIGNAL_ICC_0x532_ICC_OpenPercCmd = 5353,
  SIGNAL_ICC_0x614_EHU_ResvACChrgStrtTi_Hr = 5417,
  SIGNAL_ICC_0x614_EHU_ResvACChrgStrtTi_Mins = 5418,
  SIGNAL_ICC_0x614_EHU_ResvACChrgStrtSet = 5419,
  SIGNAL_ICC_0x614_EHU_MonResvACChrgRepStrtSet = 5420,
  SIGNAL_ICC_0x614_EHU_ResvACChrgEndTi_Hr = 5427,
  SIGNAL_ICC_0x614_EHU_ResvACChrgEndTi_Mins = 5428,
  SIGNAL_ICC_0x614_EHU_ResvACChrgEndSet = 5429,
  SIGNAL_ICC_0x614_EHU_MonResvACChrgRepEndSet = 5430,
  SIGNAL_VCU_0x357_VCU_RefrshModRestrntFctCmd = 5443,
  SIGNAL_VCU_0x511_VCU_RemPwrBattHeatgEndCmd = 5446,
  SIGNAL_VCU_0x511_VCU_RemBattHeatgFailReason = 5447,
  SIGNAL_VCU_0x511_VCU_PetsModFobdReason = 5448,
  SIGNAL_VCU_0x511_VCU_PetsModWarn = 5449,
  SIGNAL_VCU_0x511_VCU_VehCrtChrgEndSOC = 5451,
  SIGNAL_ECC_0x582_ECC_InsdT = 5453,
  SIGNAL_ECC_0x582_ECC_ParticleConc = 5454,
  SIGNAL_VCU_0x105_VCU_AlrmLamp_FS = 5463,
  SIGNAL_VCU_0x105_VCU_CruiseFltTip = 5464,
  SIGNAL_VCU_0x105_VCU_DrvModExtnSig = 5465,
  SIGNAL_VCU_0x105_VCU_CruiseSts = 5466,
  SIGNAL_VCU_0x105_VCU_CruiseAimSpd = 5467,
  SIGNAL_VCU_0x503_VCU_MemChrgRmnMilgThd = 5470,
  SIGNAL_VCU_0x503_VCU_UsrHMIPromt = 5476,
  SIGNAL_VCU_0x503_VCU_ResvChrgStsDisp = 5477,
  SIGNAL_VCU_0x504_VCU_MonrPwrBattThermRunawayAlrm = 5479,
  SIGNAL_VCU_0x504_VCU_LnchCtrlTrigRmn = 5480,
  SIGNAL_VCU_0x504_VCU_ShiftOperRmn = 5481,
  SIGNAL_VCU_0x504_VCU_MCUFSysOverTDisp = 5484,
  SIGNAL_VCU_0x504_VCU_MCURSysOverTDisp = 5485,
  SIGNAL_VCU_0x504_VCU_VehSysFltLamp = 5486,
  SIGNAL_VCU_0x504_VCU_RmnUsrClsECCDispCmd = 5487,
  SIGNAL_VCU_0x504_VCU_RmnUsrECCFctLmtDispCmd = 5488,
  SIGNAL_VCU_0x504_VCU_LnchCtrlModDiRmn = 5489,
  SIGNAL_VCU_0x505_VCU_EgyRecovForbnFlg = 5497,
  SIGNAL_VCU_0x505_VCU_DrvPwrLimIndcrLamp = 5498,
  SIGNAL_VCU_0x505_VCU_BattFltIndcn = 5501,
  SIGNAL_VCU_0x505_VCU_ChrgIndcrLamp = 5502,
  SIGNAL_VCU_0x505_VCU_ChrgGunStrt = 5506,
  SIGNAL_VCU_0x50C_VCU_SOCLoChrgRmn = 5510,
  SIGNAL_BMS_0x215_BMS_AlrmLamp_FS = 5518,
  SIGNAL_BMS_0x240_BMS_ChrgCRateDisp = 5522,
  SIGNAL_BMS_0x363_BMS_FbRemHeatgOperSts = 5528,
  SIGNAL_BMS_0x363_BMS_VehExtDchaSts = 5529,
  SIGNAL_BMS_0x363_BMS_ChrgInsulFctOpenSts = 5532,
  SIGNAL_APA_0x2A0_APA_FMEBSts = 5557,
  SIGNAL_APA_0x2A0_APA_RMEBSts = 5559,
  SIGNAL_APA_0x558_APA_Sts_FPAS = 5584,
  SIGNAL_APA_0x558_APA_Sts_RPAS = 5587,
  SIGNAL_ESP_0x332_ESP_FltIndcn_HHC = 5667,
  SIGNAL_ESP_0x332_ESP_ActvIndcn_AVH = 5668,
  SIGNAL_ESP_0x332_ESP_SwtIndcn_AVH = 5669,
  SIGNAL_ESP_0x332_ESP_FltIndcn_AVH = 5670,
  SIGNAL_EPS_0x470_EPS_FltIndcn = 5674,
  SIGNAL_BCM_0x321_BCM_RLS_LIghtSwtReq = 5675,
  SIGNAL_BCM_0x321_BCM_SWH_SteerWhlHeatgSts = 5676,
  SIGNAL_BCM_0x321_BCM_TimeoutPower_OffFeedback = 5677,
  SIGNAL_BCM_0x321_BCM_LightReqReason_RLS = 5683,
  SIGNAL_BCM_0x321_BCM_OffUnlckSetStsFb = 5684,
  SIGNAL_BCM_0x345_BCM_SunshadeRunngSts = 5688,
  SIGNAL_BCM_0x345_BCM_SunshadePosnInfo = 5689,
  SIGNAL_BCM_0x539_BCM_LePosnLampFlt = 5690,
  SIGNAL_BCM_0x539_BCM_RiPosnLampFlt = 5691,
  SIGNAL_BCM_0x539_BCM_LeLoBeamFlt = 5692,
  SIGNAL_BCM_0x539_BCM_RiLoBeamFlt = 5693,
  SIGNAL_BCM_0x539_BCM_LeHiBeamFlt = 5694,
  SIGNAL_BCM_0x539_BCM_RiHiBeamFlt = 5695,
  SIGNAL_BCM_0x539_BCM_LeDRLFlt = 5696,
  SIGNAL_BCM_0x539_BCM_RiDRLFlt = 5697,
  SIGNAL_BCM_0x539_BCM_LeFrntFogLampFlt = 5698,
  SIGNAL_BCM_0x539_BCM_RiFrntFogLampFlt = 5699,
  SIGNAL_BCM_0x539_BCM_ReFogLampFlt = 5700,
  SIGNAL_BCM_0x539_BCM_LoBrkLampFlt = 5701,
  SIGNAL_BCM_0x539_BCM_HiBrkLampFlt = 5702,
  SIGNAL_BCM_0x539_BCM_RvsLampFlt = 5703,
  SIGNAL_BCM_0x539_BCM_LeTurnLampFlt = 5704,
  SIGNAL_BCM_0x539_BCM_RiTurnLampFlt = 5705,
  SIGNAL_BCM_0x539_BCM_LicPlateLampFlt = 5706,
  SIGNAL_BCM_0x539_BCM_WinLockSwInput = 5707,
  SIGNAL_MPC_0x32C_ADAS_HandsOffTakeOverReq = 5726,
  SIGNAL_MPC_0x32C_ADAS_AudioWarn = 5734,
  SIGNAL_MPC_0x32E_MPC_OverSpdWarn_SLA = 5743,
  SIGNAL_MPC_0x32E_MPC_SpdLimUnit_SLA = 5749,
  SIGNAL_MPC_0x32E_MPC_SpdLim_SLA = 5750,
  SIGNAL_MPC_0x33C_ADAS_Warn_FCW = 5751,
  SIGNAL_MPC_0x33C_ADAS_Sts_FCW = 5753,
  SIGNAL_MPC_0x33C_ADAS_Sts_AEB = 5754,
  SIGNAL_MPC_0x347_ADAS_TakeOverReq_ACC = 5773,
  SIGNAL_MPC_0x347_ADAS_SpdLim_ASL = 5777,
  SIGNAL_MPC_0x347_ADAS_SpdLimSts_ASL = 5778,
  SIGNAL_MPC_0x347_ADAS_ACC_OperTxt = 5779,
  SIGNAL_MPC_0x334_MPC_Sts_HMA = 5802,
  SIGNAL_MPC_0x334_MPC_WarnSign = 5805,
  SIGNAL_MPC_0x334_MPC_FobdSign = 5806,
  SIGNAL_MPC_0x334_MPC_OverTakeSign = 5807,
  SIGNAL_MPC_0x334_MPC_FrntCamBli = 5808,
  SIGNAL_MPC_0x334_MPC_HMASetFb = 5809,
  SIGNAL_MPC_0x334_MPC_FrntCamFlt = 5810,
  SIGNAL_MPC_0x340_ADAS_LeLineColor = 5818,
  SIGNAL_MPC_0x340_ADAS_RiLineColor = 5819,
  SIGNAL_MPC_0x340_ADAS_IntegtCrsSwtFb = 5825,
  SIGNAL_MPC_0x340_ADAS_FltIndcr = 5828,
  SIGNAL_MPC_0x340_ADAS_IntecnFltTxt = 5830,
  SIGNAL_CMRR_RL_0x338_CMRR_RL_Sts_LCA = 5833,
  SIGNAL_CMRR_RL_0x338_CMRR_RL_LeWarn_DOW = 5835,
  SIGNAL_CMRR_RL_0x338_CMRR_RL_LeWarn_RCTA = 5840,
  SIGNAL_MPC_0x243_MPC_CMRR_FR_RiWarn_FCTA = 5854,
  SIGNAL_MPC_0x243_MPC_CMRR_FL_LeWarn_FCTA = 5858,
  SIGNAL_TBOX_0x4F4_TBOX_ResvACChrgOpenSts = 5887,
  SIGNAL_TBOX_0x62E_TBOX_ResvACChrgStrtTi_Hr = 5892,
  SIGNAL_TBOX_0x62E_TBOX_ResvACChrgStrtTi_Mins = 5894,
  SIGNAL_TBOX_0x62E_TBOX_MonResvACChrgRepStrtSet = 5895,
  SIGNAL_TBOX_0x62E_TBOX_ResvACChrgEndTi_Hr = 5902,
  SIGNAL_TBOX_0x62E_TBOX_ResvACChrgEndTi_Mins = 5903,
  SIGNAL_TBOX_0x62E_TBOX_ResvACChrgEndSet = 5904,
  SIGNAL_TBOX_0x62E_TBOX_MonResvACChrgRepEndSet = 5905,
  SIGNAL_HUD_0x562_HUD_Swt = 5912,
  SIGNAL_HUD_0x562_HUD_CrtSysSts = 5913,
  SIGNAL_HUD_0x562_HUD_ILLAdj = 5914,
  SIGNAL_HUD_0x562_HUD_HeiAdj = 5915,
  SIGNAL_HUD_0x562_HUD_ModSwt = 5916,
  SIGNAL_HUD_0x562_HUD_SnowModSwtSts = 5917,
  SIGNAL_HUD_0x562_HUD_CrtLanguage = 5918,
  SIGNAL_TBOX_0x62F_TBOX_CrtTi_Day = 5919,
  SIGNAL_TBOX_0x62F_TBOX_CrtTi_Hr = 5920,
  SIGNAL_TBOX_0x62F_TBOX_CrtTi_Mins = 5921,
  SIGNAL_TBOX_0x62F_TBOX_CrtTi_Yr = 5922,
  SIGNAL_TBOX_0x62F_TBOX_CrtTi_Mth = 5923,
  SIGNAL_TBOX_0x62F_TBOX_CrtTi_Sec = 5924,
  SIGNAL_MCU_R_0x151_MCU_R_AlrmLamp_FS = 5957,
  SIGNAL_ESM_0x30C_ESM_SpoilerModSts = 5966,
  SIGNAL_ESM_0x30C_ESM_SpoilerMovementStsFB = 5968,
  SIGNAL_ESM_0x30C_ESM_SpoilerCrlStsFB = 5969,
  SIGNAL_ESM_0x30C_ESM_SpoilerWelcomeFunSetSts = 5970,
  SIGNAL_ICC_0x3C6_ICC_EPSModReq = 5981,
  SIGNAL_ICC_0x3C6_ICC_BrakeEgyRecovIntenReq = 5982,
  SIGNAL_ICC_0x3C6_ICC_AVHSwtSig = 5983,
  SIGNAL_ICC_0x3C6_ICC_DrvgModReq = 5984,
  SIGNAL_ICC_0x3C6_ICC_AFONSts = 5985,
  SIGNAL_ICC_0x3C6_ICC_AFChannelSet = 5986,
  SIGNAL_ICC_0x3C6_ICC_AFConcentration = 5987,
  SIGNAL_ICC_0x3C6_ICC_WashCarSwt = 5988,
  SIGNAL_ICC_0x3C6_ICC_SpoilerModSwt = 5989,
  SIGNAL_ICC_0x3C6_ICC_SpoilerWelcomeFunSwt = 5990,
  SIGNAL_ICC_0x3C6_ICC_WormSts = 5991,
  SIGNAL_ICC_0x3C6_ICC_AcceModReq = 5992,
  SIGNAL_ICC_0x3C6_ICC_ParkChargeSt = 5994,
  SIGNAL_AMP_0x49_AMP_BalSetSts = 6001,
  SIGNAL_AMP_0x49_AMP_FaderSetSts = 6004,
  SIGNAL_AMP_0x49_AMP_IESSModSts = 6006,
  SIGNAL_AMP_0x49_AMP_LoFrqAudioSetSts = 6007,
  SIGNAL_AMP_0x49_AMP_MidFrqAudioSetSts = 6009,
  SIGNAL_AMP_0x49_AMP_HiFrqAudioSetSts = 6011,
  SIGNAL_AMP_0x49_AMP_HFTVolSetSts = 6013,
  SIGNAL_AMP_0x49_AMP_NavVolSetSts = 6015,
  SIGNAL_AMP_0x49_AMP_MaiVolSetSts = 6016,
  SIGNAL_ICC_0x3A8_ICC_LBAdjustSet = 6024,
  SIGNAL_SLC_0x3A7_SLC_LBAdjustSts = 6036,
  SIGNAL_SLC_0x3A7_SLC_ContrastColorSts6 = 6037,
  SIGNAL_SLC_0x3A7_SLC_ContrastColorSts4 = 6038,
  SIGNAL_SLC_0x3A7_SLC_ContrastColorSts = 6039,
  SIGNAL_SLC_0x3A7_SLC_AutoColorSts1 = 6043,
  SIGNAL_ICC_0x327_ICC_ScrnBriLeSet = 6044,
  SIGNAL_ICC_0x327_ICC_ScrnBriAutoSet = 6045,
  SIGNAL_ICC_0x327_ICC_HumAiSpchSts = 6046,
  SIGNAL_ICC_0x31B_ICC_MusicFrq1 = 6047,
  SIGNAL_ICC_0x31B_ICC_MusicFrq2 = 6048,
  SIGNAL_ICC_0x31B_ICC_MusicFrq3 = 6049,
  SIGNAL_ICC_0x31B_ICC_MusicFrq4 = 6050,
  SIGNAL_ICC_0x31B_ICC_MusicFrq5 = 6051,
  SIGNAL_ICC_0x31B_ICC_MusicFrq6 = 6052,
  SIGNAL_ICC_0x31B_ICC_MusicFrq7 = 6053,
  SIGNAL_ICC_0x31B_ICC_MusicFrq8 = 6054,
  SIGNAL_ICC_0x3ED_ICC_AlcBriBreaModSwt = 6056,
  SIGNAL_ICC_0x3ED_ICC_AlcClrBreaModSwt2 = 6058,
  SIGNAL_ICC_0x3ED_ICC_AlcMusicRhyModSwt = 6059,
  SIGNAL_ICC_0x3ED_ICC_AlcSpdRhyModSwt = 6061,
  SIGNAL_ICC_0x3ED_ICC_AlcSpchSwt = 6062,
  SIGNAL_ICC_0x3ED_ICC_AlcAcModSwt = 6063,
  SIGNAL_ICC_0x3ED_ICC_AlcDOWModSwt = 6064,
  SIGNAL_ICC_0x3ED_ICC_AlcDrvModRhySwt = 6065,
  SIGNAL_ICC_0x3ED_ICC_AlcMobChaRemdModSwt = 6066,
  SIGNAL_ICC_0x3ED_ICC_AlcWelModSwt = 6067,
  SIGNAL_ICC_0x3ED_ICC_VehMdMeSts = 6068,
  SIGNAL_ICC_0x3ED_ICC_ApplianceClsLvl = 6069,
  SIGNAL_ICC_0x3ED_ICC_ScrnBriLeSet_DualArea = 6070,
  SIGNAL_ICC_0x3ED_ICC_Airvetn_AL_Swt = 6071,
  SIGNAL_ICC_0x3ED_ICC_AlcDualAreaModSwt = 6072,
  SIGNAL_DSMC_0x3AA_DSMC_PasSeatTrackPosn = 6080,
  SIGNAL_DSMC_0x3AA_DSMC_PasSeatBackPosn = 6082,
  SIGNAL_DSMC_0x66_DSMC_PassSeatMemRecallFb = 6084,
  SIGNAL_DSMC_0x66_DSMC_PassSeatMemDataUpdFb = 6085,
  SIGNAL_DSMC_0x328_DSMC_PassSeatTrackSwtSts = 6086,
  SIGNAL_DSMC_0x328_DSMC_PassSeatBackSwtSts = 6087,
  SIGNAL_DSMC_0x512_DSMC_PassSeatHeatgSts = 6091,
  SIGNAL_DSMC_0x512_DSMC_PassSeatVentnSts = 6092,
  SIGNAL_DSMC_0x512_DSMC_PassSeatTrackAdjSts = 6093,
  SIGNAL_DSMC_0x512_DSMC_PassSeatBackAdjSts = 6094,
  SIGNAL_DSMC_0x512_DSMC_RRSeatHeatSts = 6095,
  SIGNAL_DSMC_0x512_DSMC_PassLumbarUpdSts = 6097,
  SIGNAL_DSMC_0x512_DSMC_PassLumbarDwnSts = 6098,
  SIGNAL_DSMC_0x512_DSMC_PassLumbarFwdSts = 6099,
  SIGNAL_DSMC_0x512_DSMC_PassLumbarBackwSts = 6100,
  SIGNAL_DSMC_0x512_DSMC_SecRowSeatWelFctSetFb = 6103,
  SIGNAL_DSMC_0x512_DSMC_MASFR_SeatMasMod = 6105,
  SIGNAL_DSMC_0x512_DSMC_MASFR_SeatMasGradeSts = 6106,
  SIGNAL_VCU_0x49C_VCU_ChrgGunAntithftOpenSts = 6116,
  SIGNAL_VCU_0x49C_VCU_EHUChrgDchaReq = 6118,
  SIGNAL_VCU_0x49C_VCU_ChrgDchaBtnReq = 6119,
  SIGNAL_BMS_0x49D_BMS_ChrgStsDisp = 6120,
  SIGNAL_EPS_0x475_EPS_ModSts = 6121,
  SIGNAL_BMS_0x3C3_BMS_BattLowTempInd = 6122,
  SIGNAL_BMS_0x29F_BMS_RmngChrgTiDisply = 6123,
  SIGNAL_ICC_0x68_EHU_DrvrSeatTrackPercentReq = 6124,
  SIGNAL_ICC_0x68_EHU_DrvrSeatTiltPercentReq = 6125,
  SIGNAL_ICC_0x68_EHU_DrvrHeiPercentReq = 6126,
  SIGNAL_ICC_0x68_EHU_DrvrSeatBackPercentReq = 6127,
  SIGNAL_ICC_0x68_EHU_PassSeatTrackPercentReq = 6128,
  SIGNAL_ICC_0x68_EHU_PassSeatTiltPercentReq = 6129,
  SIGNAL_ICC_0x68_EHU_PassHeiPercentReq = 6130,
  SIGNAL_ICC_0x68_EHU_PassSeatBackPercentReq = 6131,
  SIGNAL_ICC_0x3AC_EHU_MirrAdjUp = 6132,
  SIGNAL_ICC_0x3AC_EHU_MirrAdjDown = 6133,
  SIGNAL_ICC_0x3AC_EHU_MirrAdjLef = 6134,
  SIGNAL_ICC_0x3AC_EHU_MirrAdjRi = 6135,
  SIGNAL_ICC_0x3AC_EHU_LeReMirrAdjCmd = 6136,
  SIGNAL_ICC_0x3AC_EHU_DrvrMirrTurnDwnAllwd = 6137,
  SIGNAL_ICC_0x3AC_ICC_ExhibCarModNoticeFlag = 6138,
  SIGNAL_VCU_0x102_VCU_DrvModSig = 6141,
  SIGNAL_VCU_0x102_VCU_AclrTiReq = 6143,
  SIGNAL_VCU_0x3A2_VCU_SportModAccTi = 6144,
  SIGNAL_VCU_0x3A2_VCU_ComfortModAccTi = 6145,
  SIGNAL_VCU_0x3A2_VCU_EcoModAccTi = 6146,
  SIGNAL_VCU_0x3A2_VCU_OnePedalModAccTi = 6147,
  SIGNAL_VCU_0x3A2_VCU_PersonalModAccTi = 6148,
  SIGNAL_VCU_0x3A2_VCU_DrvStyleFactor = 6149,
  SIGNAL_VCU_0x3A2_VCU_DrvStyle = 6150,
  SIGNAL_VCU_0x3E8_VCU_BrkEgyRecovIntenSts = 6152,
  SIGNAL_VCU_0x3E8_VCU_WormSts = 6153,
  SIGNAL_VCU_0x3E8_VCU_AclrModSts = 6154,
  SIGNAL_VCU_0x3AB_VCU_ChrgPortEnaFlg = 6155,
  SIGNAL_VCU_0x3AB_VCU_OpenClsFltInfoDisp = 6156,
  SIGNAL_VCU_0x3AB_VCU_VehCurDischrgEndMile = 6157,
  SIGNAL_VCU_0x3AB_VCU_ChrgPortDoorPosSt = 6158,
  SIGNAL_ICC_0x587_ICM_PhnMsgSts = 6160,
  SIGNAL_ICC_0x587_ICM_PhnMsgCallingTimeH = 6161,
  SIGNAL_ICC_0x587_ICM_PhnMsgCallingTimeM = 6162,
  SIGNAL_ICC_0x587_ICM_PhnMsgCallingTimeS = 6163,
  SIGNAL_ICC_0x3B3_ICC_ModSwt_HUD = 6167,
  SIGNAL_ICC_0x3B3_ICC_Cmd_VoiceControlHUD = 6168,
  SIGNAL_BCM_0x3A9_BCM_EEMInfoReqQuiescentCrt = 6212,
  SIGNAL_BCM_0x3A9_BCM_EEMFiMinSavEleMod = 6213,
  SIGNAL_BCM_0x3A9_BCM_VeVMMEnumVehMdMe = 6214,
  SIGNAL_VCU_0x50B_VCU_CarWashModEna = 6215,
  SIGNAL_VCU_0x50B_VCU_CarWashModSts = 6216,
  SIGNAL_MFS_0x514_MFS_IncFolwDst = 6217,
  SIGNAL_MFS_0x514_MFS_DecFolwDst = 6218,
  SIGNAL_MFS_0x514_MFS_CustBtn = 6219,
  SIGNAL_MFS_0x514_MFS_ParkAid = 6220,
  SIGNAL_MFS_0x514_MFS_LeRollUp = 6221,
  SIGNAL_MFS_0x514_MFS_LeRollDwn = 6222,
  SIGNAL_MFS_0x514_MFS_SrcSwtBtn = 6224,
  SIGNAL_MFS_0x514_MFS_VoiceRctcnBtn = 6225,
  SIGNAL_MFS_0x514_MFS_PrevSongTuneSig = 6226,
  SIGNAL_MFS_0x514_MFS_NextSongTuneSig = 6227,
  SIGNAL_MFS_0x514_MFS_RiRollUp = 6228,
  SIGNAL_MFS_0x514_MFS_RiRollDwn = 6229,
  SIGNAL_MFS_0x514_MFS_RiRollPress = 6230,
  SIGNAL_MFS_0x514_MFS_SteerWhlHeatgIndcrLampSts = 6231,
  SIGNAL_EUM_0x309_EUM_LeChildLockSts = 6244,
  SIGNAL_EUM_0x309_EUM_RiChildLockSts = 6245,
  SIGNAL_ECC_0x4FC_ECC_AFU_SwSts = 6250,
  SIGNAL_ECC_0x4FC_ECC_AFU_ChannelSet = 6251,
  SIGNAL_ECC_0x4FC_ECC_AFU_Concentration = 6252,
  SIGNAL_ECC_0x4FC_ECC_AFU_ChannelChgSts = 6254,
  SIGNAL_ECC_0x4FC_ECC_AFU_Ch1Sts = 6255,
  SIGNAL_ECC_0x4FC_ECC_AFU_Ch2Sts = 6256,
  SIGNAL_ECC_0x4FC_ECC_AFU_Ch3Sts = 6257,
  SIGNAL_ECC_0x4FC_ECC_AFU_Ch1LevSts = 6258,
  SIGNAL_ECC_0x4FC_ECC_AFU_Ch2LevSts = 6259,
  SIGNAL_ECC_0x4FC_ECC_AFU_Ch3LevSts = 6260,
  SIGNAL_ECC_0x4FC_ECC_AFU_Ch1ExpirationReminder = 6261,
  SIGNAL_ECC_0x4FC_ECC_AFU_Ch2ExpirationReminder = 6262,
  SIGNAL_ECC_0x4FC_ECC_AFU_Ch3ExpirationReminder = 6263,
  SIGNAL_ICC_0x51A_ICC_CSTSwtSig = 6267,
  SIGNAL_ICC_0x51A_ICC_PwrOffReq = 6273,
  SIGNAL_ICC_0x51A_ICC_PreHeatReq = 6274,
  SIGNAL_VCU_0x4DC_VCU_SoktSplyInteractiveSts = 6275,
  SIGNAL_VCU_0x4DC_VCU_SoktFunOperPromt_EHU = 6277,
  SIGNAL_VCU_0x219_VCU_ExhibCarModSig = 6280,
  SIGNAL_ICC_0x37C_ICC_SetACChrgCurtLimit = 6281,
  SIGNAL_ICC_0x4E2_EHU_CtrlSts_PWC = 6293,
  SIGNAL_BCM_0x3A3_BCM_BackGndBriLvl = 6302,
  SIGNAL_VCU_0x3EE_VCU_PetsModReqFlg = 6310,
  SIGNAL_VCU_0x3EE_VCU_PwrAntiTheft = 6311,
  SIGNAL_ICC_0x91_ICC_LocksoundpromptSwt = 6313,
  SIGNAL_ICC_0x91_ICC_LFWinCrl = 6314,
  SIGNAL_ICC_0x91_ICC_RFWinCrl = 6315,
  SIGNAL_ICC_0x91_ICC_LRWinCrl = 6316,
  SIGNAL_ICC_0x91_ICC_RRWinCrl = 6317,
  SIGNAL_ICC_0x91_ICC_OpenDoorLamplanguage = 6319,
  SIGNAL_ICC_0x91_ICC_LeChildLockSts = 6320,
  SIGNAL_ICC_0x91_ICC_RiChildLockSts = 6321,
  SIGNAL_ICC_0x91_ICC_SmartOpenTrunk = 6322,
  SIGNAL_VCU_0x3BD_VCU_ChrgModFunSts = 6323,
  SIGNAL_APM_0x3E0_APM_LRLockWinSts = 6324,
  SIGNAL_APM_0x3E0_APM_RRLockWinSts = 6325,
  SIGNAL_APM_0x3E0_APM_LFWinPosFB = 6326,
  SIGNAL_APM_0x3E0_APM_RFWinPosFB = 6327,
  SIGNAL_APM_0x3E0_APM_LRWinPosFB = 6328,
  SIGNAL_APM_0x3E0_APM_RRWinPosFB = 6329,
  SIGNAL_VCU_0x51C_VCU_ChrgStopSOCPlanVal = 6335,
  SIGNAL_ICC_0xBA_ICC_AlcClrCstmSet = 6507,
  SIGNAL_ICC_0x69_ICC_ALcAdj_Color1Set = 6509,
  SIGNAL_ICC_0x69_ICC_ALcAdj_Color2Set = 6510,
  SIGNAL_SLC_0x510_SLC_AlcSts = 6511,
  SIGNAL_SLC_0x510_SLC_ScrnBriAutoSts = 6512,
  SIGNAL_SLC_0x510_SLC_ColorCstmSts = 6513,
  SIGNAL_SLC_0x510_SLC_ALcCrtAdj_Color1 = 6516,
  SIGNAL_SLC_0x510_SLC_ALcCrtAdj_Color2 = 6517,
  SIGNAL_SLC_0x510_SLC_Airvetn_AL_Sts = 6518,
  SIGNAL_SLC_0x510_SLC_AlcDualAreaModSts = 6519,
  SIGNAL_SLC_0x510_SLC_TurnlampModSts = 6521,
  SIGNAL_ECC_0x3F7_ECC_UVCSts = 6523,
  SIGNAL_HUD_0x6B_HUD_ARPara1Fb1 = 6527,
  SIGNAL_HUD_0x6B_HUD_ARPara1Fb2 = 6528,
  SIGNAL_HUD_0x6B_HUD_ARPara1Fb3 = 6529,
  SIGNAL_HUD_0x6B_HUD_ARPara1Fb4 = 6530,
  SIGNAL_HUD_0x6B_HUD_ARPara1Fb5 = 6531,
  SIGNAL_HUD_0x6B_HUD_ARPara1Fb6 = 6532,
  SIGNAL_HUD_0x6B_HUD_ARPara1Fb7 = 6533,
  SIGNAL_HUD_0x6B_HUD_ARPara1Fb8 = 6534,
  SIGNAL_HUD_0x6B_HUD_ARPara1Fb9 = 6535,
  SIGNAL_HUD_0x6B_HUD_ARPara1Fb10 = 6536,
  SIGNAL_HUD_0x6B_HUD_ARPara1Fb11 = 6537,
  SIGNAL_HUD_0x6B_HUD_ARPara1Fb12 = 6538,
  SIGNAL_HUD_0x6B_HUD_ARPara1Fb13 = 6539,
  SIGNAL_HUD_0x6B_HUD_ARPara1Fb14 = 6540,
  SIGNAL_HUD_0x6B_HUD_ARPara1Fb15 = 6541,
  SIGNAL_HUD_0x6B_HUD_ARPara1Fb16 = 6542,
  SIGNAL_HUD_0x6B_HUD_ARPara2Fb1 = 6543,
  SIGNAL_HUD_0x6B_HUD_ARPara2Fb2 = 6544,
  SIGNAL_HUD_0x6B_HUD_ARPara2Fb3 = 6545,
  SIGNAL_HUD_0x6B_HUD_ARPara2Fb4 = 6546,
  SIGNAL_HUD_0x6B_HUD_ARPara2Fb5 = 6547,
  SIGNAL_HUD_0x6B_HUD_ARPara2Fb6 = 6548,
  SIGNAL_HUD_0x6B_HUD_ARPara2Fb7 = 6549,
  SIGNAL_HUD_0x6B_HUD_ARPara2Fb8 = 6550,
  SIGNAL_HUD_0x6B_HUD_ARPara2Fb9 = 6551,
  SIGNAL_HUD_0x6B_HUD_ARPara2Fb10 = 6552,
  SIGNAL_HUD_0x6B_HUD_ARPara2Fb11 = 6553,
  SIGNAL_HUD_0x6B_HUD_ARPara2Fb12 = 6554,
  SIGNAL_HUD_0x6B_HUD_ARPara2Fb13 = 6555,
  SIGNAL_HUD_0x6B_HUD_ARPara2Fb14 = 6556,
  SIGNAL_HUD_0x6B_HUD_ARPara2Fb15 = 6557,
  SIGNAL_HUD_0x6B_HUD_ARPara2Fb16 = 6558,
  SIGNAL_HUD_0x6C_HUD_ARPara3Fb1 = 6559,
  SIGNAL_HUD_0x6C_HUD_ARPara3Fb2 = 6560,
  SIGNAL_HUD_0x6C_HUD_ARPara3Fb3 = 6561,
  SIGNAL_HUD_0x6C_HUD_ARPara3Fb4 = 6562,
  SIGNAL_HUD_0x6C_HUD_ARPara3Fb5 = 6563,
  SIGNAL_HUD_0x6C_HUD_ARPara3Fb6 = 6564,
  SIGNAL_HUD_0x6C_HUD_ARPara3Fb7 = 6565,
  SIGNAL_HUD_0x6C_HUD_ARPara3Fb8 = 6566,
  SIGNAL_HUD_0x6C_HUD_ARPara3Fb9 = 6567,
  SIGNAL_HUD_0x6C_HUD_ARPara3Fb10 = 6568,
  SIGNAL_HUD_0x6C_HUD_ARPara3Fb11 = 6569,
  SIGNAL_HUD_0x6C_HUD_ARPara3Fb12 = 6570,
  SIGNAL_HUD_0x6C_HUD_ARPara3Fb13 = 6571,
  SIGNAL_HUD_0x6C_HUD_ARPara3Fb14 = 6572,
  SIGNAL_HUD_0x6C_HUD_ARPara3Fb15 = 6573,
  SIGNAL_HUD_0x6C_HUD_ARPara3Fb16 = 6574,
  SIGNAL_HUD_0x6C_HUD_ARPara4Fb1 = 6575,
  SIGNAL_HUD_0x6C_HUD_ARPara4Fb2 = 6576,
  SIGNAL_HUD_0x6C_HUD_ARPara4Fb3 = 6577,
  SIGNAL_HUD_0x6C_HUD_ARPara4Fb4 = 6578,
  SIGNAL_HUD_0x6C_HUD_ARPara4Fb5 = 6579,
  SIGNAL_HUD_0x6C_HUD_ARPara4Fb6 = 6580,
  SIGNAL_HUD_0x6C_HUD_ARPara4Fb7 = 6581,
  SIGNAL_HUD_0x6C_HUD_ARPara4Fb8 = 6582,
  SIGNAL_ICC_0x49A_ICC_ARParaFb1 = 6583,
  SIGNAL_ICC_0x49A_ICC_ARParaFb2 = 6584,
  SIGNAL_ICC_0x49A_ICC_ARParaFb3 = 6585,
  SIGNAL_ICC_0x49A_ICC_ARParaFb4 = 6586,
  SIGNAL_ICC_0x49A_ICC_LOGOLigSwt = 6587,
  SIGNAL_ICC_0x49A_ICC_ContrastColorSwt1 = 6588,
  SIGNAL_ICC_0x49A_ICC_ContrastColorSwt2 = 6589,
  SIGNAL_ICC_0x49A_ICC_ContrastColorSwt3 = 6590,
  SIGNAL_BMS_0x5BF_BMS_MaxLoadPwr = 6591,
  SIGNAL_BCM_0x311_BCM_OpenDoorLamplanguageSwtSts = 6618,
  SIGNAL_BCM_0x311_BCM_LocksoundpromptSwt = 6619,
  SIGNAL_BCM_0x311_BCM_SmartOpenTrunk = 6620,
  SIGNAL_BCM_0x311_RSM_SsMovement = 6621,
  SIGNAL_BCM_0x311_RSM_SSPosPecr = 6622,
  SIGNAL_BCM_0x311_BCM_LockAutoClsSunSSwtFb = 6623,
  SIGNAL_VCU_0x3B6_VCU_DrRangDisPercent_HigPrecDisplayRequire = 6763,
  SIGNAL_VCU_0x3B6_VCU_DrRangDisPercent_HigPrec = 6764,
  SIGNAL_VCU_0x50E_VCU_EHUPwrOffEna = 6769,
  SIGNAL_CPD_0x3B2_CPD_ChildCallASwtSts = 6870,
  SIGNAL_VCU_0x509_PDCU_ExhibCarModNotice = 6946,
  SIGNAL_VCU_0x509_PDCU_ExhibCarmodText = 6947,
  SIGNAL_VCU_0x509_VCU_ExhibCarModDisableNotice = 6948,
  SIGNAL_BCM_0x3DE_BCM_ExhibCarModDisableNotice = 6950,
  SIGNAL_TBOX_0x3FF_TBOX_ExhibCarModDisableNotice = 6951,
  SIGNAL_AVAP_0x549_AVAP_SentryModStsFb = 6959,
  SIGNAL_ICC_0x61_ICC_SentryModTi1Vld = 6966,
  SIGNAL_ICC_0x61_ICC_SentryModTi2Vld = 6967,
  SIGNAL_ICC_0x61_ICC_SentryModStrtTi1 = 6968,
  SIGNAL_ICC_0x61_ICC_SentryModStrtTi2 = 6970,
  SIGNAL_ICC_0x61_ICC_SentryModEndTi1 = 6971,
  SIGNAL_ICC_0x61_ICC_SentryModEndTi2 = 6972,
  SIGNAL_ICC_0x63_ICC_SentryModSw = 6973,
  SIGNAL_ICC_0x63_ICC_SentiSnvtySet = 6978,
  SIGNAL_VCU_0x5A9_VCU_FsTextTip = 6987,
  SIGNAL_BCM_0x51F_BCM_DRLSwtSts = 6991,
  SIGNAL_HDCU_0x31F_PdcuChkEngLamp = 6992,
  SIGNAL_HDCU_0x31F_PdcuRngLim = 6993,
  SIGNAL_PDCU_0x3D0_PdcuEnrgMod = 6994,
  SIGNAL_PDCU_0x3D0_EmsOil_Pfault = 6995,
  SIGNAL_PDCU_0x3D0_IndModEgyFbDis = 6996,
  SIGNAL_PDCU_0x3D0_IndModAccModDis = 6997,
  SIGNAL_PDCU_0x3D0_IndModSteeModDis = 6998,
  SIGNAL_PDCU_0x3D0_IndModCreModDis = 6999,
  SIGNAL_PDCU_0x3D0_PdcuParkChargeSt = 7001,
  SIGNAL_HDCU_0x54B_VCU_OBC_REMIND_LAMP = 7009,
  SIGNAL_TBOX_0xB7_TBOX_SentryModSw = 6986,
  SIGNAL_ICC_0x542_ICC_100kmAvrgPwrCnsptn_AS = 7037,
  SIGNAL_VCU_0x3B8_VCU_PwrCnsptnDiag_AB_AS = 7044,
  SIGNAL_VCU_0x3B8_PdcuEnrgModRejHint_AS = 7059,
  SIGNAL_VCU_0x3B8_PdcuHydOperMod_AS = 7061,
  SIGNAL_VCU_0x3B8_PdcuIdleModSt_AS = 7063,
  SIGNAL_VCU_0x3B8_PdcuEmissTestModSt_AS = 7066,
  SIGNAL_VCU_0x3B8_PdcuRefSwitchSts_AS = 7069,
  SIGNAL_VCU_0x3B8_PdcuRefuNotAllwd_AS = 7070,
  SIGNAL_VCU_0x3B8_PdcuFuFilrDoorRmn_AS = 7071,
  SIGNAL_VCU_0x3B8_PdcuFuTankRelsProgs_AS = 7072,
  SIGNAL_VCU_0x3B8_HDCU_WashModPromptSig_AB_AS = 7074,
  SIGNAL_VCU_0x56B_VCU_VehDrvCnseEgy_AS = 7081,
  SIGNAL_VCU_0x56B_VCU_BHMCnseEgy_AS = 7082,
  SIGNAL_VCU_0x56B_VCU_VehDrvCnseEgy_AB = 7091,
  SIGNAL_MCU_F_0x150_MCU_F_CrtRotDir = 4419,
  SIGNAL_MCU_F_0x150_MCU_F_AlrmLamp_FS = 4423
};
bool SignalId_IsValid(int value);
constexpr SignalId SignalId_MIN = SIGNAL_VCU_0x214_VCU_GearSigVld;
constexpr SignalId SignalId_MAX = SIGNAL_TPMS_0x589_TPMS_SnsrMatchSts;
constexpr int SignalId_ARRAYSIZE = SignalId_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SignalId_descriptor();
template<typename T>
inline const std::string& SignalId_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SignalId>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SignalId_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SignalId_descriptor(), enum_t_value);
}
inline bool SignalId_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SignalId* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SignalId>(
    SignalId_descriptor(), name, value);
}
// ===================================================================

class MsgSignal final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:autolink.platform.vehicle.pb.MsgSignal) */ {
 public:
  inline MsgSignal() : MsgSignal(nullptr) {}
  ~MsgSignal() override;
  explicit constexpr MsgSignal(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MsgSignal(const MsgSignal& from);
  MsgSignal(MsgSignal&& from) noexcept
    : MsgSignal() {
    *this = ::std::move(from);
  }

  inline MsgSignal& operator=(const MsgSignal& from) {
    CopyFrom(from);
    return *this;
  }
  inline MsgSignal& operator=(MsgSignal&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MsgSignal& default_instance() {
    return *internal_default_instance();
  }
  static inline const MsgSignal* internal_default_instance() {
    return reinterpret_cast<const MsgSignal*>(
               &_MsgSignal_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(MsgSignal& a, MsgSignal& b) {
    a.Swap(&b);
  }
  inline void Swap(MsgSignal* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MsgSignal* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MsgSignal* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MsgSignal>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MsgSignal& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MsgSignal& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MsgSignal* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.platform.vehicle.pb.MsgSignal";
  }
  protected:
  explicit MsgSignal(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStringValueFieldNumber = 3,
    kBytesValueFieldNumber = 9,
    kBoolValueFieldNumber = 4,
    kUint32ValueFieldNumber = 5,
    kUint64ValueFieldNumber = 7,
    kSint64ValueFieldNumber = 8,
    kSint32ValueFieldNumber = 6,
    kSignalIdFieldNumber = 1,
    kValueTypeFieldNumber = 2,
  };
  // optional string string_value = 3;
  bool has_string_value() const;
  private:
  bool _internal_has_string_value() const;
  public:
  void clear_string_value();
  const std::string& string_value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_string_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_string_value();
  PROTOBUF_NODISCARD std::string* release_string_value();
  void set_allocated_string_value(std::string* string_value);
  private:
  const std::string& _internal_string_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_string_value(const std::string& value);
  std::string* _internal_mutable_string_value();
  public:

  // optional bytes bytes_value = 9;
  bool has_bytes_value() const;
  private:
  bool _internal_has_bytes_value() const;
  public:
  void clear_bytes_value();
  const std::string& bytes_value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_bytes_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_bytes_value();
  PROTOBUF_NODISCARD std::string* release_bytes_value();
  void set_allocated_bytes_value(std::string* bytes_value);
  private:
  const std::string& _internal_bytes_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_bytes_value(const std::string& value);
  std::string* _internal_mutable_bytes_value();
  public:

  // optional bool bool_value = 4;
  bool has_bool_value() const;
  private:
  bool _internal_has_bool_value() const;
  public:
  void clear_bool_value();
  bool bool_value() const;
  void set_bool_value(bool value);
  private:
  bool _internal_bool_value() const;
  void _internal_set_bool_value(bool value);
  public:

  // optional uint32 uint32_value = 5;
  bool has_uint32_value() const;
  private:
  bool _internal_has_uint32_value() const;
  public:
  void clear_uint32_value();
  uint32_t uint32_value() const;
  void set_uint32_value(uint32_t value);
  private:
  uint32_t _internal_uint32_value() const;
  void _internal_set_uint32_value(uint32_t value);
  public:

  // optional uint64 uint64_value = 7;
  bool has_uint64_value() const;
  private:
  bool _internal_has_uint64_value() const;
  public:
  void clear_uint64_value();
  uint64_t uint64_value() const;
  void set_uint64_value(uint64_t value);
  private:
  uint64_t _internal_uint64_value() const;
  void _internal_set_uint64_value(uint64_t value);
  public:

  // optional sint64 sint64_value = 8;
  bool has_sint64_value() const;
  private:
  bool _internal_has_sint64_value() const;
  public:
  void clear_sint64_value();
  int64_t sint64_value() const;
  void set_sint64_value(int64_t value);
  private:
  int64_t _internal_sint64_value() const;
  void _internal_set_sint64_value(int64_t value);
  public:

  // optional sint32 sint32_value = 6;
  bool has_sint32_value() const;
  private:
  bool _internal_has_sint32_value() const;
  public:
  void clear_sint32_value();
  int32_t sint32_value() const;
  void set_sint32_value(int32_t value);
  private:
  int32_t _internal_sint32_value() const;
  void _internal_set_sint32_value(int32_t value);
  public:

  // required .autolink.platform.vehicle.pb.SignalId signal_id = 1;
  bool has_signal_id() const;
  private:
  bool _internal_has_signal_id() const;
  public:
  void clear_signal_id();
  ::autolink::platform::vehicle::pb::SignalId signal_id() const;
  void set_signal_id(::autolink::platform::vehicle::pb::SignalId value);
  private:
  ::autolink::platform::vehicle::pb::SignalId _internal_signal_id() const;
  void _internal_set_signal_id(::autolink::platform::vehicle::pb::SignalId value);
  public:

  // required .autolink.platform.vehicle.pb.SignalValueType value_type = 2;
  bool has_value_type() const;
  private:
  bool _internal_has_value_type() const;
  public:
  void clear_value_type();
  ::autolink::platform::vehicle::pb::SignalValueType value_type() const;
  void set_value_type(::autolink::platform::vehicle::pb::SignalValueType value);
  private:
  ::autolink::platform::vehicle::pb::SignalValueType _internal_value_type() const;
  void _internal_set_value_type(::autolink::platform::vehicle::pb::SignalValueType value);
  public:

  // @@protoc_insertion_point(class_scope:autolink.platform.vehicle.pb.MsgSignal)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr string_value_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr bytes_value_;
  bool bool_value_;
  uint32_t uint32_value_;
  uint64_t uint64_value_;
  int64_t sint64_value_;
  int32_t sint32_value_;
  int signal_id_;
  int value_type_;
  friend struct ::TableStruct_autolink_2eplatform_2evehicle_2eproto;
};
// -------------------------------------------------------------------

class MsgSignalSp final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:autolink.platform.vehicle.pb.MsgSignalSp) */ {
 public:
  inline MsgSignalSp() : MsgSignalSp(nullptr) {}
  ~MsgSignalSp() override;
  explicit constexpr MsgSignalSp(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MsgSignalSp(const MsgSignalSp& from);
  MsgSignalSp(MsgSignalSp&& from) noexcept
    : MsgSignalSp() {
    *this = ::std::move(from);
  }

  inline MsgSignalSp& operator=(const MsgSignalSp& from) {
    CopyFrom(from);
    return *this;
  }
  inline MsgSignalSp& operator=(MsgSignalSp&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MsgSignalSp& default_instance() {
    return *internal_default_instance();
  }
  static inline const MsgSignalSp* internal_default_instance() {
    return reinterpret_cast<const MsgSignalSp*>(
               &_MsgSignalSp_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(MsgSignalSp& a, MsgSignalSp& b) {
    a.Swap(&b);
  }
  inline void Swap(MsgSignalSp* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MsgSignalSp* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MsgSignalSp* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MsgSignalSp>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MsgSignalSp& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MsgSignalSp& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MsgSignalSp* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.platform.vehicle.pb.MsgSignalSp";
  }
  protected:
  explicit MsgSignalSp(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSignalFieldNumber = 2,
    kStateFieldNumber = 1,
  };
  // required .autolink.platform.vehicle.pb.MsgSignal signal = 2;
  bool has_signal() const;
  private:
  bool _internal_has_signal() const;
  public:
  void clear_signal();
  const ::autolink::platform::vehicle::pb::MsgSignal& signal() const;
  PROTOBUF_NODISCARD ::autolink::platform::vehicle::pb::MsgSignal* release_signal();
  ::autolink::platform::vehicle::pb::MsgSignal* mutable_signal();
  void set_allocated_signal(::autolink::platform::vehicle::pb::MsgSignal* signal);
  private:
  const ::autolink::platform::vehicle::pb::MsgSignal& _internal_signal() const;
  ::autolink::platform::vehicle::pb::MsgSignal* _internal_mutable_signal();
  public:
  void unsafe_arena_set_allocated_signal(
      ::autolink::platform::vehicle::pb::MsgSignal* signal);
  ::autolink::platform::vehicle::pb::MsgSignal* unsafe_arena_release_signal();

  // required .autolink.platform.vehicle.pb.PduState state = 1;
  bool has_state() const;
  private:
  bool _internal_has_state() const;
  public:
  void clear_state();
  ::autolink::platform::vehicle::pb::PduState state() const;
  void set_state(::autolink::platform::vehicle::pb::PduState value);
  private:
  ::autolink::platform::vehicle::pb::PduState _internal_state() const;
  void _internal_set_state(::autolink::platform::vehicle::pb::PduState value);
  public:

  // @@protoc_insertion_point(class_scope:autolink.platform.vehicle.pb.MsgSignalSp)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::autolink::platform::vehicle::pb::MsgSignal* signal_;
  int state_;
  friend struct ::TableStruct_autolink_2eplatform_2evehicle_2eproto;
};
// -------------------------------------------------------------------

class MsgPdu final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:autolink.platform.vehicle.pb.MsgPdu) */ {
 public:
  inline MsgPdu() : MsgPdu(nullptr) {}
  ~MsgPdu() override;
  explicit constexpr MsgPdu(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MsgPdu(const MsgPdu& from);
  MsgPdu(MsgPdu&& from) noexcept
    : MsgPdu() {
    *this = ::std::move(from);
  }

  inline MsgPdu& operator=(const MsgPdu& from) {
    CopyFrom(from);
    return *this;
  }
  inline MsgPdu& operator=(MsgPdu&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MsgPdu& default_instance() {
    return *internal_default_instance();
  }
  static inline const MsgPdu* internal_default_instance() {
    return reinterpret_cast<const MsgPdu*>(
               &_MsgPdu_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(MsgPdu& a, MsgPdu& b) {
    a.Swap(&b);
  }
  inline void Swap(MsgPdu* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MsgPdu* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MsgPdu* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MsgPdu>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MsgPdu& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MsgPdu& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MsgPdu* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.platform.vehicle.pb.MsgPdu";
  }
  protected:
  explicit MsgPdu(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSignalsFieldNumber = 3,
    kStateFieldNumber = 2,
    kPduIdFieldNumber = 1,
  };
  // repeated .autolink.platform.vehicle.pb.MsgSignal signals = 3;
  int signals_size() const;
  private:
  int _internal_signals_size() const;
  public:
  void clear_signals();
  ::autolink::platform::vehicle::pb::MsgSignal* mutable_signals(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgSignal >*
      mutable_signals();
  private:
  const ::autolink::platform::vehicle::pb::MsgSignal& _internal_signals(int index) const;
  ::autolink::platform::vehicle::pb::MsgSignal* _internal_add_signals();
  public:
  const ::autolink::platform::vehicle::pb::MsgSignal& signals(int index) const;
  ::autolink::platform::vehicle::pb::MsgSignal* add_signals();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgSignal >&
      signals() const;

  // required .autolink.platform.vehicle.pb.PduState state = 2;
  bool has_state() const;
  private:
  bool _internal_has_state() const;
  public:
  void clear_state();
  ::autolink::platform::vehicle::pb::PduState state() const;
  void set_state(::autolink::platform::vehicle::pb::PduState value);
  private:
  ::autolink::platform::vehicle::pb::PduState _internal_state() const;
  void _internal_set_state(::autolink::platform::vehicle::pb::PduState value);
  public:

  // required .autolink.platform.vehicle.pb.PduId pdu_id = 1;
  bool has_pdu_id() const;
  private:
  bool _internal_has_pdu_id() const;
  public:
  void clear_pdu_id();
  ::autolink::platform::vehicle::pb::PduId pdu_id() const;
  void set_pdu_id(::autolink::platform::vehicle::pb::PduId value);
  private:
  ::autolink::platform::vehicle::pb::PduId _internal_pdu_id() const;
  void _internal_set_pdu_id(::autolink::platform::vehicle::pb::PduId value);
  public:

  // @@protoc_insertion_point(class_scope:autolink.platform.vehicle.pb.MsgPdu)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgSignal > signals_;
  int state_;
  int pdu_id_;
  friend struct ::TableStruct_autolink_2eplatform_2evehicle_2eproto;
};
// -------------------------------------------------------------------

class MsgList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:autolink.platform.vehicle.pb.MsgList) */ {
 public:
  inline MsgList() : MsgList(nullptr) {}
  ~MsgList() override;
  explicit constexpr MsgList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MsgList(const MsgList& from);
  MsgList(MsgList&& from) noexcept
    : MsgList() {
    *this = ::std::move(from);
  }

  inline MsgList& operator=(const MsgList& from) {
    CopyFrom(from);
    return *this;
  }
  inline MsgList& operator=(MsgList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MsgList& default_instance() {
    return *internal_default_instance();
  }
  static inline const MsgList* internal_default_instance() {
    return reinterpret_cast<const MsgList*>(
               &_MsgList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(MsgList& a, MsgList& b) {
    a.Swap(&b);
  }
  inline void Swap(MsgList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MsgList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MsgList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MsgList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MsgList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MsgList& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MsgList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.platform.vehicle.pb.MsgList";
  }
  protected:
  explicit MsgList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPdusFieldNumber = 1,
    kSignalsFieldNumber = 2,
  };
  // repeated .autolink.platform.vehicle.pb.MsgPdu pdus = 1;
  int pdus_size() const;
  private:
  int _internal_pdus_size() const;
  public:
  void clear_pdus();
  ::autolink::platform::vehicle::pb::MsgPdu* mutable_pdus(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgPdu >*
      mutable_pdus();
  private:
  const ::autolink::platform::vehicle::pb::MsgPdu& _internal_pdus(int index) const;
  ::autolink::platform::vehicle::pb::MsgPdu* _internal_add_pdus();
  public:
  const ::autolink::platform::vehicle::pb::MsgPdu& pdus(int index) const;
  ::autolink::platform::vehicle::pb::MsgPdu* add_pdus();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgPdu >&
      pdus() const;

  // repeated .autolink.platform.vehicle.pb.MsgSignalSp signals = 2;
  int signals_size() const;
  private:
  int _internal_signals_size() const;
  public:
  void clear_signals();
  ::autolink::platform::vehicle::pb::MsgSignalSp* mutable_signals(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgSignalSp >*
      mutable_signals();
  private:
  const ::autolink::platform::vehicle::pb::MsgSignalSp& _internal_signals(int index) const;
  ::autolink::platform::vehicle::pb::MsgSignalSp* _internal_add_signals();
  public:
  const ::autolink::platform::vehicle::pb::MsgSignalSp& signals(int index) const;
  ::autolink::platform::vehicle::pb::MsgSignalSp* add_signals();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgSignalSp >&
      signals() const;

  // @@protoc_insertion_point(class_scope:autolink.platform.vehicle.pb.MsgList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgPdu > pdus_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgSignalSp > signals_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_autolink_2eplatform_2evehicle_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// MsgSignal

// required .autolink.platform.vehicle.pb.SignalId signal_id = 1;
inline bool MsgSignal::_internal_has_signal_id() const {
  bool value = (_has_bits_[0] & 0x00000080u) != 0;
  return value;
}
inline bool MsgSignal::has_signal_id() const {
  return _internal_has_signal_id();
}
inline void MsgSignal::clear_signal_id() {
  signal_id_ = 4426;
  _has_bits_[0] &= ~0x00000080u;
}
inline ::autolink::platform::vehicle::pb::SignalId MsgSignal::_internal_signal_id() const {
  return static_cast< ::autolink::platform::vehicle::pb::SignalId >(signal_id_);
}
inline ::autolink::platform::vehicle::pb::SignalId MsgSignal::signal_id() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignal.signal_id)
  return _internal_signal_id();
}
inline void MsgSignal::_internal_set_signal_id(::autolink::platform::vehicle::pb::SignalId value) {
  // assert(::autolink::platform::vehicle::pb::SignalId_IsValid(value));
  _has_bits_[0] |= 0x00000080u;
  signal_id_ = value;
}
inline void MsgSignal::set_signal_id(::autolink::platform::vehicle::pb::SignalId value) {
  _internal_set_signal_id(value);
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgSignal.signal_id)
}

// required .autolink.platform.vehicle.pb.SignalValueType value_type = 2;
inline bool MsgSignal::_internal_has_value_type() const {
  bool value = (_has_bits_[0] & 0x00000100u) != 0;
  return value;
}
inline bool MsgSignal::has_value_type() const {
  return _internal_has_value_type();
}
inline void MsgSignal::clear_value_type() {
  value_type_ = 1;
  _has_bits_[0] &= ~0x00000100u;
}
inline ::autolink::platform::vehicle::pb::SignalValueType MsgSignal::_internal_value_type() const {
  return static_cast< ::autolink::platform::vehicle::pb::SignalValueType >(value_type_);
}
inline ::autolink::platform::vehicle::pb::SignalValueType MsgSignal::value_type() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignal.value_type)
  return _internal_value_type();
}
inline void MsgSignal::_internal_set_value_type(::autolink::platform::vehicle::pb::SignalValueType value) {
  // assert(::autolink::platform::vehicle::pb::SignalValueType_IsValid(value));
  _has_bits_[0] |= 0x00000100u;
  value_type_ = value;
}
inline void MsgSignal::set_value_type(::autolink::platform::vehicle::pb::SignalValueType value) {
  _internal_set_value_type(value);
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgSignal.value_type)
}

// optional string string_value = 3;
inline bool MsgSignal::_internal_has_string_value() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool MsgSignal::has_string_value() const {
  return _internal_has_string_value();
}
inline void MsgSignal::clear_string_value() {
  string_value_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& MsgSignal::string_value() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignal.string_value)
  return _internal_string_value();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MsgSignal::set_string_value(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 string_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgSignal.string_value)
}
inline std::string* MsgSignal::mutable_string_value() {
  std::string* _s = _internal_mutable_string_value();
  // @@protoc_insertion_point(field_mutable:autolink.platform.vehicle.pb.MsgSignal.string_value)
  return _s;
}
inline const std::string& MsgSignal::_internal_string_value() const {
  return string_value_.Get();
}
inline void MsgSignal::_internal_set_string_value(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  string_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* MsgSignal::_internal_mutable_string_value() {
  _has_bits_[0] |= 0x00000001u;
  return string_value_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* MsgSignal::release_string_value() {
  // @@protoc_insertion_point(field_release:autolink.platform.vehicle.pb.MsgSignal.string_value)
  if (!_internal_has_string_value()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = string_value_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (string_value_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    string_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void MsgSignal::set_allocated_string_value(std::string* string_value) {
  if (string_value != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  string_value_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), string_value,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (string_value_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    string_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:autolink.platform.vehicle.pb.MsgSignal.string_value)
}

// optional bool bool_value = 4;
inline bool MsgSignal::_internal_has_bool_value() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool MsgSignal::has_bool_value() const {
  return _internal_has_bool_value();
}
inline void MsgSignal::clear_bool_value() {
  bool_value_ = false;
  _has_bits_[0] &= ~0x00000004u;
}
inline bool MsgSignal::_internal_bool_value() const {
  return bool_value_;
}
inline bool MsgSignal::bool_value() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignal.bool_value)
  return _internal_bool_value();
}
inline void MsgSignal::_internal_set_bool_value(bool value) {
  _has_bits_[0] |= 0x00000004u;
  bool_value_ = value;
}
inline void MsgSignal::set_bool_value(bool value) {
  _internal_set_bool_value(value);
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgSignal.bool_value)
}

// optional uint32 uint32_value = 5;
inline bool MsgSignal::_internal_has_uint32_value() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool MsgSignal::has_uint32_value() const {
  return _internal_has_uint32_value();
}
inline void MsgSignal::clear_uint32_value() {
  uint32_value_ = 0u;
  _has_bits_[0] &= ~0x00000008u;
}
inline uint32_t MsgSignal::_internal_uint32_value() const {
  return uint32_value_;
}
inline uint32_t MsgSignal::uint32_value() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignal.uint32_value)
  return _internal_uint32_value();
}
inline void MsgSignal::_internal_set_uint32_value(uint32_t value) {
  _has_bits_[0] |= 0x00000008u;
  uint32_value_ = value;
}
inline void MsgSignal::set_uint32_value(uint32_t value) {
  _internal_set_uint32_value(value);
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgSignal.uint32_value)
}

// optional sint32 sint32_value = 6;
inline bool MsgSignal::_internal_has_sint32_value() const {
  bool value = (_has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool MsgSignal::has_sint32_value() const {
  return _internal_has_sint32_value();
}
inline void MsgSignal::clear_sint32_value() {
  sint32_value_ = 0;
  _has_bits_[0] &= ~0x00000040u;
}
inline int32_t MsgSignal::_internal_sint32_value() const {
  return sint32_value_;
}
inline int32_t MsgSignal::sint32_value() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignal.sint32_value)
  return _internal_sint32_value();
}
inline void MsgSignal::_internal_set_sint32_value(int32_t value) {
  _has_bits_[0] |= 0x00000040u;
  sint32_value_ = value;
}
inline void MsgSignal::set_sint32_value(int32_t value) {
  _internal_set_sint32_value(value);
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgSignal.sint32_value)
}

// optional uint64 uint64_value = 7;
inline bool MsgSignal::_internal_has_uint64_value() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool MsgSignal::has_uint64_value() const {
  return _internal_has_uint64_value();
}
inline void MsgSignal::clear_uint64_value() {
  uint64_value_ = uint64_t{0u};
  _has_bits_[0] &= ~0x00000010u;
}
inline uint64_t MsgSignal::_internal_uint64_value() const {
  return uint64_value_;
}
inline uint64_t MsgSignal::uint64_value() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignal.uint64_value)
  return _internal_uint64_value();
}
inline void MsgSignal::_internal_set_uint64_value(uint64_t value) {
  _has_bits_[0] |= 0x00000010u;
  uint64_value_ = value;
}
inline void MsgSignal::set_uint64_value(uint64_t value) {
  _internal_set_uint64_value(value);
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgSignal.uint64_value)
}

// optional sint64 sint64_value = 8;
inline bool MsgSignal::_internal_has_sint64_value() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool MsgSignal::has_sint64_value() const {
  return _internal_has_sint64_value();
}
inline void MsgSignal::clear_sint64_value() {
  sint64_value_ = int64_t{0};
  _has_bits_[0] &= ~0x00000020u;
}
inline int64_t MsgSignal::_internal_sint64_value() const {
  return sint64_value_;
}
inline int64_t MsgSignal::sint64_value() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignal.sint64_value)
  return _internal_sint64_value();
}
inline void MsgSignal::_internal_set_sint64_value(int64_t value) {
  _has_bits_[0] |= 0x00000020u;
  sint64_value_ = value;
}
inline void MsgSignal::set_sint64_value(int64_t value) {
  _internal_set_sint64_value(value);
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgSignal.sint64_value)
}

// optional bytes bytes_value = 9;
inline bool MsgSignal::_internal_has_bytes_value() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool MsgSignal::has_bytes_value() const {
  return _internal_has_bytes_value();
}
inline void MsgSignal::clear_bytes_value() {
  bytes_value_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& MsgSignal::bytes_value() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignal.bytes_value)
  return _internal_bytes_value();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MsgSignal::set_bytes_value(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000002u;
 bytes_value_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgSignal.bytes_value)
}
inline std::string* MsgSignal::mutable_bytes_value() {
  std::string* _s = _internal_mutable_bytes_value();
  // @@protoc_insertion_point(field_mutable:autolink.platform.vehicle.pb.MsgSignal.bytes_value)
  return _s;
}
inline const std::string& MsgSignal::_internal_bytes_value() const {
  return bytes_value_.Get();
}
inline void MsgSignal::_internal_set_bytes_value(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  bytes_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* MsgSignal::_internal_mutable_bytes_value() {
  _has_bits_[0] |= 0x00000002u;
  return bytes_value_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* MsgSignal::release_bytes_value() {
  // @@protoc_insertion_point(field_release:autolink.platform.vehicle.pb.MsgSignal.bytes_value)
  if (!_internal_has_bytes_value()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  auto* p = bytes_value_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (bytes_value_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    bytes_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void MsgSignal::set_allocated_bytes_value(std::string* bytes_value) {
  if (bytes_value != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  bytes_value_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), bytes_value,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (bytes_value_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    bytes_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:autolink.platform.vehicle.pb.MsgSignal.bytes_value)
}

// -------------------------------------------------------------------

// MsgSignalSp

// required .autolink.platform.vehicle.pb.PduState state = 1;
inline bool MsgSignalSp::_internal_has_state() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool MsgSignalSp::has_state() const {
  return _internal_has_state();
}
inline void MsgSignalSp::clear_state() {
  state_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::autolink::platform::vehicle::pb::PduState MsgSignalSp::_internal_state() const {
  return static_cast< ::autolink::platform::vehicle::pb::PduState >(state_);
}
inline ::autolink::platform::vehicle::pb::PduState MsgSignalSp::state() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignalSp.state)
  return _internal_state();
}
inline void MsgSignalSp::_internal_set_state(::autolink::platform::vehicle::pb::PduState value) {
  // assert(::autolink::platform::vehicle::pb::PduState_IsValid(value));
  _has_bits_[0] |= 0x00000002u;
  state_ = value;
}
inline void MsgSignalSp::set_state(::autolink::platform::vehicle::pb::PduState value) {
  _internal_set_state(value);
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgSignalSp.state)
}

// required .autolink.platform.vehicle.pb.MsgSignal signal = 2;
inline bool MsgSignalSp::_internal_has_signal() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || signal_ != nullptr);
  return value;
}
inline bool MsgSignalSp::has_signal() const {
  return _internal_has_signal();
}
inline void MsgSignalSp::clear_signal() {
  if (signal_ != nullptr) signal_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::autolink::platform::vehicle::pb::MsgSignal& MsgSignalSp::_internal_signal() const {
  const ::autolink::platform::vehicle::pb::MsgSignal* p = signal_;
  return p != nullptr ? *p : reinterpret_cast<const ::autolink::platform::vehicle::pb::MsgSignal&>(
      ::autolink::platform::vehicle::pb::_MsgSignal_default_instance_);
}
inline const ::autolink::platform::vehicle::pb::MsgSignal& MsgSignalSp::signal() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignalSp.signal)
  return _internal_signal();
}
inline void MsgSignalSp::unsafe_arena_set_allocated_signal(
    ::autolink::platform::vehicle::pb::MsgSignal* signal) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(signal_);
  }
  signal_ = signal;
  if (signal) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:autolink.platform.vehicle.pb.MsgSignalSp.signal)
}
inline ::autolink::platform::vehicle::pb::MsgSignal* MsgSignalSp::release_signal() {
  _has_bits_[0] &= ~0x00000001u;
  ::autolink::platform::vehicle::pb::MsgSignal* temp = signal_;
  signal_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::autolink::platform::vehicle::pb::MsgSignal* MsgSignalSp::unsafe_arena_release_signal() {
  // @@protoc_insertion_point(field_release:autolink.platform.vehicle.pb.MsgSignalSp.signal)
  _has_bits_[0] &= ~0x00000001u;
  ::autolink::platform::vehicle::pb::MsgSignal* temp = signal_;
  signal_ = nullptr;
  return temp;
}
inline ::autolink::platform::vehicle::pb::MsgSignal* MsgSignalSp::_internal_mutable_signal() {
  _has_bits_[0] |= 0x00000001u;
  if (signal_ == nullptr) {
    auto* p = CreateMaybeMessage<::autolink::platform::vehicle::pb::MsgSignal>(GetArenaForAllocation());
    signal_ = p;
  }
  return signal_;
}
inline ::autolink::platform::vehicle::pb::MsgSignal* MsgSignalSp::mutable_signal() {
  ::autolink::platform::vehicle::pb::MsgSignal* _msg = _internal_mutable_signal();
  // @@protoc_insertion_point(field_mutable:autolink.platform.vehicle.pb.MsgSignalSp.signal)
  return _msg;
}
inline void MsgSignalSp::set_allocated_signal(::autolink::platform::vehicle::pb::MsgSignal* signal) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete signal_;
  }
  if (signal) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::autolink::platform::vehicle::pb::MsgSignal>::GetOwningArena(signal);
    if (message_arena != submessage_arena) {
      signal = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, signal, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  signal_ = signal;
  // @@protoc_insertion_point(field_set_allocated:autolink.platform.vehicle.pb.MsgSignalSp.signal)
}

// -------------------------------------------------------------------

// MsgPdu

// required .autolink.platform.vehicle.pb.PduId pdu_id = 1;
inline bool MsgPdu::_internal_has_pdu_id() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool MsgPdu::has_pdu_id() const {
  return _internal_has_pdu_id();
}
inline void MsgPdu::clear_pdu_id() {
  pdu_id_ = 4099;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::autolink::platform::vehicle::pb::PduId MsgPdu::_internal_pdu_id() const {
  return static_cast< ::autolink::platform::vehicle::pb::PduId >(pdu_id_);
}
inline ::autolink::platform::vehicle::pb::PduId MsgPdu::pdu_id() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgPdu.pdu_id)
  return _internal_pdu_id();
}
inline void MsgPdu::_internal_set_pdu_id(::autolink::platform::vehicle::pb::PduId value) {
  // assert(::autolink::platform::vehicle::pb::PduId_IsValid(value));
  _has_bits_[0] |= 0x00000002u;
  pdu_id_ = value;
}
inline void MsgPdu::set_pdu_id(::autolink::platform::vehicle::pb::PduId value) {
  _internal_set_pdu_id(value);
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgPdu.pdu_id)
}

// required .autolink.platform.vehicle.pb.PduState state = 2;
inline bool MsgPdu::_internal_has_state() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool MsgPdu::has_state() const {
  return _internal_has_state();
}
inline void MsgPdu::clear_state() {
  state_ = 0;
  _has_bits_[0] &= ~0x00000001u;
}
inline ::autolink::platform::vehicle::pb::PduState MsgPdu::_internal_state() const {
  return static_cast< ::autolink::platform::vehicle::pb::PduState >(state_);
}
inline ::autolink::platform::vehicle::pb::PduState MsgPdu::state() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgPdu.state)
  return _internal_state();
}
inline void MsgPdu::_internal_set_state(::autolink::platform::vehicle::pb::PduState value) {
  // assert(::autolink::platform::vehicle::pb::PduState_IsValid(value));
  _has_bits_[0] |= 0x00000001u;
  state_ = value;
}
inline void MsgPdu::set_state(::autolink::platform::vehicle::pb::PduState value) {
  _internal_set_state(value);
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgPdu.state)
}

// repeated .autolink.platform.vehicle.pb.MsgSignal signals = 3;
inline int MsgPdu::_internal_signals_size() const {
  return signals_.size();
}
inline int MsgPdu::signals_size() const {
  return _internal_signals_size();
}
inline void MsgPdu::clear_signals() {
  signals_.Clear();
}
inline ::autolink::platform::vehicle::pb::MsgSignal* MsgPdu::mutable_signals(int index) {
  // @@protoc_insertion_point(field_mutable:autolink.platform.vehicle.pb.MsgPdu.signals)
  return signals_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgSignal >*
MsgPdu::mutable_signals() {
  // @@protoc_insertion_point(field_mutable_list:autolink.platform.vehicle.pb.MsgPdu.signals)
  return &signals_;
}
inline const ::autolink::platform::vehicle::pb::MsgSignal& MsgPdu::_internal_signals(int index) const {
  return signals_.Get(index);
}
inline const ::autolink::platform::vehicle::pb::MsgSignal& MsgPdu::signals(int index) const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgPdu.signals)
  return _internal_signals(index);
}
inline ::autolink::platform::vehicle::pb::MsgSignal* MsgPdu::_internal_add_signals() {
  return signals_.Add();
}
inline ::autolink::platform::vehicle::pb::MsgSignal* MsgPdu::add_signals() {
  ::autolink::platform::vehicle::pb::MsgSignal* _add = _internal_add_signals();
  // @@protoc_insertion_point(field_add:autolink.platform.vehicle.pb.MsgPdu.signals)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgSignal >&
MsgPdu::signals() const {
  // @@protoc_insertion_point(field_list:autolink.platform.vehicle.pb.MsgPdu.signals)
  return signals_;
}

// -------------------------------------------------------------------

// MsgList

// repeated .autolink.platform.vehicle.pb.MsgPdu pdus = 1;
inline int MsgList::_internal_pdus_size() const {
  return pdus_.size();
}
inline int MsgList::pdus_size() const {
  return _internal_pdus_size();
}
inline void MsgList::clear_pdus() {
  pdus_.Clear();
}
inline ::autolink::platform::vehicle::pb::MsgPdu* MsgList::mutable_pdus(int index) {
  // @@protoc_insertion_point(field_mutable:autolink.platform.vehicle.pb.MsgList.pdus)
  return pdus_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgPdu >*
MsgList::mutable_pdus() {
  // @@protoc_insertion_point(field_mutable_list:autolink.platform.vehicle.pb.MsgList.pdus)
  return &pdus_;
}
inline const ::autolink::platform::vehicle::pb::MsgPdu& MsgList::_internal_pdus(int index) const {
  return pdus_.Get(index);
}
inline const ::autolink::platform::vehicle::pb::MsgPdu& MsgList::pdus(int index) const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgList.pdus)
  return _internal_pdus(index);
}
inline ::autolink::platform::vehicle::pb::MsgPdu* MsgList::_internal_add_pdus() {
  return pdus_.Add();
}
inline ::autolink::platform::vehicle::pb::MsgPdu* MsgList::add_pdus() {
  ::autolink::platform::vehicle::pb::MsgPdu* _add = _internal_add_pdus();
  // @@protoc_insertion_point(field_add:autolink.platform.vehicle.pb.MsgList.pdus)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgPdu >&
MsgList::pdus() const {
  // @@protoc_insertion_point(field_list:autolink.platform.vehicle.pb.MsgList.pdus)
  return pdus_;
}

// repeated .autolink.platform.vehicle.pb.MsgSignalSp signals = 2;
inline int MsgList::_internal_signals_size() const {
  return signals_.size();
}
inline int MsgList::signals_size() const {
  return _internal_signals_size();
}
inline void MsgList::clear_signals() {
  signals_.Clear();
}
inline ::autolink::platform::vehicle::pb::MsgSignalSp* MsgList::mutable_signals(int index) {
  // @@protoc_insertion_point(field_mutable:autolink.platform.vehicle.pb.MsgList.signals)
  return signals_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgSignalSp >*
MsgList::mutable_signals() {
  // @@protoc_insertion_point(field_mutable_list:autolink.platform.vehicle.pb.MsgList.signals)
  return &signals_;
}
inline const ::autolink::platform::vehicle::pb::MsgSignalSp& MsgList::_internal_signals(int index) const {
  return signals_.Get(index);
}
inline const ::autolink::platform::vehicle::pb::MsgSignalSp& MsgList::signals(int index) const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgList.signals)
  return _internal_signals(index);
}
inline ::autolink::platform::vehicle::pb::MsgSignalSp* MsgList::_internal_add_signals() {
  return signals_.Add();
}
inline ::autolink::platform::vehicle::pb::MsgSignalSp* MsgList::add_signals() {
  ::autolink::platform::vehicle::pb::MsgSignalSp* _add = _internal_add_signals();
  // @@protoc_insertion_point(field_add:autolink.platform.vehicle.pb.MsgList.signals)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgSignalSp >&
MsgList::signals() const {
  // @@protoc_insertion_point(field_list:autolink.platform.vehicle.pb.MsgList.signals)
  return signals_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace pb
}  // namespace vehicle
}  // namespace platform
}  // namespace autolink

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::autolink::platform::vehicle::pb::PduState> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::autolink::platform::vehicle::pb::PduState>() {
  return ::autolink::platform::vehicle::pb::PduState_descriptor();
}
template <> struct is_proto_enum< ::autolink::platform::vehicle::pb::SignalValueType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::autolink::platform::vehicle::pb::SignalValueType>() {
  return ::autolink::platform::vehicle::pb::SignalValueType_descriptor();
}
template <> struct is_proto_enum< ::autolink::platform::vehicle::pb::PduId> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::autolink::platform::vehicle::pb::PduId>() {
  return ::autolink::platform::vehicle::pb::PduId_descriptor();
}
template <> struct is_proto_enum< ::autolink::platform::vehicle::pb::SignalId> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::autolink::platform::vehicle::pb::SignalId>() {
  return ::autolink::platform::vehicle::pb::SignalId_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_autolink_2eplatform_2evehicle_2eproto
