// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: autolink.platform.vehicle.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_autolink_2eplatform_2evehicle_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_autolink_2eplatform_2evehicle_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019005 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_autolink_2eplatform_2evehicle_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_autolink_2eplatform_2evehicle_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[4]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_autolink_2eplatform_2evehicle_2eproto;
namespace autolink {
namespace platform {
namespace vehicle {
namespace pb {
class MsgList;
struct MsgListDefaultTypeInternal;
extern MsgListDefaultTypeInternal _MsgList_default_instance_;
class MsgPdu;
struct MsgPduDefaultTypeInternal;
extern MsgPduDefaultTypeInternal _MsgPdu_default_instance_;
class MsgSignal;
struct MsgSignalDefaultTypeInternal;
extern MsgSignalDefaultTypeInternal _MsgSignal_default_instance_;
class MsgSignalSp;
struct MsgSignalSpDefaultTypeInternal;
extern MsgSignalSpDefaultTypeInternal _MsgSignalSp_default_instance_;
}  // namespace pb
}  // namespace vehicle
}  // namespace platform
}  // namespace autolink
PROTOBUF_NAMESPACE_OPEN
template<> ::autolink::platform::vehicle::pb::MsgList* Arena::CreateMaybeMessage<::autolink::platform::vehicle::pb::MsgList>(Arena*);
template<> ::autolink::platform::vehicle::pb::MsgPdu* Arena::CreateMaybeMessage<::autolink::platform::vehicle::pb::MsgPdu>(Arena*);
template<> ::autolink::platform::vehicle::pb::MsgSignal* Arena::CreateMaybeMessage<::autolink::platform::vehicle::pb::MsgSignal>(Arena*);
template<> ::autolink::platform::vehicle::pb::MsgSignalSp* Arena::CreateMaybeMessage<::autolink::platform::vehicle::pb::MsgSignalSp>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace autolink {
namespace platform {
namespace vehicle {
namespace pb {

enum PduState : int {
  DEFAULT = 0,
  INACTIVE = 1,
  ACTIVE = 2
};
bool PduState_IsValid(int value);
constexpr PduState PduState_MIN = DEFAULT;
constexpr PduState PduState_MAX = ACTIVE;
constexpr int PduState_ARRAYSIZE = PduState_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PduState_descriptor();
template<typename T>
inline const std::string& PduState_Name(T enum_t_value) {
  static_assert(::std::is_same<T, PduState>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function PduState_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    PduState_descriptor(), enum_t_value);
}
inline bool PduState_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, PduState* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<PduState>(
    PduState_descriptor(), name, value);
}
enum SignalValueType : int {
  TYPE_U8 = 1,
  TYPE_U16 = 2,
  TYPE_UINT32 = 3,
  TYPE_BOOLEAN = 4,
  TYPE_S8 = 5,
  TYPE_S16 = 6,
  TYPE_SINT32 = 7,
  TYPE_UINT64 = 8,
  TYPE_SINT64 = 9,
  TYPE_STRING = 10,
  TYPE_BYTES = 11
};
bool SignalValueType_IsValid(int value);
constexpr SignalValueType SignalValueType_MIN = TYPE_U8;
constexpr SignalValueType SignalValueType_MAX = TYPE_BYTES;
constexpr int SignalValueType_ARRAYSIZE = SignalValueType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SignalValueType_descriptor();
template<typename T>
inline const std::string& SignalValueType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SignalValueType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SignalValueType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SignalValueType_descriptor(), enum_t_value);
}
inline bool SignalValueType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SignalValueType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SignalValueType>(
    SignalValueType_descriptor(), name, value);
}
enum PduId : int {
  PDU_ID_MAX = 0
};
bool PduId_IsValid(int value);
constexpr PduId PduId_MIN = PDU_ID_MAX;
constexpr PduId PduId_MAX = PDU_ID_MAX;
constexpr int PduId_ARRAYSIZE = PduId_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PduId_descriptor();
template<typename T>
inline const std::string& PduId_Name(T enum_t_value) {
  static_assert(::std::is_same<T, PduId>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function PduId_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    PduId_descriptor(), enum_t_value);
}
inline bool PduId_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, PduId* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<PduId>(
    PduId_descriptor(), name, value);
}
enum SignalId : int {
  SIGNAL_ICC_0x321_ICCExtrLampSwt = 4199,
  SIGNAL_ICC_0x321_ReFogLiReq = 4203,
  SIGNAL_ICC_0x377_ICCAcBlowrLelSet2 = 4547,
  SIGNAL_ICC_0x377_ICCAcBlowrLelSet3 = 4548,
  SIGNAL_ICC_0x377_ICCAcAirDistbnSet1_L = 4554,
  SIGNAL_ICC_0x377_ICCAcOnOffSwt1 = 4557,
  SIGNAL_ICC_0x377_ICCAcAcSwt = 4560,
  SIGNAL_ICC_0x377_ICCAcAutoSwt1 = 4561,
  SIGNAL_ICC_0x377_ICCAcDefrstSwt = 4562,
  SIGNAL_ICC_0x377_ICCAcAirRecircSwt = 4563,
  SIGNAL_ICC_0x377_ICCAcBlowerLeSet1 = 4567,
  SIGNAL_ICC_0x377_ICCAcTSet1_L = 4568,
  SIGNAL_ICC_0x377_ICCAcAirDistbnSet1_R = 4578,
  SIGNAL_ICC_0x377_ICCAcTSet1_R = 4579,
  SIGNAL_ECC_0x271_AcOnOffStsDisp1 = 4908,
  SIGNAL_ECC_0x271_AcBlowrLelDisp1 = 4911,
  SIGNAL_ECC_0x271_AcBlowrLelDisp3 = 4912,
  SIGNAL_ECC_0x271_AcBlowrLelDisp5 = 4913,
  SIGNAL_ECC_0x271_AcLeTDisp1 = 4914,
  SIGNAL_ECC_0x271_AcRiTDisp1 = 4915,
  SIGNAL_ECC_0x271_AcLeAirDistbnDisp1 = 4918,
  SIGNAL_ECC_0x271_AcRiAirDistbnDisp1 = 4919,
  SIGNAL_ECC_0x271_AcDefrstDisp = 4922,
  SIGNAL_ECC_0x271_AcRecircDisp = 4923,
  SIGNAL_ECC_0x271_AcLeAutoDisp1 = 4924,
  SIGNAL_ECC_0x271_AcAcDisp = 4927,
  SIGNAL_VDC_0x2D2_BDCExtrLampSts = 5268,
  SIGNAL_VDC_0x2D2_ReFogLampSwt = 5275
};
bool SignalId_IsValid(int value);
constexpr SignalId SignalId_MIN = SIGNAL_ICC_0x321_ICCExtrLampSwt;
constexpr SignalId SignalId_MAX = SIGNAL_VDC_0x2D2_ReFogLampSwt;
constexpr int SignalId_ARRAYSIZE = SignalId_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SignalId_descriptor();
template<typename T>
inline const std::string& SignalId_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SignalId>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SignalId_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SignalId_descriptor(), enum_t_value);
}
inline bool SignalId_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SignalId* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SignalId>(
    SignalId_descriptor(), name, value);
}
// ===================================================================

class MsgSignal final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:autolink.platform.vehicle.pb.MsgSignal) */ {
 public:
  inline MsgSignal() : MsgSignal(nullptr) {}
  ~MsgSignal() override;
  explicit constexpr MsgSignal(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MsgSignal(const MsgSignal& from);
  MsgSignal(MsgSignal&& from) noexcept
    : MsgSignal() {
    *this = ::std::move(from);
  }

  inline MsgSignal& operator=(const MsgSignal& from) {
    CopyFrom(from);
    return *this;
  }
  inline MsgSignal& operator=(MsgSignal&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MsgSignal& default_instance() {
    return *internal_default_instance();
  }
  static inline const MsgSignal* internal_default_instance() {
    return reinterpret_cast<const MsgSignal*>(
               &_MsgSignal_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(MsgSignal& a, MsgSignal& b) {
    a.Swap(&b);
  }
  inline void Swap(MsgSignal* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MsgSignal* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MsgSignal* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MsgSignal>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MsgSignal& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MsgSignal& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MsgSignal* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.platform.vehicle.pb.MsgSignal";
  }
  protected:
  explicit MsgSignal(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStringValueFieldNumber = 3,
    kBytesValueFieldNumber = 9,
    kBoolValueFieldNumber = 4,
    kUint32ValueFieldNumber = 5,
    kUint64ValueFieldNumber = 7,
    kSint64ValueFieldNumber = 8,
    kSint32ValueFieldNumber = 6,
    kSignalIdFieldNumber = 1,
    kValueTypeFieldNumber = 2,
  };
  // optional string string_value = 3;
  bool has_string_value() const;
  private:
  bool _internal_has_string_value() const;
  public:
  void clear_string_value();
  const std::string& string_value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_string_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_string_value();
  PROTOBUF_NODISCARD std::string* release_string_value();
  void set_allocated_string_value(std::string* string_value);
  private:
  const std::string& _internal_string_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_string_value(const std::string& value);
  std::string* _internal_mutable_string_value();
  public:

  // optional bytes bytes_value = 9;
  bool has_bytes_value() const;
  private:
  bool _internal_has_bytes_value() const;
  public:
  void clear_bytes_value();
  const std::string& bytes_value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_bytes_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_bytes_value();
  PROTOBUF_NODISCARD std::string* release_bytes_value();
  void set_allocated_bytes_value(std::string* bytes_value);
  private:
  const std::string& _internal_bytes_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_bytes_value(const std::string& value);
  std::string* _internal_mutable_bytes_value();
  public:

  // optional bool bool_value = 4;
  bool has_bool_value() const;
  private:
  bool _internal_has_bool_value() const;
  public:
  void clear_bool_value();
  bool bool_value() const;
  void set_bool_value(bool value);
  private:
  bool _internal_bool_value() const;
  void _internal_set_bool_value(bool value);
  public:

  // optional uint32 uint32_value = 5;
  bool has_uint32_value() const;
  private:
  bool _internal_has_uint32_value() const;
  public:
  void clear_uint32_value();
  uint32_t uint32_value() const;
  void set_uint32_value(uint32_t value);
  private:
  uint32_t _internal_uint32_value() const;
  void _internal_set_uint32_value(uint32_t value);
  public:

  // optional uint64 uint64_value = 7;
  bool has_uint64_value() const;
  private:
  bool _internal_has_uint64_value() const;
  public:
  void clear_uint64_value();
  uint64_t uint64_value() const;
  void set_uint64_value(uint64_t value);
  private:
  uint64_t _internal_uint64_value() const;
  void _internal_set_uint64_value(uint64_t value);
  public:

  // optional sint64 sint64_value = 8;
  bool has_sint64_value() const;
  private:
  bool _internal_has_sint64_value() const;
  public:
  void clear_sint64_value();
  int64_t sint64_value() const;
  void set_sint64_value(int64_t value);
  private:
  int64_t _internal_sint64_value() const;
  void _internal_set_sint64_value(int64_t value);
  public:

  // optional sint32 sint32_value = 6;
  bool has_sint32_value() const;
  private:
  bool _internal_has_sint32_value() const;
  public:
  void clear_sint32_value();
  int32_t sint32_value() const;
  void set_sint32_value(int32_t value);
  private:
  int32_t _internal_sint32_value() const;
  void _internal_set_sint32_value(int32_t value);
  public:

  // required .autolink.platform.vehicle.pb.SignalId signal_id = 1;
  bool has_signal_id() const;
  private:
  bool _internal_has_signal_id() const;
  public:
  void clear_signal_id();
  ::autolink::platform::vehicle::pb::SignalId signal_id() const;
  void set_signal_id(::autolink::platform::vehicle::pb::SignalId value);
  private:
  ::autolink::platform::vehicle::pb::SignalId _internal_signal_id() const;
  void _internal_set_signal_id(::autolink::platform::vehicle::pb::SignalId value);
  public:

  // required .autolink.platform.vehicle.pb.SignalValueType value_type = 2;
  bool has_value_type() const;
  private:
  bool _internal_has_value_type() const;
  public:
  void clear_value_type();
  ::autolink::platform::vehicle::pb::SignalValueType value_type() const;
  void set_value_type(::autolink::platform::vehicle::pb::SignalValueType value);
  private:
  ::autolink::platform::vehicle::pb::SignalValueType _internal_value_type() const;
  void _internal_set_value_type(::autolink::platform::vehicle::pb::SignalValueType value);
  public:

  // @@protoc_insertion_point(class_scope:autolink.platform.vehicle.pb.MsgSignal)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr string_value_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr bytes_value_;
  bool bool_value_;
  uint32_t uint32_value_;
  uint64_t uint64_value_;
  int64_t sint64_value_;
  int32_t sint32_value_;
  int signal_id_;
  int value_type_;
  friend struct ::TableStruct_autolink_2eplatform_2evehicle_2eproto;
};
// -------------------------------------------------------------------

class MsgSignalSp final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:autolink.platform.vehicle.pb.MsgSignalSp) */ {
 public:
  inline MsgSignalSp() : MsgSignalSp(nullptr) {}
  ~MsgSignalSp() override;
  explicit constexpr MsgSignalSp(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MsgSignalSp(const MsgSignalSp& from);
  MsgSignalSp(MsgSignalSp&& from) noexcept
    : MsgSignalSp() {
    *this = ::std::move(from);
  }

  inline MsgSignalSp& operator=(const MsgSignalSp& from) {
    CopyFrom(from);
    return *this;
  }
  inline MsgSignalSp& operator=(MsgSignalSp&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MsgSignalSp& default_instance() {
    return *internal_default_instance();
  }
  static inline const MsgSignalSp* internal_default_instance() {
    return reinterpret_cast<const MsgSignalSp*>(
               &_MsgSignalSp_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(MsgSignalSp& a, MsgSignalSp& b) {
    a.Swap(&b);
  }
  inline void Swap(MsgSignalSp* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MsgSignalSp* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MsgSignalSp* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MsgSignalSp>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MsgSignalSp& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MsgSignalSp& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MsgSignalSp* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.platform.vehicle.pb.MsgSignalSp";
  }
  protected:
  explicit MsgSignalSp(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSignalFieldNumber = 2,
    kStateFieldNumber = 1,
  };
  // required .autolink.platform.vehicle.pb.MsgSignal signal = 2;
  bool has_signal() const;
  private:
  bool _internal_has_signal() const;
  public:
  void clear_signal();
  const ::autolink::platform::vehicle::pb::MsgSignal& signal() const;
  PROTOBUF_NODISCARD ::autolink::platform::vehicle::pb::MsgSignal* release_signal();
  ::autolink::platform::vehicle::pb::MsgSignal* mutable_signal();
  void set_allocated_signal(::autolink::platform::vehicle::pb::MsgSignal* signal);
  private:
  const ::autolink::platform::vehicle::pb::MsgSignal& _internal_signal() const;
  ::autolink::platform::vehicle::pb::MsgSignal* _internal_mutable_signal();
  public:
  void unsafe_arena_set_allocated_signal(
      ::autolink::platform::vehicle::pb::MsgSignal* signal);
  ::autolink::platform::vehicle::pb::MsgSignal* unsafe_arena_release_signal();

  // required .autolink.platform.vehicle.pb.PduState state = 1;
  bool has_state() const;
  private:
  bool _internal_has_state() const;
  public:
  void clear_state();
  ::autolink::platform::vehicle::pb::PduState state() const;
  void set_state(::autolink::platform::vehicle::pb::PduState value);
  private:
  ::autolink::platform::vehicle::pb::PduState _internal_state() const;
  void _internal_set_state(::autolink::platform::vehicle::pb::PduState value);
  public:

  // @@protoc_insertion_point(class_scope:autolink.platform.vehicle.pb.MsgSignalSp)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::autolink::platform::vehicle::pb::MsgSignal* signal_;
  int state_;
  friend struct ::TableStruct_autolink_2eplatform_2evehicle_2eproto;
};
// -------------------------------------------------------------------

class MsgPdu final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:autolink.platform.vehicle.pb.MsgPdu) */ {
 public:
  inline MsgPdu() : MsgPdu(nullptr) {}
  ~MsgPdu() override;
  explicit constexpr MsgPdu(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MsgPdu(const MsgPdu& from);
  MsgPdu(MsgPdu&& from) noexcept
    : MsgPdu() {
    *this = ::std::move(from);
  }

  inline MsgPdu& operator=(const MsgPdu& from) {
    CopyFrom(from);
    return *this;
  }
  inline MsgPdu& operator=(MsgPdu&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MsgPdu& default_instance() {
    return *internal_default_instance();
  }
  static inline const MsgPdu* internal_default_instance() {
    return reinterpret_cast<const MsgPdu*>(
               &_MsgPdu_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(MsgPdu& a, MsgPdu& b) {
    a.Swap(&b);
  }
  inline void Swap(MsgPdu* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MsgPdu* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MsgPdu* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MsgPdu>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MsgPdu& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MsgPdu& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MsgPdu* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.platform.vehicle.pb.MsgPdu";
  }
  protected:
  explicit MsgPdu(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSignalsFieldNumber = 3,
    kPduIdFieldNumber = 1,
    kStateFieldNumber = 2,
  };
  // repeated .autolink.platform.vehicle.pb.MsgSignal signals = 3;
  int signals_size() const;
  private:
  int _internal_signals_size() const;
  public:
  void clear_signals();
  ::autolink::platform::vehicle::pb::MsgSignal* mutable_signals(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgSignal >*
      mutable_signals();
  private:
  const ::autolink::platform::vehicle::pb::MsgSignal& _internal_signals(int index) const;
  ::autolink::platform::vehicle::pb::MsgSignal* _internal_add_signals();
  public:
  const ::autolink::platform::vehicle::pb::MsgSignal& signals(int index) const;
  ::autolink::platform::vehicle::pb::MsgSignal* add_signals();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgSignal >&
      signals() const;

  // required .autolink.platform.vehicle.pb.PduId pdu_id = 1;
  bool has_pdu_id() const;
  private:
  bool _internal_has_pdu_id() const;
  public:
  void clear_pdu_id();
  ::autolink::platform::vehicle::pb::PduId pdu_id() const;
  void set_pdu_id(::autolink::platform::vehicle::pb::PduId value);
  private:
  ::autolink::platform::vehicle::pb::PduId _internal_pdu_id() const;
  void _internal_set_pdu_id(::autolink::platform::vehicle::pb::PduId value);
  public:

  // required .autolink.platform.vehicle.pb.PduState state = 2;
  bool has_state() const;
  private:
  bool _internal_has_state() const;
  public:
  void clear_state();
  ::autolink::platform::vehicle::pb::PduState state() const;
  void set_state(::autolink::platform::vehicle::pb::PduState value);
  private:
  ::autolink::platform::vehicle::pb::PduState _internal_state() const;
  void _internal_set_state(::autolink::platform::vehicle::pb::PduState value);
  public:

  // @@protoc_insertion_point(class_scope:autolink.platform.vehicle.pb.MsgPdu)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgSignal > signals_;
  int pdu_id_;
  int state_;
  friend struct ::TableStruct_autolink_2eplatform_2evehicle_2eproto;
};
// -------------------------------------------------------------------

class MsgList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:autolink.platform.vehicle.pb.MsgList) */ {
 public:
  inline MsgList() : MsgList(nullptr) {}
  ~MsgList() override;
  explicit constexpr MsgList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MsgList(const MsgList& from);
  MsgList(MsgList&& from) noexcept
    : MsgList() {
    *this = ::std::move(from);
  }

  inline MsgList& operator=(const MsgList& from) {
    CopyFrom(from);
    return *this;
  }
  inline MsgList& operator=(MsgList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MsgList& default_instance() {
    return *internal_default_instance();
  }
  static inline const MsgList* internal_default_instance() {
    return reinterpret_cast<const MsgList*>(
               &_MsgList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(MsgList& a, MsgList& b) {
    a.Swap(&b);
  }
  inline void Swap(MsgList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MsgList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MsgList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MsgList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MsgList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MsgList& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MsgList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.platform.vehicle.pb.MsgList";
  }
  protected:
  explicit MsgList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPdusFieldNumber = 1,
    kSignalsFieldNumber = 2,
  };
  // repeated .autolink.platform.vehicle.pb.MsgPdu pdus = 1;
  int pdus_size() const;
  private:
  int _internal_pdus_size() const;
  public:
  void clear_pdus();
  ::autolink::platform::vehicle::pb::MsgPdu* mutable_pdus(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgPdu >*
      mutable_pdus();
  private:
  const ::autolink::platform::vehicle::pb::MsgPdu& _internal_pdus(int index) const;
  ::autolink::platform::vehicle::pb::MsgPdu* _internal_add_pdus();
  public:
  const ::autolink::platform::vehicle::pb::MsgPdu& pdus(int index) const;
  ::autolink::platform::vehicle::pb::MsgPdu* add_pdus();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgPdu >&
      pdus() const;

  // repeated .autolink.platform.vehicle.pb.MsgSignalSp signals = 2;
  int signals_size() const;
  private:
  int _internal_signals_size() const;
  public:
  void clear_signals();
  ::autolink::platform::vehicle::pb::MsgSignalSp* mutable_signals(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgSignalSp >*
      mutable_signals();
  private:
  const ::autolink::platform::vehicle::pb::MsgSignalSp& _internal_signals(int index) const;
  ::autolink::platform::vehicle::pb::MsgSignalSp* _internal_add_signals();
  public:
  const ::autolink::platform::vehicle::pb::MsgSignalSp& signals(int index) const;
  ::autolink::platform::vehicle::pb::MsgSignalSp* add_signals();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgSignalSp >&
      signals() const;

  // @@protoc_insertion_point(class_scope:autolink.platform.vehicle.pb.MsgList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgPdu > pdus_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgSignalSp > signals_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_autolink_2eplatform_2evehicle_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// MsgSignal

// required .autolink.platform.vehicle.pb.SignalId signal_id = 1;
inline bool MsgSignal::_internal_has_signal_id() const {
  bool value = (_has_bits_[0] & 0x00000080u) != 0;
  return value;
}
inline bool MsgSignal::has_signal_id() const {
  return _internal_has_signal_id();
}
inline void MsgSignal::clear_signal_id() {
  signal_id_ = 4199;
  _has_bits_[0] &= ~0x00000080u;
}
inline ::autolink::platform::vehicle::pb::SignalId MsgSignal::_internal_signal_id() const {
  return static_cast< ::autolink::platform::vehicle::pb::SignalId >(signal_id_);
}
inline ::autolink::platform::vehicle::pb::SignalId MsgSignal::signal_id() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignal.signal_id)
  return _internal_signal_id();
}
inline void MsgSignal::_internal_set_signal_id(::autolink::platform::vehicle::pb::SignalId value) {
  // assert(::autolink::platform::vehicle::pb::SignalId_IsValid(value));
  _has_bits_[0] |= 0x00000080u;
  signal_id_ = value;
}
inline void MsgSignal::set_signal_id(::autolink::platform::vehicle::pb::SignalId value) {
  _internal_set_signal_id(value);
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgSignal.signal_id)
}

// required .autolink.platform.vehicle.pb.SignalValueType value_type = 2;
inline bool MsgSignal::_internal_has_value_type() const {
  bool value = (_has_bits_[0] & 0x00000100u) != 0;
  return value;
}
inline bool MsgSignal::has_value_type() const {
  return _internal_has_value_type();
}
inline void MsgSignal::clear_value_type() {
  value_type_ = 1;
  _has_bits_[0] &= ~0x00000100u;
}
inline ::autolink::platform::vehicle::pb::SignalValueType MsgSignal::_internal_value_type() const {
  return static_cast< ::autolink::platform::vehicle::pb::SignalValueType >(value_type_);
}
inline ::autolink::platform::vehicle::pb::SignalValueType MsgSignal::value_type() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignal.value_type)
  return _internal_value_type();
}
inline void MsgSignal::_internal_set_value_type(::autolink::platform::vehicle::pb::SignalValueType value) {
  // assert(::autolink::platform::vehicle::pb::SignalValueType_IsValid(value));
  _has_bits_[0] |= 0x00000100u;
  value_type_ = value;
}
inline void MsgSignal::set_value_type(::autolink::platform::vehicle::pb::SignalValueType value) {
  _internal_set_value_type(value);
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgSignal.value_type)
}

// optional string string_value = 3;
inline bool MsgSignal::_internal_has_string_value() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool MsgSignal::has_string_value() const {
  return _internal_has_string_value();
}
inline void MsgSignal::clear_string_value() {
  string_value_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& MsgSignal::string_value() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignal.string_value)
  return _internal_string_value();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MsgSignal::set_string_value(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 string_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgSignal.string_value)
}
inline std::string* MsgSignal::mutable_string_value() {
  std::string* _s = _internal_mutable_string_value();
  // @@protoc_insertion_point(field_mutable:autolink.platform.vehicle.pb.MsgSignal.string_value)
  return _s;
}
inline const std::string& MsgSignal::_internal_string_value() const {
  return string_value_.Get();
}
inline void MsgSignal::_internal_set_string_value(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  string_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* MsgSignal::_internal_mutable_string_value() {
  _has_bits_[0] |= 0x00000001u;
  return string_value_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* MsgSignal::release_string_value() {
  // @@protoc_insertion_point(field_release:autolink.platform.vehicle.pb.MsgSignal.string_value)
  if (!_internal_has_string_value()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = string_value_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (string_value_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    string_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void MsgSignal::set_allocated_string_value(std::string* string_value) {
  if (string_value != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  string_value_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), string_value,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (string_value_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    string_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:autolink.platform.vehicle.pb.MsgSignal.string_value)
}

// optional bool bool_value = 4;
inline bool MsgSignal::_internal_has_bool_value() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool MsgSignal::has_bool_value() const {
  return _internal_has_bool_value();
}
inline void MsgSignal::clear_bool_value() {
  bool_value_ = false;
  _has_bits_[0] &= ~0x00000004u;
}
inline bool MsgSignal::_internal_bool_value() const {
  return bool_value_;
}
inline bool MsgSignal::bool_value() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignal.bool_value)
  return _internal_bool_value();
}
inline void MsgSignal::_internal_set_bool_value(bool value) {
  _has_bits_[0] |= 0x00000004u;
  bool_value_ = value;
}
inline void MsgSignal::set_bool_value(bool value) {
  _internal_set_bool_value(value);
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgSignal.bool_value)
}

// optional uint32 uint32_value = 5;
inline bool MsgSignal::_internal_has_uint32_value() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool MsgSignal::has_uint32_value() const {
  return _internal_has_uint32_value();
}
inline void MsgSignal::clear_uint32_value() {
  uint32_value_ = 0u;
  _has_bits_[0] &= ~0x00000008u;
}
inline uint32_t MsgSignal::_internal_uint32_value() const {
  return uint32_value_;
}
inline uint32_t MsgSignal::uint32_value() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignal.uint32_value)
  return _internal_uint32_value();
}
inline void MsgSignal::_internal_set_uint32_value(uint32_t value) {
  _has_bits_[0] |= 0x00000008u;
  uint32_value_ = value;
}
inline void MsgSignal::set_uint32_value(uint32_t value) {
  _internal_set_uint32_value(value);
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgSignal.uint32_value)
}

// optional sint32 sint32_value = 6;
inline bool MsgSignal::_internal_has_sint32_value() const {
  bool value = (_has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool MsgSignal::has_sint32_value() const {
  return _internal_has_sint32_value();
}
inline void MsgSignal::clear_sint32_value() {
  sint32_value_ = 0;
  _has_bits_[0] &= ~0x00000040u;
}
inline int32_t MsgSignal::_internal_sint32_value() const {
  return sint32_value_;
}
inline int32_t MsgSignal::sint32_value() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignal.sint32_value)
  return _internal_sint32_value();
}
inline void MsgSignal::_internal_set_sint32_value(int32_t value) {
  _has_bits_[0] |= 0x00000040u;
  sint32_value_ = value;
}
inline void MsgSignal::set_sint32_value(int32_t value) {
  _internal_set_sint32_value(value);
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgSignal.sint32_value)
}

// optional uint64 uint64_value = 7;
inline bool MsgSignal::_internal_has_uint64_value() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool MsgSignal::has_uint64_value() const {
  return _internal_has_uint64_value();
}
inline void MsgSignal::clear_uint64_value() {
  uint64_value_ = uint64_t{0u};
  _has_bits_[0] &= ~0x00000010u;
}
inline uint64_t MsgSignal::_internal_uint64_value() const {
  return uint64_value_;
}
inline uint64_t MsgSignal::uint64_value() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignal.uint64_value)
  return _internal_uint64_value();
}
inline void MsgSignal::_internal_set_uint64_value(uint64_t value) {
  _has_bits_[0] |= 0x00000010u;
  uint64_value_ = value;
}
inline void MsgSignal::set_uint64_value(uint64_t value) {
  _internal_set_uint64_value(value);
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgSignal.uint64_value)
}

// optional sint64 sint64_value = 8;
inline bool MsgSignal::_internal_has_sint64_value() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool MsgSignal::has_sint64_value() const {
  return _internal_has_sint64_value();
}
inline void MsgSignal::clear_sint64_value() {
  sint64_value_ = int64_t{0};
  _has_bits_[0] &= ~0x00000020u;
}
inline int64_t MsgSignal::_internal_sint64_value() const {
  return sint64_value_;
}
inline int64_t MsgSignal::sint64_value() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignal.sint64_value)
  return _internal_sint64_value();
}
inline void MsgSignal::_internal_set_sint64_value(int64_t value) {
  _has_bits_[0] |= 0x00000020u;
  sint64_value_ = value;
}
inline void MsgSignal::set_sint64_value(int64_t value) {
  _internal_set_sint64_value(value);
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgSignal.sint64_value)
}

// optional bytes bytes_value = 9;
inline bool MsgSignal::_internal_has_bytes_value() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool MsgSignal::has_bytes_value() const {
  return _internal_has_bytes_value();
}
inline void MsgSignal::clear_bytes_value() {
  bytes_value_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& MsgSignal::bytes_value() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignal.bytes_value)
  return _internal_bytes_value();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MsgSignal::set_bytes_value(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000002u;
 bytes_value_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgSignal.bytes_value)
}
inline std::string* MsgSignal::mutable_bytes_value() {
  std::string* _s = _internal_mutable_bytes_value();
  // @@protoc_insertion_point(field_mutable:autolink.platform.vehicle.pb.MsgSignal.bytes_value)
  return _s;
}
inline const std::string& MsgSignal::_internal_bytes_value() const {
  return bytes_value_.Get();
}
inline void MsgSignal::_internal_set_bytes_value(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  bytes_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* MsgSignal::_internal_mutable_bytes_value() {
  _has_bits_[0] |= 0x00000002u;
  return bytes_value_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* MsgSignal::release_bytes_value() {
  // @@protoc_insertion_point(field_release:autolink.platform.vehicle.pb.MsgSignal.bytes_value)
  if (!_internal_has_bytes_value()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  auto* p = bytes_value_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (bytes_value_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    bytes_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void MsgSignal::set_allocated_bytes_value(std::string* bytes_value) {
  if (bytes_value != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  bytes_value_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), bytes_value,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (bytes_value_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    bytes_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:autolink.platform.vehicle.pb.MsgSignal.bytes_value)
}

// -------------------------------------------------------------------

// MsgSignalSp

// required .autolink.platform.vehicle.pb.PduState state = 1;
inline bool MsgSignalSp::_internal_has_state() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool MsgSignalSp::has_state() const {
  return _internal_has_state();
}
inline void MsgSignalSp::clear_state() {
  state_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::autolink::platform::vehicle::pb::PduState MsgSignalSp::_internal_state() const {
  return static_cast< ::autolink::platform::vehicle::pb::PduState >(state_);
}
inline ::autolink::platform::vehicle::pb::PduState MsgSignalSp::state() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignalSp.state)
  return _internal_state();
}
inline void MsgSignalSp::_internal_set_state(::autolink::platform::vehicle::pb::PduState value) {
  // assert(::autolink::platform::vehicle::pb::PduState_IsValid(value));
  _has_bits_[0] |= 0x00000002u;
  state_ = value;
}
inline void MsgSignalSp::set_state(::autolink::platform::vehicle::pb::PduState value) {
  _internal_set_state(value);
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgSignalSp.state)
}

// required .autolink.platform.vehicle.pb.MsgSignal signal = 2;
inline bool MsgSignalSp::_internal_has_signal() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || signal_ != nullptr);
  return value;
}
inline bool MsgSignalSp::has_signal() const {
  return _internal_has_signal();
}
inline void MsgSignalSp::clear_signal() {
  if (signal_ != nullptr) signal_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::autolink::platform::vehicle::pb::MsgSignal& MsgSignalSp::_internal_signal() const {
  const ::autolink::platform::vehicle::pb::MsgSignal* p = signal_;
  return p != nullptr ? *p : reinterpret_cast<const ::autolink::platform::vehicle::pb::MsgSignal&>(
      ::autolink::platform::vehicle::pb::_MsgSignal_default_instance_);
}
inline const ::autolink::platform::vehicle::pb::MsgSignal& MsgSignalSp::signal() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgSignalSp.signal)
  return _internal_signal();
}
inline void MsgSignalSp::unsafe_arena_set_allocated_signal(
    ::autolink::platform::vehicle::pb::MsgSignal* signal) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(signal_);
  }
  signal_ = signal;
  if (signal) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:autolink.platform.vehicle.pb.MsgSignalSp.signal)
}
inline ::autolink::platform::vehicle::pb::MsgSignal* MsgSignalSp::release_signal() {
  _has_bits_[0] &= ~0x00000001u;
  ::autolink::platform::vehicle::pb::MsgSignal* temp = signal_;
  signal_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::autolink::platform::vehicle::pb::MsgSignal* MsgSignalSp::unsafe_arena_release_signal() {
  // @@protoc_insertion_point(field_release:autolink.platform.vehicle.pb.MsgSignalSp.signal)
  _has_bits_[0] &= ~0x00000001u;
  ::autolink::platform::vehicle::pb::MsgSignal* temp = signal_;
  signal_ = nullptr;
  return temp;
}
inline ::autolink::platform::vehicle::pb::MsgSignal* MsgSignalSp::_internal_mutable_signal() {
  _has_bits_[0] |= 0x00000001u;
  if (signal_ == nullptr) {
    auto* p = CreateMaybeMessage<::autolink::platform::vehicle::pb::MsgSignal>(GetArenaForAllocation());
    signal_ = p;
  }
  return signal_;
}
inline ::autolink::platform::vehicle::pb::MsgSignal* MsgSignalSp::mutable_signal() {
  ::autolink::platform::vehicle::pb::MsgSignal* _msg = _internal_mutable_signal();
  // @@protoc_insertion_point(field_mutable:autolink.platform.vehicle.pb.MsgSignalSp.signal)
  return _msg;
}
inline void MsgSignalSp::set_allocated_signal(::autolink::platform::vehicle::pb::MsgSignal* signal) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete signal_;
  }
  if (signal) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::autolink::platform::vehicle::pb::MsgSignal>::GetOwningArena(signal);
    if (message_arena != submessage_arena) {
      signal = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, signal, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  signal_ = signal;
  // @@protoc_insertion_point(field_set_allocated:autolink.platform.vehicle.pb.MsgSignalSp.signal)
}

// -------------------------------------------------------------------

// MsgPdu

// required .autolink.platform.vehicle.pb.PduId pdu_id = 1;
inline bool MsgPdu::_internal_has_pdu_id() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool MsgPdu::has_pdu_id() const {
  return _internal_has_pdu_id();
}
inline void MsgPdu::clear_pdu_id() {
  pdu_id_ = 0;
  _has_bits_[0] &= ~0x00000001u;
}
inline ::autolink::platform::vehicle::pb::PduId MsgPdu::_internal_pdu_id() const {
  return static_cast< ::autolink::platform::vehicle::pb::PduId >(pdu_id_);
}
inline ::autolink::platform::vehicle::pb::PduId MsgPdu::pdu_id() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgPdu.pdu_id)
  return _internal_pdu_id();
}
inline void MsgPdu::_internal_set_pdu_id(::autolink::platform::vehicle::pb::PduId value) {
  // assert(::autolink::platform::vehicle::pb::PduId_IsValid(value));
  _has_bits_[0] |= 0x00000001u;
  pdu_id_ = value;
}
inline void MsgPdu::set_pdu_id(::autolink::platform::vehicle::pb::PduId value) {
  _internal_set_pdu_id(value);
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgPdu.pdu_id)
}

// required .autolink.platform.vehicle.pb.PduState state = 2;
inline bool MsgPdu::_internal_has_state() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool MsgPdu::has_state() const {
  return _internal_has_state();
}
inline void MsgPdu::clear_state() {
  state_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::autolink::platform::vehicle::pb::PduState MsgPdu::_internal_state() const {
  return static_cast< ::autolink::platform::vehicle::pb::PduState >(state_);
}
inline ::autolink::platform::vehicle::pb::PduState MsgPdu::state() const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgPdu.state)
  return _internal_state();
}
inline void MsgPdu::_internal_set_state(::autolink::platform::vehicle::pb::PduState value) {
  // assert(::autolink::platform::vehicle::pb::PduState_IsValid(value));
  _has_bits_[0] |= 0x00000002u;
  state_ = value;
}
inline void MsgPdu::set_state(::autolink::platform::vehicle::pb::PduState value) {
  _internal_set_state(value);
  // @@protoc_insertion_point(field_set:autolink.platform.vehicle.pb.MsgPdu.state)
}

// repeated .autolink.platform.vehicle.pb.MsgSignal signals = 3;
inline int MsgPdu::_internal_signals_size() const {
  return signals_.size();
}
inline int MsgPdu::signals_size() const {
  return _internal_signals_size();
}
inline void MsgPdu::clear_signals() {
  signals_.Clear();
}
inline ::autolink::platform::vehicle::pb::MsgSignal* MsgPdu::mutable_signals(int index) {
  // @@protoc_insertion_point(field_mutable:autolink.platform.vehicle.pb.MsgPdu.signals)
  return signals_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgSignal >*
MsgPdu::mutable_signals() {
  // @@protoc_insertion_point(field_mutable_list:autolink.platform.vehicle.pb.MsgPdu.signals)
  return &signals_;
}
inline const ::autolink::platform::vehicle::pb::MsgSignal& MsgPdu::_internal_signals(int index) const {
  return signals_.Get(index);
}
inline const ::autolink::platform::vehicle::pb::MsgSignal& MsgPdu::signals(int index) const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgPdu.signals)
  return _internal_signals(index);
}
inline ::autolink::platform::vehicle::pb::MsgSignal* MsgPdu::_internal_add_signals() {
  return signals_.Add();
}
inline ::autolink::platform::vehicle::pb::MsgSignal* MsgPdu::add_signals() {
  ::autolink::platform::vehicle::pb::MsgSignal* _add = _internal_add_signals();
  // @@protoc_insertion_point(field_add:autolink.platform.vehicle.pb.MsgPdu.signals)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgSignal >&
MsgPdu::signals() const {
  // @@protoc_insertion_point(field_list:autolink.platform.vehicle.pb.MsgPdu.signals)
  return signals_;
}

// -------------------------------------------------------------------

// MsgList

// repeated .autolink.platform.vehicle.pb.MsgPdu pdus = 1;
inline int MsgList::_internal_pdus_size() const {
  return pdus_.size();
}
inline int MsgList::pdus_size() const {
  return _internal_pdus_size();
}
inline void MsgList::clear_pdus() {
  pdus_.Clear();
}
inline ::autolink::platform::vehicle::pb::MsgPdu* MsgList::mutable_pdus(int index) {
  // @@protoc_insertion_point(field_mutable:autolink.platform.vehicle.pb.MsgList.pdus)
  return pdus_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgPdu >*
MsgList::mutable_pdus() {
  // @@protoc_insertion_point(field_mutable_list:autolink.platform.vehicle.pb.MsgList.pdus)
  return &pdus_;
}
inline const ::autolink::platform::vehicle::pb::MsgPdu& MsgList::_internal_pdus(int index) const {
  return pdus_.Get(index);
}
inline const ::autolink::platform::vehicle::pb::MsgPdu& MsgList::pdus(int index) const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgList.pdus)
  return _internal_pdus(index);
}
inline ::autolink::platform::vehicle::pb::MsgPdu* MsgList::_internal_add_pdus() {
  return pdus_.Add();
}
inline ::autolink::platform::vehicle::pb::MsgPdu* MsgList::add_pdus() {
  ::autolink::platform::vehicle::pb::MsgPdu* _add = _internal_add_pdus();
  // @@protoc_insertion_point(field_add:autolink.platform.vehicle.pb.MsgList.pdus)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgPdu >&
MsgList::pdus() const {
  // @@protoc_insertion_point(field_list:autolink.platform.vehicle.pb.MsgList.pdus)
  return pdus_;
}

// repeated .autolink.platform.vehicle.pb.MsgSignalSp signals = 2;
inline int MsgList::_internal_signals_size() const {
  return signals_.size();
}
inline int MsgList::signals_size() const {
  return _internal_signals_size();
}
inline void MsgList::clear_signals() {
  signals_.Clear();
}
inline ::autolink::platform::vehicle::pb::MsgSignalSp* MsgList::mutable_signals(int index) {
  // @@protoc_insertion_point(field_mutable:autolink.platform.vehicle.pb.MsgList.signals)
  return signals_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgSignalSp >*
MsgList::mutable_signals() {
  // @@protoc_insertion_point(field_mutable_list:autolink.platform.vehicle.pb.MsgList.signals)
  return &signals_;
}
inline const ::autolink::platform::vehicle::pb::MsgSignalSp& MsgList::_internal_signals(int index) const {
  return signals_.Get(index);
}
inline const ::autolink::platform::vehicle::pb::MsgSignalSp& MsgList::signals(int index) const {
  // @@protoc_insertion_point(field_get:autolink.platform.vehicle.pb.MsgList.signals)
  return _internal_signals(index);
}
inline ::autolink::platform::vehicle::pb::MsgSignalSp* MsgList::_internal_add_signals() {
  return signals_.Add();
}
inline ::autolink::platform::vehicle::pb::MsgSignalSp* MsgList::add_signals() {
  ::autolink::platform::vehicle::pb::MsgSignalSp* _add = _internal_add_signals();
  // @@protoc_insertion_point(field_add:autolink.platform.vehicle.pb.MsgList.signals)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::platform::vehicle::pb::MsgSignalSp >&
MsgList::signals() const {
  // @@protoc_insertion_point(field_list:autolink.platform.vehicle.pb.MsgList.signals)
  return signals_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace pb
}  // namespace vehicle
}  // namespace platform
}  // namespace autolink

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::autolink::platform::vehicle::pb::PduState> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::autolink::platform::vehicle::pb::PduState>() {
  return ::autolink::platform::vehicle::pb::PduState_descriptor();
}
template <> struct is_proto_enum< ::autolink::platform::vehicle::pb::SignalValueType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::autolink::platform::vehicle::pb::SignalValueType>() {
  return ::autolink::platform::vehicle::pb::SignalValueType_descriptor();
}
template <> struct is_proto_enum< ::autolink::platform::vehicle::pb::PduId> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::autolink::platform::vehicle::pb::PduId>() {
  return ::autolink::platform::vehicle::pb::PduId_descriptor();
}
template <> struct is_proto_enum< ::autolink::platform::vehicle::pb::SignalId> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::autolink::platform::vehicle::pb::SignalId>() {
  return ::autolink::platform::vehicle::pb::SignalId_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_autolink_2eplatform_2evehicle_2eproto
