// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: autolink.platform.vehicle.proto

#include "autolink.platform.vehicle.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace autolink {
namespace platform {
namespace vehicle {
namespace pb {
constexpr MsgSignal::MsgSignal(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : string_value_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , bytes_value_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , bool_value_(false)
  , uint32_value_(0u)
  , uint64_value_(uint64_t{0u})
  , sint64_value_(int64_t{0})
  , sint32_value_(0)
  , signal_id_(4199)

  , value_type_(1)
{}
struct MsgSignalDefaultTypeInternal {
  constexpr MsgSignalDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MsgSignalDefaultTypeInternal() {}
  union {
    MsgSignal _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MsgSignalDefaultTypeInternal _MsgSignal_default_instance_;
constexpr MsgSignalSp::MsgSignalSp(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : signal_(nullptr)
  , state_(0)
{}
struct MsgSignalSpDefaultTypeInternal {
  constexpr MsgSignalSpDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MsgSignalSpDefaultTypeInternal() {}
  union {
    MsgSignalSp _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MsgSignalSpDefaultTypeInternal _MsgSignalSp_default_instance_;
constexpr MsgPdu::MsgPdu(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : signals_()
  , pdu_id_(0)

  , state_(0)
{}
struct MsgPduDefaultTypeInternal {
  constexpr MsgPduDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MsgPduDefaultTypeInternal() {}
  union {
    MsgPdu _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MsgPduDefaultTypeInternal _MsgPdu_default_instance_;
constexpr MsgList::MsgList(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : pdus_()
  , signals_(){}
struct MsgListDefaultTypeInternal {
  constexpr MsgListDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MsgListDefaultTypeInternal() {}
  union {
    MsgList _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MsgListDefaultTypeInternal _MsgList_default_instance_;
}  // namespace pb
}  // namespace vehicle
}  // namespace platform
}  // namespace autolink
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_autolink_2eplatform_2evehicle_2eproto[4];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_autolink_2eplatform_2evehicle_2eproto[4];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_autolink_2eplatform_2evehicle_2eproto = nullptr;

const uint32_t TableStruct_autolink_2eplatform_2evehicle_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, signal_id_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, value_type_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, string_value_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, bool_value_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, uint32_value_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, sint32_value_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, uint64_value_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, sint64_value_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignal, bytes_value_),
  7,
  8,
  0,
  2,
  3,
  6,
  4,
  5,
  1,
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignalSp, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignalSp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignalSp, state_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgSignalSp, signal_),
  1,
  0,
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgPdu, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgPdu, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgPdu, pdu_id_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgPdu, state_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgPdu, signals_),
  0,
  1,
  ~0u,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgList, pdus_),
  PROTOBUF_FIELD_OFFSET(::autolink::platform::vehicle::pb::MsgList, signals_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, 15, -1, sizeof(::autolink::platform::vehicle::pb::MsgSignal)},
  { 24, 32, -1, sizeof(::autolink::platform::vehicle::pb::MsgSignalSp)},
  { 34, 43, -1, sizeof(::autolink::platform::vehicle::pb::MsgPdu)},
  { 46, -1, -1, sizeof(::autolink::platform::vehicle::pb::MsgList)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::autolink::platform::vehicle::pb::_MsgSignal_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::autolink::platform::vehicle::pb::_MsgSignalSp_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::autolink::platform::vehicle::pb::_MsgPdu_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::autolink::platform::vehicle::pb::_MsgList_default_instance_),
};

const char descriptor_table_protodef_autolink_2eplatform_2evehicle_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\037autolink.platform.vehicle.proto\022\034autol"
  "ink.platform.vehicle.pb\"\240\002\n\tMsgSignal\0229\n"
  "\tsignal_id\030\001 \002(\0162&.autolink.platform.veh"
  "icle.pb.SignalId\022A\n\nvalue_type\030\002 \002(\0162-.a"
  "utolink.platform.vehicle.pb.SignalValueT"
  "ype\022\024\n\014string_value\030\003 \001(\t\022\022\n\nbool_value\030"
  "\004 \001(\010\022\024\n\014uint32_value\030\005 \001(\r\022\024\n\014sint32_va"
  "lue\030\006 \001(\021\022\024\n\014uint64_value\030\007 \001(\004\022\024\n\014sint6"
  "4_value\030\010 \001(\022\022\023\n\013bytes_value\030\t \001(\014\"}\n\013Ms"
  "gSignalSp\0225\n\005state\030\001 \002(\0162&.autolink.plat"
  "form.vehicle.pb.PduState\0227\n\006signal\030\002 \002(\013"
  "2\'.autolink.platform.vehicle.pb.MsgSigna"
  "l\"\256\001\n\006MsgPdu\0223\n\006pdu_id\030\001 \002(\0162#.autolink."
  "platform.vehicle.pb.PduId\0225\n\005state\030\002 \002(\016"
  "2&.autolink.platform.vehicle.pb.PduState"
  "\0228\n\007signals\030\003 \003(\0132\'.autolink.platform.ve"
  "hicle.pb.MsgSignal\"y\n\007MsgList\0222\n\004pdus\030\001 "
  "\003(\0132$.autolink.platform.vehicle.pb.MsgPd"
  "u\022:\n\007signals\030\002 \003(\0132).autolink.platform.v"
  "ehicle.pb.MsgSignalSp*1\n\010PduState\022\013\n\007DEF"
  "AULT\020\000\022\014\n\010INACTIVE\020\001\022\n\n\006ACTIVE\020\002*\276\001\n\017Sig"
  "nalValueType\022\013\n\007TYPE_U8\020\001\022\014\n\010TYPE_U16\020\002\022"
  "\017\n\013TYPE_UINT32\020\003\022\020\n\014TYPE_BOOLEAN\020\004\022\013\n\007TY"
  "PE_S8\020\005\022\014\n\010TYPE_S16\020\006\022\017\n\013TYPE_SINT32\020\007\022\017"
  "\n\013TYPE_UINT64\020\010\022\017\n\013TYPE_SINT64\020\t\022\017\n\013TYPE"
  "_STRING\020\n\022\016\n\nTYPE_BYTES\020\013*\027\n\005PduId\022\016\n\nPD"
  "U_ID_MAX\020\000*\264\010\n\010SignalId\022$\n\037SIGNAL_ICC_0x"
  "321_ICCExtrLampSwt\020\347 \022 \n\033SIGNAL_ICC_0x32"
  "1_ReFogLiReq\020\353 \022\'\n\"SIGNAL_ICC_0x377_ICCA"
  "cBlowrLelSet2\020\303#\022\'\n\"SIGNAL_ICC_0x377_ICC"
  "AcBlowrLelSet3\020\304#\022*\n%SIGNAL_ICC_0x377_IC"
  "CAcAirDistbnSet1_L\020\312#\022$\n\037SIGNAL_ICC_0x37"
  "7_ICCAcOnOffSwt1\020\315#\022 \n\033SIGNAL_ICC_0x377_"
  "ICCAcAcSwt\020\320#\022#\n\036SIGNAL_ICC_0x377_ICCAcA"
  "utoSwt1\020\321#\022$\n\037SIGNAL_ICC_0x377_ICCAcDefr"
  "stSwt\020\322#\022\'\n\"SIGNAL_ICC_0x377_ICCAcAirRec"
  "ircSwt\020\323#\022\'\n\"SIGNAL_ICC_0x377_ICCAcBlowe"
  "rLeSet1\020\327#\022\"\n\035SIGNAL_ICC_0x377_ICCAcTSet"
  "1_L\020\330#\022*\n%SIGNAL_ICC_0x377_ICCAcAirDistb"
  "nSet1_R\020\342#\022\"\n\035SIGNAL_ICC_0x377_ICCAcTSet"
  "1_R\020\343#\022%\n SIGNAL_ECC_0x271_AcOnOffStsDis"
  "p1\020\254&\022%\n SIGNAL_ECC_0x271_AcBlowrLelDisp"
  "1\020\257&\022%\n SIGNAL_ECC_0x271_AcBlowrLelDisp3"
  "\020\260&\022%\n SIGNAL_ECC_0x271_AcBlowrLelDisp5\020"
  "\261&\022 \n\033SIGNAL_ECC_0x271_AcLeTDisp1\020\262&\022 \n\033"
  "SIGNAL_ECC_0x271_AcRiTDisp1\020\263&\022(\n#SIGNAL"
  "_ECC_0x271_AcLeAirDistbnDisp1\020\266&\022(\n#SIGN"
  "AL_ECC_0x271_AcRiAirDistbnDisp1\020\267&\022\"\n\035SI"
  "GNAL_ECC_0x271_AcDefrstDisp\020\272&\022\"\n\035SIGNAL"
  "_ECC_0x271_AcRecircDisp\020\273&\022#\n\036SIGNAL_ECC"
  "_0x271_AcLeAutoDisp1\020\274&\022\036\n\031SIGNAL_ECC_0x"
  "271_AcAcDisp\020\277&\022$\n\037SIGNAL_VDC_0x2D2_BDCE"
  "xtrLampSts\020\224)\022\"\n\035SIGNAL_VDC_0x2D2_ReFogL"
  "ampSwt\020\233)"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_autolink_2eplatform_2evehicle_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_autolink_2eplatform_2evehicle_2eproto = {
  false, false, 2129, descriptor_table_protodef_autolink_2eplatform_2evehicle_2eproto, "autolink.platform.vehicle.proto", 
  &descriptor_table_autolink_2eplatform_2evehicle_2eproto_once, nullptr, 0, 4,
  schemas, file_default_instances, TableStruct_autolink_2eplatform_2evehicle_2eproto::offsets,
  file_level_metadata_autolink_2eplatform_2evehicle_2eproto, file_level_enum_descriptors_autolink_2eplatform_2evehicle_2eproto, file_level_service_descriptors_autolink_2eplatform_2evehicle_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_autolink_2eplatform_2evehicle_2eproto_getter() {
  return &descriptor_table_autolink_2eplatform_2evehicle_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_autolink_2eplatform_2evehicle_2eproto(&descriptor_table_autolink_2eplatform_2evehicle_2eproto);
namespace autolink {
namespace platform {
namespace vehicle {
namespace pb {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PduState_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_autolink_2eplatform_2evehicle_2eproto);
  return file_level_enum_descriptors_autolink_2eplatform_2evehicle_2eproto[0];
}
bool PduState_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SignalValueType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_autolink_2eplatform_2evehicle_2eproto);
  return file_level_enum_descriptors_autolink_2eplatform_2evehicle_2eproto[1];
}
bool SignalValueType_IsValid(int value) {
  switch (value) {
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PduId_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_autolink_2eplatform_2evehicle_2eproto);
  return file_level_enum_descriptors_autolink_2eplatform_2evehicle_2eproto[2];
}
bool PduId_IsValid(int value) {
  switch (value) {
    case 0:
      return true;
    default:
      return true;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SignalId_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_autolink_2eplatform_2evehicle_2eproto);
  return file_level_enum_descriptors_autolink_2eplatform_2evehicle_2eproto[3];
}
bool SignalId_IsValid(int value) {
  switch (value) {
    case 4199:
    case 4203:
    case 4547:
    case 4548:
    case 4554:
    case 4557:
    case 4560:
    case 4561:
    case 4562:
    case 4563:
    case 4567:
    case 4568:
    case 4578:
    case 4579:
    case 4908:
    case 4911:
    case 4912:
    case 4913:
    case 4914:
    case 4915:
    case 4918:
    case 4919:
    case 4922:
    case 4923:
    case 4924:
    case 4927:
    case 5268:
    case 5275:
      return true;
    default:
      return true;
  }
}


// ===================================================================

class MsgSignal::_Internal {
 public:
  using HasBits = decltype(std::declval<MsgSignal>()._has_bits_);
  static void set_has_signal_id(HasBits* has_bits) {
    (*has_bits)[0] |= 128u;
  }
  static void set_has_value_type(HasBits* has_bits) {
    (*has_bits)[0] |= 256u;
  }
  static void set_has_string_value(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_bool_value(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_uint32_value(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_sint32_value(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
  static void set_has_uint64_value(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_sint64_value(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static void set_has_bytes_value(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000180) ^ 0x00000180) != 0;
  }
};

MsgSignal::MsgSignal(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:autolink.platform.vehicle.pb.MsgSignal)
}
MsgSignal::MsgSignal(const MsgSignal& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  string_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    string_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_string_value()) {
    string_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_string_value(), 
      GetArenaForAllocation());
  }
  bytes_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    bytes_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_bytes_value()) {
    bytes_value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_bytes_value(), 
      GetArenaForAllocation());
  }
  ::memcpy(&bool_value_, &from.bool_value_,
    static_cast<size_t>(reinterpret_cast<char*>(&value_type_) -
    reinterpret_cast<char*>(&bool_value_)) + sizeof(value_type_));
  // @@protoc_insertion_point(copy_constructor:autolink.platform.vehicle.pb.MsgSignal)
}

inline void MsgSignal::SharedCtor() {
string_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  string_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
bytes_value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  bytes_value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&bool_value_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&sint32_value_) -
    reinterpret_cast<char*>(&bool_value_)) + sizeof(sint32_value_));
signal_id_ = 4199;
value_type_ = 1;
}

MsgSignal::~MsgSignal() {
  // @@protoc_insertion_point(destructor:autolink.platform.vehicle.pb.MsgSignal)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MsgSignal::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  string_value_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  bytes_value_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void MsgSignal::ArenaDtor(void* object) {
  MsgSignal* _this = reinterpret_cast< MsgSignal* >(object);
  (void)_this;
}
void MsgSignal::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MsgSignal::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MsgSignal::Clear() {
// @@protoc_insertion_point(message_clear_start:autolink.platform.vehicle.pb.MsgSignal)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      string_value_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      bytes_value_.ClearNonDefaultToEmpty();
    }
  }
  if (cached_has_bits & 0x000000fcu) {
    ::memset(&bool_value_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&sint32_value_) -
        reinterpret_cast<char*>(&bool_value_)) + sizeof(sint32_value_));
    signal_id_ = 4199;
  }
  value_type_ = 1;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MsgSignal::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required .autolink.platform.vehicle.pb.SignalId signal_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::autolink::platform::vehicle::pb::SignalId_IsValid(val))) {
            _internal_set_signal_id(static_cast<::autolink::platform::vehicle::pb::SignalId>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // required .autolink.platform.vehicle.pb.SignalValueType value_type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::autolink::platform::vehicle::pb::SignalValueType_IsValid(val))) {
            _internal_set_value_type(static_cast<::autolink::platform::vehicle::pb::SignalValueType>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(2, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // optional string string_value = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_string_value();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          #ifndef NDEBUG
          ::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "autolink.platform.vehicle.pb.MsgSignal.string_value");
          #endif  // !NDEBUG
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bool bool_value = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _Internal::set_has_bool_value(&has_bits);
          bool_value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional uint32 uint32_value = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          _Internal::set_has_uint32_value(&has_bits);
          uint32_value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional sint32 sint32_value = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          _Internal::set_has_sint32_value(&has_bits);
          sint32_value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarintZigZag32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional uint64 uint64_value = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          _Internal::set_has_uint64_value(&has_bits);
          uint64_value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional sint64 sint64_value = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          _Internal::set_has_sint64_value(&has_bits);
          sint64_value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarintZigZag64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bytes bytes_value = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          auto str = _internal_mutable_bytes_value();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MsgSignal::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:autolink.platform.vehicle.pb.MsgSignal)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .autolink.platform.vehicle.pb.SignalId signal_id = 1;
  if (cached_has_bits & 0x00000080u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_signal_id(), target);
  }

  // required .autolink.platform.vehicle.pb.SignalValueType value_type = 2;
  if (cached_has_bits & 0x00000100u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_value_type(), target);
  }

  // optional string string_value = 3;
  if (cached_has_bits & 0x00000001u) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::VerifyUTF8StringNamedField(
      this->_internal_string_value().data(), static_cast<int>(this->_internal_string_value().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::SERIALIZE,
      "autolink.platform.vehicle.pb.MsgSignal.string_value");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_string_value(), target);
  }

  // optional bool bool_value = 4;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_bool_value(), target);
  }

  // optional uint32 uint32_value = 5;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_uint32_value(), target);
  }

  // optional sint32 sint32_value = 6;
  if (cached_has_bits & 0x00000040u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteSInt32ToArray(6, this->_internal_sint32_value(), target);
  }

  // optional uint64 uint64_value = 7;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(7, this->_internal_uint64_value(), target);
  }

  // optional sint64 sint64_value = 8;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteSInt64ToArray(8, this->_internal_sint64_value(), target);
  }

  // optional bytes bytes_value = 9;
  if (cached_has_bits & 0x00000002u) {
    target = stream->WriteBytesMaybeAliased(
        9, this->_internal_bytes_value(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:autolink.platform.vehicle.pb.MsgSignal)
  return target;
}

size_t MsgSignal::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:autolink.platform.vehicle.pb.MsgSignal)
  size_t total_size = 0;

  if (_internal_has_signal_id()) {
    // required .autolink.platform.vehicle.pb.SignalId signal_id = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_signal_id());
  }

  if (_internal_has_value_type()) {
    // required .autolink.platform.vehicle.pb.SignalValueType value_type = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_value_type());
  }

  return total_size;
}
size_t MsgSignal::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:autolink.platform.vehicle.pb.MsgSignal)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000180) ^ 0x00000180) == 0) {  // All required fields are present.
    // required .autolink.platform.vehicle.pb.SignalId signal_id = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_signal_id());

    // required .autolink.platform.vehicle.pb.SignalValueType value_type = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_value_type());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000007fu) {
    // optional string string_value = 3;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_string_value());
    }

    // optional bytes bytes_value = 9;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
          this->_internal_bytes_value());
    }

    // optional bool bool_value = 4;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 + 1;
    }

    // optional uint32 uint32_value = 5;
    if (cached_has_bits & 0x00000008u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_uint32_value());
    }

    // optional uint64 uint64_value = 7;
    if (cached_has_bits & 0x00000010u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_uint64_value());
    }

    // optional sint64 sint64_value = 8;
    if (cached_has_bits & 0x00000020u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SInt64SizePlusOne(this->_internal_sint64_value());
    }

    // optional sint32 sint32_value = 6;
    if (cached_has_bits & 0x00000040u) {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SInt32SizePlusOne(this->_internal_sint32_value());
    }

  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MsgSignal::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MsgSignal::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MsgSignal::GetClassData() const { return &_class_data_; }

void MsgSignal::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MsgSignal *>(to)->MergeFrom(
      static_cast<const MsgSignal &>(from));
}


void MsgSignal::MergeFrom(const MsgSignal& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:autolink.platform.vehicle.pb.MsgSignal)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_string_value(from._internal_string_value());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_set_bytes_value(from._internal_bytes_value());
    }
    if (cached_has_bits & 0x00000004u) {
      bool_value_ = from.bool_value_;
    }
    if (cached_has_bits & 0x00000008u) {
      uint32_value_ = from.uint32_value_;
    }
    if (cached_has_bits & 0x00000010u) {
      uint64_value_ = from.uint64_value_;
    }
    if (cached_has_bits & 0x00000020u) {
      sint64_value_ = from.sint64_value_;
    }
    if (cached_has_bits & 0x00000040u) {
      sint32_value_ = from.sint32_value_;
    }
    if (cached_has_bits & 0x00000080u) {
      signal_id_ = from.signal_id_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  if (cached_has_bits & 0x00000100u) {
    _internal_set_value_type(from._internal_value_type());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MsgSignal::CopyFrom(const MsgSignal& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:autolink.platform.vehicle.pb.MsgSignal)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MsgSignal::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void MsgSignal::InternalSwap(MsgSignal* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &string_value_, lhs_arena,
      &other->string_value_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &bytes_value_, lhs_arena,
      &other->bytes_value_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MsgSignal, sint32_value_)
      + sizeof(MsgSignal::sint32_value_)
      - PROTOBUF_FIELD_OFFSET(MsgSignal, bool_value_)>(
          reinterpret_cast<char*>(&bool_value_),
          reinterpret_cast<char*>(&other->bool_value_));
  swap(signal_id_, other->signal_id_);
  swap(value_type_, other->value_type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata MsgSignal::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_autolink_2eplatform_2evehicle_2eproto_getter, &descriptor_table_autolink_2eplatform_2evehicle_2eproto_once,
      file_level_metadata_autolink_2eplatform_2evehicle_2eproto[0]);
}

// ===================================================================

class MsgSignalSp::_Internal {
 public:
  using HasBits = decltype(std::declval<MsgSignalSp>()._has_bits_);
  static void set_has_state(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static const ::autolink::platform::vehicle::pb::MsgSignal& signal(const MsgSignalSp* msg);
  static void set_has_signal(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000003) ^ 0x00000003) != 0;
  }
};

const ::autolink::platform::vehicle::pb::MsgSignal&
MsgSignalSp::_Internal::signal(const MsgSignalSp* msg) {
  return *msg->signal_;
}
MsgSignalSp::MsgSignalSp(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:autolink.platform.vehicle.pb.MsgSignalSp)
}
MsgSignalSp::MsgSignalSp(const MsgSignalSp& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_signal()) {
    signal_ = new ::autolink::platform::vehicle::pb::MsgSignal(*from.signal_);
  } else {
    signal_ = nullptr;
  }
  state_ = from.state_;
  // @@protoc_insertion_point(copy_constructor:autolink.platform.vehicle.pb.MsgSignalSp)
}

inline void MsgSignalSp::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&signal_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&state_) -
    reinterpret_cast<char*>(&signal_)) + sizeof(state_));
}

MsgSignalSp::~MsgSignalSp() {
  // @@protoc_insertion_point(destructor:autolink.platform.vehicle.pb.MsgSignalSp)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MsgSignalSp::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete signal_;
}

void MsgSignalSp::ArenaDtor(void* object) {
  MsgSignalSp* _this = reinterpret_cast< MsgSignalSp* >(object);
  (void)_this;
}
void MsgSignalSp::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MsgSignalSp::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MsgSignalSp::Clear() {
// @@protoc_insertion_point(message_clear_start:autolink.platform.vehicle.pb.MsgSignalSp)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(signal_ != nullptr);
    signal_->Clear();
  }
  state_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MsgSignalSp::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required .autolink.platform.vehicle.pb.PduState state = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::autolink::platform::vehicle::pb::PduState_IsValid(val))) {
            _internal_set_state(static_cast<::autolink::platform::vehicle::pb::PduState>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // required .autolink.platform.vehicle.pb.MsgSignal signal = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_signal(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MsgSignalSp::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:autolink.platform.vehicle.pb.MsgSignalSp)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .autolink.platform.vehicle.pb.PduState state = 1;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_state(), target);
  }

  // required .autolink.platform.vehicle.pb.MsgSignal signal = 2;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::signal(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:autolink.platform.vehicle.pb.MsgSignalSp)
  return target;
}

size_t MsgSignalSp::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:autolink.platform.vehicle.pb.MsgSignalSp)
  size_t total_size = 0;

  if (_internal_has_signal()) {
    // required .autolink.platform.vehicle.pb.MsgSignal signal = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *signal_);
  }

  if (_internal_has_state()) {
    // required .autolink.platform.vehicle.pb.PduState state = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());
  }

  return total_size;
}
size_t MsgSignalSp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:autolink.platform.vehicle.pb.MsgSignalSp)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000003) ^ 0x00000003) == 0) {  // All required fields are present.
    // required .autolink.platform.vehicle.pb.MsgSignal signal = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *signal_);

    // required .autolink.platform.vehicle.pb.PduState state = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MsgSignalSp::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MsgSignalSp::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MsgSignalSp::GetClassData() const { return &_class_data_; }

void MsgSignalSp::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MsgSignalSp *>(to)->MergeFrom(
      static_cast<const MsgSignalSp &>(from));
}


void MsgSignalSp::MergeFrom(const MsgSignalSp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:autolink.platform.vehicle.pb.MsgSignalSp)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_mutable_signal()->::autolink::platform::vehicle::pb::MsgSignal::MergeFrom(from._internal_signal());
    }
    if (cached_has_bits & 0x00000002u) {
      state_ = from.state_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MsgSignalSp::CopyFrom(const MsgSignalSp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:autolink.platform.vehicle.pb.MsgSignalSp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MsgSignalSp::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (_internal_has_signal()) {
    if (!signal_->IsInitialized()) return false;
  }
  return true;
}

void MsgSignalSp::InternalSwap(MsgSignalSp* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MsgSignalSp, state_)
      + sizeof(MsgSignalSp::state_)
      - PROTOBUF_FIELD_OFFSET(MsgSignalSp, signal_)>(
          reinterpret_cast<char*>(&signal_),
          reinterpret_cast<char*>(&other->signal_));
}

::PROTOBUF_NAMESPACE_ID::Metadata MsgSignalSp::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_autolink_2eplatform_2evehicle_2eproto_getter, &descriptor_table_autolink_2eplatform_2evehicle_2eproto_once,
      file_level_metadata_autolink_2eplatform_2evehicle_2eproto[1]);
}

// ===================================================================

class MsgPdu::_Internal {
 public:
  using HasBits = decltype(std::declval<MsgPdu>()._has_bits_);
  static void set_has_pdu_id(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_state(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000003) ^ 0x00000003) != 0;
  }
};

MsgPdu::MsgPdu(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  signals_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:autolink.platform.vehicle.pb.MsgPdu)
}
MsgPdu::MsgPdu(const MsgPdu& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_),
      signals_(from.signals_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&pdu_id_, &from.pdu_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&state_) -
    reinterpret_cast<char*>(&pdu_id_)) + sizeof(state_));
  // @@protoc_insertion_point(copy_constructor:autolink.platform.vehicle.pb.MsgPdu)
}

inline void MsgPdu::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&pdu_id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&state_) -
    reinterpret_cast<char*>(&pdu_id_)) + sizeof(state_));
}

MsgPdu::~MsgPdu() {
  // @@protoc_insertion_point(destructor:autolink.platform.vehicle.pb.MsgPdu)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MsgPdu::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void MsgPdu::ArenaDtor(void* object) {
  MsgPdu* _this = reinterpret_cast< MsgPdu* >(object);
  (void)_this;
}
void MsgPdu::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MsgPdu::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MsgPdu::Clear() {
// @@protoc_insertion_point(message_clear_start:autolink.platform.vehicle.pb.MsgPdu)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  signals_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    ::memset(&pdu_id_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&state_) -
        reinterpret_cast<char*>(&pdu_id_)) + sizeof(state_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MsgPdu::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required .autolink.platform.vehicle.pb.PduId pdu_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::autolink::platform::vehicle::pb::PduId_IsValid(val))) {
            _internal_set_pdu_id(static_cast<::autolink::platform::vehicle::pb::PduId>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // required .autolink.platform.vehicle.pb.PduState state = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::autolink::platform::vehicle::pb::PduState_IsValid(val))) {
            _internal_set_state(static_cast<::autolink::platform::vehicle::pb::PduState>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(2, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // repeated .autolink.platform.vehicle.pb.MsgSignal signals = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_signals(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MsgPdu::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:autolink.platform.vehicle.pb.MsgPdu)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .autolink.platform.vehicle.pb.PduId pdu_id = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_pdu_id(), target);
  }

  // required .autolink.platform.vehicle.pb.PduState state = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_state(), target);
  }

  // repeated .autolink.platform.vehicle.pb.MsgSignal signals = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_signals_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, this->_internal_signals(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:autolink.platform.vehicle.pb.MsgPdu)
  return target;
}

size_t MsgPdu::RequiredFieldsByteSizeFallback() const {
// @@protoc_insertion_point(required_fields_byte_size_fallback_start:autolink.platform.vehicle.pb.MsgPdu)
  size_t total_size = 0;

  if (_internal_has_pdu_id()) {
    // required .autolink.platform.vehicle.pb.PduId pdu_id = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_pdu_id());
  }

  if (_internal_has_state()) {
    // required .autolink.platform.vehicle.pb.PduState state = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());
  }

  return total_size;
}
size_t MsgPdu::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:autolink.platform.vehicle.pb.MsgPdu)
  size_t total_size = 0;

  if (((_has_bits_[0] & 0x00000003) ^ 0x00000003) == 0) {  // All required fields are present.
    // required .autolink.platform.vehicle.pb.PduId pdu_id = 1;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_pdu_id());

    // required .autolink.platform.vehicle.pb.PduState state = 2;
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());

  } else {
    total_size += RequiredFieldsByteSizeFallback();
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .autolink.platform.vehicle.pb.MsgSignal signals = 3;
  total_size += 1UL * this->_internal_signals_size();
  for (const auto& msg : this->signals_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MsgPdu::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MsgPdu::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MsgPdu::GetClassData() const { return &_class_data_; }

void MsgPdu::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MsgPdu *>(to)->MergeFrom(
      static_cast<const MsgPdu &>(from));
}


void MsgPdu::MergeFrom(const MsgPdu& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:autolink.platform.vehicle.pb.MsgPdu)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  signals_.MergeFrom(from.signals_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      pdu_id_ = from.pdu_id_;
    }
    if (cached_has_bits & 0x00000002u) {
      state_ = from.state_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MsgPdu::CopyFrom(const MsgPdu& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:autolink.platform.vehicle.pb.MsgPdu)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MsgPdu::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  if (!::PROTOBUF_NAMESPACE_ID::internal::AllAreInitialized(signals_))
    return false;
  return true;
}

void MsgPdu::InternalSwap(MsgPdu* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  signals_.InternalSwap(&other->signals_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MsgPdu, state_)
      + sizeof(MsgPdu::state_)
      - PROTOBUF_FIELD_OFFSET(MsgPdu, pdu_id_)>(
          reinterpret_cast<char*>(&pdu_id_),
          reinterpret_cast<char*>(&other->pdu_id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata MsgPdu::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_autolink_2eplatform_2evehicle_2eproto_getter, &descriptor_table_autolink_2eplatform_2evehicle_2eproto_once,
      file_level_metadata_autolink_2eplatform_2evehicle_2eproto[2]);
}

// ===================================================================

class MsgList::_Internal {
 public:
};

MsgList::MsgList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  pdus_(arena),
  signals_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:autolink.platform.vehicle.pb.MsgList)
}
MsgList::MsgList(const MsgList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      pdus_(from.pdus_),
      signals_(from.signals_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:autolink.platform.vehicle.pb.MsgList)
}

inline void MsgList::SharedCtor() {
}

MsgList::~MsgList() {
  // @@protoc_insertion_point(destructor:autolink.platform.vehicle.pb.MsgList)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MsgList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void MsgList::ArenaDtor(void* object) {
  MsgList* _this = reinterpret_cast< MsgList* >(object);
  (void)_this;
}
void MsgList::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MsgList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MsgList::Clear() {
// @@protoc_insertion_point(message_clear_start:autolink.platform.vehicle.pb.MsgList)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  pdus_.Clear();
  signals_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MsgList::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .autolink.platform.vehicle.pb.MsgPdu pdus = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_pdus(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .autolink.platform.vehicle.pb.MsgSignalSp signals = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_signals(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MsgList::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:autolink.platform.vehicle.pb.MsgList)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .autolink.platform.vehicle.pb.MsgPdu pdus = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_pdus_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_pdus(i), target, stream);
  }

  // repeated .autolink.platform.vehicle.pb.MsgSignalSp signals = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_signals_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_signals(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:autolink.platform.vehicle.pb.MsgList)
  return target;
}

size_t MsgList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:autolink.platform.vehicle.pb.MsgList)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .autolink.platform.vehicle.pb.MsgPdu pdus = 1;
  total_size += 1UL * this->_internal_pdus_size();
  for (const auto& msg : this->pdus_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .autolink.platform.vehicle.pb.MsgSignalSp signals = 2;
  total_size += 1UL * this->_internal_signals_size();
  for (const auto& msg : this->signals_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MsgList::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MsgList::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MsgList::GetClassData() const { return &_class_data_; }

void MsgList::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MsgList *>(to)->MergeFrom(
      static_cast<const MsgList &>(from));
}


void MsgList::MergeFrom(const MsgList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:autolink.platform.vehicle.pb.MsgList)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  pdus_.MergeFrom(from.pdus_);
  signals_.MergeFrom(from.signals_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MsgList::CopyFrom(const MsgList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:autolink.platform.vehicle.pb.MsgList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MsgList::IsInitialized() const {
  if (!::PROTOBUF_NAMESPACE_ID::internal::AllAreInitialized(pdus_))
    return false;
  if (!::PROTOBUF_NAMESPACE_ID::internal::AllAreInitialized(signals_))
    return false;
  return true;
}

void MsgList::InternalSwap(MsgList* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  pdus_.InternalSwap(&other->pdus_);
  signals_.InternalSwap(&other->signals_);
}

::PROTOBUF_NAMESPACE_ID::Metadata MsgList::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_autolink_2eplatform_2evehicle_2eproto_getter, &descriptor_table_autolink_2eplatform_2evehicle_2eproto_once,
      file_level_metadata_autolink_2eplatform_2evehicle_2eproto[3]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace pb
}  // namespace vehicle
}  // namespace platform
}  // namespace autolink
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::autolink::platform::vehicle::pb::MsgSignal* Arena::CreateMaybeMessage< ::autolink::platform::vehicle::pb::MsgSignal >(Arena* arena) {
  return Arena::CreateMessageInternal< ::autolink::platform::vehicle::pb::MsgSignal >(arena);
}
template<> PROTOBUF_NOINLINE ::autolink::platform::vehicle::pb::MsgSignalSp* Arena::CreateMaybeMessage< ::autolink::platform::vehicle::pb::MsgSignalSp >(Arena* arena) {
  return Arena::CreateMessageInternal< ::autolink::platform::vehicle::pb::MsgSignalSp >(arena);
}
template<> PROTOBUF_NOINLINE ::autolink::platform::vehicle::pb::MsgPdu* Arena::CreateMaybeMessage< ::autolink::platform::vehicle::pb::MsgPdu >(Arena* arena) {
  return Arena::CreateMessageInternal< ::autolink::platform::vehicle::pb::MsgPdu >(arena);
}
template<> PROTOBUF_NOINLINE ::autolink::platform::vehicle::pb::MsgList* Arena::CreateMaybeMessage< ::autolink::platform::vehicle::pb::MsgList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::autolink::platform::vehicle::pb::MsgList >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
