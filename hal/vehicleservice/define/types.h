/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef _VI_CLIENT_TYPES_H_
#define _VI_CLIENT_TYPES_H_

#include <stdint.h>

enum class EViOperation : int32_t {
    GET         = 0x01000000,
    SET         = 0x02000000,
    GETCONFIG   = 0x03000000,
    SUBSCRIBE   = 0x04000000,
    UNSUBSCRIBE = 0x05000000,
    GET_ALL     = 0x06000000,
    MASK        = 0x0f000000,
};
enum class EViMessageId : int32_t {
    INVALID      = 0x0000,
    OS_QNX        = 0x0001,
    OS_ANDROID    = 0x0002,
    OS_ALL        = 0x0003,
    MASK         = 0x0000FFFF,
};

enum class EViAreaId : int32_t {
    GLOBAL                          = 0x0000,
    SEAT_ROW_1_LEFT                 = 0x0001,
    SEAT_ROW_1_CENTER               = 0x0002,
    SEAT_ROW_1_RIGHT                = 0x0004,
    SEAT_ROW_2_LEFT                 = 0x0010,
    SEAT_ROW_2_CENTER               = 0x0020,
    SEAT_ROW_2_RIGHT                = 0x0040,
    SEAT_ROW_3_LEFT                 = 0x0100,
    SEAT_ROW_3_CENTER               = 0x0200,
    SEAT_ROW_3_RIGHT                = 0x0400,
    WINDOW_FRONT_WINDSHIELD         = 0x00000001,
    WINDOW_REAR_WINDSHIELD          = 0x00000002,
    WINDOW_ROW_1_LEFT               = 0x00000010,
    WINDOW_ROW_1_RIGHT              = 0x00000040,
    WINDOW_ROW_2_LEFT               = 0x00000100,
    WINDOW_ROW_2_RIGHT              = 0x00000400,
    WINDOW_ROW_3_LEFT               = 0x00001000,
    WINDOW_ROW_3_RIGHT              = 0x00004000,
    WINDOW_ROOF_TOP_1               = 0x00010000,
    WINDOW_ROOF_TOP_2               = 0x00020000,
    DOOR_ROW_1_LEFT                 = 0x00000001,
    DOOR_ROW_1_RIGHT                = 0x00000004,
    DOOR_ROW_2_LEFT                 = 0x00000010,
    DOOR_ROW_2_RIGHT                = 0x00000040,
    DOOR_ROW_3_LEFT                 = 0x00000100,
    DOOR_ROW_3_RIGHT                = 0x00000400,
    DOOR_HOOD                       = 0x10000000,
    DOOR_REAR                       = 0x20000000,
    MIRROR_DRIVER_LEFT              = 0x0001,
    MIRROR_DRIVER_RIGHT             = 0x0002,
    MIRROR_DRIVER_CENTER            = 0x0004,
    BODY_FRONT                      = 0x0001,
    BODY_REAR                       = 0x0002,
    BODY_LEFT                       = 0x0004,
    BODY_RIGHT                      = 0x0008,
    BODY_FRONT_LEFT                 = 0x0010,
    BODY_FRONT_MIDDLE_LEFT          = 0x0020,
    BODY_FRONT_MIDDLE_RIGHT         = 0x0040,
    BODY_FRONT_RIGHT                = 0x0080,
    BODY_REAR_LEFT                  = 0x0100,
    BODY_REAR_MIDDLE_LEFT           = 0x0200,
    BODY_REAR_MIDDLE_RIGHT          = 0x0400,
    BODY_REAR_RIGHT                 = 0x0800,
};

template<typename ENUM>
inline constexpr int32_t toInt(ENUM const value) {
     return static_cast<int32_t>(value);
}

#endif //_VI_CLIENT_TYPES_H_
