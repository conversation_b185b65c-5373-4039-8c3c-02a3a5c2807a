{"propConfig": [{"propId": 4096, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4097, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4098, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3000}], "osTarget": 3}, {"propId": 4099, "areaType": 1, "valueType": 3, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 2147483647}], "osTarget": 3}, {"propId": 4102, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 4363, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 4000}], "osTarget": 3}, {"propId": 4367, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4369, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4370, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4371, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 4372, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 4374, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4375, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4377, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4378, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4379, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 4380, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4381, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4382, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4383, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4384, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4385, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4386, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4387, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4388, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4390, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4391, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4393, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4394, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4396, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4397, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4399, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4400, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4402, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4403, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4405, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4406, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4408, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4409, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4411, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4412, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4419, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4423, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4426, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4427, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4428, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4434, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4436, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4437, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4438, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4441, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4443, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4444, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4445, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4453, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4454, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4455, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4456, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4478, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4480, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4481, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4482, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4483, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4484, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4485, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4486, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4487, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4489, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4494, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4495, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4496, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4497, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4498, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4499, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4501, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 4502, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4503, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4504, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4506, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4507, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 2147483647}], "osTarget": 3}, {"propId": 4508, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4509, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4510, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4511, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4522, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 31}], "osTarget": 3}, {"propId": 4524, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 31}], "osTarget": 3}, {"propId": 4526, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 31}], "osTarget": 3}, {"propId": 4528, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 31}], "osTarget": 3}, {"propId": 4530, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 31}], "osTarget": 3}, {"propId": 4536, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4537, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4538, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4539, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4542, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 63}], "osTarget": 3}, {"propId": 4543, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4545, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4546, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4547, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 63}], "osTarget": 3}, {"propId": 4550, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 63}], "osTarget": 3}, {"propId": 4552, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 63}], "osTarget": 3}, {"propId": 4558, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4560, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4561, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4562, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4563, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4567, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4569, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4575, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4576, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4577, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4578, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4579, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4581, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4582, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4583, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4584, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4587, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4591, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4593, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4594, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4595, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4598, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4602, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4603, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4606, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4607, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4609, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4610, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4611, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4612, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4614, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4617, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4619, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4620, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4621, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4624, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 11}], "osTarget": 3}, {"propId": 4625, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4626, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4627, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 11}], "osTarget": 3}, {"propId": 4630, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4635, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4636, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4637, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4640, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 4641, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 4643, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4644, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4645, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4646, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4647, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4648, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4649, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4650, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4652, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4656, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4657, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4659, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4660, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4663, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4664, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4665, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4666, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4667, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4669, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4670, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4671, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4672, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4673, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 4}], "osTarget": 3}, {"propId": 4674, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4675, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 4}], "osTarget": 3}, {"propId": 4683, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4692, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4693, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4695, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4697, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4699, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4700, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4702, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4703, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4709, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4712, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4713, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4714, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4715, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4734, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 4735, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4736, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4739, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4740, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4742, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4747, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 800}], "osTarget": 3}, {"propId": 4748, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 127}], "osTarget": 3}, {"propId": 4753, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 4754, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 4755, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 4772, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4773, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4780, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4783, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4785, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4786, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4789, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4790, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4792, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4794, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4808, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 4811, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 4829, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4830, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4833, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4839, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 4840, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4841, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4849, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4851, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4852, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4853, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4856, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4857, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4858, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4859, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4860, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4861, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4870, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4871, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4872, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4873, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4874, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4875, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4878, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4879, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4880, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4883, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 4884, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4888, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4889, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4890, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4894, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4900, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4901, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4903, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4904, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4905, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4907, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4908, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4911, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4912, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4913, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4914, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4915, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4916, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 63}], "osTarget": 3}, {"propId": 4918, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 4921, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4922, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4923, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4924, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4925, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4926, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4927, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4928, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4929, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4930, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4931, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4932, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4933, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4934, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4935, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4936, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4937, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4938, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4939, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4940, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4941, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4942, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4943, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4944, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4945, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 4946, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4947, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4948, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4949, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4952, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4953, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4954, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4957, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4958, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4960, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 4961, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 4962, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 4963, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 4968, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 4969, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 4970, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 4972, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4973, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4975, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4976, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4977, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4978, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4979, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4981, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4982, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4983, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4984, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4986, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4987, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4988, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4990, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 4991, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 4}], "osTarget": 3}, {"propId": 4993, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 4994, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 4995, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 4996, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 4998, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 4999, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5001, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5002, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5006, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5008, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5027, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5028, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5035, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5045, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5058, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 2}], "osTarget": 3}, {"propId": 5071, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5072, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 5073, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 200000000}], "osTarget": 3}, {"propId": 5074, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5075, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5082, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5083, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5084, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5085, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5086, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5087, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5088, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5089, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5096, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5098, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5099, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5104, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1024}], "osTarget": 3}, {"propId": 5105, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 10240}], "osTarget": 3}, {"propId": 5106, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5108, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 5109, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5110, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5160, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5163, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5164, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 5170, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5171, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5172, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 5173, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 5174, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 5179, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 127}], "osTarget": 3}, {"propId": 5180, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5181, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 5182, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 5183, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 5224, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1000}], "osTarget": 3}, {"propId": 5225, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 2047}], "osTarget": 3}, {"propId": 5230, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1023}], "osTarget": 3}, {"propId": 5231, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 5264, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 5265, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 5270, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5271, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5272, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5273, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 5275, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5278, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5279, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5280, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5282, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5283, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5284, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5285, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5286, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 5287, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5289, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5290, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5291, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5292, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5293, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5294, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5343, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5344, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5348, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5349, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 200000000}], "osTarget": 3}, {"propId": 5350, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5351, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5352, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 5}], "osTarget": 3}, {"propId": 5353, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5417, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 23}], "osTarget": 3}, {"propId": 5418, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 59}], "osTarget": 3}, {"propId": 5419, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5420, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5427, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 23}], "osTarget": 3}, {"propId": 5428, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 59}], "osTarget": 3}, {"propId": 5429, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5430, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5443, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5444, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5446, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5447, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5448, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5449, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 5451, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 5453, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5454, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 999}], "osTarget": 3}, {"propId": 5463, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5464, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 8}], "osTarget": 3}, {"propId": 5465, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 5466, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5467, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5468, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 250}], "osTarget": 3}, {"propId": 5469, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 250}], "osTarget": 3}, {"propId": 5470, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5476, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 5477, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5478, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 20000}], "osTarget": 3}, {"propId": 5479, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5480, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5481, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 5484, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5485, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5486, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5487, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5488, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5489, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 5492, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5496, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 5497, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5498, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5501, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5502, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5504, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5506, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5510, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5511, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5518, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5522, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5528, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5529, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5532, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5557, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 5559, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 5584, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5587, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5667, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5668, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5669, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5670, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5674, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5675, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5676, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5677, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5683, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 31}], "osTarget": 3}, {"propId": 5684, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5688, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5689, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 5690, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5691, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5692, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5693, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5694, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5695, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5696, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5697, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5698, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5699, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5700, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5701, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5702, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5703, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5704, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5705, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5706, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5707, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5708, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5709, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5710, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 200000000}], "osTarget": 3}, {"propId": 5711, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5726, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5734, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5743, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5749, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5750, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5751, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5753, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5754, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5773, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5777, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5778, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5779, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5802, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5805, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 5806, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 5807, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5808, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5809, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5810, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5818, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5819, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5825, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5828, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5830, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5833, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5835, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5840, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5854, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5858, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5887, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5892, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 23}], "osTarget": 3}, {"propId": 5894, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 59}], "osTarget": 3}, {"propId": 5895, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5902, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 23}], "osTarget": 3}, {"propId": 5903, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 59}], "osTarget": 3}, {"propId": 5904, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5905, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5912, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5913, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5914, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 5915, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 31}], "osTarget": 3}, {"propId": 5916, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5917, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5918, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5919, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 30}], "osTarget": 3}, {"propId": 5920, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 23}], "osTarget": 3}, {"propId": 5921, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 59}], "osTarget": 3}, {"propId": 5922, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 5923, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 11}], "osTarget": 3}, {"propId": 5924, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 59}], "osTarget": 3}, {"propId": 5957, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5966, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5968, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5969, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 5970, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5981, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5982, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 101}], "osTarget": 3}, {"propId": 5983, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 5984, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 5985, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5986, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5987, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5988, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 5989, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 5990, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5991, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 5992, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 101}], "osTarget": 3}, {"propId": 5994, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6001, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 31}], "osTarget": 3}, {"propId": 6004, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 31}], "osTarget": 3}, {"propId": 6006, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6007, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 31}], "osTarget": 3}, {"propId": 6009, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 31}], "osTarget": 3}, {"propId": 6011, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 31}], "osTarget": 3}, {"propId": 6013, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 63}], "osTarget": 3}, {"propId": 6015, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 63}], "osTarget": 3}, {"propId": 6016, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 63}], "osTarget": 3}, {"propId": 6024, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 4}], "osTarget": 3}, {"propId": 6036, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 4}], "osTarget": 3}, {"propId": 6037, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6038, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6039, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6043, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6044, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 6045, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6046, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6047, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6048, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6049, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6050, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6051, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6052, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6053, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6054, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6056, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6058, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6059, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6061, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6062, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6063, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6064, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6065, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6066, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6067, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6068, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 6069, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 6070, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 6071, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6072, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6080, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 6082, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 6084, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6085, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6086, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6087, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6091, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 6092, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 6093, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6094, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6095, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 6097, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6098, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6099, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6100, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6103, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6105, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 6106, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 4}], "osTarget": 3}, {"propId": 6116, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6118, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6119, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 6120, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 31}], "osTarget": 3}, {"propId": 6121, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 6122, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6123, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6124, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 6125, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 6126, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 6127, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 6128, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 6129, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 6130, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 6131, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 6132, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6133, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6134, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6135, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6136, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6137, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 6138, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6141, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 6143, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6144, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 254}], "osTarget": 3}, {"propId": 6145, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 254}], "osTarget": 3}, {"propId": 6146, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 254}], "osTarget": 3}, {"propId": 6147, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 254}], "osTarget": 3}, {"propId": 6148, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 254}], "osTarget": 3}, {"propId": 6149, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 6150, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6152, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 101}], "osTarget": 3}, {"propId": 6153, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6154, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 101}], "osTarget": 3}, {"propId": 6155, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6156, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 6157, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 800}], "osTarget": 3}, {"propId": 6158, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6160, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 4}], "osTarget": 3}, {"propId": 6161, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6162, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6163, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6167, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6168, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 26}], "osTarget": 3}, {"propId": 6170, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6171, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6212, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6213, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6214, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 6215, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6216, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6217, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6218, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6219, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6220, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6221, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 6222, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 6223, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6224, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6225, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6226, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6227, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6228, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 6229, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 6230, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6231, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6244, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6245, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6250, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6251, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6252, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6254, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6255, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 6256, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 6257, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 6258, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 6259, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 6260, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 6261, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6262, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6263, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6267, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6273, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6274, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6275, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6277, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 6280, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6281, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6293, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6302, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 6310, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6311, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6313, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6314, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6315, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6316, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6317, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6319, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6320, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6321, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6322, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6323, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 6324, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6325, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6326, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6327, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6328, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6329, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6335, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 6377, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6507, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6509, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6510, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6511, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6512, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6513, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6516, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6517, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6518, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6519, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6521, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6523, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6527, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6528, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6529, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6530, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6531, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6532, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6533, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6534, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6535, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6536, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6537, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6538, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6539, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6540, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6541, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6542, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6543, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6544, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6545, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6546, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6547, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6548, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6549, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6550, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6551, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6552, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6553, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6554, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6555, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6556, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6557, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6558, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6559, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6560, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6561, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6562, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6563, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6564, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6565, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6566, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6567, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6568, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6569, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6570, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6571, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6572, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6573, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6574, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6575, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6576, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6577, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6578, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6579, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6580, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6581, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6582, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 6583, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6584, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6585, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6586, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6587, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6588, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6589, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6590, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6591, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 5000}], "osTarget": 3}, {"propId": 6592, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6618, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6619, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6620, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6621, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 6622, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 127}], "osTarget": 3}, {"propId": 6623, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6761, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 100}], "osTarget": 3}, {"propId": 6763, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6764, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 10000}], "osTarget": 3}, {"propId": 6769, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6870, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6946, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6947, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6948, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6950, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6951, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6952, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6953, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6954, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1439}], "osTarget": 3}, {"propId": 6955, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 31}], "osTarget": 3}, {"propId": 6956, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1439}], "osTarget": 3}, {"propId": 6957, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1439}], "osTarget": 3}, {"propId": 6958, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1439}], "osTarget": 3}, {"propId": 6959, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6963, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 6966, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6967, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6968, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1439}], "osTarget": 3}, {"propId": 6969, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 31}], "osTarget": 3}, {"propId": 6970, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1439}], "osTarget": 3}, {"propId": 6971, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1439}], "osTarget": 3}, {"propId": 6972, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1439}], "osTarget": 3}, {"propId": 6973, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6977, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 6978, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6981, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1439}], "osTarget": 3}, {"propId": 6982, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 31}], "osTarget": 3}, {"propId": 6983, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1439}], "osTarget": 3}, {"propId": 6984, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1439}], "osTarget": 3}, {"propId": 6985, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1439}], "osTarget": 3}, {"propId": 6986, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 6987, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6988, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6989, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6990, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 2000}], "osTarget": 3}, {"propId": 6991, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6992, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 6993, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 6994, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 6995, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 6996, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6997, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 255}], "osTarget": 3}, {"propId": 6998, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 6999, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 7001, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 7002, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 7009, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 7012, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 63}], "osTarget": 3}, {"propId": 7023, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 7024, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 7025, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 7026, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 1}], "osTarget": 3}, {"propId": 7033, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 7034, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 7035, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 7037, "areaType": 1, "valueType": 2, "access": 2, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 7038, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 7039, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 7040, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 7043, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 7044, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 31}], "osTarget": 3}, {"propId": 7059, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 7060, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 2}], "osTarget": 3}, {"propId": 7061, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 15}], "osTarget": 3}, {"propId": 7062, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 7063, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 7066, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 7067, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 7068, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 7069, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 3}], "osTarget": 3}, {"propId": 7070, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 7071, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 7072, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 7074, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 7}], "osTarget": 3}, {"propId": 7075, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 7076, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 7077, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 7078, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 7079, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 7080, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 7081, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 7082, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}, {"propId": 7091, "areaType": 1, "valueType": 2, "access": 1, "changeMode": 1, "areaCfg": [{"areaId": 0, "minValue": 0, "maxValue": 65535}], "osTarget": 3}]}