/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef DATA_DESERIAL_H_
#define DATA_DESERIAL_H_

#include "com.autolink.vehicle.pb.h"
#include "ALComVehicleClient.h"

namespace vehicleservice {

using autolink::vehicle::ViClientPropValue;

class DataDeserialize {
public:
    static bool Deserialize(MsgList& msgList, std::vector<ViClientPropValue>& propValList);
private:
    static bool setPropStatus(PduState& state, int32_t propId, ViClientPropValue& propVal);
    static std::unordered_map<uint16_t, bool> revPropIdMap_;
};

}  // namespace vehicleservice

#endif
