/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef ADAPTER_REV_JOB_H_
#define ADAPTER_REV_JOB_H_

#include <fdbus/fdbus.h>
#include "DataSerialize.h"
#include "ALComVehicleClient.h"

namespace vehicleservice {

using autolink::vehicle::ViClientPropValue;
using autolink::vehicle::ViWarningValue;
using autolink::vehicle::WarningData;
using ipc::fdbus::CBaseJob;
using ipc::fdbus::CBaseWorker;

class AdapterRcvJob : public CBaseJob {
public:
    explicit AdapterRcvJob(MsgList& msgList,
        std::function<void(uint16_t, ViClientPropValue)> cb);
    ~AdapterRcvJob();

protected:
    void run(Ptr &ref);

private:
    void rcvMsg();

private:
    MsgList msgList_;
    std::function<void(uint16_t, ViClientPropValue)> callback_;
};

}  // namespace vehicleservice

#endif
