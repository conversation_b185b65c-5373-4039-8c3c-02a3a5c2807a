/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/

#ifndef VI_CLIENT_SET_JOB_H
#define VI_CLIENT_SET_JOB_H

#include <fdbus/fdbus.h>

#include "com.autolink.vehicle.pb.h"
#include "ICmAdapterBase.h"

namespace vehicleservice {

using autolink::vehicle::ViClientPropValue;
using ipc::fdbus::CBaseJob;
using ipc::fdbus::CBaseWorker;

class VehicleSetJob : public CBaseJob {
public:
    VehicleSetJob(ViClientPropValue propVal, ICmAdapterBase* adapterBase);

protected:
    void run(Ptr &ref);

private:
    void setProp();

    ViClientPropValue propValue_;
    ICmAdapterBase* cmAdapterBase_;
};

}  // namespace vehicleservice

#endif