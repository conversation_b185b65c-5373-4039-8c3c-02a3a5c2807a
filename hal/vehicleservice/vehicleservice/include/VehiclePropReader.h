#ifndef VEHICLE_READER_H
#define VEHICLE_READER_H

#include <cstdint>
#include <cstdio>
#include <chrono>
#include <list>
#include <vector>
#include <string>
#include <cstring>
#include "rapidjson/document.h"
#include "rapidjson/istreamwrapper.h"
#include "autolinkconfig.h"


namespace vehicleservice {

//#define CONFIG_FILE_READER 1

typedef struct AreaConfig_
{
    int32_t areaId;
    int32_t minValue;
    int32_t maxValue;
} AreaConfig;

typedef struct VehiclePropertyConfigData_
{
    int32_t osTarget;
    int32_t propId;
    int32_t valueType;
    int32_t areaType;
    int32_t changeMode;
    int32_t access;
    std::vector<AreaConfig> areaVec;
} VehiclePropertyConfigData;

typedef struct VehiclePropertyValData_
{
    int32_t propId;
    int32_t valueType;
    int32_t areaId;
    int32_t initialValue;
    int32_t size;
} VehiclePropertyValData;

/// @brief   VehiclePropReader Class
class VehiclePropReader
{
public:
    static VehiclePropReader* Instance();

    static void DeleteInstance();

    bool ConfigParse(std::vector<VehiclePropertyConfigData>& cfgConfigVec, std::vector<VehiclePropertyValData>& cfgValVec);

private:
    VehiclePropReader();

    bool OpenConfigFile(const std::string& filename, rapidjson::Document& doc);

    static VehiclePropReader* instance;

#ifdef CONFIG_FILE_READER
    const std::string configFilePath_ = "vehicle_prop_config.json";
    const std::string valFilePath_ = "vehicle_prop_value.json";
#else
    const std::string configFilePath_ = std::string(AUTOLINK_MOUNT_DIR) + "/etc/vehicle/vehicle_prop_config.json";
    const std::string valFilePath_    = std::string(AUTOLINK_MOUNT_DIR) + "/etc/vehicle/vehicle_prop_value.json";
#endif

};

}

#endif  // VEHICLE_READER_H

