
#ifndef VEHICLE_PROPERTY_STORE_H
#define VEHICLE_PROPERTY_STORE_H

#include <cstdint>
#include <unordered_map>
#include <memory>
#include <mutex>
#include <map>
#include "com.autolink.vehicle.pb.h"

namespace vehicleservice {

using autolink::vehicle::ViClientPropValue;
using autolink::vehicle::ViClientPropConfig;

/**
 * Encapsulates work related to storing and accessing configuration, storing and modifying
 * vehicle property values.
 *
 * VehiclePropertyValues stored in a sorted map thus it makes easier to get range of values, e.g.
 * to get value for all areas for particular property.
 *
 * This class is thread-safe, however it uses blocking synchronization across all methods.
 */
class VehiclePropertyStore {
public:
    /* Function that used to calculate unique token for given ViClientPropValue */
    using TokenFunction = std::function<int64_t(const ViClientPropValue& value)>;

private:
    struct RecordConfig {
        ViClientPropConfig propConfig;
        TokenFunction tokenFunction;
    };

    struct RecordId {
        int32_t prop;
        int32_t area;
        int64_t token;

        bool operator==(const RecordId& other) const;
        bool operator<(const RecordId& other) const;
    };

    using PropertyMap = std::map<RecordId, ViClientPropValue>;
    using PropertyMapRange = std::pair<PropertyMap::const_iterator, PropertyMap::const_iterator>;

public:
    void initVehiclePropConfig();
    void initVehiclePropValue();

    void registerProperty(int32_t msgId, const ViClientPropConfig& config);

    /* Stores provided value. Returns true if value was written returns false if config for
     * example wasn't registered. */
    bool writeValue(const ViClientPropValue& propValue, bool updateStatus);

    void removeValue(const ViClientPropValue& propValue);
    void removeValuesForProperty(int32_t propId);

    std::vector<ViClientPropValue> readAllValues() const;
    std::vector<ViClientPropValue> readValuesForProperty(int32_t propId) const;
    std::unique_ptr<ViClientPropValue> readValueOrNull(const ViClientPropValue& request) const;
    std::unique_ptr<ViClientPropValue> readValueOrNull(int32_t prop, int32_t area = 0,
                                                      int64_t token = 0) const;

    std::vector<ViClientPropConfig> getAllConfigs(int32_t msgId) const;
    const ViClientPropConfig* getConfigOrNull(int32_t propId) const;
    const ViClientPropConfig* getConfigOrDie(int32_t propId) const;

private:
    RecordId getRecordIdLocked(const ViClientPropValue& valuePrototype) const;
    const ViClientPropValue* getValueOrNullLocked(const RecordId& recId) const;
    PropertyMapRange findRangeLocked(int32_t propId) const;

private:
    using MuxGuard = std::lock_guard<std::mutex>;
    mutable std::mutex mLock;
    std::unordered_map<int32_t , RecordConfig> mQnxConfigs;
    std::unordered_map<int32_t , RecordConfig> mAndroidConfigs;
    std::unordered_map<int32_t , RecordConfig> mAllConfigs;

    PropertyMap mPropertyValues;  // Sorted map of RecordId : ViClientPropValue.
};

}  // namespace vehicleservice

#endif //VEHICLE_PROPERTY_STORE_H
