/*
 * Copyright (C) 2015 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef VEHICLE_SERVICE_MANAGER_H_
#define VEHICLE_SERVICE_MANAGER_H_

#include <inttypes.h>
#include <stdint.h>
#include <sys/types.h>

#include <list>
#include <map>
#include <memory>
#include <set>

#include "ICmAdapterBase.h"
#include "VehiclePropertyStore.h"
#include "VehiclePropReader.h"
#include "com.autolink.vehicle.pb.h"

namespace vehicleservice {
using autolink::vehicle::ViClientPropValue;
using autolink::vehicle::ViClientPropValues;
using autolink::vehicle::ViClientPropConfig;
using autolink::vehicle::ViClientPropConfigs;
using autolink::vehicle::ViWarningValue;

class VehicleServiceManager {
public:
    VehicleServiceManager() {
        init();
    }

    void init();
    bool initVehicleProp(std::vector<VehiclePropertyConfigData>& cfgVec, std::vector<VehiclePropertyValData>& cfgValVec);
    void registerCallback(std::function<void(uint16_t, ViClientPropValue)> cb);
    bool getPropConfigs(int msgId, ViClientPropConfigs& config);
    bool get(int propId, int areaId, ViClientPropValue& propVal);
    bool get(int propId, std::vector<ViClientPropValue>& propVec);
    bool set(ViClientPropValue propVal);
    bool getWarningValue(int32_t propId, ViWarningValue& warnValue);
    bool getAllPropVal(ViClientPropValues& propVals);

private:
    bool checkWritePermission(const ViClientPropConfig &config);
    bool checkReadPermission(const ViClientPropConfig &config);
    bool checkOutofRange(ViClientPropValue& propVal);
    bool write(ViClientPropValue propVal);

    VehiclePropertyStore propStore_;
    ICmAdapterBase* cmAdapterBase_;
    std::function<void(uint16_t, ViClientPropValue)> callback_;
};

}  //vehicleservice

#endif // VEHICLE_SERVICE_MANAGER_H_
