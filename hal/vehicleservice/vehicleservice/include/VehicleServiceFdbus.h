/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef VEHICLE_SERVICE_FDBUS_H
#define VEHICLE_SERVICE_FDBUS_H

#include <iostream>
#include "com.autolink.vehicle.pb.h"
#include "VehicleServiceManager.h"

namespace vehicleservice
{

using autolink::vehicle::ViClientPropValue;
using autolink::vehicle::ViClientPropConfigs;
using autolink::vehicle::ViWarningValue;
using autolink::vehicle::ViClientPropValues;

class VehicleServer;
class VehicleServiceFdbus
{
public:
    VehicleServiceFdbus(std::shared_ptr<VehicleServiceManager> vManager);
    virtual ~VehicleServiceFdbus();
    void init();

private:
    void notifyPropertyEvent(int32_t propId, const ViClientPropValue &propVal);
    void notifyWarningEvent(int32_t propId, const ViWarningValue &warnValue);

    VehicleServer* vehicleServer_;
    std::shared_ptr<VehicleServiceManager> vhlManager_;

};

} // namespace vehicleservice

#endif
