/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef ACL_ADAPTER_H_
#define ACL_ADAPTER_H_

#include "ICmAdapterBase.h"

namespace vehicleservice
{

using autolink::vehicle::ViClientPropValue;
using autolink::IALComVehicleClientCallback;
using autolink::ALComVehicleClient;

class AclAdapter : public IALComVehicleClientCallback, public ICmAdapterBase
{
public:
    AclAdapter();
    ~AclAdapter();
    void init(std::function<void(uint16_t, ViClientPropValue)> cb) override;
    void setPropValue(MsgList& msgList) override;

private:
    void onConnect();
    void onDisconnect();
    void onRevicesMsg(MsgList& msgList);

private:
    ALComVehicleClient* aclClient_;
    bool isConnected = false;
    std::function<void(uint16_t, ViClientPropValue)> callback_;
};

} // namespace vehicleservice

#endif
