/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef DATA_SERIAL_H_
#define DATA_SERIAL_H_
#include "com.autolink.vehicle.pb.h"
#include "ALComVehicleClient.h"

namespace vehicleservice {

using autolink::vehicle::ViClientPropValue;

class DataSerialize {
public:
    static bool Serialize(ViClientPropValue& propVal, MsgList& msgList);
};

}  // namespace vehicleservice

#endif
