/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef ADAPTER_BASE_H_
#define ADAPTER_BASE_H_
#include <functional>
#include "com.autolink.vehicle.pb.h"
#include "ALComVehicleClient.h"

namespace vehicleservice
{

using autolink::vehicle::ViClientPropValue;

class ICmAdapterBase
{
public:
    virtual void init(std::function<void(uint16_t, ViClientPropValue)> cb) = 0;
    virtual void setPropValue(MsgList& msgList) = 0;
};

} // namespace vehicleservice

#endif
