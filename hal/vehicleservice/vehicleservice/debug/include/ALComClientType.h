/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef _ALCOM_CLIENT_TYPE_H
#define _ALCOM_CLIENT_TYPE_H

enum EMessageId
{
    REQ_MSG_ALCOM_BD,
    NTF_MSG_ALCOM_BD,
    REQ_MSG_ALCOM_TP_INIT,
    NTF_MSG_ALCOM_TP_INIT,
    REQ_MSG_ALCOM_TP_SPECIAL,
    NTF_MSG_ALCOM_TP_SPECIAL,
    REQ_MSG_ALCOM_TP_VEHICLE,
    NTF_MSG_ALCOM_TP_VEHICLE,
    REQ_MSG_ALCOM_TP_INPUT,
    NTF_MSG_ALCOM_TP_INPUT,
    REQ_MSG_ALCOM_TP_POWER,
    NTF_MSG_ALCOM_TP_POWER,
    REQ_MSG_ALCOM_TP_DIAG,
    NTF_MSG_ALCOM_TP_DIAG,
    REQ_MSG_ALCOM_TP_UPDATE,
    NTF_MSG_ALCOM_TP_UPDATE,
    REQ_MSG_ALCOM_TP_LOG,
    NTF_MSG_ALCOM_TP_LOG,
    REQ_MSG_ALCOM_TP_AUDIO,
    NTF_MSG_ALCOM_TP_AUDIO,
    REQ_MSG_ALCOM_TP_Factory,
    NTF_MSG_ALCOM_TP_Factory,
    REQ_MSG_ALCOM_TP_CAN,
    NTF_MSG_ALCOM_TP_CAN,
    REQ_MSG_ALCOM_TP_CHIME, // CHIME
    NTF_MSG_ALCOM_TP_CHIME,
    REQ_MSG_ALCOM_TP_SPECIAL_TIME, // TIME
    NTF_MSG_ALCOM_TP_SPECIAL_TIME,
    NTF_MSG_ALCOM_TP_INPUT_ZXD,
    NTF_MSG_ALCOM_TP_SPECIAL_THERMAL,//TP 0012h(热管理 )
    REQ_MSG_ALCOM_TP_SOC_TEMP,//TP 0012h(SOC 温度通知)

    REQ_MSG_ALCOM_TP_IVI_CUSTOM,//TP 0015h
    NTF_MSG_ALCOM_TP_IVI_CUSTOM,//TP 0015h

    NTF_MSG_ALCOM_CLIENT_LIST = 0xFF,
};

enum EModuleName
{
    ALCOM_CLIENT_SPECIAL,
    ALCOM_CLIENT_VEHICLE,
    ALCOM_CLIENT_POWER,
};

enum ALComClientMessageState
{
    Message_Failed = -1,
    Message_OK,
    Message_TimeOut,
};

enum class TPType
{
    /*
    Except for the input function, the TP of other functions can be changed according to the project
    */
    // 000xh
    TP_INIT_0_SOC_MCU = 0x0000,
    TP_INIT_1_MCU_SOC = 0x0001,
    // 001xh

    TP_SPECIAL_COMMAND_MCU_SOC = 0x001f,
    TP_SPECIAL_COMMAND_SOC_MCU = 0x0010,
    TP_SPECIAL_TIME_SOC_MCU_SOC = 0x0011,
    TP_SPECIAL_THERMAL_MCU_SOC = 0x0012,//soc->mcu   mcu->soc

    TP_SPECIAL_CUSTOM_MCU_IVI = 0x0015,//soc->mcu   mcu->soc
    // 003xh
    TP_POWER_MCU_SOC = 0x0033,
    TP_POWER_SOC_MCU = 0x0032,
    // 004xh
    TP_CAN_MCU_SOC = 0x0040,
    TP_CAN_SOC_MCU = 0x0041,
    // 005xh
    TP_VEHICLE_MCU_SOC = 0x0050,
    TP_VEHICLE_SOC_MCU = 0x0058,

    // 006xh
    TP_AUDIO_MCU_SOC = 0x0061,
    TP_AUDIO_SOC_MCU = 0x0060,

    TP_CHIME_MCU_SOC = 0x006A,
    TP_CHIME_SOC_MCU = 0x006B,
    // 009xh
    TP_INPUT_MCU_SOC = 0x0090,
    TP_INPUT_MCU_SOC_ZXD = 0x0091,
    TP_INPUT_SOC_MCU = 0x0090,
    // 00Bxh
    TP_DIAG_MCU_SOC = 0x00B0,
    TP_DIAG_SOC_MCU = 0x00BA,

    TP_EARLY_DIAG_MCU_SOC = 0x00BE,
    TP_EARLY_DIAG_SOC_MCU = 0x00BF,
    // 00Cxh
    TP_FACTORY_MCU_SOC = 0x00c0,
    TP_FACTORY_SOC_MCU = 0x00c1,
    // 00Exh
    TP_UPDATE_MCU_SOC = 0x00E0,
    TP_UPDATE_SOC_MCU = 0x00EF,
    // 00Fxh
    TP_LOG_MCU_SOC = 0x00F0,
    TP_LOG_SOC_MCU = 0x00FA,
    // reservie
    TP_TRIGGER_TEST = 0xFFFF,
};

#endif //_ALCOM_CLIENT_TYPE_H
