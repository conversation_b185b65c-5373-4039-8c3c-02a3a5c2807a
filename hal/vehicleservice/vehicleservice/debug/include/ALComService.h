/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#ifndef _ALCOMSERVICE_H
#define _ALCOMSERVICE_H
#include <fdbus/fdbus.h>
#include <fdbus/CFdbProtoMsgBuilder.h>
#include <fdbus/cJSON/cJSON.h>
#include "autolink.platform.vehicle.pb.h"

using namespace autolink::platform::vehicle::pb;

namespace autolink
{
    using namespace ipc::fdbus;

    class ALComService : public CBaseWorker {
    public:
        ALComService();
        virtual ~ALComService();
        static ALComService *getInstance();
        void initALComService();
        bool destory();
        bool setData(char* type, char* status, int32_t prop_id, const std::vector<std::string>& values);

    private:
        void sendFrameMsg(MsgList& msgList);
        void initfdbusService();

        static std::mutex mSingletonLock;
        static ALComService *mInstance;
    };
} // namespace autolink

#endif //_ALCOMSERVICE_H
