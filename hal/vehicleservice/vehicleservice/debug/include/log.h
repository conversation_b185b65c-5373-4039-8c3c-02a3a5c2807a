#pragma once
#include <stdio.h>

#ifdef ANDROID
#include <log/log.h>
#endif

#ifdef __QNXNTO__
#ifndef LOG_TAG
#define LOG_TAG ""
#endif
//#include <log.h>
#define ALOGV(...) do {printf(__VA_ARGS__); putchar('\n');} while(0)
#define ALOGD(...) do {printf(__VA_ARGS__); putchar('\n');} while(0)
#define ALOGI(...) do {printf(__VA_ARGS__); putchar('\n');} while(0)
#define ALOGW(...) do {printf(__VA_ARGS__); putchar('\n');} while(0)
#define ALOGE(...) do {printf(__VA_ARGS__); putchar('\n');} while(0)

#define LOG_DEBUG(...) do {printf(__VA_ARGS__); putchar('\n');} while(0)
#define LOG_INFO(...) do {printf(__VA_ARGS__); putchar('\n');} while(0)
#define LOG_WARNING(...) do {printf(__VA_ARGS__); putchar('\n');} while(0)
#define LOG_ERROR(...) do {printf(__VA_ARGS__); putchar('\n');} while(0)
#define LOG_FATAL(...) do {printf(__VA_ARGS__); putchar('\n');} while(0)
#endif