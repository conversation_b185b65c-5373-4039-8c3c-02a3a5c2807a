#include "ALComService.h"

#include <iostream>
#include <chrono>
#include <thread>

#include <string_view>
#include <iostream>

using namespace std;
using namespace autolink;

void usage(int argc, char **argv) {

  printf("usage:\n");
  printf("!!!使用之前请将QNX系统中杀掉laucher 进程后再杀掉alcom进程\n\n");

  printf("%s -c xx -i yy -t [int|intvec|bytes] {prop id} {value}+\n", argv[0]);
  printf("-c {count}:             cycle number, default=1\n");
  printf(
      "-i {ms}:                send message interval in milisecond, "
      "default=1000\n");
  printf("-t [int|intvec|bytes]:  value type, default=int\n\n");
  printf("-s [valid|invalid|lost]: Signal status, default=valid\n\n");
  printf("Example: %s -c 100 -i 100 -t int 0x1000  1\n", argv[0]);
  printf("Example: %s -c 100 -i 100 -t intvec 0x1001  1 0x2 0x300\n", argv[0]);
  printf("Example: %s -c 100 -i 100 -t bytes 0x1002  100088ff5533\n\n\n", argv[0]);
  printf("Example: %s -c 1 -i 100 -s lost -t int 0x1000  1\n", argv[0]);
}

void getValTypeStr(int valType, std::string& ValTypeStr) {
  switch (valType) {
    case 0:
      ValTypeStr = "STRING";
      break;
    case 1:
      ValTypeStr = "BOOL";
      break;
    case 2:
      ValTypeStr = "INT32";
      break;
    case 3:
      ValTypeStr = "INT32_VEC";
      break;
    case 4:
      ValTypeStr = "INT64";
      break;
    case 5:
      ValTypeStr = "INT64_VEC";
      break;
    case 6:
      ValTypeStr = "FLOAT";
      break;
    case 7:
      ValTypeStr = "FLOAT_VEC";
      break;
    case 8:
      ValTypeStr = "BYTES";
      break;
    default:
      ValTypeStr = "unkown";
      break;
  }
  return;
}

void getAccessModeStr(int accessMode, std::string& accessStr) {
  switch (accessMode) {
    case 0:
      accessStr = "NONE";
      break;
    case 0x100:
      accessStr = "READ";
      break;
    case 0x200:
      accessStr = "WRITE";
      break;
    case 0x300:
      accessStr = "READ_WRITE";
      break;
    default:
      accessStr = "unkown";
      break;
  }
  return;
}

int main_server(int argc, char **argv) {
  int opt;
  int interval = 1000;
  int count = 1;
  char *type = "int";
  char *signalStatus = "valid";
  int32_t propId = 0;

  while ((opt = getopt(argc, argv, "c:i:t:p:s:")) != -1) {
    switch (opt) {
      case 'c':
        printf("count=%d\n", std::stoi(optarg));
        count = std::stoi(optarg);
        break;
      case 'i':
        printf("interval=%d\n", std::stoi(optarg));
        interval = std::stoi(optarg);
        break;
      case 't':
        printf("type=%s\n", optarg);
        type = optarg;
        break;
      case 'p':
        printf("propID=%d\n", std::stoi(optarg, nullptr, 16));
        propId = std::stoi(optarg, nullptr, 16);
        break;
      case 's':
        printf("signal status=%s\n", optarg);
        signalStatus = optarg;
        break;
      case '?':
        printf("unkonwn option: %c\n", optopt);
        break;
      default:
        break;
    }
  }

  #if 0
  if (propId != 0) {
    SignalConfig config;
    if (!ALComService::getSignalConfig(propId, config)) {
      printf("getSignalConfig failed, propId = %d \n", __FUNCTION__, propId);
      return false;
    }
    else {
      std::string valTypeStr = "INT32";
      std::string accessStr = "NONE";
      getValTypeStr(config.valueType, valTypeStr);
      getAccessModeStr(config.access, accessStr);

      printf("propId = 0x%04x, valueType = %s, accessMode = %s, minValue = %d, maxValue = %u \n",
              propId, valTypeStr.c_str(), accessStr.c_str(), config.minValue, config.maxValue);
      return true;
    }
  }
  #endif

  if(optind >= argc) {
    cout << "invalid arguments." << endl;
    usage(argc, argv);
    return 0;
  }
  // 处理非选项参数
  int32_t prop_id = std::stoi(argv[optind], nullptr, 16);
  std::vector<std::string> values;
  for (int i = optind + 1; i < argc; i++) {
    values.emplace_back(argv[i]);
  }
  printf("prop id: 0x%04x values: ", prop_id);
  for(size_t i = 0; i < values.size(); i++) {
    printf("[%d]=%s ", i, values[i].c_str());
    if((i+1)%10 == 0) {
        putchar('\n');
    }
  }
  putchar('\n');

  ALComService *mService = ALComService::getInstance();
  mService->initALComService();
  std::this_thread::sleep_for(std::chrono::milliseconds(1000));
  bool ret = false;
  while (count--)
  {
    ret = mService->setData(type, signalStatus, prop_id, values);
    if(!ret) {
        printf("%s: set data for 0x%04x failed.", __func__, prop_id);
        break;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(interval));
  }
  return 0;
}

int main(int argc, char **argv) {
  return main_server(argc, argv);
}
