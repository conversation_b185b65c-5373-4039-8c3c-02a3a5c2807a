/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/

#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <iostream>
#include <sstream>
#include <fdbus/fdbus.h>
#include <fdbus/cJSON/cJSON.h>
#include <fdbus/CFdbCJsonMsgBuilder.h>
#include <fdbus/CFdbProtoMsgBuilder.h>
#include "log.h"
#include "ALComService.h"
#include "ALComClientType.h"
#include "com.autolink.vehicle.pb.h"


#define ALCOM_VEHICLE_SIGNAL_NAME_INDEX (6)
#define ALCOM_VEHICLE_SIGNAL_DATA_INDEX (8)
namespace autolink
{
    /*code for ALCom server; inherit from CBaseServer */
    class ALComServiceFdbus : public CBaseServer
    {
    public:
        ALComServiceFdbus(const char *name,  CBaseWorker *worker = 0)
            : CBaseServer(name, worker)
        {
            enableUDP(true);
            enableAysncRead(true);
            enableAysncWrite(true);
        }
        bool broadcastMsg(FdbMsgCode_t code, MsgList& msgList)
        {
            CFdbProtoMsgBuilder builder(msgList);
            return broadcast(code, builder);
        }

    protected:
        void onOnline(const CFdbOnlineInfo &info)
        {
            LOG_ERROR("%s session: %d ,is_first=%d\n", __FUNCTION__, info.mSid, info.mFirstOrLast);
            // auto session = FDB_CONTEXT->getSession(sid);
            // printf("**zc***name:%s\n", session->senderName().c_str());
            if (info.mFirstOrLast)
            {
                 LOG_INFO("first client enabled\n");
            }
        }

        void onOffline(const CFdbOnlineInfo &info)
        {
            LOG_ERROR("%s session: %d ,is_first=%d\n", __FUNCTION__, info.mSid, info.mFirstOrLast);
            // auto session = FDB_CONTEXT->getSession(sid);
            // printf("======name:%s \n", session->senderName().c_str());
            if (info.mFirstOrLast)
            {
                // LOG_INFO("last client off\n");
            }
            deleteConnetClient(info.mSid);
        }

        void onInvoke(CBaseJob::Ptr &msg_ref)
        {
            MsgList msgList;
            auto msg = castToMessage<CBaseMessage *>(msg_ref);
            CFdbProtoMsgParser parser(msgList);
            if (!msg->deserialize(parser)) {
                msg->status(msg_ref, FdbMsgStatusCode::FDB_ST_MSG_DECODE_FAIL, "Fail to decode msgList!");
                return;
            }

            for (int i = 0; i < msgList.signals_size(); i++) {
                MsgSignal signal = msgList.signals(i).signal();
                switch (signal.value_type()) {
                    case SignalValueType::TYPE_SINT32:
                        LOG_INFO("%s: propId = 0x%04x, valuetype = %d, value = %d", __func__,
                            signal.signal_id(), autolink::vehicle::ValueType::INT32, signal.sint32_value());
                        break;
                    case SignalValueType::TYPE_UINT32:
                        LOG_INFO("%s: propId = 0x%04x, valuetype = %d, value = %d", __func__,
                            signal.signal_id(), autolink::vehicle::ValueType::INT32, signal.uint32_value());
                        break;
                    default:
                        LOG_ERROR("Comd Broadcast INT32 PropId = 0x%04x type = %d error\n", signal.signal_id(), signal.value_type());
                        break;
                }
            }

            for (int i = 0; i < msgList.pdus_size(); i++) {
                int32_t propId = 0;
                MsgPdu pdu = msgList.pdus(i);
                LOG_INFO("%s: propId = 0x%04x, valuetype = %d, value = %d", __func__,
                    pdu.signals(0).signal_id(), autolink::vehicle::ValueType::INT32_VEC, pdu.signals(0).sint32_value());
            }

            msg->reply(msg_ref);
        }

        /* called when client call subscribe() to register message */
        void onSubscribe(CBaseJob::Ptr &msg_ref)
        {
            auto msg = castToMessage<CFdbMessage *>(msg_ref);
            const CFdbMsgSubscribeItem *sub_item;
            /* iterate all message id subscribed */
            FDB_BEGIN_FOREACH_SIGNAL(msg, sub_item)
            {
                FdbMsgCode_t msg_code = sub_item->msg_code();
                LOG_ERROR("%s,msg_code =%d,clientNname = %s,session=%d\n", __FUNCTION__, msg_code, msg->senderName().c_str(), msg->session());
            }
            FDB_END_FOREACH_SIGNAL()
            msg->status(msg_ref, FDB_ST_SUBSCRIBE_OK, "Subscribe success from ALCom!");
        }
        void updateConnetClient(FdbSessionId_t sid, FdbMsgCode_t code)
        {
            std::lock_guard<std::mutex> _l(mConnetClientLock);
            mConnetClient[sid] = code;
        }
        void deleteConnetClient(FdbSessionId_t sid)
        {
            std::lock_guard<std::mutex> _l(mConnetClientLock);
            if (mConnetClient.count(sid) > 0)
            {
                FdbMsgCode_t code = mConnetClient[sid];
                mConnetClient.erase(sid);
            }
        }

    private:
        bool mVehicleClientSubscribe = false;
        std::map<FdbSessionId_t, FdbMsgCode_t> mConnetClient;
        static std::mutex mConnetClientLock;
    };
    std::mutex ALComServiceFdbus::mConnetClientLock;

    static ALComServiceFdbus *S_ALComServiceFdbus = nullptr;
    // ALComeservice
    static CBaseWorker *main_worker = nullptr;
    std::mutex ALComService::mSingletonLock;
    ALComService *ALComService::mInstance = nullptr;

    ALComService::ALComService()
        : CBaseWorker("ALCOM_SERVICE_THREAD_DEF", FDB_WORKER_DEFAULT, 512, 512)
    {
    }
    ALComService *ALComService::getInstance()
    {
        if (!mInstance)
        {
            std::lock_guard<std::mutex> _l(mSingletonLock);
            if (!mInstance)
            {
                mInstance = new ALComService();
            }
        }
        return mInstance;
    }

    ALComService::~ALComService()
    {
        if (S_ALComServiceFdbus)
        {
            delete S_ALComServiceFdbus;
            S_ALComServiceFdbus = nullptr;
        }
    }

    void ALComService::initALComService()
    {
        initfdbusService();
        start();
    }

    void ALComService::initfdbusService()
    {
        std::string server_name("ALCom");
        std::string url(FDB_URL_SVC);
        url += server_name;
        if (S_ALComServiceFdbus == nullptr)
        {
            CFdbContext::enableLogger(false);
            /* start fdbus context thread */
            FDB_CONTEXT->start();
            /* create servers and bind the address: svc://service_name */
            server_name += "_Server";
            main_worker = new CBaseWorker("ALComServiceFdbus");
            main_worker->start();
            S_ALComServiceFdbus = new ALComServiceFdbus(server_name.c_str(), main_worker);
            if (S_ALComServiceFdbus)
            {
                S_ALComServiceFdbus->bind(url.c_str());
                LOG_INFO("%s: bind c_str = %s", __func__, url.c_str());
            }
        }
    }
    bool ALComService::destory()
    {
        delete this;
        mInstance = nullptr;
        return true;
    }

    void ALComService::sendFrameMsg(MsgList& msgList)
    {
        if (S_ALComServiceFdbus) {
            S_ALComServiceFdbus->broadcastMsg(NTF_MSG_ALCOM_TP_VEHICLE, msgList);
        }
    }

    bool ALComService::setData(char* type, char* status, int32_t propId,
                                    const std::vector<std::string>& values) {
        LOG_INFO("%s: begin2", __func__);
        MsgList msgList;
        if(strcmp(type, "int") == 0) {
            int val = std::stoi(values[0]);
            LOG_INFO("%s: val = 0x%x", __func__, val);
            MsgSignalSp* sigSpPtr = msgList.add_signals();

            MsgSignal* sigPtr = sigSpPtr->mutable_signal();
            sigPtr->set_signal_id((SignalId)propId);
            sigPtr->set_sint32_value(val);
            sigPtr->set_value_type(SignalValueType::TYPE_SINT32);
            sigSpPtr->set_state(PduState::ACTIVE);

            if(strcmp(status, "lost") == 0) {
               sigSpPtr->set_state(PduState::INACTIVE);
            }
            else if(strcmp(status, "invalid") == 0) {
                sigSpPtr->set_state(PduState::DEFAULT);
            }
            else {
                sigSpPtr->set_state(PduState::ACTIVE);
            }
        }
        else if(strcmp(type, "intvec") == 0) {
            MsgPdu* pduPtr = msgList.add_pdus();
            if(strcmp(status, "lost") == 0) {
                pduPtr->set_state(PduState::INACTIVE);
            }
            else if(strcmp(status, "invalid") == 0) {
                pduPtr->set_state(PduState::DEFAULT);
            }
            else {
                pduPtr->set_state(PduState::ACTIVE);
            }
            pduPtr->set_pdu_id((PduId)propId);

            for (int i = 0; i < values.size(); i++) {
                int32_t value = std::stoi(values[i]);

                MsgSignal* sigPtr = pduPtr->add_signals();
                sigPtr->set_signal_id((SignalId)(0x8000 + i));
                sigPtr->set_value_type(SignalValueType::TYPE_SINT32);
                sigPtr->set_sint32_value(value);
                LOG_DEBUG("======Set PropId = 0x%04x  Value[%d] = 0x%04x======\n",
                            propId,  i, value);
            }
        }
        else if(strcmp(type, "bytes") == 0) {
            //TODO
        }
        else {
            LOG_ERROR("invald data type");
            return false;
        }
        LOG_INFO("%s: send start", __func__);
        sendFrameMsg(msgList);
        LOG_INFO("%s: end", __func__);
        return true;

    }

} // namespace autolink
