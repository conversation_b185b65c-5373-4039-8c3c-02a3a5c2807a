/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#include "AclAdapter.h"

#include "log.h"
#include <stdio.h>

#include <iostream>

#include "AdapterRcvJob.h"
#include "types.h"

namespace vehicleservice {

#define LOG_TAG "VS::AclAdapter"

static CBaseWorker rcvWorker("receive_thread", FDB_WORKER_DEFAULT, 3000);

AclAdapter::AclAdapter()
    : aclClient_(nullptr) {
}

AclAdapter::~AclAdapter() {
    if (aclClient_ != nullptr) {
        delete aclClient_;
        aclClient_ = nullptr;
    }
}

void AclAdapter::init(std::function<void(uint16_t, ViClientPropValue)> cb) {
    callback_ = cb;
    if (aclClient_ == nullptr) {
        aclClient_ = new ALComVehicleClient();
    }
    /*start receive worker thread*/
    rcvWorker.start();
    aclClient_->connectService(this);

    LOG_TAG_WARNING("AclAdapter start: connectService done \n");
}

void AclAdapter::setPropValue(MsgList& msgList) {
    if (isConnected) {
        //LOG_TAG_DEBUG("AclAdapter setPropValue\n");
        int retry = 2;
        int ret = 0;
        while (retry--) {
            ret = aclClient_->sendMsg(msgList);
            if (ret == 0) {
                break;
            }
        }
    } else {
        LOG_TAG_ERROR("AclAdapter setPropValue error:not connect ALComdService \n");
    }
}

void AclAdapter::onConnect() {
    isConnected = true;
    LOG_TAG_WARNING("AclAdapter onConnect done \n");
}

void AclAdapter::onDisconnect() {
    isConnected = false;
    LOG_TAG_WARNING("AclAdapter onDisconnect done \n");
}

void AclAdapter::onRevicesMsg(MsgList& msgList) {
    //LOG_TAG_INFO("[AclAdapter::%s] propID: %d\n", __func__, signal.signal_id());
    CBaseJob* job = new AdapterRcvJob(msgList, callback_);
    rcvWorker.sendAsync(job);
}

}  // namespace vehicleservice
