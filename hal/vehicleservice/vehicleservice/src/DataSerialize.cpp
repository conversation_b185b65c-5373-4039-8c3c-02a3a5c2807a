#define LOG_TAG "VS::DataSerialize"
#include "DataSerialize.h"
#include "log.h"
using namespace autolink::vehicle;

namespace vehicleservice {
bool DataSerialize::Serialize(ViClientPropValue& propVal, MsgList& msgList) {
    uint32_t propId = propVal.propid();

    bool ret = true;

    switch (propVal.valuetype()) {
        case ValueType::INT32:{
            MsgSignalSp* sigSpPtr = msgList.add_signals();

            int32_t value = propVal.data().int32values(0);

            MsgSignal* sigPtr = sigSpPtr->mutable_signal();
            sigPtr->set_signal_id((SignalId)propId);
            sigPtr->set_sint32_value(value);
            sigPtr->set_value_type(SignalValueType::TYPE_SINT32);
            sigSpPtr->set_state(PduState::ACTIVE);
            LOG_TAG_INFO("Set PropId = 0x%04x Value = 0x%04x\n",
                        propId, value);

            break;
        }
        case ValueType::INT32_VEC: {
            MsgPdu* pduPtr = msgList.add_pdus();
            pduPtr->set_state(PduState::ACTIVE);
            pduPtr->set_pdu_id(PDU_ID_MAX);

            for (int i = 0; i < propVal.data().int32values_size(); i++) {
                int32_t value = propVal.data().int32values(i);

                MsgSignal* sigPtr = pduPtr->add_signals();
                sigPtr->set_signal_id((SignalId)propId);
                sigPtr->set_value_type(SignalValueType::TYPE_SINT32);
                sigPtr->set_sint32_value(value);
                LOG_TAG_INFO("======Set PropId = 0x%04x  Value[%d] = 0x%04x======\n",
                            propId,  i, value);
            }
            break;
        }

        default: {
            LOG_TAG_ERROR("unsupport data type = %d\n", propVal.valuetype());
            ret = false;
            break;
        }
    }
    return ret;
}
}  // namespace vehicleservice
