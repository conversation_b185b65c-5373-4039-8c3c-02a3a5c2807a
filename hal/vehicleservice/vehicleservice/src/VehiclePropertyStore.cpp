#define LOG_TAG "VS::VehiclePropertyStore"
#include "log.h"
#include "types.h"

#include "VehiclePropertyStore.h"

namespace vehicleservice {

bool VehiclePropertyStore::RecordId::operator==(const VehiclePropertyStore::RecordId& other) const {
    return prop == other.prop && area == other.area && token == other.token;
}

bool VehiclePropertyStore::RecordId::operator<(const VehiclePropertyStore::RecordId& other) const  {
    return prop < other.prop
           || (prop == other.prop && area < other.area)
           || (prop == other.prop && area == other.area && token < other.token);
}

void VehiclePropertyStore::registerProperty(int32_t msgId, const ViClientPropConfig& config) {
    MuxGuard g(mLock);
    switch (msgId) {
        case toInt(EViMessageId::OS_ALL): {
            (void)mAllConfigs.insert({ config.propid(), RecordConfig { config, nullptr } });
            break;
        }
        case toInt(EViMessageId::OS_QNX): {
            (void)mQnxConfigs.insert({ config.propid(), RecordConfig { config, nullptr } });
            break;
        }
        case toInt(EViMessageId::OS_ANDROID): {
            (void)mAndroidConfigs.insert({ config.propid(), RecordConfig { config, nullptr } });
            break;
        }
        default:
            break;
    }

}

bool VehiclePropertyStore::writeValue(const ViClientPropValue& propValue,
                                        bool updateStatus) {
    MuxGuard g(mLock);
    if (!mAllConfigs.count(propValue.propid()) &&
        !mQnxConfigs.count(propValue.propid()) &&
        !mAndroidConfigs.count(propValue.propid()))
        return false;

    RecordId recId = getRecordIdLocked(propValue);
    ViClientPropValue* valueToUpdate = const_cast<ViClientPropValue*>(getValueOrNullLocked(recId));
    if (valueToUpdate == nullptr) {
        (void)mPropertyValues.insert({ recId, propValue });
    } else {
        *valueToUpdate = propValue;
    }
    return true;
}

void VehiclePropertyStore::removeValue(const ViClientPropValue& propValue) {
    MuxGuard g(mLock);
    RecordId recId = getRecordIdLocked(propValue);
    auto it = mPropertyValues.find(recId);
    if (it != mPropertyValues.end()) {
        (void)mPropertyValues.erase(it);
    }
}

void VehiclePropertyStore::removeValuesForProperty(int32_t propId) {
    MuxGuard g(mLock);
    auto range = findRangeLocked(propId);
    (void)mPropertyValues.erase(range.first, range.second);
}

std::vector<ViClientPropValue> VehiclePropertyStore::readAllValues() const {
    MuxGuard g(mLock);
    std::vector<ViClientPropValue> allValues;
    allValues.reserve(mPropertyValues.size());
    for (auto&& it : mPropertyValues) {
        allValues.push_back(it.second);
    }
    return allValues;
}

std::vector<ViClientPropValue> VehiclePropertyStore::readValuesForProperty(int32_t propId) const {
    std::vector<ViClientPropValue> values;
    MuxGuard g(mLock);
    auto range = findRangeLocked(propId);
    for (auto it = range.first; it != range.second; ++it) {
        values.push_back(it->second);
    }

    return values;
}

std::unique_ptr<ViClientPropValue> VehiclePropertyStore::readValueOrNull(
        const ViClientPropValue& request) const {
    MuxGuard g(mLock);
    RecordId recId = getRecordIdLocked(request);
    const ViClientPropValue* internalValue = getValueOrNullLocked(recId);
    return internalValue ? std::make_unique<ViClientPropValue>(*internalValue) : nullptr;
}

std::unique_ptr<ViClientPropValue> VehiclePropertyStore::readValueOrNull(
        int32_t prop, int32_t area, int64_t token) const {
    RecordId recId = {prop, area, token };
    MuxGuard g(mLock);
    const ViClientPropValue* internalValue = getValueOrNullLocked(recId);
    return internalValue ? std::make_unique<ViClientPropValue>(*internalValue) : nullptr;
}


std::vector<ViClientPropConfig> VehiclePropertyStore::getAllConfigs(int32_t msgId) const {
    MuxGuard g(mLock);
    std::vector<ViClientPropConfig> configs;
    if (msgId == toInt(EViMessageId::OS_ALL)) {
        configs.reserve(mAllConfigs.size());
        for (auto&& recordConfigIt: mAllConfigs) {
            configs.push_back(recordConfigIt.second.propConfig);
        }
    }
    else if (msgId == toInt(EViMessageId::OS_QNX)) {
        configs.reserve(mQnxConfigs.size());
        for (auto&& recordConfigIt: mQnxConfigs) {
            configs.push_back(recordConfigIt.second.propConfig);
        }
    }
    else if (msgId == toInt(EViMessageId::OS_ANDROID)) {
        configs.reserve(mAndroidConfigs.size());
        for (auto&& recordConfigIt: mAndroidConfigs) {
            configs.push_back(recordConfigIt.second.propConfig);
        }
    }
    return configs;
}

const ViClientPropConfig* VehiclePropertyStore::getConfigOrNull(int32_t propId) const {
    MuxGuard g(mLock);
    auto recordConfigIt = mAllConfigs.find(propId);
    if (recordConfigIt != mAllConfigs.end()) {
        return &recordConfigIt->second.propConfig;
    }

    recordConfigIt = mQnxConfigs.find(propId);
    if (recordConfigIt != mQnxConfigs.end()) {
        return &recordConfigIt->second.propConfig;
    }

    recordConfigIt = mAndroidConfigs.find(propId);
    if (recordConfigIt != mAndroidConfigs.end()) {
        return &recordConfigIt->second.propConfig;
    }
    return nullptr;
}

const ViClientPropConfig* VehiclePropertyStore::getConfigOrDie(int32_t propId) const {
    auto cfg = getConfigOrNull(propId);
    if (!cfg) {
        LOG_TAG_WARNING("%s: config not found for property: 0x%x", __func__, propId);
        abort();
    }
    return cfg;
}

VehiclePropertyStore::RecordId VehiclePropertyStore::getRecordIdLocked(
        const ViClientPropValue& valuePrototype) const {
    RecordId recId = {
        .prop = valuePrototype.propid(),
        .area = valuePrototype.areaid(),
        .token = 0
    };

    if (!mAllConfigs.count(recId.prop) &&
        !mQnxConfigs.count(recId.prop) &&
        !mAndroidConfigs.count(recId.prop))
        return {};

    return recId;
}

const ViClientPropValue* VehiclePropertyStore::getValueOrNullLocked(
        const VehiclePropertyStore::RecordId& recId) const  {
    auto it = mPropertyValues.find(recId);
    return it == mPropertyValues.end() ? nullptr : &it->second;
}

VehiclePropertyStore::PropertyMapRange VehiclePropertyStore::findRangeLocked(int32_t propId) const {
    // Based on the fact that mPropertyValues is a sorted map by RecordId.
    auto beginIt = mPropertyValues.lower_bound( RecordId { propId, INT32_MIN, 0 });
    auto endIt = mPropertyValues.lower_bound( RecordId { propId + 1, INT32_MIN, 0 });

    return  PropertyMapRange { beginIt, endIt };
}

}  // namespace vehicleservice
