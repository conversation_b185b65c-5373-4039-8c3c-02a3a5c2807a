#define LOG_TAG "VS::VehiclePropReader"
#include "VehiclePropReader.h"
#include <iostream>
#include <fstream>
#include "log.h"

namespace vehicleservice {

VehiclePropReader* VehiclePropReader::instance = nullptr;

VehiclePropReader::VehiclePropReader(){
}

VehiclePropReader* VehiclePropReader::Instance() {
    if (!instance) {
        instance = new VehiclePropReader();
    }
    return instance;
}

void VehiclePropReader::DeleteInstance() {
    if (instance) {
        delete instance;
        instance = nullptr;
    }
}

bool VehiclePropReader::OpenConfigFile(const std::string& filename, rapidjson::Document& doc) {
    std::ifstream ifs(filename);
    if (!ifs.is_open()) return false;

    rapidjson::IStreamWrapper isw(ifs);
    doc.ParseStream(isw);
    return !doc.HasParseError();
}

bool VehiclePropReader::ConfigParse(std::vector<VehiclePropertyConfigData>& cfgConfigVec, std::vector<VehiclePropertyValData>& cfgValVec) {
    rapidjson::Document cfgDoc;
    if (OpenConfigFile(configFilePath_, cfgDoc)) {
        // 成功解析后访问数据
        if (cfgDoc.HasMember("propConfig") ) {
            const rapidjson::Value& cfgJsonVec = cfgDoc["propConfig"];
            for (auto& cfgJson : cfgJsonVec.GetArray()) {
                VehiclePropertyConfigData cfgConfig;
                cfgConfig.propId = cfgJson.HasMember("propId") ? cfgJson["propId"].GetInt(): 0;
                cfgConfig.valueType = cfgJson.HasMember("valueType") ? cfgJson["valueType"].GetInt(): 0;
                cfgConfig.areaType = cfgJson.HasMember("areaType") ? cfgJson["areaType"].GetInt(): 0;
                cfgConfig.access = cfgJson.HasMember("access") ? cfgJson["access"].GetInt(): 0;
                cfgConfig.changeMode = cfgJson.HasMember("changeMode") ? cfgJson["changeMode"].GetInt(): 0;
                cfgConfig.osTarget = cfgJson.HasMember("osTarget") ? cfgJson["osTarget"].GetInt(): 0;
                if (cfgJson.HasMember("areaCfg")) {
                    const rapidjson::Value& areaJsonVec = cfgJson["areaCfg"];
                    for (auto& areaJson: areaJsonVec.GetArray()) {
                        AreaConfig areaConfig;
                        areaConfig.minValue = areaJson.HasMember("minValue") ? areaJson["minValue"].GetInt(): 0;
                        areaConfig.maxValue = areaJson.HasMember("maxValue") ? areaJson["maxValue"].GetInt(): 0;
                        areaConfig.areaId = areaJson.HasMember("areaId") ? areaJson["areaId"].GetInt(): 0;
                        cfgConfig.areaVec.push_back(areaConfig);
                    }
                }
                //LOG_TAG_INFO("cfgVec parse = %d", cfgConfig.propId);
                cfgConfigVec.push_back(cfgConfig);
            }
        }
    }
    else {
        LOG_TAG_ERROR("open json file failed, %s", configFilePath_.c_str());
        return false;
    }

    rapidjson::Document valDoc;
    if (OpenConfigFile(valFilePath_, valDoc)) {
        // 成功解析后访问数据
        if (valDoc.HasMember("propValue") ) {
            const rapidjson::Value& valJsonVec = valDoc["propValue"];
            for (auto& valJson : valJsonVec.GetArray()) {
                VehiclePropertyValData valConfig;
                valConfig.propId = valJson.HasMember("propId") ? valJson["propId"].GetInt(): 0;
                valConfig.valueType = valJson.HasMember("valueType") ? valJson["valueType"].GetInt(): 0;
                valConfig.areaId = valJson.HasMember("areaId") ? valJson["areaId"].GetInt(): 0;
                valConfig.initialValue = valJson.HasMember("initialValue") ? valJson["initialValue"].GetInt(): 0;
                valConfig.size = valJson.HasMember("size") ? valJson["size"].GetInt(): 0;
                cfgValVec.push_back(valConfig);
                //LOG_TAG_INFO("valVec parse = %d", valConfig.propId);
            }
        }
    }
    else {
        LOG_TAG_ERROR("open json file failed, %s", valFilePath_.c_str());
        return false;
   }

    return true;
}

}

