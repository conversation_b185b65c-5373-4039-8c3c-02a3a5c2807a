/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#define LOG_TAG "VS::VehicleServiceFdbus"
#include "VehicleServiceFdbus.h"

#include <fdbus/CFdbProtoMsgBuilder.h>
#include <fdbus/cJSON/cJSON.h>
#include <fdbus/fdbus.h>
#include "log.h"
#include <stdio.h>
#include <string.h>
#include <unistd.h>

#include <algorithm>
#include <iostream>
#include <map>
#include <sstream>
#include <thread>
#include <vector>

#include "types.h"

namespace vehicleservice {

using autolink::vehicle::ViClientPropGetReq;
using autolink::vehicle::ViClientPropValues;
using autolink::vehicle::ViPropConfigGetReq;
using autolink::vehicle::ViResponse;
using namespace ipc::fdbus;
using namespace autolink::vehicle;

static CBaseWorker main_worker("fdbus_callback_thread");

class VehicleServer : public CBaseServer {
public:
    VehicleServer(const char *name, std::shared_ptr<VehicleServiceManager> vManager, CBaseWorker *worker = 0)
        : CBaseServer(name, worker) {
        vhlManager_ = vManager;
    }

    void sendVehicleMessage(int32_t propId, const ViClientPropValue &propVal) {
        CFdbProtoMsgBuilder builder(propVal);
        broadcast(propId, builder);
    }

    void sendWarningMesaage(int32_t propId, const ViWarningValue &warnValue) {
        CFdbProtoMsgBuilder builder(warnValue);
        broadcast(propId, builder);
        // LOG_TAG_DEBUG("broadcast WarningEvent propId = %d first_warnId = %d status = %d \n", propId, warnValue.data(0).warningid(), propId, warnValue.data(0).value());
    }

protected:
    void onOnline(const CFdbOnlineInfo &info) {
        LOG_TAG_DEBUG("VehicleService server session up: %d\n", info.mSid);
    }

    void onOffline(const CFdbOnlineInfo &info) {
        LOG_TAG_DEBUG("VehicleService server session shutdown: %d\n", info.mSid);
    }

    void onInvoke(CBaseJob::Ptr &msg_ref) {
        if (!vhlManager_) {
            LOG_TAG_ERROR("[%s] VehicleServiceManager  is nullptr \n", __func__);
            return;
        }
        auto msg = castToMessage<CBaseMessage *>(msg_ref);
        switch (msg->code()) {
        case toInt(EViOperation::GET): {
            ViClientPropGetReq propGet_req;
            CFdbProtoMsgParser parser(propGet_req);
            if (!msg->deserialize(parser)) {
                msg->status(msg_ref, FdbMsgStatusCode::FDB_ST_MSG_DECODE_FAIL, "Fail to decode request!");
                return;
            }
            int32_t propId = propGet_req.propid();
            int32_t areaId = propGet_req.areaid();
            LOG_TAG_INFO("[%s] get prop(propId = 0x%04x areaId = 0x%04x)  value  \n", __func__, propId, areaId);
            ViClientPropValue propValue;
            if (vhlManager_) {
                vhlManager_->get(propId, areaId, propValue);
            }
            if (propValue.has_data()) {
                CFdbProtoMsgBuilder builder(propValue);
                msg->reply(msg_ref, builder);
            } else {
                LOG_TAG_ERROR("[%s] get prop(propId = 0x%04x areaId = 0x%04x)  value fail \n", __func__, propId, areaId);
            }
            break;
        }

        case toInt(EViOperation::SET): {
            ViClientPropValue propSet_req;
            CFdbProtoMsgParser parser(propSet_req);
            ViResponse response;
            if (!msg->deserialize(parser)) {
                msg->status(msg_ref, FdbMsgStatusCode::FDB_ST_MSG_DECODE_FAIL, "Fail to decode request!");
                response.set_status(repsonseSts::FAILED);
                CFdbProtoMsgBuilder builder(response);
                msg->reply(msg_ref, builder);
                return;
            }
            //LOG_TAG_INFO("setPropValue(propId = 0x%04x )\n", propSet_req.propid());
            if (vhlManager_) {
                vhlManager_->set(propSet_req);
                response.set_status(repsonseSts::SUCCESS);
            }
            else {
                response.set_status(repsonseSts::FAILED);
            }
            CFdbProtoMsgBuilder builder(response);
            msg->reply(msg_ref, builder);
            break;
        }

        case toInt(EViOperation::GETCONFIG): {
            ViPropConfigGetReq propConfigGet_req;
            CFdbProtoMsgParser parser(propConfigGet_req);
            if (!msg->deserialize(parser)) {
                msg->status(msg_ref, FdbMsgStatusCode::FDB_ST_MSG_DECODE_FAIL, "Fail to decode request!");
                return;
            }
            ViClientPropConfigs propConfigs_rep;
            if (vhlManager_) {
                vhlManager_->getPropConfigs(propConfigGet_req.msgid(), propConfigs_rep);
            }
            if (propConfigs_rep.config_size() != 0) {
                CFdbProtoMsgBuilder builder(propConfigs_rep);
                msg->reply(msg_ref, builder);
            } else {
                LOG_TAG_ERROR("[%s] get prop config fail \n", __func__);
            }
            break;
        }

        case toInt(EViOperation::GET_ALL): {
            ViClientPropValues propValues;
            if (vhlManager_) {
                vhlManager_->getAllPropVal(propValues);
            }
            if (propValues.propvalue_size() != 0) {
                CFdbProtoMsgBuilder builder(propValues);
                msg->reply(msg_ref, builder);
            } else {
                LOG_TAG_ERROR("[%s] get all propvalue fail \n", __func__);
            }
            break;
        }

        default:
            break;
        }
    }

    void onSubscribe(CBaseJob::Ptr &msg_ref) {
        auto msg = castToMessage<CFdbMessage *>(msg_ref);
        const CFdbMsgSubscribeItem *sub_item;
        /* iterate all message id subscribed */
        FDB_BEGIN_FOREACH_SIGNAL(msg, sub_item) {
            FdbMsgCode_t msg_code = sub_item->msg_code();
            std::vector<ViClientPropValue> propVec;
            vhlManager_->get(msg_code, propVec);
            for (auto &propValue : propVec) {
                //LOG_TAG_INFO("onSubscribe: propId = 0x%04x\n", propValue.propid());
                sendVehicleMessage(msg_code, propValue);
            }
        }
        FDB_END_FOREACH_SIGNAL()
    }

private:
    std::shared_ptr<VehicleServiceManager> vhlManager_;
};

// class VehicleServiceFdbus
VehicleServiceFdbus::VehicleServiceFdbus(std::shared_ptr<VehicleServiceManager> vManager) {
    vehicleServer_ = nullptr;
    vhlManager_ = vManager;
    if (vhlManager_) {
        vhlManager_->registerCallback([this](uint16_t propId, ViClientPropValue propVal) {
            //LOG_TAG_INFO("alc callback: property: 0x%x", propId);
            notifyPropertyEvent((int32_t)propId, propVal);
        });
    }
}

VehicleServiceFdbus::~VehicleServiceFdbus() {
    if (vehicleServer_ != nullptr) {
        delete vehicleServer_;
    }
    vehicleServer_ = nullptr;
}

void VehicleServiceFdbus::init() {
    std::string server_name("autolink_vehicle_service");
    std::string url(FDB_URL_SVC);
    url += server_name;
    if (vehicleServer_ == nullptr) {
        /* start fdbus context thread */
        CFdbContext::enableLogger(false);
        FDB_CONTEXT->start();
        CBaseWorker *worker_ptr = &main_worker;
        /* start worker thread */
        worker_ptr->start();
        /* create servers and bind the address: svc://service_name */
        server_name += "_server";
        vehicleServer_ = new VehicleServer(server_name.c_str(), vhlManager_, worker_ptr);
    }
    vehicleServer_->bind(url.c_str());
    LOG_TAG_DEBUG("VehicleServiceFdbus init Server has bind \n");
}

void VehicleServiceFdbus::notifyPropertyEvent(int32_t propId, const ViClientPropValue &propVal) {
    if (vehicleServer_ != nullptr) {
        if ((propVal.valuetype() == ValueType::INT32) || (propVal.valuetype() == ValueType::INT32_VEC)) {
            LOG_TAG_INFO("notifyPropertyEvent: property: 0x%x, value = %d", propId, propVal.data().int32values(0));
        }
        vehicleServer_->sendVehicleMessage(propId, propVal);
    }
}

void VehicleServiceFdbus::notifyWarningEvent(int32_t propId, const ViWarningValue &warnValue) {
    if (vehicleServer_ != nullptr) {
        vehicleServer_->sendWarningMesaage(propId, warnValue);
    }
}

}  // namespace vehicleservice
