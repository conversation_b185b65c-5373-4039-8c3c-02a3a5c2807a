/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#define LOG_TAG "VS::DataDeserialize"
#include "DataDeserialize.h"

#include <log.h>

#include <string>
#include <thread>

#include "ALComVehicleClient.h"
#include "types.h"
#include "log.h"

namespace vehicleservice {
std::unordered_map<uint16_t, bool> DataDeserialize::revPropIdMap_;
bool DataDeserialize::setPropStatus(PduState& state, int32_t propId, ViClientPropValue& propVal) {
    bool isRevNormalValue = false;  //是否已经接收到信号的正常值

    propVal.set_propid(propId);
    propVal.set_areaid(0); //always global
    propVal.set_propstatus(autolink::vehicle::PROPSTATUS::AVAILABLE);
    auto iter = revPropIdMap_.find(propId);
    if (iter != revPropIdMap_.end()) {
        isRevNormalValue = true;
    }
    else {
        isRevNormalValue = false;
    }

    if (state == PduState::ACTIVE) {
            if (!isRevNormalValue) {
            //第一次接收到正常值时，将propId插入接收列表中，表示已经接收到正常值。
            revPropIdMap_.insert({propId, true});
            //LOG_TAG_INFO("INT32 PropId = 0x%04x  first receive normal value\n", propId);
        }
    }
    else {
        LOG_TAG_ERROR("PropId = 0x%04x Data is lost or invalid \n", propId);
        //当没有收到正常值得情况下，发送上去默认值
        if (isRevNormalValue) {
            //已经收到正常值的情况下，将状态设置为ERROR
            propVal.set_propstatus(autolink::vehicle::PROPSTATUS::ERROR);
        }
    }
    return true;
}

bool DataDeserialize::Deserialize(MsgList& msgList, std::vector<ViClientPropValue>& propValList) {
    bool ret = true;
    for (int i = 0; i < msgList.signals_size(); i++) {
        ViClientPropValue propVal;
        PduState state = msgList.signals(i).state();
        MsgSignal signal = msgList.signals(i).signal();
        DataDeserialize::setPropStatus(state, (int32_t)(signal.signal_id()), propVal);
        autolink::vehicle::RawValue* rawValPtr = propVal.mutable_data();
        switch (signal.value_type()) {
            case SignalValueType::TYPE_S8:
            case SignalValueType::TYPE_S16:
            case SignalValueType::TYPE_SINT32:
                propVal.set_valuetype(autolink::vehicle::ValueType::INT32);
                rawValPtr->add_int32values(signal.sint32_value());
                break;
            case SignalValueType::TYPE_U8:
            case SignalValueType::TYPE_U16:
            case SignalValueType::TYPE_UINT32:
                propVal.set_valuetype(autolink::vehicle::ValueType::INT32);
                rawValPtr->add_int32values(signal.uint32_value());
                break;
            case SignalValueType::TYPE_BOOLEAN:
                propVal.set_valuetype(autolink::vehicle::ValueType::INT32);
                rawValPtr->add_int32values(signal.bool_value());
                break;
            default:
                LOG_TAG_ERROR("Comd Broadcast INT32 PropId = 0x%04x type = %d error\n",  signal.signal_id(), signal.value_type());
                propVal.set_propstatus(autolink::vehicle::PROPSTATUS::ERROR);
                break;
        }
        propValList.push_back(propVal);
    }

    for (int i = 0; i < msgList.pdus_size(); i++) {
        int32_t propId = 0;
        MsgPdu pdu = msgList.pdus(i);
        PduState state = pdu.state();
        propId = pdu.pdu_id();

        ViClientPropValue propVal;
        propVal.set_valuetype(autolink::vehicle::ValueType::INT32_VEC);
        autolink::vehicle::RawValue* rawValPtr = propVal.mutable_data();


#if VEHICLE_TYPE == 5
        //重组胎压的数据包，这个是临时方案
        if (propId = 0x1003) {
            std::unordered_map<int32_t, int32_t> pdu_map;
            int value = 0;
            for (int j = 0; j < pdu.signals_size(); j++) {
                switch (pdu.signals(j).value_type()) {
                    case SignalValueType::TYPE_S8:
                    case SignalValueType::TYPE_S16:
                    case SignalValueType::TYPE_SINT32:
                        value = pdu.signals(j).sint32_value();
                        break;
                    case SignalValueType::TYPE_U8:
                    case SignalValueType::TYPE_U16:
                    case SignalValueType::TYPE_UINT32:
                        value = pdu.signals(j).uint32_value();
                        break;
                    case SignalValueType::TYPE_BOOLEAN:
                        value = pdu.signals(j).bool_value();
                        break;
                    default:
                        break;
                }
                pdu_map[pdu.signals(j).signal_id()] = value;
                //LOG_TAG_INFO("deserialize signal id = %04x, value = %d", pdu.signals(j).signal_id(), value);
            }
            rawValPtr->add_int32values(pdu_map[0x8004]); //SNGTIREPSTS
            rawValPtr->add_int32values(pdu_map[0x8008]); //SNGTIREP
            rawValPtr->add_int32values(pdu_map[0x8007]); //SNGTIRET
            rawValPtr->add_int32values(pdu_map[0x8000]); //TIREPOSN
            rawValPtr->add_int32values(pdu_map[0x8001]);  //SNGTIRESIGSTS
            rawValPtr->add_int32values(pdu_map[0x8002]);  //SYSSTS
            rawValPtr->add_int32values(pdu_map[0x8005]);  //TIRETSTSFLG
            rawValPtr->add_int32values(pdu_map[0x8009]);  //SYSDATASTS
            rawValPtr->add_int32values(pdu_map[0x8003]);   //SNGTIRELEAKAGESTS
            rawValPtr->add_int32values(pdu_map[0x8006]);  //SNGTIRESNSRBATTVOLTSTS
            rawValPtr->add_int32values(pdu_map[0x800A]);  //SNSRMATCHSTS
        }
        else {
#endif
            for (int j = 0; j < pdu.signals_size(); j++) {
                switch (pdu.signals(j).value_type()) {
                    case SignalValueType::TYPE_S8:
                    case SignalValueType::TYPE_S16:
                    case SignalValueType::TYPE_SINT32:
                        rawValPtr->add_int32values(pdu.signals(j).sint32_value());
                        break;
                    case SignalValueType::TYPE_U8:
                    case SignalValueType::TYPE_U16:
                    case SignalValueType::TYPE_UINT32:
                        rawValPtr->add_int32values(pdu.signals(j).uint32_value());
                        break;
                    case SignalValueType::TYPE_BOOLEAN:
                        rawValPtr->add_int32values(pdu.signals(j).bool_value());
                        break;
                    default:
                        LOG_TAG_ERROR("Comd Broadcast INT32 PropId = 0x%04x type = %d error\n",  propId, pdu.signals(j).value_type());
                        propVal.set_propstatus(autolink::vehicle::PROPSTATUS::ERROR);
                        break;
                }
            }
#if VEHICLE_TYPE == 5
        }
#endif
        DataDeserialize::setPropStatus(state, propId, propVal);
        propValList.push_back(propVal);
    }
    return true;
}
}
