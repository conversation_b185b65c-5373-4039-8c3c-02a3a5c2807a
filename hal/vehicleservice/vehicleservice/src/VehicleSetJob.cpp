/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#define LOG_TAG "VS::VehicleSetJob"

#include "VehicleSetJob.h"
#include <thread>
#include "log.h"
#include "DataSerialize.h"

namespace vehicleservice {

VehicleSetJob::VehicleSetJob(ViClientPropValue propVal, ICmAdapterBase* adapterBase)
    : propValue_(propVal), cmAdapterBase_(adapterBase) {
}

void VehicleSetJob::setProp() {
    MsgList msgList;
    if (!DataSerialize::Serialize(propValue_, msgList)) {
        LOG_TAG_ERROR("data serial failed");
        return;
    }

    if (cmAdapterBase_ != nullptr) {
        cmAdapterBase_->setPropValue(msgList);
    }
    else {
        LOG_TAG_ERROR("cmAdapterBase_ is nullptr");
    }
}

void VehicleSetJob::run(Ptr &ref) {
    setProp();
}

}  // namespace vehicleservice
