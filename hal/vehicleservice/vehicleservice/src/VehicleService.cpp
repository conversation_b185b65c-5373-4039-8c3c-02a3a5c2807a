#define LOG_TAG "VS::VehicleService"
#include <mutex>
#include <condition_variable>

#include "VehicleServiceManager.h"
#include "VehicleServiceFdbus.h"
#include "kpi_log.h"

using namespace vehicleservice;

int main(int argc, char *argv[]) {
    KPI_LOG_START("vehicleservice");
    KPI_LOG_SCOPE_BEGIN("vehicleservice init");
    std::shared_ptr<VehicleServiceManager> vhlManager = std::make_shared<VehicleServiceManager>();
    std::shared_ptr<VehicleServiceFdbus> vhlFdbus = std::make_shared<VehicleServiceFdbus>(vhlManager);

    vhlFdbus->init();
    KPI_LOG_SCOPE_END("vehicleservice init");
    KPI_LOG_READY("vehicleservice");

    std::mutex mutex_;
    std::condition_variable condition_variable_;
    std::unique_lock<std::mutex> Lock(mutex_);
    condition_variable_.wait(Lock);
}

