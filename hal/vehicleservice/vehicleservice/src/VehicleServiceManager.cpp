
#define LOG_TAG "VS::VehicleServiceManager"
#include <fdbus/fdbus.h>
#include <iostream>
#include <chrono>
#include "VehicleServiceManager.h"
#include "VehiclePropReader.h"
#include "log.h"
#include "AclAdapter.h"
#include "com.autolink.vehicle.pb.h"
#include "types.h"
#include "VehicleSetJob.h"


namespace vehicleservice {
using ipc::fdbus::CBaseJob;
using ipc::fdbus::CBaseWorker;
using namespace autolink::vehicle;
static CBaseWorker setWorker("set_value_thread", FDB_WORKER_DEFAULT, 3000);

void VehicleServiceManager::init() {
    auto now = std::chrono::system_clock::now();
    auto timestamp_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()
    ).count();

    LOG_TAG_INFO("VehicleServiceManager::init, timestamp = %ld", timestamp_ms);
    VehiclePropReader* instance = VehiclePropReader::Instance();
    std::vector<VehiclePropertyConfigData> cfgVec;
    std::vector<VehiclePropertyValData> valVec;
    instance->ConfigParse(cfgVec, valVec);
    LOG_TAG_INFO("VehicleServiceManager::init, cfgVec size = %d, val Vec size = %d", cfgVec.size(), valVec.size());

    initVehicleProp(cfgVec, valVec);
    auto now_end = std::chrono::system_clock::now();
    auto timestamp_ms_end = std::chrono::duration_cast<std::chrono::milliseconds>(
        now_end.time_since_epoch()
    ).count();

    LOG_TAG_INFO("VehicleServiceManager::initVehicleProp end, timestamp = %ld", timestamp_ms_end);
    setWorker.start();
    cmAdapterBase_ = new AclAdapter();
    cmAdapterBase_->init([this](uint16_t propId, ViClientPropValue propVal) {
        if (callback_ != nullptr) {
            checkOutofRange(propVal);
            callback_(propId, propVal);
            write(propVal);
            //LOG_TAG_INFO("alc callback: property: 0x%x", propId);
        }
    });
}

bool VehicleServiceManager::initVehicleProp(std::vector<VehiclePropertyConfigData>& cfgVec, std::vector<VehiclePropertyValData>& cfgValVec) {
    for (auto cfg : cfgVec) {
        ViClientPropConfig propConfig0;
        propConfig0.set_propid(cfg.propId);
        propConfig0.set_areatype((autolink::vehicle::AreaType)cfg.areaType);
        propConfig0.set_valuetype((autolink::vehicle::ValueType)cfg.valueType);
        propConfig0.set_access((autolink::vehicle::Access)cfg.access);
        propConfig0.set_changemode((autolink::vehicle::ChangeMode)cfg.changeMode);
        for (auto areaCfg : cfg.areaVec) {
            autolink::vehicle::AreaConfig * areaConfig0 = propConfig0.add_areaconfigs();
            areaConfig0->set_areaid(areaCfg.areaId);
            areaConfig0->set_minint32value(areaCfg.minValue);
            areaConfig0->set_maxint32value(areaCfg.maxValue);
        }
        propStore_.registerProperty(cfg.osTarget, propConfig0);
    }

    for (auto cfg : cfgValVec) {
        ViClientPropValue propVal0;
        RawValue* valPtr0 =  propVal0.mutable_data();
        propVal0.set_propid(cfg.propId);
        propVal0.set_valuetype((autolink::vehicle::ValueType)cfg.valueType);
        propVal0.set_areaid(cfg.areaId);
        propVal0.set_propstatus(autolink::vehicle::PROPSTATUS::AVAILABLE);
        if (cfg.valueType == autolink::vehicle::ValueType::INT32_VEC) {
            for (int i = 0; i < cfg.size; i++) {
                valPtr0->add_int32values(cfg.initialValue);
            }
        }
        else {
            valPtr0->add_int32values(cfg.initialValue);
        }
        bool ret = propStore_.writeValue(propVal0, true);
        //LOG_TAG_INFO("writeValue: 0x%x, initValue = %d, ret = %d", cfg.propId, cfg.initialValue, ret);
    }
    return true;
}

void VehicleServiceManager::registerCallback(std::function<void(uint16_t, ViClientPropValue)> cb) {
    callback_ = cb;
}

bool VehicleServiceManager::getPropConfigs(int msgId, ViClientPropConfigs& config) {
    if (msgId < toInt(EViMessageId::OS_QNX) || msgId > toInt(EViMessageId::OS_ALL)) {
        LOG_TAG_ERROR("getPropConfigs msgId = %d error \n", msgId);
        return false;
    }
    std::vector<ViClientPropConfig> configVec = propStore_.getAllConfigs(msgId);
    for (auto& value : configVec) {
        config.add_config()->CopyFrom(value);
    }
    return true;
}

bool VehicleServiceManager::get(int propId, int areaId, ViClientPropValue& propVal) {
    const auto* config = propStore_.getConfigOrNull(propId);
    if (config == nullptr) {
        LOG_TAG_ERROR("Failed to set value: config not found, property: 0x%x", propId);
        return false;
    }

    if (checkReadPermission(*config)) {
        std::unique_ptr<ViClientPropValue> ptr = propStore_.readValueOrNull(propId, areaId, 0);
        if (ptr) {
            LOG_TAG_INFO("get value success 0x%x", propId);
            propVal = *ptr;
        }
        else {
            LOG_TAG_ERROR("get value success 0x%x", propId);
        }
        return true;
    }
    else {
        propVal.set_propid(propId);
        propVal.set_valuetype(autolink::vehicle::ValueType::INT32);
        propVal.set_areaid(areaId);
        propVal.set_propstatus(autolink::vehicle::PROPSTATUS::NOT_AVAILABLE);
        propVal.mutable_data()->add_int32values(-1);
        return false;
    }
}

bool VehicleServiceManager::get(int propId, std::vector<ViClientPropValue>& propVec) {
    propVec = propStore_.readValuesForProperty(propId);
    return true;
}

bool VehicleServiceManager::set(ViClientPropValue propVal) {
    int32_t prop = propVal.propid();
    const auto* config = propStore_.getConfigOrNull(prop);
    if (config == nullptr) {
        LOG_TAG_ERROR("Failed to set value: config not found, property: 0x%x", prop);
        return false;
    }

    if (!checkWritePermission(*config)) {
        return false;
    }
    CBaseJob* job = new VehicleSetJob(propVal, cmAdapterBase_);
    setWorker.sendAsync(job);
    return true;
}

bool VehicleServiceManager::write(ViClientPropValue propVal) {
    //LOG_TAG_DEBUG("Property 0x%04x write value", propVal.propid());
    propStore_.writeValue(propVal, true);
    return true;
}

bool VehicleServiceManager::getWarningValue(int32_t propId, ViWarningValue& warnValue) {
    //TODO
    return true;
}

bool VehicleServiceManager::getAllPropVal(ViClientPropValues& propVals) {
    std::vector<ViClientPropValue> proVec = propStore_.readAllValues();
    for (auto& value : proVec) {
        propVals.add_propvalue()->CopyFrom(value);
    }
    return true;
}

bool VehicleServiceManager::checkWritePermission(const ViClientPropConfig &config) {
    if (!(config.access() & Access::WRITE)) {
        LOG_TAG_WARNING("Property 0%x has no write access", config.propid());
        return false;
    } else {
        return true;
    }
}

bool VehicleServiceManager::checkReadPermission(const ViClientPropConfig &config) {
    if (!(config.access() & Access::READ) && !(config.access() & Access::WRITE)) {
        LOG_TAG_WARNING("Property 0%x has no read access", config.propid());
        return false;
    } else {
        return true;
    }
}

bool VehicleServiceManager::checkOutofRange(ViClientPropValue& propVal) {
    const auto* config = propStore_.getConfigOrNull(propVal.propid());
    if (config == nullptr) {
        LOG_TAG_ERROR("checkOutofRange: config not found, property: 0x%x", propVal.propid());
        return false;
    }

    int32_t value = propVal.data().int32values(0);
    int32_t minValue = config->areaconfigs(0).minint32value();
    int32_t maxValue = config->areaconfigs(0).maxint32value();
    if (value < minValue || value > maxValue) {
        propVal.set_propstatus(autolink::vehicle::PROPSTATUS::ERROR);
        LOG_TAG_ERROR("checkOutofRange: out of range, property: 0x%x, value = %d, minValue = %d, maxValue = %d", 
            propVal.propid(), value, minValue, maxValue);
    }
    //LOG_TAG_DEBUG("property: 0x%x, type = %d, value = %d",  propVal.propid(), propVal.valuetype(), value);
    return true;
}

}  // vehicleservice

