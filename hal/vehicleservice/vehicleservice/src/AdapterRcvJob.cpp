/******************************************************************************
/                              Copyright
/------------------------------------------------------------------------------
*******************************************************************************/
#define LOG_TAG "VS::AdapterRcvJob"
#include "AdapterRcvJob.h"

#include <string>
#include <thread>

#include "ALComVehicleClient.h"
#include "types.h"
#include "log.h"
#include "DataDeserialize.h"

#define WARNING_STATUS_OFF (0x00)
#define WARNING_STATUS_ON (0x01)
#define SIGNAL_VALID_BIT (1 << 31)
#define SIGNAL_LOST_BIT (1 << 30)

namespace vehicleservice {

AdapterRcvJob::AdapterRcvJob(MsgList& msgList,
        std::function<void(uint16_t, ViClientPropValue)> cb) {
    msgList_ = msgList;
    callback_ = cb;
}

AdapterRcvJob::~AdapterRcvJob() {
    callback_ = nullptr;
}

void AdapterRcvJob::rcvMsg() {
    bool ret = false;

    std::vector<ViClientPropValue> propList;
    ret = DataDeserialize::Deserialize(msgList_, propList);
    //LOG_TAG_INFO("[%s] propID: %d area id = %d \n", __func__, config.propID, config.areaID);
    if ((callback_ != nullptr) && ret){
        for (auto prop : propList) {
            callback_(prop.propid(), prop);
        }
    }
    else {
        LOG_TAG_ERROR("callback_ is nullptr or deserialize data error, abonded\n");
    }
}

void AdapterRcvJob::run(Ptr& ref) {
    rcvMsg();
}

}  // namespace vehicleservice
