#ifndef _HAL_VEHICLESERVICE_VI_CLIENT_H_
#define _HAL_VEHICLESERVICE_VI_CLIENT_H_

#include <stdint.h>

#include "com.autolink.vehicle.pb.h"

using autolink::vehicle::ViClientPropConfigs;
using autolink::vehicle::ViClientPropValue;
using autolink::vehicle::ViClientPropValues;

class ViCallback {
public:
    ViCallback() = default;
    virtual ~ViCallback() = default;
    virtual void onConnect() = 0;
    virtual void onDisconnect() = 0;
    virtual void onBroadcast(int32_t code, ViClientPropValue& val) = 0;
};

class ViClient final {
public:
    ViClient(ViCallback* callback);
    ~ViClient();
    ViClient(const ViClient&) = delete;
    ViClient& operator=(const ViClient&) = delete;

    void connectService();
    void disConnectService();

    bool subscribe(const int32_t* propList, uint16_t size);
    bool unsubscribe(const int32_t* propList, uint16_t size);

    bool subscribeContinousProp(int32_t prop, int32_t sample);
    bool unsubscribeContinousProp(int32_t prop);

    bool getPropConfig(int32_t msgId, ViClientPropConfigs& result);

    bool getProp(int32_t prop, int32_t areaId, ViClientPropValue& result);
    bool getAllProp(ViClientPropValues& result);

    bool setStringProp(int32_t prop, int32_t areaId, const char* val);
    bool setBoolProp(int32_t prop, int32_t areaId, bool val);
    bool setInt32Prop(int32_t prop, int32_t areaId, int32_t val);
    bool setInt32VecProp(int32_t prop, int32_t areaId, const int32_t* val, uint16_t size);
    bool setInt64Prop(int32_t prop, int32_t areaId, int64_t val);
    bool setInt64VecProp(int32_t prop, int32_t areaId, const int64_t* val, uint16_t size);
    bool setFloatProp(int32_t prop, int32_t areaId, float val);
    bool setFloatVecProp(int32_t prop, int32_t areaId, const float* val, uint16_t size);
    bool setBytesProp(int32_t prop, int32_t areaId, const uint8_t* val, uint16_t size);

    static int SignalName2Id(const char* inSignalName);
    static std::string SignalId2Name(int inSignalID);

private:
    struct ViClientPrivate* p_;
};

#endif  //_HAL_VEHICLESERVICE_VI_CLIENT_H_
