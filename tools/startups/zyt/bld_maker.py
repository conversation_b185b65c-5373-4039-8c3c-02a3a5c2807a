import os
import sys
from pathlib import Path

def generate_description(p1, p2):
    """ 生成描述文件 """
    p1_abs = os.path.abspath(p1)  # 获取 p1 的绝对路径
    lines = []  # 用于存储生成的行

    # 遍历 p1 目录下的 etc, bin, lib 子目录
    path = Path(p1_abs)
    for subdir in path.iterdir():
        subdir=str(subdir).replace(p1_abs,"")
        subdir = subdir.replace(subdir[0], '', 1)  # 替换第一个字符为空字符串，限制替换次数为1
    #for subdir in ['etc', 'bin', 'lib']:
        subdir_path = os.path.join(p1, subdir)
        if os.path.isdir(subdir_path):
            lines.append(f"[uid=0 gid=0 type=dir dperms=0777] /{subdir}\n")
            for root, dirs, files in os.walk(subdir_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, p1_abs)  # 使用 p1 的绝对路径
                    if (os.path.islink(file_path)):
                        real_path = os.readlink(file_path)
                        lines.append(f"[type=link uid=0 gid=0 dperms=0777] /{subdir}/{file}={real_path}\n")
                    else:
                        #lines.append(f"[uid=0 gid=0 dperms=0777] /{subdir}/{file}={p1_abs}/{relative_path}\n")
                        lines.append(f"[uid=0 gid=0 dperms=0777] /{relative_path}={p1_abs}/{relative_path}\n")
        else:
            #lines.append(f"[error=Directory {subdir_path} does not exist]\n")
            lines.append(f"[uid=0 gid=0 dperms=0777] /{subdir}={p1_abs}/{subdir}\n")

    # 写入生成的行以及固定的最后一行
    with open(p2, 'w') as outfile:
        outfile.writelines(lines)
        outfile.write("[num_sectors=*1%:2K]\n")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python script.py <p1> <p2>")
        sys.exit(1)

    p1 = sys.argv[1]
    p2 = sys.argv[2]

    if not os.path.isdir(p1):
        print(f"Error: {p1} is not a valid directory")
        sys.exit(1)

    generate_description(p1, p2)
    print(f"Description file generated at {p2}")
