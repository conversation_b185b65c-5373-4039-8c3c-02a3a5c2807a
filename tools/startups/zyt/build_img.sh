#!/bin/bash
echo "int build img es11"
qnx_sdp_path=/opt/qnx/qnx710_es11
rm -rf autolink
mkdir autolink
cp -r ../../release/baic*/* autolink/
cd ${qnx_sdp_path}
source qnxsdp-env.sh
cd -
QNX_TARGET=${qnx_sdp_path}/host/linux/x86_64/usr/bin:$QNX_TARGET
mkimg_tool="${qnx_sdp_path}/host/linux/x86_64/usr/bin/mkqnx6fsimg"
local_path=`pwd`
app_path=${local_path}/autolink
python3 bld_maker.py ${app_path} autolink.bld
$mkimg_tool autolink.bld autolink.img
