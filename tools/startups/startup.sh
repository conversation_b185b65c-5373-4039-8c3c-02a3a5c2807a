#!/bin/sh
set -e

log_launch()
{
    echo "component_launch $1" > /dev/bmetrics
}

log_launch "ALStartup Start"

# product_name=""
startup_mode=""

if [ -e /mnt/dji/apps/al-qnx-apps ]; then
    echo "al-qnx-apps already exists"
else
    ln -Ps /mnt/autolink/apps/al-qnx-apps/ /mnt/dji/apps/al-qnx-apps
fi

export LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/mnt/autolink/apps/al-qnx-apps/lib:/mnt/autolink/apps/al-qnx-apps/lib/someip
export MACHINE_MANIFEST="/apps/midware_resources/someip/machine_manifest.toml"
export ARA_SERVICE_INSTANCE="/apps/midware_resources/someip/service_instance_icc_qnx.toml"
export EXEC_MANIFEST="/apps/midware_resources/someip/MANIFEST_icc_qnx.toml"

if [ ! -d /mnt/autolink/partitions/log/qnx/AL/fdbus ]; then
    mkdir -p /mnt/autolink/partitions/log/qnx/AL/fdbus
fi

cd /mnt/autolink/apps/al-qnx-apps/bin

log_launch "ALStartup Start Launcher"

/ifs/bin/on -p 25 /mnt/autolink/apps/al-qnx-apps/bin/ham -f /dev/console -V 5 & 

# 将第一个运行参数转换成startup_mode
if [ -z "$1" ]; then
        startup_mode="normal"
        ./launcher --startup_normal /mnt/autolink/apps/al-qnx-apps/apps.list &
        echo "startup.sh default startup" > /tmp/cluster_startup.txt
else
    case "$1" in
        --startup)
            startup_mode="normal"
            ./launcher --startup_normal /mnt/autolink/apps/al-qnx-apps/apps.list &
            echo "startup.sh startup" > /tmp/cluster_startup.txt
            ;;
        --startup_recovery)
            startup_mode="recovery"
            ./launcher --startup_recovery /mnt/autolink/apps/al-qnx-apps/apps.list &
            echo "startup.sh recovery" > /tmp/cluster_startup.txt
            ;;
        *)
            startup_mode="unknown"
            echo "startup.sh error" > /tmp/cluster_startup.txt
            ;;
    esac
fi

pps -m /tmp/pps_al/ -p /mnt/autolink/partitions/persist/ -t 2000 &

#aps modify -m 15 pAVM

# log_launch "ALStartup $product_name:$startup_mode"
log_launch "ALStartup $startup_mode"
