{"apps": [{"name": "name_server", "startup_mode": "normal", "aps": "", "priority": 10, "usr": "cluster", "path": "/mnt/autolink/apps/al-qnx-apps/bin/name_server", "export": []}, {"name": "host_server", "startup_mode": "normal", "aps": "", "priority": 10, "usr": "cluster", "path": "/mnt/autolink/apps/al-qnx-apps/bin/host_server", "export": []}, {"name": "alpowerhal", "startup_mode": "normal", "aps": "", "priority": 10, "usr": "cluster", "path": "/mnt/autolink/apps/al-qnx-apps/bin/alpowerhal", "export": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "startup_mode": "normal", "aps": "", "priority": 10, "usr": "cluster", "path": "/mnt/autolink/apps/al-qnx-apps/bin/aldisplayhal", "monitor": true, "export": []}, {"name": "psis_server", "startup_mode": "normal", "aps": "", "priority": 10, "usr": "cluster", "path": "/mnt/autolink/apps/al-qnx-apps/bin/psis_server", "export": []}, {"name": "alcom", "startup_mode": "normal", "aps": "", "priority": 10, "usr": "cluster", "path": "/mnt/autolink/apps/al-qnx-apps/bin/alcom", "export": [], "dependencies_env": [{"env_key": "FACTORY_MODE", "env_value": "none"}]}, {"name": "logsvc", "startup_mode": "normal", "aps": "", "priority": 10, "usr": "cluster", "args": "-o -j /mnt/autolink/partitions/log/qnx/AL/fdbus,204800,40960", "path": "/mnt/autolink/apps/al-qnx-apps/bin/logsvc", "export": []}, {"name": "variant_server", "startup_mode": "normal", "aps": "", "priority": 30, "usr": "cluster", "path": "/mnt/autolink/apps/al-qnx-apps/bin/variant_server", "export": ["LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/ifs/lib64/camera"]}, {"name": "vehicleservice", "startup_mode": "normal", "aps": "", "priority": 20, "usr": "cluster", "path": "/mnt/autolink/apps/al-qnx-apps/bin/vehicleservice", "export": []}, {"name": "diagserver", "startup_mode": "normal", "aps": "", "priority": 10, "usr": "cluster", "args": "--ip ************* -e", "path": "/mnt/autolink/apps/al-qnx-apps/bin/diagserver", "export": []}, {"name": "cluster_early_midware", "startup_mode": "normal", "aps": "", "priority": 10, "usr": "cluster", "path": "/mnt/autolink/apps/al-qnx-apps/bin/cluster_early_midware", "export": []}, {"name": "anim_midware", "startup_mode": "normal", "aps": "", "priority": 30, "usr": "cluster", "path": "/mnt/autolink/apps/al-qnx-apps/bin/anim_midware", "waitfor": ["/dev/snd/deva-media", "/dev/snd/deva-chime"], "export": []}, {"name": "cluster_midware", "startup_mode": "normal", "aps": "", "priority": 10, "usr": "cluster", "path": "/mnt/autolink/apps/al-qnx-apps/bin/cluster_midware", "waitfor": ["/dev/snd/deva-chime"], "export": []}, {"name": "avas_midware", "startup_mode": "normal", "aps": "", "priority": 10, "usr": "cluster", "path": "/mnt/autolink/apps/al-qnx-apps/bin/avas_midware", "export": ["LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/mnt/autolink/apps/al-qnx-apps/avas"]}, {"name": "EarlyTT", "startup_mode": "normal", "aps": "", "priority": 10, "usr": "cluster", "path": "/mnt/autolink/apps/al-qnx-apps/bin/EarlyTT", "export": []}, {"name": "cluster-hmi", "startup_mode": "normal", "aps": "", "priority": 10, "usr": "cluster", "path": "/mnt/autolink/apps/al-qnx-apps/hmi_resource/cluster-hmi", "work_dir": "/mnt/autolink/apps/al-qnx-apps/hmi_resource", "export": []}, {"name": "diag_midware", "startup_mode": "normal", "aps": "", "priority": 10, "usr": "cluster", "path": "/mnt/autolink/apps/al-qnx-apps/bin/diag_midware", "export": []}, {"name": "baic_uac", "startup_mode": "normal", "aps": "", "priority": 10, "usr": "cluster", "path": "/mnt/autolink/apps/al-qnx-apps/bin/baic_uac", "export": ["LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/mnt/dji/apps/dsar-app/lib:/mnt/dji/apps/middleware/lib"]}, {"name": "rtk_server", "startup_mode": "normal", "aps": "", "priority": 10, "usr": "cluster", "path": "/mnt/autolink/apps/al-qnx-apps/bin/rtk_server", "export": []}, {"name": "facreset_midware", "startup_mode": "normal", "aps": "", "priority": 10, "usr": "cluster", "path": "/mnt/autolink/apps/al-qnx-apps/bin/facreset_midware", "export": ["LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/ifs/lib64/camera"]}, {"name": "sysmonitor", "startup_mode": "normal", "aps": "", "priority": 10, "usr": "cluster", "path": "/mnt/autolink/apps/al-qnx-apps/bin/sysmonitor", "export": []}]}