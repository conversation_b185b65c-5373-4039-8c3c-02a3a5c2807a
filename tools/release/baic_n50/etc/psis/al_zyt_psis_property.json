{"app_param_version": "1.6", "partition_path": {"main": "/dev/disk/ptnr_param", "backup": "/dev/disk/ptnr_param_bk"}, "partition_size": 16777216, "app_param_key_lists": {"cus_cfg.CFG_ALPSIS_VERSION": {"index": 0, "value_size": 16, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "sample.SAMPLE_INT8": {"index": 1, "value_size": 1, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "sample.SAMPLE_UINT8": {"index": 2, "value_size": 1, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "sample.SAMPLE_CHAR": {"index": 3, "value_size": 1, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "sample.SAMPLE_INT16": {"index": 4, "value_size": 2, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "sample.SAMPLE_UINT16": {"index": 5, "value_size": 2, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "sample.SAMPLE_INT32": {"index": 6, "value_size": 4, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "sample.SAMPLE_UINT32": {"index": 7, "value_size": 4, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "sample.SAMPLE_FLOAT": {"index": 8, "value_size": 4, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "sample.SAMPLE_INT64": {"index": 9, "value_size": 4, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "sample.SAMPLE_UINT64": {"index": 10, "value_size": 4, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "sample.SAMPLE_DOUBLE": {"index": 11, "value_size": 4, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "sample.SAMPLE_CUSTOM1": {"index": 12, "value_size": 10, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "sample.SAMPLE_CUSTOM2": {"index": 13, "value_size": 5, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "clusterapp_usr.USR_LANGUAGE_SETTING": {"index": 14, "value_size": 4, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "clusterapp_usr.USR_DAYNIGHT_MODE": {"index": 15, "value_size": 4, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "clusterapp_usr.USR_EXTERNAL_APP_ACTIVE_CODE": {"index": 16, "value_size": 32, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "clusterapp_usr.USR_BATTERY_LEVEL_UNIT": {"index": 17, "value_size": 4, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "clusterapp_usr.USR_TIME_FORMAT": {"index": 18, "value_size": 4, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "car_cfg.CFG_DID_F011": {"index": 19, "value_size": 1024, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server", "/mnt/dji/apps/dsar-app/bin/dji_application"]}, "car_cfg.CFG_VENDER_AL_CAR_VIN": {"index": 20, "value_size": 32, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server", "/mnt/dji/apps/dsar-app/bin/dji_application"]}, "car_cfg.CFG_AL_PSIS_METAS": {"index": 21, "value_size": 16384, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "car_cfg.CFG_AL_PSIS_METAS_SIZE": {"index": 22, "value_size": 4, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "car_cfg.CFG_VENDOR_CAR_ACIC_SN": {"index": 23, "value_size": 32, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server", "/mnt/dji/apps/dsar-app/bin/dji_application"]}, "car_cfg.CFG_VENDOR_OEM_CAR_BOARD_HW_VER": {"index": 24, "value_size": 32, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server", "/mnt/dji/apps/dsar-app/bin/dji_application"]}, "car_cfg.CFG_VENDOR_AL_BUILD_TSPENV": {"index": 25, "value_size": 32, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "clusterapp_usr.USR_HUD_BSD_SWITCH": {"index": 26, "value_size": 4, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "clusterapp_usr.USR_HUD_SWITCH": {"index": 27, "value_size": 4, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "clusterapp_usr.USR_HUD_HEIADJ": {"index": 28, "value_size": 4, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "clusterapp_usr.USR_HUD_ILLADJ": {"index": 29, "value_size": 4, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "clusterapp_usr.USR_HUD_MODSWT": {"index": 30, "value_size": 4, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "clusterapp_usr.USR_HUD_SNOWMODESWTSTS": {"index": 31, "value_size": 4, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "clusterapp_usr.USR_HUD_CRTLANGUAGE": {"index": 32, "value_size": 4, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "sample.SAMPLE_FLOAT1": {"index": 33, "value_size": 8, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}, "sample.SAMPLE_DOUBLE1": {"index": 34, "value_size": 8, "write_permission": ["/mnt/autolink/apps/al-qnx-apps/bin/psis_server"]}}}