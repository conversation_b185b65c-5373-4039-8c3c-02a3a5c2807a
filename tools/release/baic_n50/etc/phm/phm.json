{"name_server": {"ID": 1, "cyclic_restart": false, "retry_times": 20, "?severity": "support value: <PERSON><PERSON><PERSON>, ERROR_WITH_DEPENDENCY, ERROR", "severity": "ERROR", "dependency": []}, "host_server": {"ID": 2, "cyclic_restart": false, "retry_times": 20, "severity": "ERROR", "?dependency": "App,Client1 depend on Service1", "dependency": []}, "alpowerhal": {"ID": 3, "cyclic_restart": false, "retry_times": 5, "severity": "ERROR", "dependency": []}, "aldisplayhal": {"ID": 4, "cyclic_restart": false, "retry_times": 5, "severity": "ERROR", "dependency": []}, "psis_server": {"ID": 5, "cyclic_restart": false, "retry_times": 5, "severity": "ERROR", "dependency": []}, "alcom": {"ID": 6, "cyclic_restart": false, "retry_times": 5, "severity": "ERROR", "dependency": []}, "logsvc": {"ID": 7, "cyclic_restart": false, "retry_times": 5, "severity": "ERROR", "dependency": []}, "variant_server": {"ID": 8, "cyclic_restart": false, "retry_times": 5, "severity": "ERROR", "dependency": []}, "vehicleservice": {"ID": 9, "cyclic_restart": false, "retry_times": 5, "severity": "ERROR", "dependency": []}, "diagserver": {"ID": 10, "cyclic_restart": false, "retry_times": 5, "severity": "ERROR", "dependency": []}, "cluster_early_midware": {"ID": 11, "cyclic_restart": false, "retry_times": 5, "severity": "ERROR", "dependency": []}, "anim_midware": {"ID": 12, "cyclic_restart": false, "retry_times": 5, "severity": "ERROR", "dependency": []}, "cluster_midware": {"ID": 13, "cyclic_restart": false, "retry_times": 5, "severity": "ERROR", "dependency": []}, "EarlyTT": {"ID": 14, "cyclic_restart": false, "retry_times": 5, "severity": "ERROR", "dependency": []}, "cluster-hmi": {"ID": 15, "cyclic_restart": false, "retry_times": 5, "severity": "ERROR", "dependency": []}, "diag_midware": {"ID": 16, "cyclic_restart": false, "retry_times": 5, "severity": "ERROR", "dependency": []}, "baic_uac": {"ID": 17, "cyclic_restart": false, "retry_times": 5, "severity": "ERROR", "dependency": []}, "rtk_server": {"ID": 18, "cyclic_restart": false, "retry_times": 5, "severity": "ERROR", "dependency": []}, "facreset_midware": {"ID": 19, "cyclic_restart": false, "retry_times": 5, "severity": "ERROR", "dependency": []}, "avas_midware": {"ID": 20, "cyclic_restart": false, "retry_times": 5, "severity": "ERROR", "dependency": []}, "sysmonitor": {"ID": 21, "cyclic_restart": false, "retry_times": 5, "severity": "ERROR", "dependency": []}}