[[someip.service_interface]]
interface_deployment_name = "SOMEIP_ADCC_DrivingHmiEnv"
interface_identifier = {name = "ADCC_DrivingHmiEnv", major_contract_version = 1, minor_contract_version = 1}
service_id = 0xAB00
major_version = 1
minor_version = 1
event_group = [
	{id = 1, event = [1,2,3,5,257,258,259,260]},
]

[[someip.service_interface.event]]
name = "NtfDynamicObjectList_Drvg"
id = 1
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "udp"

[[someip.service_interface.event]]
name = "NtfStaticObjectList_Drvg"
id = 2
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "udp"

[[someip.service_interface.event]]
name = "NtfLaneLineListt_Drvg"
id = 3
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "udp"

[[someip.service_interface.event]]
name = "NtfRoadMarkerList"
id = 5
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "udp"

[[someip.service_interface.event]]
name = "NtfLaneLineList_Ext_Drvg"
id = 257
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "udp"

[[someip.service_interface.event]]
name = "NtfRoadStruct_Drvg"
id = 258
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "tcp"

[[someip.service_interface.event]]
name = "NtfFreeSpace"
id = 259
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "tcp"

[[someip.service_interface.event]]
name = "NtfOCCList"
id = 260
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "tcp"

[[someip.service_interface]]
interface_deployment_name = "SOMEIP_ADCC_SensorInfo"
interface_identifier = {name = "ADCC_SensorInfo", major_contract_version = 1, minor_contract_version = 1}
service_id = 0xAB01
major_version = 1
minor_version = 1
event_group = [
	{id = 1, event = [16]},
]

[[someip.service_interface.event]]
name = "NtfSensorStAll"
id = 16
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "udp"

[[someip.service_interface]]
interface_deployment_name = "SOMEIP_ADCC_DrivingFuncInfo"
interface_identifier = {name = "ADCC_DrivingFuncInfo", major_contract_version = 1, minor_contract_version = 1}
service_id = 0xAB02
major_version = 1
minor_version = 1
event_group = [
	{id = 1, event = [257,258,259,260,261,262,263,264,265]},
]

[[someip.service_interface.event]]
name = "NtfDrivingLvl12_Drvg"
id = 257
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "udp"

[[someip.service_interface.event]]
name = "NtfDrivingSafetyFuncInfo_Drvg"
id = 258
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "udp"

[[someip.service_interface.event]]
name = "NtfDrivingLvl2Plus_Drvg"
id = 259
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "udp"

[[someip.service_interface.event]]
name = "NtfCNOAPathDetail"
id = 260
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "tcp"

[[someip.service_interface.event]]
name = "CNOATrainingPathInfo"
id = 261
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "tcp"

[[someip.service_interface.event]]
name = "CNOARepalyPathInfo"
id = 262
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "tcp"

[[someip.service_interface.event]]
name = "NtfOffRoadPathDetail"
id = 263
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "tcp"

[[someip.service_interface.event]]
name = "NtfOffRoadTrainingPathInfo"
id = 264
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "tcp"

[[someip.service_interface.event]]
name = "NtfOffRoadRepalyPathInfo"
id = 265
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "tcp"

[[someip.service_interface]]
interface_deployment_name = "SOMEIP_ADCC_ParkingFuncInfo"
interface_identifier = {name = "ADCC_ParkingFuncInfo", major_contract_version = 1, minor_contract_version = 1}
service_id = 0xAB03
major_version = 1
minor_version = 1
event_group = [
	{id = 1, event = [3,4,5,6,10,257,258,259,260,261]},
]

[[someip.service_interface.event]]
name = "NtfAdvanceParkingInfo"
id = 3
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "udp"

[[someip.service_interface.event]]
name = "NtfHPAPathDetail"
id = 4
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "tcp"

[[someip.service_interface.event]]
name = "NtfHPATrainigPathInfo"
id = 5
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "tcp"

[[someip.service_interface.event]]
name = "NtfVPASelfBuildMapDate"
id = 6
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "tcp"

[[someip.service_interface.event]]
name = "NtfHPARepalyPathInfo"
id = 10
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "tcp"

[[someip.service_interface.event]]
name = "NtfParkingL2GeneralInfo"
id = 257
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "udp"

[[someip.service_interface.event]]
name = "NtfAPARPAInfo_Ext"
id = 258
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "udp"

[[someip.service_interface.event]]
name = "NtfSvsFuncStatus"
id = 259
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "udp"

[[someip.service_interface.event]]
name = "NtfHPASelfBuiltMapInfo"
id = 260
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "tcp"

[[someip.service_interface.event]]
name = "NtfHPAPathDetail_Ext"
id = 261
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "tcp"

[[someip.service_interface]]
interface_deployment_name = "SOMEIP_ADCC_ParkingHmiEnv"
interface_identifier = {name = "ADCC_ParkingHmiEnv", major_contract_version = 1, minor_contract_version = 1}
service_id = 0xAB04
major_version = 1
minor_version = 1
event_group = [
	{id = 1, event = [1,2,3]},
]

[[someip.service_interface.event]]
name = "NtfDynamicObjectList_Prkg"
id = 1
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "udp"

[[someip.service_interface.event]]
name = "NtfStaticObjectList_Prkg"
id = 2
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "udp"

[[someip.service_interface.event]]
name = "NtfParkingSlot_Prkg"
id = 3
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "udp"

[[someip.service_interface]]
interface_deployment_name = "SOMEIP_ADCC_AomMMSt"
interface_identifier = {name = "ADCC_AomMMSt", major_contract_version = 1, minor_contract_version = 1}
service_id = 0xAB05
major_version = 1
minor_version = 1
event_group = [
	{id = 1, event = [1]},
]

[[someip.service_interface.event]]
name = "notifyExtMMResWLnk"
id = 1
max_seg_len = 0
separation_time = 0.0
serializer = "someip"
protocol = "tcp"

[[someip.required_service_instance]]
specifier = "/ICC_QNX_Inst/ADCC_DrivingHmiEnv"
interface_deployment_name = "SOMEIP_ADCC_DrivingHmiEnv"
required_instance_id = 1
required_minor_version = 1
denied_version_list = []
capability_record = []
find_behavior = "exactOrAnyMinorVersion"
unicast_address = "*************"
network_mask = "*************"
tcp_port = 30501
udp_port = 30501

# sd_client_config
max_initial_delay = 0.1
min_initial_delay = 0.0
initial_delay = 0.03
max_initial_repetitions = 3
service_find_ttl = 3

required_event_group = [
  {id = 1, max_delay = 0, min_delay = 0, retry_delay = 0, retry_max = 0, ttl = 5},
]

method_request_props = []


#e2e
e2e_event_protection_props = [
]
e2e_method_protection_props = [
]

#tls_secure_com_props
local_certificate = ""
local_private_key = ""


[[someip.required_service_instance]]
specifier = "/ICC_QNX_Inst/ADCC_SensorInfo"
interface_deployment_name = "SOMEIP_ADCC_SensorInfo"
required_instance_id = 1
required_minor_version = 1
denied_version_list = []
capability_record = []
find_behavior = "exactOrAnyMinorVersion"
unicast_address = "*************"
network_mask = "*************"
tcp_port = 30501
udp_port = 30501

# sd_client_config
max_initial_delay = 0.1
min_initial_delay = 0.0
initial_delay = 0.03
max_initial_repetitions = 3
service_find_ttl = 3

required_event_group = [
  {id = 1, max_delay = 0, min_delay = 0, retry_delay = 0, retry_max = 0, ttl = 5},
]

method_request_props = []


#e2e
e2e_event_protection_props = [
]
e2e_method_protection_props = [
]

#tls_secure_com_props
local_certificate = ""
local_private_key = ""


[[someip.required_service_instance]]
specifier = "/ICC_QNX_Inst/ADCC_DrivingFuncInfo"
interface_deployment_name = "SOMEIP_ADCC_DrivingFuncInfo"
required_instance_id = 1
required_minor_version = 1
denied_version_list = []
capability_record = []
find_behavior = "exactOrAnyMinorVersion"
unicast_address = "*************"
network_mask = "*************"
tcp_port = 30501
udp_port = 30501

# sd_client_config
max_initial_delay = 0.1
min_initial_delay = 0.0
initial_delay = 0.03
max_initial_repetitions = 3
service_find_ttl = 3

required_event_group = [
  {id = 1, max_delay = 0, min_delay = 0, retry_delay = 0, retry_max = 0, ttl = 5},
]

method_request_props = []


#e2e
e2e_event_protection_props = [
{event=257, id=[2869068033], max_len=32768, min_len=96, len=0, profile_config="PSI_ADCC_DrivingFuncInfo_Event_NtfDrivingLvl12_Drvg"},
{event=258, id=[2869068034], max_len=32768, min_len=96, len=0, profile_config="PSI_ADCC_DrivingFuncInfo_Event_NtfDrivingSafetyFuncInfo_Drvg"},
]
e2e_method_protection_props = [
]

#tls_secure_com_props
local_certificate = ""
local_private_key = ""

[[e2e_protection.profile_config]] 
name="PSI_ADCC_DrivingFuncInfo_Event_NtfDrivingLvl12_Drvg"
clear_from_valid_to_invalid=false
data_id_mode=""
max_delta_counter=2
max_error_state_init=1
max_error_state_invalid=1
max_error_state_valid=1
min_ok_state_init=1
min_ok_state_invalid=1
min_ok_state_valid=1
profile_name="PROFILE_04"
window_size_init=2
window_size_invalid=2
window_size_valid=2

[[e2e_protection.profile_config]] 
name="PSI_ADCC_DrivingFuncInfo_Event_NtfDrivingSafetyFuncInfo_Drvg"
clear_from_valid_to_invalid=false
data_id_mode=""
max_delta_counter=2
max_error_state_init=1
max_error_state_invalid=1
max_error_state_valid=1
min_ok_state_init=1
min_ok_state_invalid=1
min_ok_state_valid=1
profile_name="PROFILE_04"
window_size_init=2
window_size_invalid=2
window_size_valid=2


[[someip.required_service_instance]]
specifier = "/ICC_QNX_Inst/ADCC_ParkingFuncInfo"
interface_deployment_name = "SOMEIP_ADCC_ParkingFuncInfo"
required_instance_id = 1
required_minor_version = 1
denied_version_list = []
capability_record = []
find_behavior = "exactOrAnyMinorVersion"
unicast_address = "*************"
network_mask = "*************"
tcp_port = 30501
udp_port = 30501

# sd_client_config
max_initial_delay = 0.1
min_initial_delay = 0.0
initial_delay = 0.03
max_initial_repetitions = 3
service_find_ttl = 3

required_event_group = [
  {id = 1, max_delay = 0, min_delay = 0, retry_delay = 0, retry_max = 0, ttl = 5},
]

method_request_props = []


#e2e
e2e_event_protection_props = [
]
e2e_method_protection_props = [
]

#tls_secure_com_props
local_certificate = ""
local_private_key = ""


[[someip.required_service_instance]]
specifier = "/ICC_QNX_Inst/ADCC_ParkingHmiEnv"
interface_deployment_name = "SOMEIP_ADCC_ParkingHmiEnv"
required_instance_id = 1
required_minor_version = 1
denied_version_list = []
capability_record = []
find_behavior = "exactOrAnyMinorVersion"
unicast_address = "*************"
network_mask = "*************"
tcp_port = 30501
udp_port = 30501

# sd_client_config
max_initial_delay = 0.1
min_initial_delay = 0.0
initial_delay = 0.03
max_initial_repetitions = 3
service_find_ttl = 3

required_event_group = [
  {id = 1, max_delay = 0, min_delay = 0, retry_delay = 0, retry_max = 0, ttl = 5},
]

method_request_props = []


#e2e
e2e_event_protection_props = [
]
e2e_method_protection_props = [
]

#tls_secure_com_props
local_certificate = ""
local_private_key = ""


[[someip.required_service_instance]]
specifier = "/ICC_QNX_Inst/ADCC_AomMMSt"
interface_deployment_name = "SOMEIP_ADCC_AomMMSt"
required_instance_id = 1
required_minor_version = 1
denied_version_list = []
capability_record = []
find_behavior = "exactOrAnyMinorVersion"
unicast_address = "*************"
network_mask = "*************"
tcp_port = 30501
udp_port = 30501

# sd_client_config
max_initial_delay = 0.1
min_initial_delay = 0.0
initial_delay = 0.03
max_initial_repetitions = 3
service_find_ttl = 3

required_event_group = [
  {id = 1, max_delay = 0, min_delay = 0, retry_delay = 0, retry_max = 0, ttl = 5},
]

method_request_props = []


#e2e
e2e_event_protection_props = [
]
e2e_method_protection_props = [
]

#tls_secure_com_props
local_certificate = ""
local_private_key = ""



