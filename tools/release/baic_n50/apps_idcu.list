{"apps": [{"name": "cluster_early_midware", "startup_mode": "normal", "aps": "pCluster", "priority": 10, "usr": "cluster", "path": "/apps/bin/cluster_early_midware", "monitor": true, "export": ["LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/apps/avas"]}, {"name": "EarlyTT", "startup_mode": "normal", "aps": "pCluster", "priority": 10, "usr": "cluster", "path": "/apps/bin/EarlyTT", "monitor": true, "export": []}, {"name": "anim_midware", "startup_mode": "normal", "aps": "pCluster", "priority": 30, "usr": "cluster", "path": "/apps/bin/anim_midware", "monitor": true, "export": []}, {"name": "cluster_midware", "startup_mode": "normal", "aps": "pCluster", "priority": 10, "usr": "cluster", "path": "/apps/bin/cluster_midware", "monitor": true, "export": []}, {"name": "ex_avm_midware", "startup_mode": "normal", "aps": "pAVM", "priority": 10, "usr": "cluster", "path": "/apps/bin/ex_avm_midware", "monitor": true, "export": []}, {"name": "drm_midware", "startup_mode": "normal", "aps": "pAVM", "priority": 10, "usr": "cluster", "path": "/apps/bin/drm_midware", "monitor": true, "export": []}, {"name": "cluster-hmi", "startup_mode": "normal", "aps": "pCluster", "priority": 10, "usr": "cluster", "path": "/apps/hmi_resource/cluster-hmi", "work_dir": "/apps/hmi_resource", "monitor": true, "export": []}, {"name": "avm-hmi", "startup_mode": "normal", "aps": "pAVM", "priority": 10, "usr": "cluster", "path": "/apps/avm_resource/avm-hmi", "work_dir": "/apps/avm_resource", "monitor": true, "export": []}, {"name": "diag_midware", "startup_mode": "normal", "aps": "pCluster", "priority": 10, "usr": "cluster", "path": "/apps/bin/diag_midware", "launch_delay": 5, "monitor": true, "export": []}, {"name": "doip_uds_fdb", "startup_mode": "normal", "aps": "pCluster", "priority": 10, "usr": "cluster", "path": "/apps/bin/doip_uds_fdb", "monitor": true, "export": []}, {"name": "rb_uac", "startup_mode": "normal", "aps": "pCluster", "priority": 10, "usr": "cluster", "path": "/apps/bin/rb_uac", "launch_delay": 10, "monitor": true, "export": ["LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/apps/lib/uac_lib"]}, {"name": "ara_com_daemon", "startup_mode": "normal", "aps": "pCluster", "priority": 10, "usr": "cluster", "args": "--someip", "path": "/apps/bin/ara_com_daemon", "waitfor": "/tmp/vlancfg_ready", "launch_delay": 10, "monitor": true, "export": ["EXEC_MANIFEST=/apps/midware_resources/someip/MANIFEST_ara_com_daemon.toml"]}, {"name": "admsysiApp", "startup_mode": "normal", "aps": "pAndroid", "priority": 10, "usr": "cluster", "args": "-c /apps/etc/liteconfig.ini", "path": "/apps/bin/admsysiApp", "launch_delay": 20, "monitor": true, "export": ["LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/apps/lib/lion_lib"]}, {"name": "dms_midware", "startup_mode": "normal", "aps": "pAVM", "priority": 10, "usr": "cluster", "path": "/apps/bin/dms_midware", "monitor": true, "export": ["LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/apps/lib/dms_lib"]}, {"name": "st_avas_app", "startup_mode": "normal", "aps": "pCluster", "priority": 10, "usr": "cluster", "path": "/apps/avas/st_avas_app", "launch_delay": 5, "monitor": true, "export": ["LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/apps/avas"]}, {"name": "oms_midware", "startup_mode": "normal", "aps": "pAVM", "priority": 10, "usr": "cluster", "path": "/apps/bin/oms_midware", "monitor": true, "export": ["LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/apps/lib/dms_lib"]}, {"name": "idps_nodeManager", "startup_mode": "normal", "aps": "pCluster", "priority": 10, "usr": "cluster", "args": "-c /apps/idps/etc/idps -r /apps/idps/bin -l /calibration/idps/ -b /apps/idps/lib64 -p 3 -D", "path": "/apps/idps/bin/idps_nodeManager", "launch_delay": 3, "monitor": false, "export": ["LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/apps/idps/lib64"]}]}