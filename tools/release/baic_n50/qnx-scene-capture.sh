#!/bin/sh

if [ $# -ne 1 ]; then
	echo "Usage: $(basename ${0}) <capture_out_dir>"
	echo "\t\t\tcapture_out_dir: the absolute path of the directory"
	exit 0
fi

CAPTURE_OUT_DIR="${1}"
CAPTURE_OUT_FILE=${CAPTURE_OUT_DIR}/runtime_scene_out.txt

[ ! -d ${CAPTURE_OUT_DIR} ] && mkdir -p ${CAPTURE_OUT_DIR}

echo "date: `date '+%Y-%m-%d %H:%M:%S'`\n" >> ${CAPTURE_OUT_FILE}

execute_cmd()
{
    cmd=$@
    echo "cmd: ${cmd}" >> ${CAPTURE_OUT_FILE}
    eval  ${cmd} >> ${CAPTURE_OUT_FILE} 2>&1
	echo "" >> ${CAPTURE_OUT_FILE}
}

execute_cmd pidin ar
execute_cmd netstat -an
execute_cmd df -h
execute_cmd cp /dev/bmetrics ${CAPTURE_OUT_DIR}/bmetrics
execute_cmd cp /dev/pdbg/qcore/bootlog ${CAPTURE_OUT_DIR}/bootlog
execute_cmd cp /mnt/partner/apps/al-qnx-apps/socVersion ${CAPTURE_OUT_DIR}/socVersion
