set(AUTOLINK_MIDWARE_FACTORYRESET_SOURCES_DIR ${DEV_ROOT}vendor/autolink/midware_swp/factoryreset)

# file factoryreset_midware
file(GLOB_RECURSE FACTORYRESET_SOURCES ${AUTOLINK_MIDWARE_FACTORYRESET_SOURCES_DIR}/*.cpp)

# src factoryreset_midware
set(FACTORYRESET_MIDWARE_SOURCES
    ${FACTORYRESET_SOURCES}
    ${AUTOLINK_MIDWARE_FACTORYRESET_SOURCES_DIR}/proto/com.autolink.factoryreset.pb.cc
)

# bin facreset_midware
add_executable(facreset_midware ${FACTORYRESET_MIDWARE_SOURCES})
target_link_libraries(facreset_midware al_log al_core al_cm al_pa crypto)
target_compile_options(facreset_midware PRIVATE -g)
target_include_directories(facreset_midware PUBLIC
    ${AUTOLINK_MIDWARE_FACTORYRESET_SOURCES_DIR}/include
    ${AUTOLINK_MIDWARE_FACTORYRESET_SOURCES_DIR}/impl
    ${AUTOLINK_MIDWARE_FACTORYRESET_SOURCES_DIR}/proto
)
