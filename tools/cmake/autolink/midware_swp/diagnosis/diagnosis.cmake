set(AUTOLINK_DIAGNOSIS_MIDWARE_SOURCES_DIR ${DEV_ROOT}vendor/autolink/midware_swp/diagnosis)

# file diag_midware
file(GLOB_RECURSE DIAGNOSIS_SOURCES ${AUTOLINK_DIAGNOSIS_MIDWARE_SOURCES_DIR}/src/*.cpp)

# src diag_midware
set(DIAG_MIDWARE_SOURCES
    ${DIAGNOSIS_SOURCES}
    ${AUTOLINK_DIAGNOSIS_MIDWARE_SOURCES_DIR}/src/diag_midware.cpp
)

set(module_name diag_midware)

# bin diag_midware
add_executable(${module_name}
    ${DIAG_MIDWARE_SOURCES}
    ${DEV_ROOT}/vendor/autolink/api/protobuf/private/idl_gen/com.autolink.cluster.diag.pb.cc
)

target_link_libraries(${module_name} al_log al_core al_cm al_pa crypto)

target_compile_options(${module_name} PRIVATE -g)

target_include_directories(${module_name} PUBLIC
    ${AUTOLINK_DIAGNOSIS_MIDWARE_SOURCES_DIR}/include
    ${DEV_ROOT}/vendor/autolink/api/protobuf/private/idl_gen
)
