option(QNX "cross compile for qnx platform" ON)
option(TEST_INSTALLER "build rb_installer to test communication" ON)
option(DEPLOYMENT "relase env" ON)
option(MANUAL_COMPILE_PROTO "compile proto files manually" ON)

set(UAC_SOURCE_ROOT ${DEV_ROOT}vendor/autolink/midware_swp/uac)
message("UAC_SOURCE_ROOT: " ${UAC_SOURCE_ROOT})
set(UAC_CMAKE_ROOT ${DEV_ROOT}vendor/autolink/tools/cmake/autolink/midware_swp/uac)

# file uac_midware
file(GLOB_RECURSE UAC_SOURCES ${UAC_SOURCE_ROOT}/src/*.cpp)
file(GLOB_RECURSE UAC_IDL_SOURCES ${UAC_SOURCE_ROOT}/idl/idl-gen/*.cc)

# src uac_midware
set(UAC_MIDWARE_SOURCES
    ${UAC_SOURCES}
    ${UAC_IDL_SOURCES}
)



if (DEPLOYMENT)
    add_definitions("-DDEPLOYMENT_ENV=1")
endif (DEPLOYMENT)

include(idl-gen.cmake)

# test platform
if (TEST_INSTALLER)
    include(baic_installer.cmake)
endif (TEST_INSTALLER)

# bin baic_uac
include_directories(${UAC_SOURCE_ROOT}
                    ${DEV_ROOT}/vendor/autolink/external/protobuf
                    ${DEV_ROOT}/vendor/autolink/infrastructure/fdbus/public
                    ${DEV_ROOT}/vendor/autolink/frameworks/log)

add_executable(baic_uac ${UAC_MIDWARE_SOURCES})
target_link_libraries(baic_uac al_log al_core al_cm  fdbus  ota_adapter_so)
target_compile_options(baic_uac PRIVATE -g -D__AB_SIZEOF_POINTER__=8 -D__PROTOCOL_VERCODE__=12)
target_link_directories(baic_uac PUBLIC ${DEV_ROOT}/vendor/autolink/midware_swp/uac/extlib/installer/lib
    ${DEV_ROOT}vendor/autolink/external/protobuf/lib/
    )