set(MANUAL_GEN_DIR ${UAC_SOURCE_ROOT}/idl/idl-gen)
set(SELF_GEN_ROOT ${UAC_SOURCE_ROOT}/idl/idl-gen)

if (MANUAL_COMPILE_PROTO)
    FILE(GLOB_RECURSE PROTO_SOURCES "${UAC_SOURCE_ROOT}/idl/*.proto")

    if (EXT_PROTOBUF)
        set(gen_tool "protoc")
    else()
        set(gen_tool "${DEV_ROOT}vendor/autolink/external/protobuf/bin/linux-x86_64/protoc")
    endif (EXT_PROTOBUF)

    message("idl-gen_cmake: " $ENV{PATH})
    file(MAKE_DIRECTORY ${MANUAL_GEN_DIR})

    set(PROTO_TARGET "build_proto_source")
    add_custom_target(${PROTO_TARGET})

    foreach(idl ${PROTO_SOURCES})
        get_filename_component(base_name ${idl} NAME)
        string(FIND ${base_name} "." SHORTEST_EXT_POS REVERSE)
        string(SUBSTRING ${base_name} 0 ${SHORTEST_EXT_POS} base_name_we)

        set(gen_header ${MANUAL_GEN_DIR}/${base_name_we}.pb.h)
        set(gen_source ${MANUAL_GEN_DIR}/${base_name_we}.pb.cc)

        add_custom_command(OUTPUT ${gen_source} ${gen_header}
            COMMAND ${gen_tool} -I${UAC_SOURCE_ROOT}/idl --cpp_out=${MANUAL_GEN_DIR} --python_out=${MANUAL_GEN_DIR} --java_out=${MANUAL_GEN_DIR} ${idl}
            DEPENDS ${idl}
            WORKING_DIRECTORY ${UAC_SOURCE_ROOT}/idl
        )
        add_custom_target(${base_name_we} ALL DEPENDS ${gen_source} ${gen_header})
        add_dependencies(${PROTO_TARGET} ${base_name_we})
        set_directory_properties(PROPERTIES ADDITIONAL_MAKE_CLEAN_FILES "${gen_header};${gen_source}")
    endforeach()
    set(IDL_GEN_ROOT ${MANUAL_GEN_DIR})
else()
    set(IDL_GEN_ROOT ${SELF_GEN_ROOT})
endif(MANUAL_COMPILE_PROTO)

message("IDL_GEN_ROOT: " ${IDL_GEN_ROOT})
include_directories(${IDL_GEN_ROOT}/..)


