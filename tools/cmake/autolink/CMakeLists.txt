cmake_minimum_required(VERSION 3.0.0)

set(BUILD_VERSION $ENV{BUILD_VERSION})

set(BUILD_PRODUCT $ENV{BUILD_PRODUCT})

set(BUILD_PATH $ENV{BUILD_PATH})

set(VEHICLE_TYPE_STR $ENV{VEHICLE_TYPE})
if (VEHICLE_TYPE_STR STREQUAL "n50" OR "n51")
    set(VEHICLE_TYPE 5)
elseif (VEHICLE_TYPE_STR STREQUAL "n80")
    set(VEHICLE_TYPE 8)
endif()
add_definitions(-DVEHICLE_TYPE=${VEHICLE_TYPE})

option(ENA<PERSON><PERSON>_SCREEN "enable screen function" ON)
option(ENABLE_CAMERA "enable camera function" ON)
option(ENABLE_AUDIO "enable audio function" ON)
option(ENABLE_CARPROPERTY_MANAGER "enable carproperty manager function" ON)
option(ENABLE_PPS "enable pps function" ON)
option(ENA<PERSON>E_VIDC "enable video function" ON)

set(QNX_SDP_HOME $ENV{QNX_SDP_HOME})

if (QNX_SDP_HOME STREQUAL "/opt/qnx/qnx710_es6")
    set(QNX_SDP_VERSION 6)
elseif (QNX_SDP_HOME STREQUAL "/opt/qnx/qnx710_es11")
    set(QNX_SDP_VERSION 11)
elseif (QNX_SDP_HOME STREQUAL "/opt/qnx710")
    set(QNX_SDP_VERSION 8)
else()
    message(FATAL_ERROR "QNX_SDP_HOME is not correctly")
endif()

set(AUTOLINK_MOUNT_DIR $ENV{MOUNT_DIR})
set (PROJECT_CONFIG_DIR ${CMAKE_BINARY_DIR})
configure_file(${CMAKE_SOURCE_DIR}/autolink/autolinkconfig.h.in
               ${PROJECT_CONFIG_DIR}/autolinkconfig.h)

include(${CMAKE_BINARY_DIR}/../../cmake/autolink/conanbuildinfo.cmake)
conan_basic_setup()

set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,-rpath-link ${CONAN_BSP-QCOMM_ROOT}/bsp_qcomm/aarch64le/lib")

# < vendor >
include_directories(${DEV_ROOT}vendor)

# < protobuf >
include_directories(${DEV_ROOT}vendor/autolink/external/protobuf/include)

include(infrastructure.cmake)

add_subdirectory(infrastructure)
add_subdirectory(hal)
add_subdirectory(midware_swp)

include(frameworks.cmake)

include(launchservice.cmake)

include(healthymanager.cmake)

include(midware.cmake)

include(hmi.cmake)

if (ENABLE_CARPROPERTY_MANAGER)
    include(carpropmgrdbgr.cmake)
endif(ENABLE_CARPROPERTY_MANAGER)

if (ENABLE_AUDIO)
    include(audiodbgr.cmake)
endif(ENABLE_AUDIO)

if (ENABLE_CAMERA)
    include(videodbgr.cmake)
endif(ENABLE_CAMERA)

if (ENABLE_SCREEN)
    include(iscreendbgr.cmake)
endif(ENABLE_SCREEN)