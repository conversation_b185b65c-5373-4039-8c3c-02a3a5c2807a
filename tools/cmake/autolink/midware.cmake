set(AUTOLINK_MIDWARE_SOURCES_DIR ${DEV_ROOT}vendor/autolink/midware)
include_directories(${DEV_ROOT}/vendor/autolink/midware)
include_directories(${DEV_ROOT}/vendor/sensetime/dms/include)

# file cluster_early_midware
file(GLOB_RECURSE POWER_SOURCES ${AUTOLINK_MIDWARE_SOURCES_DIR}/powermode/*.cpp)
file(GLOB_RECURSE TELLTALE_SOURCES ${AUTOLINK_MIDWARE_SOURCES_DIR}/telltale/*.cpp)
file(GLOB_RECURSE SCCCOM_SOURCES ${AUTOLINK_MIDWARE_SOURCES_DIR}/scccom/*.cpp)
file(GLOB_RECURSE AVAS_SOURCES ${AUTOLINK_MIDWARE_SOURCES_DIR}/avas/*.cpp)

# file anim_midware
file(GLOB_RECURSE ANIMATION_SOURCES ${AUTOLINK_MIDWARE_SOURCES_DIR}/startupanimation/*.cpp)

# file cluster_midware
file(GLOB_RECURSE CHIME_SOURCES ${AUTOLINK_MIDWARE_SOURCES_DIR}/chime/*.cpp)
file(GLOB_RECURSE DMS_SOURCES ${AUTOLINK_MIDWARE_SOURCES_DIR}/dms/dms/*.cpp)
file(GLOB_RECURSE OMS_SOURCES ${AUTOLINK_MIDWARE_SOURCES_DIR}/dms/oms/*.cpp)
file(GLOB_RECURSE DRIVINGINFO_SOURCES ${AUTOLINK_MIDWARE_SOURCES_DIR}/drivinginfo/*.cpp)
file(GLOB_RECURSE HARDKEY_SOURCES ${AUTOLINK_MIDWARE_SOURCES_DIR}/hardkey/*.cpp)
file(GLOB_RECURSE HMICTRL_SOURCES ${AUTOLINK_MIDWARE_SOURCES_DIR}/hmictrl/*.cpp)
file(GLOB_RECURSE WARNING_SOURCES ${AUTOLINK_MIDWARE_SOURCES_DIR}/warning/*.cpp)
file(GLOB_RECURSE SETTINGS_SOURCES ${AUTOLINK_MIDWARE_SOURCES_DIR}/settings/*.cpp)
file(GLOB_RECURSE HUD_SOURCES ${AUTOLINK_MIDWARE_SOURCES_DIR}/hud/*.cpp)

# file avm_midware
file(GLOB_RECURSE AVM_SOURCES ${AUTOLINK_MIDWARE_SOURCES_DIR}/avm/*.cpp)

# file drm_midware
#file(GLOB_RECURSE DRM_SOURCES ${AUTOLINK_MIDWARE_SOURCES_DIR}/drm/*.cpp)

# src cluster_early_midware
set(CLUSTER_EARLY_MIDWARE_SOURCES
    ${POWER_SOURCES}
    ${TELLTALE_SOURCES}
    ${SCCCOM_SOURCES}
    # ${AVAS_SOURCES}
    ${AUTOLINK_MIDWARE_SOURCES_DIR}/executables/cluster_early_midware.cpp
)

# src anim_midware
set(ANIMATION_MIDWARE_SOURCES
    ${ANIMATION_SOURCES}
    ${AUTOLINK_MIDWARE_SOURCES_DIR}/executables/anim_midware.cpp
)

# src cluster_midware
set(CLUSTER_MIDWARE_SOURCES
    ${ADAS_SOURCES}
    ${WARNING_SOURCES}
    ${DRIVINGINFO_SOURCES}
    ${CHIME_SOURCES}
    ${HARDKEY_SOURCES}
    ${HMICTRL_SOURCES}
    ${SETTINGS_SOURCES}
    ${HUD_SOURCES}
    ${AUTOLINK_MIDWARE_SOURCES_DIR}/executables/cluster_midware.cpp
)

# src dms_midware
set(DMS_MIDWARE_SOURCES
    ${DMS_SOURCES}
    ${AUTOLINK_MIDWARE_SOURCES_DIR}/executables/dms_midware.cpp
)

# src oms_midware
set(OMS_MIDWARE_SOURCES
    ${OMS_SOURCES}
    ${AUTOLINK_MIDWARE_SOURCES_DIR}/executables/oms_midware.cpp
)

# src avm_midware
set(AVM_MIDWARE_SOURCES
    ${AVM_SOURCES}
    ${AUTOLINK_MIDWARE_SOURCES_DIR}/executables/avm_midware.cpp
)

set(AVAS_FUNCTION_SOURCES
    ${AVAS_SOURCES}
    ${AUTOLINK_MIDWARE_SOURCES_DIR}/executables/avas_function.cpp
)

# src drm_midware
# set(DRM_MIDWARE_SOURCES
#     ${DRM_SOURCES}
#     ${AUTOLINK_MIDWARE_SOURCES_DIR}/executables/drm_midware.cpp
# )

# bin cluster_early_midware
# set(AVAS_DIR ${DEV_ROOT}/external/avas/)
# include_directories(${AVAS_DIR}/mq)
# set(AVAS_LIBS ${AVAS_DIR}/mq/libst_message_queue.so)
add_executable(cluster_early_midware ${CLUSTER_EARLY_MIDWARE_SOURCES})
# target_link_libraries(cluster_early_midware al_log al_core al_cm al_pa ${AVAS_LIBS})
target_link_libraries(cluster_early_midware al_log al_core al_cm al_pa)
target_compile_options(cluster_early_midware PRIVATE -g)

set(AVAS_DIR ${DEV_ROOT}/vendor/autolink/supplier/dayintec/avas/avas)
include_directories(${AVAS_DIR})
file(GLOB_RECURSE AVAS_LIBS  ${AVAS_DIR}/*.so)
add_executable(avas_midware ${AVAS_FUNCTION_SOURCES})
target_link_libraries(avas_midware al_log al_core al_cm al_pa ${AVAS_LIBS})
target_compile_options(avas_midware PRIVATE -g)
message("avas_midware end")

# bin anim_midware
add_executable(anim_midware ${ANIMATION_MIDWARE_SOURCES})
target_link_libraries(anim_midware al_log al_core al_cm al_pa)
target_compile_options(anim_midware PRIVATE -g)

# bin cluster_midware
add_executable(cluster_midware ${CLUSTER_MIDWARE_SOURCES})
target_link_libraries(cluster_midware al_log al_core al_cm al_pa)
target_compile_options(cluster_midware PRIVATE -g)
# install(TARGETS ${PROJECT_NAME} RUNTIME DESTINATION ${DEV_ROOT}build/chy_d01/autolink/clusterhmi)

# bin dms_midware
#add_executable(dms_midware ${DMS_MIDWARE_SOURCES})
#target_link_libraries(dms_midware al_log al_core al_cm al_pa)
#target_compile_options(dms_midware PRIVATE -g)
#target_link_directories(dms_midware PUBLIC ${DEV_ROOT}/vendor/sensetime/dms/lib)
#target_link_libraries(dms_midware sensedriver healthpipeline)

# bin oms_midware
#add_executable(oms_midware ${OMS_MIDWARE_SOURCES})
#target_link_libraries(oms_midware al_log al_core al_cm al_pa)
#target_compile_options(oms_midware PRIVATE -g)
#target_link_directories(oms_midware PUBLIC ${DEV_ROOT}/vendor/sensetime/oms/lib)
#target_link_libraries(oms_midware sensecabin)

# bin avm_midware
#add_executable(ex_avm_midware ${AVM_MIDWARE_SOURCES})
#target_link_libraries(ex_avm_midware al_log al_core al_cm al_pa)
#target_compile_options(ex_avm_midware PRIVATE -g)

# bin drm_midware
#add_executable(drm_midware ${DRM_MIDWARE_SOURCES})
#target_link_libraries(drm_midware al_log al_core al_cm al_pa)
#target_compile_options(drm_midware PRIVATE -g)



message("BUILD_PRODUCT, ${BUILD_PRODUCT}")

message("BUILD_PATH, ${BUILD_PATH}")


# lion_vdi
#include(midware_lion_vdi.cmake)