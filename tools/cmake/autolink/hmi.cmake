cmake_minimum_required(VERSION 3.5.1)
project(cluster-hmi)

set(KANZI_HOME $ENV{KANZI_HOME})
set(CMAKE_CROSSCOMPILING TRUE)
message("hmb ${KANZI_HOME}")
set(PROJECT_ROOT ${DEV_ROOT}vendor/autolink/hmi)
if (VEHICLE_TYPE STREQUAL "8")
    set(PROJECT_TYPE N80)
else()
    set(PROJECT_TYPE N50)
endif()
set(PROJECT_LIB ${DEV_ROOT}vendor/autolink/hmi-lib)

#TT build
set(TT_NAME EarlyTT)
set(TT_INC
    ${PROJECT_ROOT}/${PROJECT_TYPE}/EarlyTT_HardRendering/EarlyTT/App
    ${PROJECT_ROOT}/${PROJECT_TYPE}/EarlyTT_HardRendering/EarlyTT/Mapping
    ${PROJECT_LIB}/LibEarlyTT/Controller
    ${PROJECT_LIB}/LibEarlyTT/Transceiver
    ${DEV_ROOT}vendor/autolink/frameworks/cm/carpropertymanager
    ${DEV_ROOT}vendor/autolink/frameworks/cm/carpropertymanager/impl/common/generated
    ${DEV_ROOT}vendor/autolink/frameworks/log
    ${DEV_ROOT}vendor/autolink/frameworks/core
)
file(GLOB_RECURSE TT_SRC_FILES ${PROJECT_ROOT}/${PROJECT_TYPE}/EarlyTT_HardRendering/*.cpp ${PROJECT_LIB}/LibEarlyTT/*.cpp)
add_executable(${TT_NAME} ${TT_SRC_FILES})
target_compile_options(${TT_NAME} PRIVATE
    -O0
    -g
    -ggdb
    -Wall
    -std=gnu++11
)
target_compile_definitions(${TT_NAME} PRIVATE
    __QNX__
    _QNX_SOURCE
)
target_include_directories(${TT_NAME} PRIVATE ${TT_INC})
target_link_libraries(${TT_NAME} screen al_core al_log al_cm)

#TT end

#clusterfunction build
set( HMI_CLUSTER_FUNCTION_LIB_DIR ${PROJECT_LIB}/LibClusterFunction)
set( HMI_CLUSTER_FUNCTION_INC_LIST
    ${HMI_CLUSTER_FUNCTION_LIB_DIR}/Function/inc
    ${HMI_CLUSTER_FUNCTION_LIB_DIR}/Manager/inc
    ${HMI_CLUSTER_FUNCTION_LIB_DIR}/Transceiver/inc
    ${DEV_ROOT}vendor/autolink/frameworks/cm/carpropertymanager
    ${DEV_ROOT}vendor/autolink/frameworks/cm/carpropertymanager/impl/common/generated
    ${DEV_ROOT}vendor/autolink/frameworks/log
    ${DEV_ROOT}vendor/autolink/frameworks/core
)
set( HMI_CLUSTER_FUNCTION_SRC_LIST
    ${HMI_CLUSTER_FUNCTION_LIB_DIR}/Function/src/FunctionAdasInfoManager.cpp
    ${HMI_CLUSTER_FUNCTION_LIB_DIR}/Function/src/FunctionBasicInfoManager.cpp
    ${HMI_CLUSTER_FUNCTION_LIB_DIR}/Function/src/FunctionChargingInfoManager.cpp
    ${HMI_CLUSTER_FUNCTION_LIB_DIR}/Function/src/FunctionFixedInfoManager.cpp
    ${HMI_CLUSTER_FUNCTION_LIB_DIR}/Function/src/FunctionGaugeInfoManager.cpp
    ${HMI_CLUSTER_FUNCTION_LIB_DIR}/Function/src/FunctionHMIStateManager.cpp
    ${HMI_CLUSTER_FUNCTION_LIB_DIR}/Function/src/FunctionInteractionManager.cpp
    ${HMI_CLUSTER_FUNCTION_LIB_DIR}/Function/src/FunctionManagerForm.cpp
    ${HMI_CLUSTER_FUNCTION_LIB_DIR}/Function/src/FunctionMultiMediaInfoManager.cpp
    ${HMI_CLUSTER_FUNCTION_LIB_DIR}/Function/src/FunctionTripComputerInfoManager.cpp
    ${HMI_CLUSTER_FUNCTION_LIB_DIR}/Function/src/FunctionWarningInfoManager.cpp
    ${HMI_CLUSTER_FUNCTION_LIB_DIR}/Manager/src/LogicManager.cpp
    ${HMI_CLUSTER_FUNCTION_LIB_DIR}/Transceiver/src/HMIAdapter.cpp
    ${HMI_CLUSTER_FUNCTION_LIB_DIR}/Transceiver/src/LogicAdapter_API.cpp
)
add_library(ClusterFunction SHARED ${HMI_CLUSTER_FUNCTION_SRC_LIST})
target_include_directories(ClusterFunction PRIVATE ${HMI_CLUSTER_FUNCTION_INC_LIST})
target_link_libraries(ClusterFunction al_core al_log al_cm)
target_compile_definitions(ClusterFunction PUBLIC "LOGICADAPTER_API=")

#cluster-hmi build
set(KANZI_OPTIONS
    -c
    -fexceptions
    -frtti
    -O2
    -fPIC
    -Wall
    -Wextra
    -Wno-overlength-strings
    -fno-strict-aliasing
    -Wno-maybe-uninitialized
    -std=c++11
    -stdlib=libstdc++
)
set(KANZI_DEFINITIONS
    -DKZ_SUPPORT_GRAPHICS_CONTEXT_API_EGL=1
    -DKZ_SUPPORT_GRAPHICS_API_GLES=1
    -DKZ_LINKED_GLES_SYMBOLS=31
    -DKZ_DLOAD_GLES_SYMBOLS=31
    -DNDEBUG
    -DKANZI_FEATURE_3D
    -DQNX
    -DQNX_SCREEN
    -D_QNX_SOURCE
    -D__QNXNTO__
    -D__USE_SLOG__
    -DKZ_USE_C99_SNPRINTF
)
set(KANZI_INC
    ${PROJECT_ROOT}/Plugins/inc
    ${PROJECT_LIB}/LibClusterFunction/Function/inc
    ${PROJECT_LIB}/LibClusterFunction/Manager/inc
    ${PROJECT_LIB}/LibClusterFunction/Transceiver/inc
    ${KANZI_HOME}/Engine/libraries/common/v8/include
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/libpng/include
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/freetype/include
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/harfbuzz/include
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/libunibreak/include
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/icu/include
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/zlib/include
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/libjpeg/include
    #${KANZI_HOME}/Engine/libraries/common/opengl_es_2_0/include/
    ${KANZI_HOME}/Engine/include
    ${KANZI_HOME}/Engine/libraries/common/boost/include
    ${DEV_ROOT}vendor/autolink/frameworks/cm/carpropertymanager
    ${DEV_ROOT}vendor/autolink/frameworks/cm/carpropertymanager/impl/common/generated
    ${DEV_ROOT}vendor/autolink/frameworks/log
    ${DEV_ROOT}vendor/autolink/frameworks/core
)
set(EXEC_SRC_LIST
    ${PROJECT_ROOT}/${PROJECT_TYPE}/Cluster/ClusterFunction/Application/src/clusterfunction.cpp
)
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
add_definitions(-D_GLIBCXX_USE_C99=1)
set(LINK_DIR
    ${PROJECT_ROOT}/Plugins/libs/qnx710_screen_aarch64
    ${KANZI_HOME}/Engine/lib/qnx710_screen_aarch64/ES3_Release
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/freetype/lib
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/harfbuzz/lib
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/libunibreak/lib
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/icu/lib
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/zlib/lib
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/libjpeg/lib
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/libpng/lib
)


add_executable(${PROJECT_NAME} ${EXEC_SRC_LIST})
target_link_directories(${PROJECT_NAME} PRIVATE ${LINK_DIR})
target_compile_options(${PROJECT_NAME} PRIVATE ${KANZI_OPTIONS})
target_include_directories(${PROJECT_NAME} PRIVATE ${KANZI_INC})
set_target_properties(${PROJECT_NAME} PROPERTIES LINK_FLAGS "-stdlib=libstdc++")
target_link_libraries(${PROJECT_NAME} sicudata sicui18n sicuuc unibreak harfbuzz freetype GLESv2 EGL kzappfw kzui kzcoreui kzcore screen m jpeg png z )
target_link_libraries(${PROJECT_NAME} ClusterFunction LogicDataSource_component Alarm_component Localization)
target_link_libraries(${PROJECT_NAME} al_core al_log al_cm)
target_compile_definitions(${PROJECT_NAME} PRIVATE ${KANZI_DEFINITIONS})
target_compile_definitions(${PROJECT_NAME} PUBLIC LOGICADAPTER_API=)
target_compile_definitions(${PROJECT_NAME} PUBLIC LOGICDATASOURCE_API=)

# install(TARGETS ${PROJECT_NAME} RUNTIME DESTINATION ${DEV_ROOT}build/chy_d01/autolink/clusterhmi)
# install(DIRECTORY ${PROJECT_ROOT}/${PROJECT_TYPE}/Cluster/ClusterFunction/Application/bin DESTINATION ${DEV_ROOT}build/chy_d01/autolink/clusterhmi)


#AVM build
#[[ set(HMI_AVM_NAME avm-hmi)
set( HMI_AVM_LIB_DIR ${PROJECT_LIB}/LibAVM)
set( HMI_AVM_INC_LIST
    ${HMI_AVM_LIB_DIR}/Function/inc
    ${HMI_AVM_LIB_DIR}/Manager/inc
    ${HMI_AVM_LIB_DIR}/Transceiver/inc
    ${DEV_ROOT}vendor/autolink/frameworks/cm/carpropertymanager
    ${DEV_ROOT}vendor/autolink/frameworks/cm/carpropertymanager/impl/common/generated
    ${DEV_ROOT}vendor/autolink/frameworks/log
    ${DEV_ROOT}vendor/autolink/frameworks/core
)
set( HMI_AVM_SRC_LIST
    ${HMI_AVM_LIB_DIR}/Function/src/FunctionAVMManager.cpp
    ${HMI_AVM_LIB_DIR}/Function/src/FunctionManagerForm.cpp
    ${HMI_AVM_LIB_DIR}/Manager/src/LogicManager.cpp
    ${HMI_AVM_LIB_DIR}/Transceiver/src/HMIAdapter.cpp
    ${HMI_AVM_LIB_DIR}/Transceiver/src/LogicAdapter_API.cpp
)
set(KANZI_AVM_INC
    ${PROJECT_ROOT}/Plugins/inc
    ${PROJECT_LIB}/LibAVM/Function/inc
    ${PROJECT_LIB}/LibAVM/Manager/inc
    ${PROJECT_LIB}/LibAVM/Transceiver/inc
    ${KANZI_HOME}/Engine/libraries/common/v8/include
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/libpng/include
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/freetype/include
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/harfbuzz/include
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/libunibreak/include
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/icu/include
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/zlib/include
    ${KANZI_HOME}/Engine/libraries/platforms/qnx710-aarch64-gpp/libjpeg/include
    #${KANZI_HOME}/Engine/libraries/common/opengl_es_2_0/include/
    ${KANZI_HOME}/Engine/include
    ${KANZI_HOME}/Engine/libraries/common/boost/include
    ${DEV_ROOT}vendor/autolink/frameworks/cm/carpropertymanager
    ${DEV_ROOT}vendor/autolink/frameworks/log
    ${DEV_ROOT}vendor/autolink/frameworks/core
)
set( AVM_CPP_SRC_LIST
    ${PROJECT_ROOT}/${PROJECT_TYPE}/AVM/Application/src/avm.cpp
)

add_library(FunctionAVM SHARED ${HMI_AVM_SRC_LIST})
target_include_directories(FunctionAVM PRIVATE ${HMI_AVM_INC_LIST})
target_link_libraries(FunctionAVM al_core al_log al_cm)
target_compile_definitions(FunctionAVM PUBLIC "LOGICADAPTER_API=")

add_executable(${HMI_AVM_NAME} ${AVM_CPP_SRC_LIST})
target_link_directories(${HMI_AVM_NAME} PRIVATE ${LINK_DIR})
target_compile_options(${HMI_AVM_NAME} PRIVATE ${KANZI_OPTIONS})
target_include_directories(${HMI_AVM_NAME} PRIVATE ${KANZI_AVM_INC})
set_target_properties(${HMI_AVM_NAME} PROPERTIES LINK_FLAGS "-stdlib=libstdc++")
target_link_libraries(${HMI_AVM_NAME} sicudata sicui18n sicuuc unibreak harfbuzz freetype GLESv2 EGL kzappfw kzui kzcoreui kzcore screen m jpeg png z )
target_link_libraries(${HMI_AVM_NAME} FunctionAVM LogicDataSource_component)
target_link_libraries(${HMI_AVM_NAME} al_core al_log al_cm)
target_compile_definitions(${HMI_AVM_NAME} PRIVATE ${KANZI_DEFINITIONS})
target_compile_definitions(${HMI_AVM_NAME} PUBLIC LOGICADAPTER_API=)
target_compile_definitions(${HMI_AVM_NAME} PUBLIC LOGICDATASOURCE_API=)]]
