cmake_minimum_required(VERSION 3.0.0)

PROJECT(alcom)

set(AUTOLINK_VEHICLE_SOURCE_DIR ${DEV_ROOT}/vendor/autolink/infrastructure/alc/src)

# for auto generation
execute_process(COMMAND python ${AUTOLINK_VEHICLE_SOURCE_DIR}/../version.py ./src)

SET(QNX_BUILD_LIB $ENV{QNX_TARGET}/aarch64le/usr/lib)
# SET(QNX_BUILD_INCLUDE ${PROJECT_ROOT}/external/release/snapdragon/include)
# SET(QNX_BUILD_INCLUDE_AMSS_CORE ${PROJECT_ROOT}/external/release/snapdragon/include/amss/core)
# SET(QCOM_BUILD_LIB ${DEV_ROOT}/vendor/qcom/sdk/lib)
SET(BSP_BUILD_LIB ${DEV_ROOT}/vendor/autolink/infrastructure/alc/public/lib)
SET(QNX_TARGET_BUILD_INCLUDE $ENV{QNX_TARGET}/usr/include)
SET(QNX_TARGET_BUILD_LIB $ENV{QNX_TARGET}/aarch64le/lib)
SET(QNX_TARGET_BUILD_SOCKET_LIB $ENV{QNX_TARGET}/aarch64le/io-sock/lib)

# message("QNX_BUILD_INCLUDE          : ${QNX_BUILD_INCLUDE}")
# message("QNX_BUILD_INCLUDE_AMSS_CORE: ${QNX_BUILD_INCLUDE_AMSS_CORE}")
# message("QNX_BUILD_LIB              : ${QNX_BUILD_LIB}")
# message("QNX_TARGET_BUILD_INCLUDE   : ${QNX_TARGET_BUILD_INCLUDE}")
# message("QNX_TARGET_BUILD_LIB       : ${QNX_TARGET_BUILD_LIB}")

SET(LINK_DIR
    ${PROJECT_ROOT}/external/release/vehicleservice/libs/qnx
    ${QNX_BUILD_LIB}
    ${BSP_BUILD_LIB}
    ${QCOM_BUILD_LIB}
    ${QNX_TARGET_BUILD_LIB}
    ${QNX_TARGET_BUILD_SOCKET_LIB}
    ${DEV_ROOT}/vendor/autolink/external/protobuf/lib
    # ${AUTOLINK_VEHICLE_SOURCE_DIR}/../../vehicle_ipcc
    )

link_directories(${LINK_DIR})
#link_libraries( slog2 protobuf tmel spi-master spi_client gpio_client Ipcc xml2 )
link_libraries( slog2 protobuf fdbus libsocket.so.4 spi_client gpio_client)
add_definitions(-DPLATFORM_QNX)

INCLUDE_DIRECTORIES(
    ${QNX_TARGET_BUILD_INCLUDE}
    ${DEV_ROOT}/vendor/autolink/infrastructure/fdbus/public
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/../public/amss/core
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/../public/amss
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/../public/protocol
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/../public/ipcc
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/../public
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/../vehicle_ipcc/inc
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/../vehicle_ipcc/utils/inc
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/../vehicle_ipcc/cfg
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/../vehicle_ipcc/public/inc
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/../vehicle_ipcc/public/inc/Platform
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/../vehicle_ipcc/public/ipcc
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/common/inc
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/communication/inc/north/fdbus
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/communication/inc/south/spi
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/communication/inc
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/pdu/inc/common
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/pdu/inc/generate
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/pdu/inc/storage
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/pdu/inc
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/ibus/inc
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/protocol/inc/parser/jsoncpp
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/protocol/inc/parser/jsoncpp/json
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/protocol/inc/parser
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/protocol/inc
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/scheduler/inc
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/log/inc
    # ${QNX_BUILD_INCLUDE}
    # ${QNX_BUILD_INCLUDE_AMSS_CORE}
    # ${PROJECT_ROOT}/external/release/vehicleservice/inc
    )

file(GLOB_RECURSE COMM_SRC 
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/**/*.cpp
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/../public/ipcc/*.c
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/../public/amss/*.c
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/../vehicle_ipcc/**/*.c
    ${AUTOLINK_VEHICLE_SOURCE_DIR}/../public/protocol/*.cc)

#exe
ADD_EXECUTABLE(alcom ${COMM_SRC})

#install
install(TARGETS alcom RUNTIME DESTINATION ${DEV_ROOT}/vendor/autolink/tools/build/chy_d01/autolink/infrastructure)

include(debuger.cmake)
#file( COPY ${QNX_BUILD_LIB}/libgpio_client.so DESTINATION ${PROJECT_ROOT}/project/projroot/usr/lib)
#file( COPY ${QNX_BUILD_LIB}/libspi_client.so DESTINATION ${PROJECT_ROOT}/project/projroot/usr/lib)

# install(TARGETS ${QNX_BUILD_LIB}/libgpio_client.so LIBRARY DESTINATION ${PROJECT_ROOT}/project/projroot/usr/lib)
