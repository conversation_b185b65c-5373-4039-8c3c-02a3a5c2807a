set(AUTOLINK_INFRASTRUCTURE_SOURCES_DIR ${DEV_ROOT}vendor/autolink/infrastructure/alc)

file(GLOB_RECURSE HAL_DBGR_IMPL_SOURCES ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/debug/*.cpp)

set(ALC_DBGR_SOURCES
    ${HAL_DBGR_IMPL_SOURCES}
)

set(AUTOLINK_INFRA_ALC_DBG_INCLUDE_DIR
    ${DEV_ROOT}vendor/autolink/infrastructure/fdbus/public
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/debug/channel
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/debug/commander
)

set(AUTOLINK_INFRA_ALC_DBG_LIB_DIR
    ${DEV_ROOT}vendor/autolink/external/protobuf/lib
)

set(AUTOLINK_INFRA_ALC_DBG_LIBS fdbus protobuf)

add_executable(alcdbgr ${ALC_DBGR_SOURCES})

target_include_directories(alcdbgr PUBLIC ${AUTOLINK_INFRA_ALC_DBG_INCLUDE_DIR})
target_link_libraries(alcdbgr PUBLIC ${AUTOLINK_INFRA_ALC_DBG_LIBS})
target_link_directories(alcdbgr PUBLIC ${AUTOLINK_INFRA_ALC_DBG_LIB_DIR})
