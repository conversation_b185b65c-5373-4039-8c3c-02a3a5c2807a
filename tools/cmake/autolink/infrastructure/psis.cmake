# < external >
set(AUTOLINK_INFRA_PSIS_EXTERNAL_INC_DIR
    ${DEV_ROOT}/vendor/autolink/infrastructure/fdbus/public
    ${CONAN_INCLUDE_DIRS}/amss/inc
)

set(AUTOLINK_INFRA_PSIS_EXTERNAL_LIB_DIR
    ${DEV_ROOT}vendor/autolink/external/protobuf/lib
)

# < psis inc >
set(PSIS_INC_DIR
    ${DEV_ROOT}/vendor/autolink/infrastructure/psis
    ${DEV_ROOT}/vendor/autolink/infrastructure/psis/impl
    ${DEV_ROOT}/vendor/autolink/infrastructure/psis/impl/aging
    ${DEV_ROOT}/vendor/autolink/infrastructure/psis/impl/client
    ${DEV_ROOT}/vendor/autolink/infrastructure/psis/impl/com/
    ${DEV_ROOT}/vendor/autolink/infrastructure/psis/impl/com/client
    ${DEV_ROOT}/vendor/autolink/infrastructure/psis/impl/com/server
    ${DEV_ROOT}/vendor/autolink/infrastructure/psis/impl/com/proto
    ${DEV_ROOT}/vendor/autolink/infrastructure/psis/impl/inc/common
    ${DEV_ROOT}/vendor/autolink/infrastructure/psis/impl/inc/generate
    ${DEV_ROOT}/vendor/autolink/infrastructure/psis/impl/io
    ${DEV_ROOT}/vendor/autolink/infrastructure/psis/impl/service
    ${DEV_ROOT}/vendor/autolink/infrastructure/psis/impl/storage
    ${DEV_ROOT}/vendor/autolink/infrastructure/psis/impl/storage/memory
    ${DEV_ROOT}/vendor/autolink/infrastructure/psis/impl/storage/sector
    ${DEV_ROOT}/vendor/autolink/infrastructure/psis/impl/storage/sector/base
    ${DEV_ROOT}/vendor/autolink/infrastructure/psis/impl/util
)
set(AUTOLINK_INFRASTRUCTURE_PSIS_INCLUDE_DIR
    ${AUTOLINK_INFRA_PSIS_EXTERNAL_INC_DIR}
    ${PSIS_INC_DIR}
)
include_directories(${AUTOLINK_INFRASTRUCTURE_PSIS_INCLUDE_DIR})

# < psis_server bin >
file(GLOB_RECURSE AUTOLINK_INFRASTRUCTURE_PSIS_SERVER_SOURCES
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/psis/impl/service/main.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/psis/impl/service/psis_server.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/psis/impl/storage/*.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/psis/impl/io/*.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/psis/impl/util/*.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/psis/impl/com/psis_com.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/psis/impl/com/proto/autolink.infra.psis.pb.cc
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/psis/impl/com/client/*.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/psis/impl/com/server/*.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/psis/impl/aging/*.cpp
)
add_executable(psis_server ${AUTOLINK_INFRASTRUCTURE_PSIS_SERVER_SOURCES})
target_include_directories(psis_server PUBLIC ${AUTOLINK_INFRASTRUCTURE_PSIS_INCLUDE_DIR})
target_link_libraries(psis_server PUBLIC fdbus libsocket.so.4 protobuf zyt_persis al_log)
target_link_directories(psis_server PUBLIC ${AUTOLINK_INFRA_PSIS_EXTERNAL_LIB_DIR})

# < psis_client lib >
file(GLOB_RECURSE AUTOLINK_INFRASTRUCTURE_LIB_PSIS_CLIENT_SOURCES
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/psis/impl/client/psis_client.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/psis/impl/client/psis_client_impl.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/psis/impl/storage/*.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/psis/impl/util/*.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/psis/impl/com/psis_com.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/psis/impl/com/proto/autolink.infra.psis.pb.cc
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/psis/impl/com/client/*.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/psis/impl/com/server/*.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/psis/impl/aging/*.cpp
)
add_library(al_psis SHARED ${AUTOLINK_INFRASTRUCTURE_LIB_PSIS_CLIENT_SOURCES})
target_include_directories(al_psis PUBLIC ${AUTOLINK_INFRASTRUCTURE_PSIS_INCLUDE_DIR})
target_link_libraries(al_psis PUBLIC fdbus libsocket.so.4 protobuf al_log)
target_link_directories(al_psis PUBLIC ${AUTOLINK_INFRA_PSIS_EXTERNAL_LIB_DIR})

# < psis_client bin >
set(AUTOLINK_INFRASTRUCTURE_PSIS_CLIENT_APP_SOURCE
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/psis/impl/client/main.cpp
)
add_executable(psis_client ${AUTOLINK_INFRASTRUCTURE_PSIS_CLIENT_APP_SOURCE})
target_include_directories(psis_client PUBLIC ${AUTOLINK_INFRASTRUCTURE_PSIS_INCLUDE_DIR})
target_link_libraries(psis_client PUBLIC al_psis fdbus libsocket.so.4 protobuf al_log)
target_link_directories(psis_client PUBLIC ${AUTOLINK_INFRA_PSIS_EXTERNAL_LIB_DIR})