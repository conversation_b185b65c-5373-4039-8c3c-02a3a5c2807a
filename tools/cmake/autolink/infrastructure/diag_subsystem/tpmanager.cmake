set(IPLT_PA_DIR ${PLATFORM_INFRASTRUCTURE_DIR}/tpmanager/prebuild/common/include/pa)
set(IPLT_INFRA_CM_DIR ${PLATFORM_INFRASTRUCTURE_DIR}/tpmanager/prebuild/common/include/cm)
set(DIAG_SUBSYSTEM_TPMANAGER_SOURCES_DIR ${PLATFORM_INFRASTRUCTURE_DIR}/tpmanager)

source_directories_append(
        INFRA_DIAG_SUBSYSTEM_TPMANAGER_SOURCE_FILES
        GLOB
        ${DIAG_SUBSYSTEM_TPMANAGER_SOURCES_DIR}/communication/src
        ${DIAG_SUBSYSTEM_TPMANAGER_SOURCES_DIR}/communication/src/doip
        ${DIAG_SUBSYSTEM_TPMANAGER_SOURCES_DIR}/communication/src/vehicle
        ${DIAG_SUBSYSTEM_TPMANAGER_SOURCES_DIR}/core/src
        ${DIAG_SUBSYSTEM_TPMANAGER_SOURCES_DIR}/log/src
    )

set(INFRA_DIAG_SUBSYSTEM_TPMANAGER_INCLUDE_FILES
    ${DIAG_SUBSYSTEM_TPMANAGER_SOURCES_DIR}/adapter/include
    ${DIAG_SUBSYSTEM_TPMANAGER_SOURCES_DIR}/common/include
    ${DIAG_SUBSYSTEM_TPMANAGER_SOURCES_DIR}/communication/include
    ${DIAG_SUBSYSTEM_TPMANAGER_SOURCES_DIR}/communication/include/doip
    ${DIAG_SUBSYSTEM_TPMANAGER_SOURCES_DIR}/communication/include/vehicle
    ${DIAG_SUBSYSTEM_TPMANAGER_SOURCES_DIR}/core/include
    ${DIAG_SUBSYSTEM_TPMANAGER_SOURCES_DIR}/log/include
    )

message("start build tpmanager lib ...")
add_library (
            tpmanager
            SHARED
            ${INFRA_DIAG_SUBSYSTEM_TPMANAGER_SOURCE_FILES}
            )

target_include_directories(
        tpmanager
        PRIVATE
        ${INFRA_DIAG_SUBSYSTEM_TPMANAGER_INCLUDE_FILES}
        ${IPLT_PA_DIR}/platform/include
        ${IPLT_INFRA_CM_DIR}/include
        ${IPLT_INFRA_CM_DIR}/include/container
        ${IPLT_INFRA_CM_DIR}/include/timer
        ${IPLT_INFRA_CM_DIR}/include/task
        ${DIAG_SUBSYSTEM_TPMANAGER_SOURCES_DIR}/prebuild/doip/include/public
    )

target_link_directories(tpmanager PUBLIC ${DIAG_SUBSYSTEM_TPMANAGER_SOURCES_DIR}/prebuild/doip/lib)
target_link_libraries(
                    tpmanager
                    PUBLIC
                    doip
                    socket
                )

#install(TARGETS ${PROJECT_NAME} DESTINATION ${PROJECT_ROOT}/project/projroot/lib)