include(common.cmake)

set(PLATFORM_INFRASTRUCTURE_DIR ${AUTOLINK_INFRASTRUCTURE_DIR})

set(INFRA_FDBUS_INCLUDES_DIR ${PLATFORM_INFRASTRUCTURE_DIR}/fdbus/public)
set(INFRA_FDBUS_LIBS_DIR)
set(INFRA_PROTOBUF_INCLUDES_DIR ${DEV_ROOT}/vendor/autolink/external/protobuf/include)
set(INFRA_PROTOBUF_LIBS_DIR ${DEV_ROOT}/vendor/autolink/external/protobuf/lib)
set(DIAG_SUBSYSTEM_INSTALL_DIR ${AUTOLINK_INSTALL_DIR})

#include(iplt_platform_dependent.cmake)

include(tpmanager.cmake)
add_subdirectory(diagserver)
