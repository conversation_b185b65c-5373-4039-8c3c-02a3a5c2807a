message("iplt platform dependent...")
set(IPLT_PLATFORM_DEPENDENT "")
include(diagcommon.cmake)
include(doip.cmake)

#doip
add_custom_command(
        OUTPUT ${PLATFORM_INFRASTRUCTURE_DIR}/tpmanager/prebuild/doip/lib/libdoip.so
        COMMAND ${CMAKE_COMMAND} -E copy_directory
            ${PLATFORM_INFRASTRUCTURE_DIR}/doip/include/public
            ${PLATFORM_INFRASTRUCTURE_DIR}/tpmanager/prebuild/doip/include/public
        COMMAND ${CMAKE_COMMAND} -E copy
            ${DIAG_SUBSYSTEM_INSTALL_DIR}/lib/libdoip.so
            ${PLATFORM_INFRASTRUCTURE_DIR}/tpmanager/prebuild/doip/lib/libdoip.so
        DEPENDS ${DIAG_SUBSYSTEM_INSTALL_DIR}/lib/libdoip.so
        COMMENT "Copying libdoip.so..."
    )

#diagcommon
add_custom_command(
        OUTPUT ${PLATFORM_INFRASTRUCTURE_DIR}/diagserver/prebuild/common/lib/libdiagcommon.so
        COMMAND ${CMAKE_COMMAND} -E copy_directory
            ${PLATFORM_INFRASTRUCTURE_DIR}/common/pa/platform/include
            ${PLATFORM_INFRASTRUCTURE_DIR}/diagserver/prebuild/common/include/pa/platform/include
        COMMAND ${CMAKE_COMMAND} -E copy_directory
            ${PLATFORM_INFRASTRUCTURE_DIR}/common/cm/include
            ${PLATFORM_INFRASTRUCTURE_DIR}/diagserver/prebuild/common/include/cm/include
        COMMAND ${CMAKE_COMMAND} -E copy_directory
            ${PLATFORM_INFRASTRUCTURE_DIR}/common/cm/cfg/shell/include
            ${PLATFORM_INFRASTRUCTURE_DIR}/diagserver/prebuild/common/include/cm/cfg/shell/include
        COMMAND ${CMAKE_COMMAND} -E copy
            ${DIAG_SUBSYSTEM_INSTALL_DIR}/lib/libdiagcommon.so
            ${PLATFORM_INFRASTRUCTURE_DIR}/diagserver/prebuild/common/lib/libdiagcommon.so
        DEPENDS ${DIAG_SUBSYSTEM_INSTALL_DIR}/lib/libdiagcommon.so
        COMMENT "Copying diagserver libdiagcommon.so..."
    )
add_custom_command(
        OUTPUT ${PLATFORM_INFRASTRUCTURE_DIR}/tpmanager/prebuild/common/lib/libdiagcommon.so
        COMMAND ${CMAKE_COMMAND} -E copy_directory
            ${PLATFORM_INFRASTRUCTURE_DIR}/common/pa/platform/include
            ${PLATFORM_INFRASTRUCTURE_DIR}/tpmanager/prebuild/common/include/pa/platform/include
        COMMAND ${CMAKE_COMMAND} -E copy_directory
            ${PLATFORM_INFRASTRUCTURE_DIR}/common/cm/include
            ${PLATFORM_INFRASTRUCTURE_DIR}/tpmanager/prebuild/common/include/cm/include
        COMMAND ${CMAKE_COMMAND} -E copy_directory
            ${PLATFORM_INFRASTRUCTURE_DIR}/common/cm/cfg/shell/include
            ${PLATFORM_INFRASTRUCTURE_DIR}/diagserver/prebuild/common/include/cm/cfg/shell/include
        COMMAND ${CMAKE_COMMAND} -E copy
            ${DIAG_SUBSYSTEM_INSTALL_DIR}/lib/libdiagcommon.so
            ${PLATFORM_INFRASTRUCTURE_DIR}/tpmanager/prebuild/common/lib/libdiagcommon.so
        DEPENDS ${DIAG_SUBSYSTEM_INSTALL_DIR}/lib/libdiagcommon.so
        COMMENT "Copying tpmanager libdiagcommon.so..."
    )

add_custom_target(
        diagprebuild
        ALL
        DEPENDS ${PLATFORM_INFRASTRUCTURE_DIR}/tpmanager/prebuild/doip/lib/libdoip.so
        DEPENDS ${PLATFORM_INFRASTRUCTURE_DIR}/diagserver/prebuild/common/lib/libdiagcommon.so
        DEPENDS ${PLATFORM_INFRASTRUCTURE_DIR}/tpmanager/prebuild/common/lib/libdiagcommon.so
    )
