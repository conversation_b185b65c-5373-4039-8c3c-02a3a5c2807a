
# DIAG_SERVER_DIR
# diagserver

set(TP_MANAGER_PUB_INCLUDE_FILES
        ${PLATFORM_INFRASTRUCTURE_DIR}/tpmanager/common/include
        ${PLATFORM_INFRASTRUCTURE_DIR}/tpmanager/communication/include
        ${PLATFORM_INFRASTRUCTURE_DIR}/tpmanager/communication/include/doip
        ${PLATFORM_INFRASTRUCTURE_DIR}/tpmanager/communication/include/vehicle
        ${PLATFORM_INFRASTRUCTURE_DIR}/tpmanager/core/include
        ${PLATFORM_INFRASTRUCTURE_DIR}/tpmanager/log/include
    )

set(CORE_DCM_PUB_INCLUDE_FILES
        ${DIAG_SERVER_DIR}/core/dcm/include/public
        ${DIAG_SERVER_DIR}/core/dcm/include/public/mem
        ${DIAG_SERVER_DIR}/core/dcm/include/public/tp
    )

source_directories_append(
        DIAG_SERVER_SOURCE_FILES
        GLOB
        ${DIAG_SERVER_DIR}
        ${DIAG_SERVER_DIR}/communication/src
        ${DIAG_SERVER_DIR}/core/engine/src
        ${DIAG_SERVER_DIR}/log/src
        ${DIAG_SERVER_PROTO_DIR}
    )

add_executable(diagserver ${DIAG_SERVER_SOURCE_FILES})

target_include_directories(
        diagserver
        PRIVATE
        ${DIAG_SERVER_DIR}/communication/include
        ${DIAG_SERVER_DIR}/core/engine/include
        ${DIAG_SERVER_DIR}/log/include
        ${DIAG_SERVER_DIR}/common/include
        ${DIAG_SERVER_PROTO_DIR}
        ${IPLT_PA_DIR}/platform/include
        ${IPLT_INFRA_CM_DIR}/include
        ${IPLT_INFRA_CM_DIR}/include/container
        ${IPLT_INFRA_CM_DIR}/include
        ${IPLT_INFRA_CM_DIR}/include/timer
        ${IPLT_INFRA_CM_DIR}/include/task
        ${IPLT_INFRA_CM_DIR}/include/argparse
        ${TP_MANAGER_PUB_INCLUDE_FILES}
        ${CORE_DCM_PUB_INCLUDE_FILES}
        ${INFRA_FDBUS_INCLUDES_DIR}
        ${INFRA_PROTOBUF_INCLUDES_DIR}
    )

target_link_directories(diagserver PUBLIC ${INFRA_FDBUS_LIBS_DIR})
target_link_directories(diagserver PUBLIC ${INFRA_PROTOBUF_LIBS_DIR})
target_link_libraries(
        diagserver
        PUBLIC
        protobuf
        fdbus
    )

target_link_directories(diagserver PRIVATE ${DIAG_SERVER_DIR}/prebuild/common/lib)
target_link_directories(diagserver PRIVATE ${DIAG_SERVER_DIR}/core/dcm/lib)
target_link_libraries(
        diagserver
        PRIVATE
        diagcommon
        tpmanager
        diagdcm
    )

install(TARGETS diagserver RUNTIME DESTINATION ${DIAG_SERVER_INSTALL_DIR}/bin)
