set(TOOL_DIR ${DIAG_SERVER_DIR}/tools)


# diagserver debugger
set(TOOL_DIAG_DEBUGGER_DIR ${TOOL_DIR}/debugger)

source_directories_append(
        DIAG_SERVER_DEBUGGER_SOURCE_FILES
        GLOB
        ${TOOL_DIAG_DEBUGGER_DIR}
        ${TOOL_DIAG_DEBUGGER_DIR}/core/src
    )

add_executable(diagserver_debugger ${DIAG_SERVER_DEBUGGER_SOURCE_FILES})
target_include_directories(
        diagserver_debugger
        PRIVATE
        ${TOOL_DIAG_DEBUGGER_DIR}/core/include
        ${IPLT_PA_DIR}/platform/include
        ${IPLT_INFRA_CM_DIR}/include/shell
        ${IPLT_INFRA_CM_DIR}/cfg/shell/include
        ${IPLT_INFRA_CM_DIR}/include
        ${IPLT_INFRA_CM_DIR}/include/task
    )

target_link_directories(diagserver_debugger PRIVATE ${PLATFORM_SYSTEM_LD_DIRS})
target_link_directories(diagserver_debugger PRIVATE ${DIAG_SERVER_DIR}/prebuild/common/lib)
target_link_libraries(
        diagserver_debugger
        PRIVATE
        diagcommon
        socket
    )

install(TARGETS diagserver_debugger RUNTIME DESTINATION ${DIAG_SERVER_INSTALL_DIR}/bin/tools)
