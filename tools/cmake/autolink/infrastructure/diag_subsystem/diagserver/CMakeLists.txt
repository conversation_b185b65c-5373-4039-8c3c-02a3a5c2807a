set(DIAG_SERVER_DIR ${PLATFORM_INFRASTRUCTURE_DIR}/diagserver)
set(DIAG_SERVER_INSTALL_DIR ${DIAG_SUBSYSTEM_INSTALL_DIR})
set(DIAG_SERVER_PROTO_DIR ${DIAG_SERVER_DIR}/proto/generate)
set(IPLT_PA_DIR ${PLATFORM_INFRASTRUCTURE_DIR}/diagserver/prebuild/common/include/pa)
set(IPLT_INFRA_CM_DIR ${PLATFORM_INFRASTRUCTURE_DIR}/diagserver/prebuild/common/include/cm)

if(DEFINED IPLT_PLATFORM_DEPENDENT)
include(dcm.cmake)
endif()
include(proxy.cmake)
include(diagserver.cmake)
include(tools.cmake)
include(example.cmake)
