set(EXAMPLE_DIR ${DIAG_SERVER_DIR}/example)

# example dcm
set(EXAMPLE_DCM_DIR ${EXAMPLE_DIR}/dcm)

source_directories_append(
        EXAMPLE_DCM_SOURCE_FILES
        GLOB
        ${EXAMPLE_DCM_DIR}
    )

add_executable(ex_dcm ${EXAMPLE_DCM_SOURCE_FILES})

target_include_directories(
        ex_dcm
        PRIVATE
        ${EXAMPLE_DCM_DIR}/inc
        ${DIAG_SERVER_DIR}/core/dcm/include/public
        ${DIAG_SERVER_DIR}/core/dcm/include/public/mem
        ${DIAG_SERVER_DIR}/core/dcm/include/public/tp
        ${IPLT_PA_DIR}/platform/include
        ${IPLT_INFRA_CM_DIR}/include
        ${IPLT_INFRA_CM_DIR}/include/container
        ${IPLT_INFRA_CM_DIR}/include/cmgr
        ${IPLT_INFRA_CM_DIR}/include/timer
        ${IPLT_INFRA_CM_DIR}/include/task
    )

target_link_directories(ex_dcm PRIVATE ${DIAG_SERVER_DIR}/core/dcm/lib)
target_link_directories(ex_dcm PRIVATE ${DIAG_SERVER_DIR}/prebuild/common/lib)
target_link_libraries(
        ex_dcm
        PRIVATE
        diagcommon
        diagdcm
    )

install(TARGETS ex_dcm RUNTIME DESTINATION ${DIAG_SERVER_INSTALL_DIR}/bin/example)


# example diagproxy
set(EXAMPLE_DIAG_PROXY_DIR ${EXAMPLE_DIR}/diagproxy)
set(PROXY_DIAG_PROXY_DIR ${DIAG_SERVER_DIR}/proxy/diag)

source_directories_append(
        EXAMPLE_DIAG_PROXY_SOURCE_FILES
        GLOB
        ${EXAMPLE_DIAG_PROXY_DIR}
    )

add_executable(ex_diag ${EXAMPLE_DIAG_PROXY_SOURCE_FILES})

target_include_directories(
        ex_diag
        PRIVATE
        ${EXAMPLE_DIAG_PROXY_DIR}/inc
        ${PROXY_DIAG_PROXY_DIR}/include
        ${IPLT_PA_DIR}/platform/include
        ${IPLT_INFRA_CM_DIR}/include
        ${IPLT_INFRA_CM_DIR}/include/shell
        ${IPLT_INFRA_CM_DIR}/cfg/shell/include
    )

target_link_directories(ex_diag PRIVATE ${DIAG_SERVER_DIR}/prebuild/common/lib)
target_link_libraries(
                     ex_diag
                     PRIVATE
                     diagcommon
                     diagproxy
                     )

install(TARGETS ex_diag RUNTIME DESTINATION ${DIAG_SERVER_INSTALL_DIR}/bin/example)


# example dtcproxy
set(EXAMPLE_DTC_PROXY_DIR ${EXAMPLE_DIR}/dtcproxy)
set(PROXY_DTC_PROXY_DIR ${DIAG_SERVER_DIR}/proxy/dtc)

source_directories_append(
        EXAMPLE_DTC_PROXY_SOURCE_FILES
        GLOB
        ${EXAMPLE_DTC_PROXY_DIR}
    )

add_executable(ex_dtc ${EXAMPLE_DTC_PROXY_SOURCE_FILES})

target_include_directories(
        ex_dtc
        PRIVATE
        ${EXAMPLE_DTC_PROXY_DIR}/inc
        ${PROXY_DTC_PROXY_DIR}/include
        ${IPLT_PA_DIR}/platform/include
        ${IPLT_INFRA_CM_DIR}/include
        ${IPLT_INFRA_CM_DIR}/include/shell
        ${IPLT_INFRA_CM_DIR}/cfg/shell/include
        ${IPLT_INFRA_CM_DIR}/include/task
    )

target_link_directories(ex_dtc PRIVATE ${DIAG_SERVER_DIR}/prebuild/common/lib)
target_link_libraries(
                     ex_dtc
                     PRIVATE
                     diagcommon
                     dtcproxy
                     )

install(TARGETS ex_dtc RUNTIME DESTINATION ${DIAG_SERVER_INSTALL_DIR}/bin/example)
