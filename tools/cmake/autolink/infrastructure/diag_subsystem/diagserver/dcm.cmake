set(DIAG_SERVER_CORE_DIR ${DIAG_SERVER_DIR}/core)

# dcm
if(DEFINED IPLT_PLATFORM_DEPENDENT)
set(CORE_DCM_DIR ${PLATFORM_INFRASTRUCTURE_DIR}/dcm)
add_custom_command(
        OUTPUT ${PLATFORM_INFRASTRUCTURE_DIR}/diagserver/core/dcm/lib/libdiagdcm.so
        COMMAND ${CMAKE_COMMAND} -E copy_directory
            ${CORE_DCM_DIR}/include/public
            ${PLATFORM_INFRASTRUCTURE_DIR}/diagserver/core/dcm/include/public
        COMMAND ${CMAKE_COMMAND} -E copy
            ${DIAG_SUBSYSTEM_INSTALL_DIR}/lib/libdiagdcm.so
            ${PLATFORM_INFRASTRUCTURE_DIR}/diagserver/core/dcm/lib/libdiagdcm.so
        DEPENDS ${DIAG_SUBSYSTEM_INSTALL_DIR}/lib/libdiagdcm.so
        COMMENT "Copying libdiagdcm.so..."
    )

add_custom_target(
        diag_core_dcm_prebuild
        ALL
        DEPENDS ${PLATFORM_INFRASTRUCTURE_DIR}/diagserver/core/dcm/lib/libdiagdcm.so
    )
else()
set(CORE_DCM_DIR ${DIAG_SERVER_CORE_DIR}/dcm)
endif()

source_directories_append(
        CORE_DCM_SOURCE_FILES
        GLOB
        ${CORE_DCM_DIR}/cfg/src
        ${CORE_DCM_DIR}/cfg/src/services
        ${CORE_DCM_DIR}/src
        ${CORE_DCM_DIR}/src/configure
        ${CORE_DCM_DIR}/src/msg
        ${CORE_DCM_DIR}/src/protocol
        ${CORE_DCM_DIR}/src/protocol/dsl
        ${CORE_DCM_DIR}/src/protocol/dscl
        ${CORE_DCM_DIR}/src/protocol/dspl
        ${CORE_DCM_DIR}/src/protocol/dspl/services
        ${CORE_DCM_DIR}/src/tp
        ${CORE_DCM_DIR}/src/mem
    )

set(CORE_DCM_INCLUDE_FILES
        ${CORE_DCM_DIR}/cfg/include
        ${CORE_DCM_DIR}/cfg/include/services
        ${CORE_DCM_DIR}/include
        ${CORE_DCM_DIR}/include/public
        ${CORE_DCM_DIR}/include/public/tp
        ${CORE_DCM_DIR}/include/public/mem
        ${CORE_DCM_DIR}/include/configure
        ${CORE_DCM_DIR}/include/msg
        ${CORE_DCM_DIR}/include/protocol
        ${CORE_DCM_DIR}/include/protocol/dsl
        ${CORE_DCM_DIR}/include/protocol/dscl
        ${CORE_DCM_DIR}/include/protocol/dspl
        ${CORE_DCM_DIR}/include/protocol/dspl/services
        ${CORE_DCM_DIR}/include/tp
        ${CORE_DCM_DIR}/include/mem
    )

message("start build core dcm lib ... ${PROJECT_PLATFORM}")

add_library (
        diagdcm
        SHARED
        ${CORE_DCM_SOURCE_FILES}
    )

target_include_directories(
        diagdcm
        PRIVATE
        ${CORE_DCM_INCLUDE_FILES}
        ${IPLT_PA_DIR}/platform/include
        ${IPLT_INFRA_CM_DIR}/include
        ${IPLT_INFRA_CM_DIR}/include/container
        ${IPLT_INFRA_CM_DIR}/include/timer
        ${IPLT_INFRA_CM_DIR}/include/task
    )

if(DEFINED IPLT_PLATFORM_DEPENDENT)
add_dependencies(diagdcm diag_core_dcm_prebuild)
endif()

install(TARGETS diagdcm DESTINATION ${DIAG_SERVER_INSTALL_DIR}/lib)
