
set(PROXY_DIR ${DIAG_SERVER_DIR}/proxy)

# diag proxy
set(PROXY_DIAG_PROXY_DIR ${PROXY_DIR}/diag)

source_directories_append(
        DIAG_PROXY_SOURCE_FILES
        GLOB
        ${DIAG_SERVER_PROTO_DIR}
        ${PROXY_DIAG_PROXY_DIR}/src
    )

add_library (
        diagproxy
        SHARED
        ${DIAG_PROXY_SOURCE_FILES}
    )

target_include_directories(
        diagproxy
        PRIVATE
        ${DIAG_SERVER_PROTO_DIR}
        ${PROXY_DIAG_PROXY_DIR}/include
        ${INFRA_FDBUS_INCLUDES_DIR}
        ${INFRA_PROTOBUF_INCLUDES_DIR}
    )

target_link_directories(diagproxy PRIVATE ${INFRA_FDBUS_LIBS_DIR})
target_link_directories(diagproxy PUBLIC ${INFRA_PROTOBUF_LIBS_DIR})
target_link_libraries(
    diagproxy
    PUBLIC
    protobuf
    fdbus
)

install(TARGETS diagproxy DESTINATION ${DIAG_SERVER_INSTALL_DIR}/lib)

# dtc proxy
set(PROXY_DTC_PROXY_DIR ${PROXY_DIR}/dtc)

source_directories_append(
        DTC_PROXY_SOURCE_FILES
        GLOB
        ${DIAG_SERVER_PROTO_DIR}
        ${PROXY_DTC_PROXY_DIR}/src
    )

add_library (
        dtcproxy
        SHARED
        ${DTC_PROXY_SOURCE_FILES}
    )

target_include_directories(
        dtcproxy
        PRIVATE
        ${DIAG_SERVER_PROTO_DIR}
        ${PROXY_DTC_PROXY_DIR}/include
        ${INFRA_FDBUS_INCLUDES_DIR}
        ${INFRA_PROTOBUF_INCLUDES_DIR}
    )

target_link_directories(dtcproxy PRIVATE ${INFRA_FDBUS_LIBS_DIR})
target_link_directories(dtcproxy PUBLIC ${INFRA_PROTOBUF_LIBS_DIR})
target_link_libraries(
    dtcproxy
    PUBLIC
    protobuf
    fdbus
)

install(TARGETS dtcproxy DESTINATION ${DIAG_SERVER_INSTALL_DIR}/lib)
