
##########################################################################
#                  Include buildCentral common rules                     #
##########################################################################
set(PROJECT_PLATFORM PLATFORM_QNX)

macro(generate_files_list)
    foreach(prefix *.c *.cpp *.cxx *.cc ${CMAKE_C_SOURCE_FILE_EXTENSIONS} ${CMAKE_CXX_SOURCE_FILE_EXTENSIONS} ${CMAKE_ASM_SOURCE_FILE_EXTENSIONS})
        list(APPEND ${ARGV0} ${ARGV1}/*.${prefix})
    endforeach()
endmacro()

macro(source_directories_append)
    set(dirs ${ARGV})
    list(REMOVE_AT dirs 0 1)
    unset(glob_list)
    foreach(dir ${dirs})
        generate_files_list(glob_list ${dir})
        file(${ARGV1} src_list LIST_DIRECTORIES false ${glob_list})
        list(APPEND ${ARGV0} ${src_list})
    endforeach()
endmacro()

add_definitions(-DPLATFORM_PRJOECT_BAIC_ZYT)

set(AUTOLINK_INSTALL_DIR
        ${DEV_ROOT}/vendor/autolink/tools/build/${AUTOLINK_PLATFORM_PROJECT_NAME}/autolink
    )
link_directories(${AUTOLINK_INSTALL_DIR}/lib)

set(AUTOLINK_INFRASTRUCTURE_DIR ${DEV_ROOT}/vendor/autolink/infrastructure)
set(PLATFORM_SYSTEM_LD_DIRS
        /opt/qnx/qnx710_hqx4560/target/qnx7/aarch64le/io-sock/lib
        /opt/qnx/qnx710_hqx4560/target/qnx7/aarch64le/lib
    )
