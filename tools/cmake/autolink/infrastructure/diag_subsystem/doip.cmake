set(INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR ${PLATFORM_INFRASTRUCTURE_DIR}/doip)

source_directories_append(
                            INFRA_DIAG_SUBSYSTEM_DOIP_SOURCE_FILES
                            GLOB
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/src/configure
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/src/endpoint
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/src/engine
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/src/msg
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/src/net
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/src/protocol
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/src/protocol/entity
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/src/protocol/entity/handler
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/src/protocol/entity/state
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/src/protocol/tester
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/src/protocol/tester/handler
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/src/protocol/tester/state
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/src/schedule
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/src/tp
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/src/log
                        )

set(INFRA_DIAG_SUBSYSTEM_DOIP_INCLUDE_FILES
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/include
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/include/configure
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/include/endpoint
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/include/engine
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/include/msg
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/include/net
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/include/protocol
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/include/protocol/entity
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/include/protocol/entity/handler
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/include/protocol/entity/state
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/include/protocol/tester
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/include/protocol/tester/handler
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/include/protocol/tester/state
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/include/protocol/handler
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/include/protocol/state
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/include/schedule
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/include/tp
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/include/public
                            ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCES_DIR}/include/log
                        )

message("start build doip lib ...")

add_library (
        doip
        SHARED
        ${INFRA_DIAG_SUBSYSTEM_DOIP_SOURCE_FILES}
    )

target_include_directories(
        doip
        PUBLIC
        ${INFRA_DIAG_SUBSYSTEM_DOIP_INCLUDE_FILES}
        ${INFRA_CM_INCLUDE_FILES}
        ${PA_INCLUDE_FILES}
    )

#install(TARGETS ${PROJECT_NAME} DESTINATION usr/lib)
#install(TARGETS ${PROJECT_NAME} DESTINATION ${PROJECT_ROOT}/project/projroot/lib)