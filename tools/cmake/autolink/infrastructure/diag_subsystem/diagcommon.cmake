set(IPLT_PA_DIR ${PLATFORM_INFRASTRUCTURE_DIR}/common/pa)
set(IPLT_INFRA_CM_DIR ${PLATFORM_INFRASTRUCTURE_DIR}/common/cm)

source_directories_append(
                PA_SOURCE_FILES
                GLOB
                ${IPLT_PA_DIR}/platform
            )

set(PA_INCLUDE_FILES
                ${IPLT_PA_DIR}/platform/include
            )

add_definitions(-DSHELL_CFG_USER="shell_cfg_user.h")
source_directories_append(
        INFRA_CM_SOURCE_FILES
        GLOB
        ${IPLT_INFRA_CM_DIR}/src/container/list
        ${IPLT_INFRA_CM_DIR}/src/task
        ${IPLT_INFRA_CM_DIR}/src/mem_pool
        ${IPLT_INFRA_CM_DIR}/src/buffer_stream
        ${IPLT_INFRA_CM_DIR}/src/crc
        ${IPLT_INFRA_CM_DIR}/src/timer
        ${IPLT_INFRA_CM_DIR}/src/cmgr
        ${IPLT_INFRA_CM_DIR}/src/shell
        ${IPLT_INFRA_CM_DIR}/cfg/shell
        ${IPLT_INFRA_CM_DIR}/src/argparse
    )

set(INFRA_CM_INCLUDE_FILES
                ${IPLT_INFRA_CM_DIR}/include/container
                ${IPLT_INFRA_CM_DIR}/include
                ${IPLT_INFRA_CM_DIR}/include/task
                ${IPLT_INFRA_CM_DIR}/include/timer
                ${IPLT_INFRA_CM_DIR}/include/cmgr
                ${IPLT_INFRA_CM_DIR}/include/shell
                ${IPLT_INFRA_CM_DIR}/cfg/shell/include
            )

add_library(
            diagcommon
            SHARED
            ${PA_SOURCE_FILES}
            ${INFRA_CM_SOURCE_FILES}
            )

target_include_directories(
                            diagcommon
                            PRIVATE
                            ${INFRA_CM_INCLUDE_FILES}
                            ${PA_INCLUDE_FILES}
                            /opt/qnx/qnx710_hqx4560/target/qnx7/usr/include
                        )

if(DEFINED IPLT_PLATFORM_DEPENDENT)
add_dependencies(diagcommon diagprebuild)
endif()
