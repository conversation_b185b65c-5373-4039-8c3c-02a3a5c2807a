set(AUTOLINK_DOIP_SOURCES_DIR ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/doip)

# protocol stack
source_directories_append(
    AUTOLINK_DOIP_SOURCES
    GLOB
    ${AUTOLINK_DOIP_SOURCES_DIR}/source/Abup_com/src
    ${AUTOLINK_DOIP_SOURCES_DIR}/source/DIAG/Core
    ${AUTOLINK_DOIP_SOURCES_DIR}/source/DIAG/IL
    ${AUTOLINK_DOIP_SOURCES_DIR}/source/DIAG/IL/SA
    ${AUTOLINK_DOIP_SOURCES_DIR}/source/DoIP
    ${AUTOLINK_DOIP_SOURCES_DIR}/source/Q_DoIP
    ${AUTOLINK_DOIP_SOURCES_DIR}/source/Q_DoIP_FOTA
    ${AUTOLINK_DOIP_SOURCES_DIR}/source/Timer
    ${AUTOLINK_DOIP_SOURCES_DIR}/source/Utility
)
list(APPEND AUTOLINK_DOIP_SOURCES ${AUTOLINK_DOIP_SOURCES_DIR}/source/uds_ota_main.cpp)

add_executable(doip_uds_fdb ${AUTOLINK_DOIP_SOURCES})

target_include_directories(
                doip_uds_fdb
                PRIVATE
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/DIAG/Core
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/DIAG/IL
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/DIAG/IL/SA
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/DoIP
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/Include
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/NL/IL
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/Q_DoIP
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/Q_DoIP_FOTA
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/Utility
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/Abup_com/inc
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/Timer
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/Fdbus

                ${DEV_ROOT}/vendor/bosch/target/usr/include
                ${QNX_SDP_HOME}/target/qnx7/usr/include
            )

target_compile_options(doip_uds_fdb PRIVATE -fPIC -Wall -O0)

target_link_directories(doip_uds_fdb PUBLIC ${DEV_ROOT}/vendor/bosch/target/usr/lib)
target_link_directories(doip_uds_fdb PUBLIC ${QNX_SDP_HOME}/target/qnx7/aarch64le/io-sock/lib)
target_link_directories(doip_uds_fdb PUBLIC ${QNX_SDP_HOME}/target/qnx7/aarch64le/lib)

target_link_libraries(
    doip_uds_fdb
    c 
    socket
    fdbus
    slog2
)

# protocol stack test
add_executable(doip_uds_fdb_test ${AUTOLINK_DOIP_SOURCES_DIR}/source/Fdbus/fdb_cltx_uds_server.cpp)

target_include_directories(
                doip_uds_fdb_test
                PRIVATE
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/DIAG/Core
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/DIAG/IL
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/DIAG/IL/SA
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/DoIP
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/Include
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/NL/IL
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/Q_DoIP
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/Q_DoIP_FOTA
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/Utility
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/Abup_com/inc
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/Timer
                ${AUTOLINK_DOIP_SOURCES_DIR}/source/Fdbus

                ${DEV_ROOT}/vendor/bosch/target/usr/include
                ${QNX_SDP_HOME}/target/qnx7/usr/include
            )

target_link_directories(doip_uds_fdb_test PUBLIC ${DEV_ROOT}/vendor/bosch/target/usr/lib)
target_link_directories(doip_uds_fdb_test PUBLIC ${QNX_SDP_HOME}/target/qnx7/aarch64le/io-sock/lib)
target_link_directories(doip_uds_fdb_test PUBLIC ${QNX_SDP_HOME}/target/qnx7/aarch64le/lib)

target_link_libraries(
    doip_uds_fdb_test
    c 
    socket
    fdbus
)