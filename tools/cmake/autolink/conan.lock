{"graph_lock": {"nodes": {"0": {"options": "bsp-qcomm:shared=False", "requires": ["1"], "path": "conanfile.txt", "context": "host"}, "1": {"ref": "bsp-qcomm/7.99.1-baic.15@ZYT/release", "options": "shared=False", "package_id": "eefefbb60bb187b4316ff794be41c53c5db63b97", "prev": "0", "context": "host"}}, "revisions_enabled": false}, "version": "0.4", "profile_host": "[settings]\narch=arm64\nbuild_type=Release\ncompiler=gcc\ncompiler.libcxx=libstdc++11\ncompiler.version=8.3\nos=qnx\nos.platform=sa8775\nos.toolchain_version=7.1\nos.type=safety\nsdk_version=sdk11\n[options]\n[build_requires]\n[env]\nAR=/opt/qcom_build/qos225_8775p_build_tools/host/linux/x86_64/usr/bin/aarch64-unknown-nto-qnx7.1.0-ar\nAS=/opt/qcom_build/qos225_8775p_build_tools/host/linux/x86_64/usr/bin/aarch64-unknown-nto-qnx7.1.0-as\nCC=qcc -Vgcc_ntoaarch64le_cxx -std=gnu99\nCHOST=/opt/qcom_build/qos225_8775p_build_tools/host/linux/x86_64\nCONAN_CMAKE_TOOLCHAIN_FILE=/opt/qcom_build/cmake_config/qnx_aarch64le_toolchain_iosock.cmake\nCXX=q++ -Vgcc_ntoaarch64le_cxx -lang-c++\nDJI_ARCH=arm64\nDJI_BOARD=sa8775\nDJI_OS=qnx\nDJI_OSTYPE=safety\nDJI_SDKVERSION=sdk11\nDJI_TOOLCHAINVERSION=7.1\nRANLIB=/opt/qcom_build/qos225_8775p_build_tools/host/linux/x86_64/usr/bin/aarch64-unknown-nto-qnx7.1.0-ranlib\nSTRIP=/opt/qcom_build/qos225_8775p_build_tools/host/linux/x86_64/usr/bin/aarch64-unknown-nto-qnx7.1.0-strip\nqnx_version=2.2.5"}