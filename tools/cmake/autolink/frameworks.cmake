set(CMAKE_BUILD_TYPE Debug)

set(AUTOLINK_FRAMEWORKS_SOURCES_DIR ${DEV_ROOT}/vendor/autolink/frameworks)

option(ENABLE_SOMEIP "enable someip function" OFF)
option(ENABLE_VEHICLE "enable vehicle function" ON)
option(ENABLE_IVI "enable ivi function" ON)
option(ENABLE_PSIS "enable psis function" ON)
option(ENABLE_CUSTOM_PROPERTY "enable customproperty function" ON)

# < al_log >
#file(GLOB_RECURSE AUTOLINK_FRAMEWORKS_LOG_SOURCES ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/log/impl/*.cpp)
#add_library(al_log SHARED ${AUTOLINK_FRAMEWORKS_LOG_SOURCES})
#target_link_libraries(al_log slog2)

# < al_core >
file(GLOB_RECURSE AUTOLINK_FRAMEWORKS_CORE_SOURCES ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/core/impl/*.cpp)
add_library(al_core SHARED ${AUTOLINK_FRAMEWORKS_CORE_SOURCES})

# < al_core ut >
set(AUTOLINK_FRAMEWORKS_CORE_UT_SOURCES
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/core/unit_tests/test.cpp
)
add_executable(al_core_ut ${AUTOLINK_FRAMEWORKS_CORE_UT_SOURCES})
target_link_libraries(al_core_ut al_core al_log gtest)
target_compile_definitions(al_core_ut PRIVATE BUILD_UT_APPLICATION)

# < persistency for tmp >
#set(PERSISTENCY_SOURCES
#    ${DEV_ROOT}/vendor/autolink/frameworks/pa/IPSIS/persistency/persistency.cpp
#)
#add_library(al_psis SHARED ${PERSISTENCY_SOURCES})
#add_executable(psis_client ${PERSISTENCY_SOURCES})

# < qcom >
set(QCOM_INC_DIR
    ${DEV_ROOT}/vendor/qcom/sdk/target/usr/include
)

# <zyt>
set(ZYT_INC_DIR
    ${CONAN_INCLUDE_DIRS}
    ${CONAN_INCLUDE_DIRS}/test
    ${CONAN_INCLUDE_DIRS}/amss/multimedia/inc
    ${CONAN_INCLUDE_DIRS}/amss/multimedia/camera_qcx
)

set(QCOM_LIB_DIR
    ${DEV_ROOT}/vendor/qcom/sdk/target/usr/lib
    # ${DEV_ROOT}/vendor/qcom/sdk/lib
)

# < IPC >
set(IPC_INC_DIR
    ${DEV_ROOT}/vendor/autolink/infrastructure/fdbus/public
    ${DEV_ROOT}/vendor/autolink/hal/vehicleservice
)
set(IPC_LIB_DIR
    ${DEV_ROOT}/vendor/autolink/external/protobuf/lib
)
set(IPC_LIBS fdbus libsocket.so.4 protobuf autolink_vehicleclient_qnx alcomeclient)

# < al_pa >
file(GLOB_RECURSE HIRAIN_LIBS
    ${DEV_ROOT}/vendor/autolink/frameworks/pa/ISomeIP/impl/hirain/ThirdPart/lib/lib*)

#message("HIRAIN_LIBS: ${HIRAIN_LIBS}")

if (ENABLE_SOMEIP)
    set(HIRAIN_LIBS_PATH
        ${DEV_ROOT}/vendor/autolink/frameworks/pa/ISomeIP/impl/hirain/ThirdPart/lib
    )
endif(ENABLE_SOMEIP)

file(GLOB_RECURSE AUTOLINK_FRAMEWORKS_PA_SOURCES
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/common/impl/*.cpp
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/IAudio/impl/*.cpp
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/ICamera/impl/*.cpp
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/IDiag/impl/*.cpp
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/IDispCtrl/impl/*.cpp
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/IInc/impl/*.cpp
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/IIVI/impl/*.cpp
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/ILCM/impl/*.cpp
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/INotifyCenter/impl/*.cpp
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/IPPS/impl/*.cpp
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/IPSIS/impl/*.cpp
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/IScreen/impl/*.cpp
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/ivehicle/impl/*.cpp
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/IVidc/impl/*.cpp
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/IADAS/impl/*.cpp
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/IHM/impl/*.cpp
)

if (ENABLE_SOMEIP)
    list(APPEND AUTOLINK_FRAMEWORKS_PA_SOURCES
            ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/ISomeIP/impl/*.cpp
    )
endif(ENABLE_SOMEIP)

if (ENABLE_SOMEIP)
    file(GLOB_RECURSE AUTOLINK_FRAMEWORKS_PA_HIRAIN_SOURCES ${DEV_ROOT}/vendor/autolink/frameworks/pa/ISomeIP/impl/hirain/*.cc)
endif(ENABLE_SOMEIP)

set(VIDC_LIB
    OSAbstraction libstd pmem_client ioctlClient AACParserLib AC3ParserLib AMRNBParserLib AMRWBParserLib ASFParserLib AVIParserLib FileDemux_Common EVRCBParserLib EVRCWBParserLib FLACParserLib ID3Lib MP2ParserLib MP3ParserLib OGGParserLib QCPParserLib RawParserLib SeekLib SeekTableLib VideoFMTReaderLib WAVParserLib ISOBaseFileLib MKAVParserLib AIFFParserLib FileBaseLib FileSource
)

file(GLOB_RECURSE ALSDK_BOSCH_PLATFORM_PA_SOURCES ${DEV_ROOT}/vendor/bosch/idl/protobuf/generated/*.cc)

list(APPEND
    AUTOLINK_FRAMEWORKS_PA_SOURCES
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/common/impl/fdb.cc
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/IIVI/impl/protocol/pb/com.autolink.incdata.pb.cc
    ${DEV_ROOT}vendor/autolink/api/protobuf/public/idl_gen/com.autolink.power.pb.cc
    ${DEV_ROOT}vendor/autolink/api/protobuf/public/idl_gen/com.autolink.vehicle.pb.cc
    ${DEV_ROOT}vendor/autolink/api/protobuf/private/idl_gen/com.autolink.power.priv.pb.cc
    ${DEV_ROOT}vendor/autolink/api/protobuf/private/idl_gen/com.autolink.cluster.diag.pb.cc
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/IADAS/impl/protocol/pb/com.autolink.adasdata.pb.cc
    ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/pa/IHM/impl/pb/com.autolink.platform.health.pb.cc
)

if (ENABLE_SOMEIP)
    list(APPEND
        AUTOLINK_FRAMEWORKS_PA_SOURCES
        ${AUTOLINK_FRAMEWORKS_PA_HIRAIN_SOURCES}
    )
endif(ENABLE_SOMEIP)

add_library(al_pa SHARED ${AUTOLINK_FRAMEWORKS_PA_SOURCES})

if (ENABLE_SOMEIP)
    target_link_libraries(al_pa PUBLIC al_core al_log ${IPC_LIBS} screen ${HIRAIN_LIBS} ${VIDC_LIB})
else()
    target_link_libraries(al_pa PUBLIC al_core al_log ${IPC_LIBS} screen ${VIDC_LIB})
endif(ENABLE_SOMEIP)

target_link_libraries(al_pa PUBLIC pmem_client qcxosal OSAbstraction qcxclient asound)


set(AUTOLINK_INCLUDE_DIR
    ${QNX_SDP_HOME}/target/qnx7/usr/include/io-sock
    ${IPC_INC_DIR}
    ${QCOM_INC_DIR}
    ${ZYT_INC_DIR}
    ${DEV_ROOT}vendor/autolink/api/protobuf/public/idl_gen
    ${DEV_ROOT}vendor/autolink/api/protobuf/private/idl_gen
)

if (ENABLE_SOMEIP)
    set(AUTOLINK_HIRAIN_INCLUDE_DIR
        ${DEV_ROOT}/vendor/autolink/frameworks/pa/ISomeIP/impl/hirain/ThirdPart/include
        ${DEV_ROOT}/vendor/autolink/frameworks/pa/ISomeIP/impl/hirain/gen_code/include
        ${DEV_ROOT}/vendor/autolink/frameworks/pa/ISomeIP/impl/hirain/include
    )
    target_include_directories(al_pa PUBLIC ${AUTOLINK_HIRAIN_INCLUDE_DIR})
endif(ENABLE_SOMEIP)

target_include_directories(al_pa PUBLIC ${AUTOLINK_INCLUDE_DIR})
target_link_directories(al_pa PUBLIC ${QCOM_LIB_DIR} ${CONAN_LIB_DIRS} ${HIRAIN_LIBS_PATH} ${IPC_LIB_DIR})
target_compile_definitions(al_pa PRIVATE -DZYT_PLATFORM -DQNX_SDP_VERSION=${QNX_SDP_VERSION})

# < al_cm >
file(GLOB_RECURSE AUTOLINK_FRAMEWORKS_CM_SOURCES ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/cm/**/impl/*.cpp)
add_library(al_cm SHARED ${AUTOLINK_FRAMEWORKS_CM_SOURCES})

if(ENABLE_SOMEIP)
    target_compile_definitions(al_cm PRIVATE ENABLE_SOMEIP)
endif()
if(ENABLE_VEHICLE)
    target_compile_definitions(al_cm PRIVATE ENABLE_VEHICLE)
endif()
if(ENABLE_IVI)
    target_compile_definitions(al_cm PRIVATE ENABLE_IVI)
endif()
if(ENABLE_PSIS)
    target_compile_definitions(al_cm PRIVATE ENABLE_PSIS)
endif()
if(ENABLE_CUSTOM_PROPERTY)
    target_compile_definitions(al_cm PRIVATE ENABLE_CUSTOM_PROPERTY)
endif()
if(ENABLE_PPS)
    target_compile_definitions(al_cm PRIVATE ENABLE_PPS)
endif()

target_link_libraries(al_cm al_pa al_core al_log json)
