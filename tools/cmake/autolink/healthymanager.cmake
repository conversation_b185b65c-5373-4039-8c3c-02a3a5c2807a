aux_source_directory(${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/healthymanager/impl SYSMONITOR_IMPL)
SET(SRC_LIST
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/healthymanager/src/SysMonitor.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/healthymanager/src/HamClient.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/healthymanager/src/SysManager.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/healthymanager/src/SupervisedResult.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/healthymanager/src/machine_manifest.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/healthymanager/src/launch_client.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/healthymanager/src/ReArm.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/healthymanager/src/iterateProc.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/healthymanager/src/utilities.cpp
    ${SYSMONITOR_IMPL}
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/healthymanager/utils/threadpool.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/healthymanager/utils/timer.cpp
)

SET(SRC_LIST2
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/healthymanager/src/AppLauncher.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/healthymanager/src/launch_client.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/healthymanager/src/ReArm.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/healthymanager/src/machine_manifest.cpp
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/healthymanager/src/iterateProc.cpp
)

link_directories(${QNX_SDP_HOME}/target/qnx7/aarch64le/lib)

add_executable(sysmonitor ${SRC_LIST})
target_link_libraries(sysmonitor ham al_log al_pa)
target_include_directories(sysmonitor PUBLIC
    ${PROJECT_CONFIG_DIR}
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/healthymanager/include
    ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/healthymanager/utils
)

add_executable(applauncher ${SRC_LIST2})
target_link_libraries(applauncher ham al_log)
target_include_directories(applauncher PUBLIC ${PROJECT_CONFIG_DIR} ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/healthymanager/include)

