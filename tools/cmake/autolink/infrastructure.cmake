set(AUTOLINK_INFRASTRUCTURE_SOURCES_DIR ${DEV_ROOT}/vendor/autolink/infrastructure)

macro(generate_files_list)
    foreach(prefix *.c *.cpp *.cxx *.cc ${CMAKE_C_SOURCE_FILE_EXTENSIONS} ${CMAKE_CXX_SOURCE_FILE_EXTENSIONS} ${CMAKE_ASM_SOURCE_FILE_EXTENSIONS})
        list(APPEND ${ARGV0} ${ARGV1}/*.${prefix})
    endforeach()
endmacro()

macro(source_directories_append)
    set(dirs ${ARGV})
    list(REMOVE_AT dirs 0 1)
    foreach(dir ${dirs})
        generate_files_list(glob_list ${dir})
        file(${ARGV1} src_list LIST_DIRECTORIES false ${glob_list})
        list(APPEND ${ARGV0} ${src_list})
    endforeach()
endmacro()

#include(infra_doip.cmake)
