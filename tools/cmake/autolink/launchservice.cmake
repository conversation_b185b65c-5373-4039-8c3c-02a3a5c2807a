include_directories(${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/launchmanager)

file(GLOB_RECURSE AUTOLINK_LAUNCHER_IMPL_SOURCES ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/launchmanager/impl/*.cpp)
add_executable(launcher ${AUTOLINK_INFRASTRUCTURE_SOURCES_DIR}/launchmanager/launcher/launcher.cpp
                        ${AUTOLINK_LAUNCHER_IMPL_SOURCES})

target_link_libraries(launcher json al_pa al_core al_log)
