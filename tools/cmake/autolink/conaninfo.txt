[settings]
    arch=arm64
    build_type=Release
    compiler=gcc
    compiler.libcxx=libstdc++11
    compiler.version=8.3
    os=qnx
    os.platform=sa8775
    os.toolchain_version=7.1
    os.type=safety
    sdk_version=sdk11

[requires]
    bsp-qcomm/7.Y.Z

[options]


[full_settings]
    arch=arm64
    build_type=Release
    compiler=gcc
    compiler.libcxx=libstdc++11
    compiler.version=8.3
    os=qnx
    os.platform=sa8775
    os.toolchain_version=7.1
    os.type=safety
    sdk_version=sdk11

[full_requires]
    bsp-qcomm/7.99.1-baic.15@ZYT/release:eefefbb60bb187b4316ff794be41c53c5db63b97

[full_options]
    bsp-qcomm:shared=False

[recipe_hash]


[env]
    AR=/opt/qcom_build/qos225_8775p_build_tools/host/linux/x86_64/usr/bin/aarch64-unknown-nto-qnx7.1.0-ar
    AS=/opt/qcom_build/qos225_8775p_build_tools/host/linux/x86_64/usr/bin/aarch64-unknown-nto-qnx7.1.0-as
    CC=qcc -Vgcc_ntoaarch64le_cxx -std=gnu99
    CHOST=/opt/qcom_build/qos225_8775p_build_tools/host/linux/x86_64
    CONAN_CMAKE_TOOLCHAIN_FILE=/opt/qcom_build/cmake_config/qnx_aarch64le_toolchain_iosock.cmake
    CXX=q++ -Vgcc_ntoaarch64le_cxx -lang-c++
    DJI_ARCH=arm64
    DJI_BOARD=sa8775
    DJI_OS=qnx
    DJI_OSTYPE=safety
    DJI_SDKVERSION=sdk11
    DJI_TOOLCHAINVERSION=7.1
    RANLIB=/opt/qcom_build/qos225_8775p_build_tools/host/linux/x86_64/usr/bin/aarch64-unknown-nto-qnx7.1.0-ranlib
    STRIP=/opt/qcom_build/qos225_8775p_build_tools/host/linux/x86_64/usr/bin/aarch64-unknown-nto-qnx7.1.0-strip
    qnx_version=2.2.5
