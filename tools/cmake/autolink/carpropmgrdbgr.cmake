# carpropmgrdbgr
file(GLOB_RECURSE CM_PROPMGRDBGR_SOURCES ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/cm/carpropertymanager/debug/carpropmgrdbgr/*.cpp)
add_executable(carpropmgrdbgr ${CM_PROPMGRDBGR_SOURCES})
target_compile_options(carpropmgrdbgr PRIVATE -g)

target_include_directories(carpropmgrdbgr PUBLIC ${DEV_ROOT}/vendor/autolink/frameworks/cm/carpropertymanager/debug/carpropmgrdbgr/playback/parser/jsoncpp)
target_link_libraries(carpropmgrdbgr al_log al_core al_pa)

# adasSender
file(GLOB_RECURSE CM_ADAS_SOURCES ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/cm/carpropertymanager/debug/adasSender/*.cpp)
add_executable(adasSender ${CM_ADAS_SOURCES})
target_compile_options(adasSender PRIVATE -g)

target_link_libraries(adasSender al_log al_core al_pa)

# perfProber
file(GLOB_RECURSE CM_PERF_TEST_SOURCES ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/cm/carpropertymanager/debug/perfProber/*.cpp)
add_executable(perfProber ${CM_PERF_TEST_SOURCES})
target_compile_options(perfProber PRIVATE -g)

target_link_libraries(perfProber al_cm al_core )

# apiVerifier
file(GLOB_RECURSE CM_API_VERIFIER_SOURCES ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/cm/carpropertymanager/debug/apiVerifier/*.cpp)
add_executable(apiVerifier ${CM_API_VERIFIER_SOURCES})
target_compile_options(apiVerifier PRIVATE -g)
target_link_libraries(apiVerifier al_pa al_core )

#carpropmgrtools
file(GLOB_RECURSE CM_PROPMGRDBGR_SOURCES ${AUTOLINK_FRAMEWORKS_SOURCES_DIR}/cm/carpropertymanager/debug/carpropmgrtools/*.cpp)
add_executable(carpropmgrtools ${CM_PROPMGRDBGR_SOURCES})
target_compile_options(carpropmgrtools PRIVATE -g)

#target_include_directories(carpropmgrtools PUBLIC ${DEV_ROOT}/vendor/autolink/frameworks/cm/carpropertymanager/debug/playback/parser/jsoncpp)
target_link_libraries(carpropmgrtools al_log al_core al_pa al_cm)
