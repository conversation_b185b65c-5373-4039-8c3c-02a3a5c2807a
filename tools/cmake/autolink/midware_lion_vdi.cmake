
# lib lion_vdi
set(LION_VDI_SOURCES_DIR ${AUTOLINK_MIDWARE_SOURCES_DIR}/lion_vdi)
add_library(udstphost SHARED
    ${LION_VDI_SOURCES_DIR}/lion/lion_common.c
    ${LION_VDI_SOURCES_DIR}/lion/lion_udstp_adaptor.c
    ${LION_VDI_SOURCES_DIR}/lion/log.c
)

target_include_directories(
                udstphost
                PUBLIC
                ${LION_VDI_SOURCES_DIR}/lion

                ${DEV_ROOT}/vendor/bosch/target/usr/include
                ${DEV_ROOT}/vendor/autolink/midware/uac/extlib/susd_client_lib/inc
            )

target_compile_options(udstphost PRIVATE -fPIC -Wall -O0)

target_link_directories(udstphost PUBLIC ${DEV_ROOT}/vendor/bosch/target/usr/lib)
target_link_directories(udstphost PUBLIC ${QNX_SDP_HOME}/target/qnx7/aarch64le/io-sock/lib)
# install(TARGETS udstphost DESTINATION ${DEV_ROOT}/external/lionOta/lib)
target_link_libraries(
    udstphost
    c 
    socket
    slog2
)

# bin lion_vdi_test
add_executable(lion_udstp_test ${LION_VDI_SOURCES_DIR}/lion/lion_main.c)
target_link_libraries(
    lion_udstp_test
    c 
    socket
    udstphost
)
