set(CMAKE_BUILD_TYPE Debug)
set(AUTOLINK_POWERHAL_SOURCES_DIR ${DEV_ROOT}/vendor/autolink/hal/power)

set(POWER_HAL_INC_LIST
    ${AUTOLINK_POWERHAL_SOURCES_DIR}/include
    ${AUTOLINK_POWERHAL_SOURCES_DIR}/include/client
    ${AUTOLINK_POWERHAL_SOURCES_DIR}/include/server
    ${AUTOLINK_POWERHAL_SOURCES_DIR}/include/utils
    ${DEV_ROOT}vendor/autolink/api/protobuf/public/idl_gen
    ${DEV_ROOT}vendor/autolink/api/protobuf/private/idl_gen
    ${DEV_ROOT}/vendor/autolink/external/protobuf/include
    ${DEV_ROOT}/vendor/autolink/infrastructure/fdbus/public
    ${DEV_ROOT}/vendor/autolink/frameworks/log
    ${CONAN_INCLUDE_DIRS}
)

set(POWER_IPC_LIB_DIR
    ${DEV_ROOT}/vendor/autolink/external/protobuf/lib
    ${QNX_AP_ROOT}/install/aarch64le/lib
    ${CONAN_LIB_DIRS}
)

#compile file list set
file(GLOB_RECURSE POWERAHL_SOURCE ${AUTOLINK_POWERHAL_SOURCES_DIR}/src/*.cpp)
file(GLOB_RECURSE CLIENT_SOURCE ${AUTOLINK_POWERHAL_SOURCES_DIR}/src/client/*.cpp)
file(GLOB_RECURSE POWER_SERVER_SOURCE ${AUTOLINK_POWERHAL_SOURCES_DIR}/src/server/*.cpp)
file(GLOB_RECURSE POWER_MANAGER_SOURCE ${AUTOLINK_POWERHAL_SOURCES_DIR}/src/manager/*.cpp)
file(GLOB_RECURSE POWERUTILS_SOURCE ${AUTOLINK_POWERHAL_SOURCES_DIR}/src/utils/*.cpp)
file(GLOB_RECURSE POWERUTILS_C_SOURCE ${AUTOLINK_POWERHAL_SOURCES_DIR}/src/utils/*.c)
file(GLOB_RECURSE POWERPROTO_SOURCE ${DEV_ROOT}vendor/autolink/api/protobuf/public/idl_gen/com.autolink.power.pb.cc
                                    ${DEV_ROOT}vendor/autolink/api/protobuf/private/idl_gen/com.autolink.power.priv.pb.cc)

#背光操作接口.c
file(GLOB_RECURSE DISPLAY_CONTROL_SOURCE ${DEV_ROOT}/../../qnx/apps/qnx_ap/AMSS/multimedia/display/Hoya/display_manager_server/src/display_manager_functions.c)

set(POWER_SOURCES
    ${POWERAHL_SOURCE}
    ${CLIENT_SOURCE}
    ${POWER_SERVER_SOURCE}
    ${POWER_MANAGER_SOURCE}
    ${POWERUTILS_SOURCE}
    ${POWERUTILS_C_SOURCE}
    ${POWERPROTO_SOURCE}
    ${DISPLAY_CONTROL_SOURCE}
)
#alpowerhal
add_executable(alpowerhal  ${POWER_SOURCES})
target_include_directories(alpowerhal  PRIVATE ${POWER_HAL_INC_LIST})
target_compile_definitions(alpowerhal  PRIVATE -DAUTOLINK_POWERHAL_QNX)
##target_compile_definitions(alpowerhal  PRIVATE -DAUTOLINK_HAS_ALC_POWERCLIENT)
target_link_directories(alpowerhal  PRIVATE  ${POWER_IPC_LIB_DIR})
target_link_libraries(alpowerhal  PRIVATE libdisplay_ctrl.so screen uhab gpio_client i2c_clientS  al_log protobuf fdbus libsocket.so.4)
target_compile_options(alpowerhal  PRIVATE -g)
