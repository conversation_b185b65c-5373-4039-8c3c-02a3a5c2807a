
set(CMAKE_BUILD_TYPE Debug)
set(CMAKE_CXX_STANDARD 17)
set(VARIANT_MODULE_ROOTDIR ${DEV_ROOT}/vendor/autolink/hal/variant)
set(PROTO_API_BASE_DIR ${DEV_ROOT}/vendor/autolink/api/protobuf)

set(module_name variant_server)

add_executable(${module_name}
    ${PROTO_API_BASE_DIR}/private/idl_gen/com.autolink.variant.meta.pb.cc
    ${PROTO_API_BASE_DIR}/public/idl_gen/com.autolink.variant.pb.cc
    ${VARIANT_MODULE_ROOTDIR}/src/VariantDataStore.cpp
    ${VARIANT_MODULE_ROOTDIR}/src/VariantCfg.cpp
    ${VARIANT_MODULE_ROOTDIR}/src/main.cpp
    ${VARIANT_MODULE_ROOTDIR}/src/VariantService.cpp
    ${VARIANT_MODULE_ROOTDIR}/src/FdbusVariantServer.cpp
)

target_link_libraries(${module_name} PUBLIC
    fdbus libsocket.so.4 protobuf slog2 al_pa
)

target_link_directories(${module_name} PUBLIC
    ${DEV_ROOT}/vendor/autolink/external/protobuf/lib
)

target_include_directories(${module_name} PUBLIC
    ${DEV_ROOT}/vendor/autolink/infrastructure/fdbus/public
    ${DEV_ROOT}/vendor/autolink/frameworks/pa/IPSIS
    ${VARIANT_MODULE_ROOTDIR}/include
    ${PROTO_API_BASE_DIR}/public/idl_gen
    ${PROTO_API_BASE_DIR}/private/idl_gen
)

# for variant_tool
set(module_tool_name variant_tool)
add_executable(${module_tool_name}
    ${PROTO_API_BASE_DIR}/private/idl_gen/com.autolink.variant.meta.pb.cc
    ${PROTO_API_BASE_DIR}/public/idl_gen/com.autolink.variant.pb.cc
    ${VARIANT_MODULE_ROOTDIR}/tools/variant_tool/src/main_qnx.cpp
    ${VARIANT_MODULE_ROOTDIR}/tools/variant_tool/src/FdbusVariantClient.cpp
    ${VARIANT_MODULE_ROOTDIR}/tools/variant_tool/src/VariantToolService.cpp
)

target_link_libraries(${module_tool_name} PUBLIC
    fdbus libsocket.so.4 protobuf slog2 al_pa
)

target_link_directories(${module_tool_name} PUBLIC
    ${DEV_ROOT}/vendor/autolink/external/protobuf/lib
)

target_include_directories(${module_tool_name} PUBLIC
    ${DEV_ROOT}/vendor/autolink/infrastructure/fdbus/public
    ${DEV_ROOT}/vendor/autolink/frameworks/pa/IPSIS
    ${VARIANT_MODULE_ROOTDIR}/include
    ${VARIANT_MODULE_ROOTDIR}/tools/variant_tool/include
    ${PROTO_API_BASE_DIR}/public/idl_gen
)