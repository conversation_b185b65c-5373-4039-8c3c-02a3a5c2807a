set(CMAKE_BUILD_TYPE Debug)
set(AUTOLINK_DISPLAYHAL_SOURCES_DIR ${DEV_ROOT}/vendor/autolink/hal/display)

set(DISPLAY_HAL_INC_LIST
    ${AUTOLINK_DISPLAYHAL_SOURCES_DIR}/display_manager_server/inc
    ${CONAN_INCLUDE_DIRS_BSP-QCOMM}/amss/multimedia/uhab
    ${CONAN_INCLUDE_DIRS_BSP-QCOMM}/amss/core
    ${CONAN_INCLUDE_DIRS_BSP-QCOMM}/amss/inc
    ${DEV_ROOT}/vendor/autolink/external/protobuf/include
    ${DEV_ROOT}/vendor/autolink/hal/display/display_manager_server/proto
    ${DEV_ROOT}/vendor/autolink/infrastructure/fdbus/public
    # ${DEV_ROOT}/vendor/autolink/hal/power/include
    #${DEV_ROOT}/vendor/autolink/hal/power/include/client
    #${DEV_ROOT}/vendor/autolink/hal/power/include/server
    #${DEV_ROOT}/vendor/autolink/hal/power/include/utils
)

set(DISPLAY_IPC_LIB_DIR
    ${DEV_ROOT}/vendor/autolink/external/protobuf/lib
    ${QNX_AP_ROOT}/install/aarch64le/lib
    ${CONAN_LIB_DIRS}
    ${CONAN_LIB_DIRS_BSP-QCOMM}
)

#compile file list set
file(GLOB_RECURSE DISPLAYHL_SOURCE ${AUTOLINK_DISPLAYHAL_SOURCES_DIR}/display_manager_server/src/*.c)
file(GLOB_RECURSE DISPLAYHL_SERVER ${AUTOLINK_DISPLAYHAL_SOURCES_DIR}/display_manager_server/src/*.cpp)
file(GLOB_RECURSE DISPLAYPROTO_SOURCE ${DEV_ROOT}/vendor/autolink/hal/display/display_manager_server/proto/*.cc)
set(DISPLAY_SOURCES
	${DISPLAYHL_SOURCE}
	${DISPLAYHL_SERVER}
	${DISPLAYPROTO_SOURCE}
	
)
#aldisplayhal
add_executable(aldisplayhal  ${DISPLAY_SOURCES})
target_include_directories(aldisplayhal  PRIVATE ${DISPLAY_HAL_INC_LIST})


target_link_directories(aldisplayhal  PRIVATE  ${DISPLAY_IPC_LIB_DIR})
target_link_libraries(aldisplayhal  PRIVATE screen uhab i2c_clientS  bmetrics slog2 slog2-extra fdbus protobuf display_ctrl)
target_compile_options(aldisplayhal  PRIVATE -g)
