
set(CMAKE_BUILD_TYPE Debug)
set(VARIANT_MODULE_ROOTDIR ${DEV_ROOT}/vendor/autolink/hal/vehicleservice)
set(INFRA_DIR ${DEV_ROOT}/vendor/autolink/infrastructure)
#set(ALC_MODULE_ROOTDIR ${DEV_ROOT}/alc)

set(module_name vehicleservice)
if (VEHICLE_TYPE STREQUAL  "8")
add_executable(${module_name}
    ${DEV_ROOT}/vendor/autolink/api/protobuf/public/idl_gen/com.autolink.vehicle.pb.cc
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/src/AclAdapter.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/src/AdapterRcvJob.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/src/DataDeserialize.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/src/DataSerialize.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/src/VehicePropReader.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/src/VehiclePropertyStore.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/src/VehicleService.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/src/VehicleServiceFdbus.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/src/VehicleServiceManager.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/src/VehicleSetJob.cpp
    ${VARIANT_MODULE_ROOTDIR}/alc/ALComVehicleClient.cpp
    ${VARIANT_MODULE_ROOTDIR}/alc/ALComClient.cpp
    ${VARIANT_MODULE_ROOTDIR}/alc/proto_n80/autolink.platform.vehicle.pb.cc

)
else()
add_executable(${module_name}
    ${DEV_ROOT}/vendor/autolink/api/protobuf/public/idl_gen/com.autolink.vehicle.pb.cc
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/src/AclAdapter.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/src/AdapterRcvJob.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/src/DataDeserialize.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/src/DataSerialize.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/src/VehicePropReader.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/src/VehiclePropertyStore.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/src/VehicleService.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/src/VehicleServiceFdbus.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/src/VehicleServiceManager.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/src/VehicleSetJob.cpp
    ${VARIANT_MODULE_ROOTDIR}/alc/ALComVehicleClient.cpp
    ${VARIANT_MODULE_ROOTDIR}/alc/ALComClient.cpp
    ${VARIANT_MODULE_ROOTDIR}/alc/proto/autolink.platform.vehicle.pb.cc

)
endif()

if (VEHICLE_TYPE STREQUAL  "8")
target_include_directories(${module_name} PUBLIC
    ${DEV_ROOT}/vendor/autolink/infrastructure/fdbus/public
    ${VARIANT_MODULE_ROOTDIR}/define
    ${INFRA_DIR}/log
    ${DEV_ROOT}/vendor/autolink/api/protobuf/public/idl_gen
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/include
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/third_party
    ${VARIANT_MODULE_ROOTDIR}/alc
    ${PROJECT_CONFIG_DIR}
    ${VARIANT_MODULE_ROOTDIR}/alc/proto_n80
    )
else()
target_include_directories(${module_name} PUBLIC
    ${DEV_ROOT}/vendor/autolink/infrastructure/fdbus/public
    ${VARIANT_MODULE_ROOTDIR}/define
    ${INFRA_DIR}/log
    ${DEV_ROOT}/vendor/autolink/api/protobuf/public/idl_gen
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/include
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/third_party
    ${VARIANT_MODULE_ROOTDIR}/alc
    ${PROJECT_CONFIG_DIR}
    ${VARIANT_MODULE_ROOTDIR}/alc/proto
    )
endif()

target_link_libraries(${module_name} PUBLIC
    fdbus libsocket.so.4 protobuf al_log
)

target_link_directories(${module_name} PUBLIC
    ${DEV_ROOT}/vendor/autolink/external/protobuf/lib
)

set(debug_module_name  vehicledbg)
if (VEHICLE_TYPE STREQUAL "8")
add_executable(${debug_module_name}
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/debug/src/ALComService.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/debug/src/main.cpp
    ${VARIANT_MODULE_ROOTDIR}/alc/proto_n80/autolink.platform.vehicle.pb.cc
)
else()
add_executable(${debug_module_name}
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/debug/src/ALComService.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/debug/src/main.cpp
    ${VARIANT_MODULE_ROOTDIR}/alc/proto/autolink.platform.vehicle.pb.cc
)
endif()

target_link_libraries(${debug_module_name} PUBLIC
    fdbus libsocket.so.4 protobuf al_log
)

target_link_directories(${debug_module_name} PUBLIC
    ${DEV_ROOT}/vendor/autolink/external/protobuf/lib
)

if (VEHICLE_TYPE STREQUAL  "8")
target_include_directories(${debug_module_name} PUBLIC
    ${DEV_ROOT}/vendor/autolink/infrastructure/fdbus/public
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/debug/include
    ${DEV_ROOT}/vendor/autolink/api/protobuf/public/idl_gen
    ${VARIANT_MODULE_ROOTDIR}/alc
    ${VARIANT_MODULE_ROOTDIR}/alc/proto_n80
    )
else()
target_include_directories(${debug_module_name} PUBLIC
    ${DEV_ROOT}/vendor/autolink/infrastructure/fdbus/public
    ${VARIANT_MODULE_ROOTDIR}/vehicleservice/debug/include
    ${DEV_ROOT}/vendor/autolink/api/protobuf/public/idl_gen
    ${VARIANT_MODULE_ROOTDIR}/alc
    ${VARIANT_MODULE_ROOTDIR}/alc/proto
    )
endif()
