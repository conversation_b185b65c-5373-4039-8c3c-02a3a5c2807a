
set(CMAKE_BUILD_TYPE Debug)
set(VARIANT_MODULE_ROOTDIR ${DEV_ROOT}/vendor/autolink/hal/vehicleservice)
set(INFRA_DIR ${DEV_ROOT}/vendor/autolink/infrastructure)

set(module_name autolink_vehicleclient_qnx)
if (VEHICLE_TYPE STREQUAL  "8")
add_library(${module_name} SHARED
    ${DEV_ROOT}/vendor/autolink/api/protobuf/public/idl_gen/com.autolink.vehicle.pb.cc
    ${VARIANT_MODULE_ROOTDIR}/vehicleclient/proto_n80/autolink.alc.signal.pb.pb.cc
    ${VARIANT_MODULE_ROOTDIR}/vehicleclient/src/ViClient.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleclient/src/VehicleClientFdbus.cpp
)
else()
add_library(${module_name} SHARED
    ${DEV_ROOT}/vendor/autolink/api/protobuf/public/idl_gen/com.autolink.vehicle.pb.cc
    ${VARIANT_MODULE_ROOTDIR}/vehicleclient/proto/autolink.alc.signal.pb.pb.cc
    ${VARIANT_MODULE_ROOTDIR}/vehicleclient/src/ViClient.cpp
    ${VARIANT_MODULE_ROOTDIR}/vehicleclient/src/VehicleClientFdbus.cpp
)
endif()

target_link_libraries(${module_name} PUBLIC
    fdbus libsocket.so.4 protobuf al_log
)

target_link_directories(${module_name} PUBLIC
    ${DEV_ROOT}/vendor/autolink/external/protobuf/lib
)

if (VEHICLE_TYPE STREQUAL  "8")
target_include_directories(${module_name} PUBLIC
    ${DEV_ROOT}/vendor/autolink/infrastructure/fdbus/public
    ${VARIANT_MODULE_ROOTDIR}/define
    ${INFRA_DIR}/log
    ${DEV_ROOT}/vendor/autolink/api/protobuf/public/idl_gen
    ${VARIANT_MODULE_ROOTDIR}/vehicleclient/proto_n80
    ${VARIANT_MODULE_ROOTDIR}/
    ${VARIANT_MODULE_ROOTDIR}/vehicleclient/include/private
    )
else()
target_include_directories(${module_name} PUBLIC
    ${DEV_ROOT}/vendor/autolink/infrastructure/fdbus/public
    ${VARIANT_MODULE_ROOTDIR}/define
    ${INFRA_DIR}/log
    ${DEV_ROOT}/vendor/autolink/api/protobuf/public/idl_gen
    ${VARIANT_MODULE_ROOTDIR}/vehicleclient/proto
    ${VARIANT_MODULE_ROOTDIR}/
    ${VARIANT_MODULE_ROOTDIR}/vehicleclient/include/private
    )
endif()


add_executable(vehicle_test
    ${VARIANT_MODULE_ROOTDIR}/vehicleclient/test/main.cpp
)

target_link_libraries(vehicle_test PUBLIC
    autolink_vehicleclient_qnx
    al_log
)

target_include_directories(vehicle_test PUBLIC
    ${INFRA_DIR}/log
    ${VARIANT_MODULE_ROOTDIR}/proto
    ${VARIANT_MODULE_ROOTDIR}/
)
