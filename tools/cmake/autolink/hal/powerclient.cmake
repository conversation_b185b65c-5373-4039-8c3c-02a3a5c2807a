set(CMAKE_BUILD_TYPE Debug)
set(AUTOLINK_POWERCLIENT_SOURCES_DIR ${DEV_ROOT}/vendor/autolink/hal/power/test/powerclient)

set(POWER_CLIENT_INC_LIST
    ${AUTOLINK_POWERCLIENT_SOURCES_DIR}/include
    ${DEV_ROOT}vendor/autolink/api/protobuf/public/idl_gen
    ${DEV_ROOT}vendor/autolink/api/protobuf/private/idl_gen
    ${DEV_ROOT}/vendor/autolink/external/protobuf/include
    ${DEV_ROOT}/vendor/autolink/infrastructure/fdbus/public
    ${DEV_ROOT}/vendor/autolink/frameworks/log
    ${DEV_ROOT}/vendor/qcom/sdk/target/usr/include
)

set(POWER_IPC_LIB_DIR
    ${DEV_ROOT}/vendor/autolink/external/protobuf/lib
)

#compile file list set
file(GLOB_RECURSE POWERCLIENT_SOURCE ${AUTOLINK_POWERCLIENT_SOURCES_DIR}/src/*.cpp)
file(GLOB_RECURSE POWERPROTO_SOURCE ${DEV_ROOT}vendor/autolink/api/protobuf/public/idl_gen/com.autolink.power.pb.cc
                                    ${DEV_ROOT}vendor/autolink/api/protobuf/private/idl_gen/com.autolink.power.priv.pb.cc)

set(POWER_SOURCES
    ${POWERCLIENT_SOURCE}
    ${POWERPROTO_SOURCE}
)

add_executable(powerclient ${POWER_SOURCES})
target_include_directories(powerclient PRIVATE ${POWER_CLIENT_INC_LIST})
target_compile_definitions(powerclient PRIVATE -DAUTOLINK_POWERHAL_QNX)
target_link_directories(powerclient PRIVATE  ${POWER_IPC_LIB_DIR})
target_link_libraries(powerclient PUBLIC screen al_log protobuf fdbus libsocket.so.4)
target_compile_options(powerclient PRIVATE -g)
