#!/usr/bin/python2
import os
import string
import sys
import shutil
import subprocess
import getpass
import optparse
import time
import json
import commands

class Product:
    buildversion = ""

    workspace = ""

    qnxsdp = ""

    kanzihome = ""

    mountdirectory = ""

    vehicletype = ""

    builder = {}

    packager = {}

    docker = {}

    environment = {}

    def __init__(self) :
        pass

__pro = Product()

def __initsdp(tools):
    print("11111")
    print(tools)
    if tools == "es11":
        print("int qnx sdp es11")
        __pro.qnxsdp = "/opt/qnx/qnx710_es11"
        subprocess.check_call(["sed -i 's/6.1.0-alpha.31/7.99.1-baic.15/g' ../cmake/autolink/conanfile.txt" ], shell=True,stderr=subprocess.STDOUT)
    elif tools == "es6":
        print("int qnx sdp es6")
        __pro.qnxsdp = "/opt/qnx/qnx710_es6"
        subprocess.check_call(["sed -i 's/7.99.1-baic.15/6.1.0-alpha.31/g' ../cmake/autolink/conanfile.txt" ], shell=True,stderr=subprocess.STDOUT)
    else:
        print("default qnx sdp es11")
        __pro.qnxsdp = "/opt/qnx/qnx710_es11"
        subprocess.check_call(["sed -i 's/6.1.0-alpha.31/7.99.1-baic.15/g' ../cmake/autolink/conanfile.txt" ], shell=True,stderr=subprocess.STDOUT)

    print(__pro.qnxsdp)
    setup = os.path.join(__pro.qnxsdp, "qnxsdp-env.sh")
    output = subprocess.check_output(["bash", "-c", ". %s > /dev/null && env"%setup])
    lines = output.splitlines()
    for line in lines:
        result = line.split("=")
        if len(result) >= 2 :
            os.environ[result[0]] = result[1]
    return

def __genFdbus():
    proto_path_pub = __pro.workspace + '../api/protobuf/public'
    proto_path_pri = __pro.workspace + '../api/protobuf/private'
    proto_path_list = [proto_path_pub, proto_path_pri]
    protoc_bin = __pro.workspace + "../external/protobuf/bin/linux-x86_64/protoc"

    for proto_path in proto_path_list:
        output_dir = os.path.join(proto_path, 'idl_gen')
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        for root, dirs, files in os.walk(proto_path):
            for file in files:
                if file.endswith('.proto'):
                    proto_file = os.path.join(root, file)
                    print("handle proto file:", proto_file)
                    protoc_cmd = [
                        protoc_bin,
                        '--proto_path={}'.format(proto_path_pub),
                        '--proto_path={}'.format(proto_path_pri),

                        '--cpp_out={}'.format(output_dir),
                        proto_file
                    ]

                    try:
                        subprocess.check_call(protoc_cmd)
                        print("proto success: ", proto_file)
                    except subprocess.CalledProcessError as e:
                        print("proto failed: {e}")
                        return False
                    except Exception as e:
                        print("Unexpected error: {e}")
                        return False
    return True

def __compile(rebuild=False):
    os.environ["CC"] = os.path.expandvars(__pro.builder.get("cc" , "gcc"))
    os.environ["CXX"] = os.path.expandvars(__pro.builder.get("cxx" , "g++"))
    os.environ["CCFLAGS"] = os.path.expandvars(__pro.builder.get("ccflags" , ""))
    os.environ["CXXFLAGS"] =  os.path.expandvars(__pro.builder.get("cxxflags" , ""))
    os.environ["LDFLAGS"] = os.path.expandvars(__pro.builder.get("ldflags" , ""))
    os.environ["KANZI_HOME"] = __pro.kanzihome
    os.environ["QNX_SDP_HOME"] = __pro.qnxsdp
    os.environ["MOUNT_DIR"] = __pro.mountdirectory
    os.environ["VEHICLE_TYPE"] = __pro.vehicletype

    # create build directory
    builddir = __pro.builder.get("builddir")
    cmakedir = __pro.builder.get("cmakedir")
    jobs = __pro.builder.get("jobs")
    verbose = int(__pro.builder.get("verbose"))

    if os.path.exists(builddir) and rebuild:
        shutil.rmtree(builddir)

    if not os.path.exists(builddir):
        os.makedirs(builddir)

    if __pro.qnxsdp == "/opt/qnx/qnx710_es11":
        conan_cmd = "cd " + os.path.abspath(cmakedir) +  "/autolink" + " && conan install -pr global-profile.sa8775-qnx-7.1-sdk11-safety . && cd " + os.path.abspath(cmakedir) +  "/../scripts/"
    elif __pro.qnxsdp == "/opt/qnx/qnx710_es6":
        conan_cmd = "cd " + os.path.abspath(cmakedir) +  "/autolink" + " && conan install -pr global-profile.sa8775-qnx-7.1-sdk6-safety . && cd " + os.path.abspath(cmakedir) +  "/../scripts/"
    #conan_cmd = "cd " + os.path.abspath(cmakedir) +  "/autolink" + " && conan install . && cd " + os.path.abspath(cmakedir) +  "/../scripts/"
    subprocess.check_call(
        conan_cmd,
        shell=True,
        stderr=subprocess.STDOUT,
        cwd=builddir
    )

    build_cmds = [
        ["cmake", "--version"],
        ["cmake", os.path.abspath(cmakedir)],
        ["make", "-j{jobs}".format(jobs=jobs), "VERBOSE={verbose}".format(verbose=verbose)]
    ]

    for cmd in build_cmds:
        subprocess.check_call(cmd, stderr=subprocess.STDOUT, cwd=builddir)
    return

def __copy(src, dst):
    dst_dir = os.path.dirname(dst)
    if not os.path.exists(dst_dir):
        os.makedirs(dst_dir)

    src = os.path.abspath(src)
    dst = os.path.abspath(dst)
    if os.path.islink(src):
        linkto = os.readlink(src)
        os.symlink(linkto, dst)
    elif os.path.isdir(src):
        if not os.path.exists(dst):
            os.makedirs(dst)
        files=os.listdir(src)
        for fname in files:
            __copy(os.path.join(src, fname), os.path.join(dst, fname))
    else:
        shutil.copy2(src, dst)
    return

def __pack():
    dst_prefix = __pro.packager.get("dst_prefix")

    if(os.path.exists(dst_prefix)):
        shutil.rmtree(dst_prefix)

    for key, value in __pro.packager.get("files", {}).items():
        src = key
        dst = os.path.join(dst_prefix, value)
        __copy(src, dst)

    # todo: generate soft link by __linklist
    return

def __script_cmd():
    script_name = os.path.abspath(sys.argv[0])
    args = sys.argv[1:]
    cmd = ' '.join([script_name] + args)

    return cmd

def __run_in_docker(cmd):
    cur_user = getpass.getuser()
    docker_build_dir = os.path.abspath(__pro.docker.get("path", "."))
    docker_image_name = __pro.docker.get("image_name", "qdbuild-ubuntu2004-%s:v1.0.1"%(cur_user))
    docker_container_name = __pro.docker.get("container_name", "%s-mkspace"%(cur_user))
    docker_user = __pro.docker.get("user", cur_user)
    docker_workspace = os.path.abspath(__pro.workspace)
    docker_workdir = os.path.abspath(".")

    docker_volumes = __pro.docker.get("volumes", {})
    docker_volumes[docker_workspace] = {"bind":docker_workspace, "mode":"rw"}
    if __pro.qnxsdp != "":
        docker_volumes[__pro.qnxsdp] = {"bind":__pro.qnxsdp, "mode":"ro"}
    if __pro.kanzihome != "":
        docker_volumes[__pro.kanzihome] = {"bind":__pro.kanzihome, "mode":"ro"}

    docker_environment = __pro.docker.get("environment", {})
    for key in __pro.environment.keys() :
        docker_environment[key] = os.environ[key]

    print("Docker config info:")
    print("  builddir : %s"%docker_build_dir)
    print("  image : %s"%docker_image_name)
    print("  container : %s"%docker_container_name)
    print("  user : %s"%docker_user)
    print("  volumes : %s"%docker_volumes)
    print("  environment : %s"%docker_environment)
    print("  workspace : %s"%docker_workspace)
    print("  workdir : %s"%docker_workdir)

    import docker as docker_api
    docker_client = docker_api.from_env()
    try:
        docker_client.images.get(docker_image_name)
    except Exception:
        print("Docker building...")
        build_args = {"host_uid":str(os.getuid()), "host_gid":str(os.getgid()), "username":docker_user}
        docker_client.images.build(path=docker_build_dir, buildargs=build_args, tag=docker_image_name, pull=False)

    container = docker_client.containers.list(all=True, filters={"name":docker_container_name})
    if container:
        print("Docker container existed, remove it!")
        container[0].remove(force=True)

    print("Docker running...")
    container = docker_client.containers.run(docker_image_name,
                                             name=docker_container_name,
                                             volumes=docker_volumes,
                                             environment=docker_environment,
                                             user=docker_user,
                                             tty=True,
                                             detach=True)

    try:
        result = container.exec_run(__script_cmd(), workdir=docker_workdir)
        print("Docker exec retrun:%d\n%s" %(result.exit_code,result.output))
    except Exception as e:
        print("Docker exec exec failed: %s" %e)
    container.remove(force=True)
    return result.exit_code

def __docker_get_enable():
    return __pro.docker.get("enable", False)

def __docker_inside():
    return os.path.exists("/.dockerenv")

def __export_environment():
    for key, value in __pro.environment.items() :
        os.environ[key] = value

def __generate_version_info():
    timestamp = time.strftime("%Y%m%d%H%M%S",time.localtime())
    git_cmd = "git branch -a 2>/dev/null | grep \"\->\" | awk '{print $NF}'"
    st , result = commands.getstatusoutput(git_cmd)
    if st != 0 :
        return
    start = result.find('al')
    if start == -1 :
        return
    end = len(result)
    branch_info = result[start : end]
    branch_info = branch_info.upper()
    release_version =  timestamp + '_' + branch_info
    project_path = __pro.environment.get("BUILD_PATH")
    release_dir = os.path.join("../build/{project_path}/".format(project_path=project_path), "socVersion")
    data = {
        "version" : release_version,
    }
    with open(release_dir, "w+") as file:
        json.dump(data, file, indent=4, sort_keys=True)


def __collect_commit_info():
    timestamp = time.strftime("%Y%m%d%H%M%S",time.localtime())
    git_cmd = "repo forall -c git log -1 --pretty=oneline"
    st , result = commands.getstatusoutput(git_cmd)
    if st != 0 :
        return
    project_path = __pro.environment.get("BUILD_PATH")
    filename = '../build/{project_path}/commit_info'.format(project_path=project_path)
    if os.path.exists(filename):
        os.remove(filename)

    with open(filename, "a") as file:
        file.write(timestamp)
        file.write("\n")
        file.write(result)
        file.write("\n")

    commits = result.split("\n", -1)
    fileHandler = open(filename,  "rwa")
    for commit in commits:
        line = fileHandler.readline()
        if not line :
            break;
        start = line.find('(')
        if start == -1 or end == -1:
            break
        end = line.find(')')
        line = line[:start] + ' : ' + line[end:]
    fileHandler.close()

def __collect_carpropertymanager_commit_info(version):
    timestamp = time.strftime("%Y%m%d%H%M%S",time.localtime())
    builddir = __pro.builder.get("builddir")
    git_cmd = "cd " + os.path.abspath(builddir) +  "/../../../frameworks/cm " + "&& git log -1 --pretty=oneline"
    st , result = commands.getstatusoutput(git_cmd)
    if st != 0 :
        return

    filename = os.path.join(builddir, "../../../frameworks/cm/carpropertymanager/version_info")
    if os.path.exists(filename):
        os.remove(filename)

    with open(filename, "a") as file:
        file.write("BuildTime: ")
        file.write(timestamp)
        file.write("\n")
        file.write("Version: ")
        file.write(version)
        file.write("\n\n")
        file.write(result)
        file.write("\n")

    commits = result.split("\n", -1)
    fileHandler = open(filename,  "rwa")
    for commit in commits:
        line = fileHandler.readline()
        if not line :
            break;
        start = line.find('(')
        if start == -1 or end == -1:
            break
        end = line.find(')')
        line = line[:start] + ' : ' + line[end:]
    fileHandler.close()

def __prebuilt_copy():
    #prebuilt
    if __pro.vehicletype == "n50":
        alcom_src_bin="../../tools/build/baic_n50/autolink/bin/alcom"
        alcom_dst_bin="../release/baic_n50/bin/alcom"
        alcom_src_lib="../../infrastructure/prebuilt/alc/lib/libalcomeclient.so"
        alcom_dst_lib="../release/baic_n50/lib/libalcomeclient.so"
        json_src_lib="../../infrastructure/prebuilt/alc/lib/libjson.so.1"
        json_dst_lib="../release/baic_n50/lib/libjson.so.1"
        ham_src_bin="../../infrastructure/prebuilt/alc/bin/ham"
        ham_dst_bin="../release/baic_n50/bin/ham"
        rtkservice_src_bin="../../midware_ad/prebuilt/rtkservice/rtk_server"
        rtkservice_dst_bin="../release/baic_n50/bin/rtk_server"
        ham_src_lib="../../infrastructure/prebuilt/alc/lib/libham.so.2"
        ham_dst_lib="../release/baic_n50/lib/libham.so.2"
        diag_dcm_src_lib="../../infrastructure/diagserver/core/dcm/lib/libdiagdcm.so"
        diag_dcm_dst_lib="../release/baic_n50/lib/libdiagdcm.so"
        diag_doip_src_lib="../../infrastructure/tpmanager/prebuild/doip/lib/libdoip.so"
        diag_doip_dst_lib="../release/baic_n50/lib/libdoip.so"
        diag_common_src_lib="../../infrastructure/diagserver/prebuild/common/lib/libdiagcommon.so"
        diag_common_dst_lib="../release/baic_n50/lib/libdiagcommon.so"
        rtkservice_src_lib="../../midware_ad/prebuilt/rtkservice/libcmcc_sdk.so"
        rtkservice_dst_lib="../release/baic_n50/lib/libcmcc_sdk.so"
        vehicleservice_prop_cfg_src_file = "../../hal/vehicleservice/files/vehicle_prop_config.json"
        vehicleservice_prop_val_src_file = "../../hal/vehicleservice/files/vehicle_prop_value.json"
        vehicleservice_prop_cfg_dst_file = "../release/baic_n50/etc/vehicle/vehicle_prop_config.json"
        vehicleservice_prop_val_dst_file = "../release/baic_n50/etc/vehicle/vehicle_prop_value.json"
    elif __pro.vehicletype == "n80":
        alcom_src_bin="../../tools/build/baic_n80/autolink/bin/alcom"
        alcom_dst_bin="../release/baic_n80/bin/alcom"
        alcom_src_lib="../../infrastructure/prebuilt/alc/lib/libalcomeclient.so"
        alcom_dst_lib="../release/baic_n80/lib/libalcomeclient.so"
        json_src_lib="../../infrastructure/prebuilt/alc/lib/libjson.so.1"
        json_dst_lib="../release/baic_n80/lib/libjson.so.1"
        ham_src_bin="../../infrastructure/prebuilt/alc/bin/ham"
        ham_dst_bin="../release/baic_n80/bin/ham"
        rtkservice_src_bin="../../midware_ad/prebuilt/rtkservice/rtk_server"
        rtkservice_dst_bin="../release/baic_n80/bin/rtk_server"
        ham_src_lib="../../infrastructure/prebuilt/alc/lib/libham.so.2"
        ham_dst_lib="../release/baic_n80/lib/libham.so.2"
        diag_dcm_src_lib="../../infrastructure/diagserver/core/dcm/lib/libdiagdcm.so"
        diag_dcm_dst_lib="../release/baic_n80/lib/libdiagdcm.so"
        diag_doip_src_lib="../../infrastructure/tpmanager/prebuild/doip/lib/libdoip.so"
        diag_doip_dst_lib="../release/baic_n80/lib/libdoip.so"
        diag_common_src_lib="../../infrastructure/diagserver/prebuild/common/lib/libdiagcommon.so"
        diag_common_dst_lib="../release/baic_n80/lib/libdiagcommon.so"
        rtkservice_src_lib="../../midware_ad/prebuilt/rtkservice/libcmcc_sdk.so"
        rtkservice_dst_lib="../release/baic_n80/lib/libcmcc_sdk.so"
        vehicleservice_prop_cfg_src_file = "../../hal/vehicleservice/files_n80/vehicle_prop_config.json"
        vehicleservice_prop_val_src_file = "../../hal/vehicleservice/files_n80/vehicle_prop_value.json"
        vehicleservice_prop_cfg_dst_file = "../release/baic_n80/etc/vehicle/vehicle_prop_config.json"
        vehicleservice_prop_val_dst_file = "../release/baic_n80/etc/vehicle/vehicle_prop_value.json"

    shutil.copy2(alcom_src_bin, alcom_dst_bin)
    shutil.copy2(alcom_src_lib, alcom_dst_lib)
    shutil.copy2(json_src_lib, json_dst_lib)
    shutil.copy2(ham_src_bin, ham_dst_bin)
    shutil.copy2(ham_src_lib, ham_dst_lib)
    shutil.copy2(rtkservice_src_bin, rtkservice_dst_bin)
    shutil.copy2(rtkservice_src_lib, rtkservice_dst_lib)
    shutil.copy2(diag_dcm_src_lib, diag_dcm_dst_lib)
    shutil.copy2(diag_dcm_src_lib, diag_dcm_dst_lib)
    shutil.copy2(diag_doip_src_lib, diag_doip_dst_lib)
    shutil.copy2(diag_common_src_lib, diag_common_dst_lib)
    shutil.copy2(vehicleservice_prop_cfg_src_file, vehicleservice_prop_cfg_dst_file)
    shutil.copy2(vehicleservice_prop_val_src_file, vehicleservice_prop_val_dst_file)

def __sdkrelease_copy():
    builddir = __pro.builder.get("builddir")
    # create bsp_qcomm directory
    bsp_qcomm_dir = os.path.join(builddir, "../../release/bsp-qcomm")
    if os.path.exists(bsp_qcomm_dir):
        shutil.rmtree(bsp_qcomm_dir)
    os.makedirs(bsp_qcomm_dir)

    # create bsp_qcomm_bin directory
    bsp_qcomm_bin_dir = os.path.join(builddir, "../../release/bsp-qcomm/bin")
    if os.path.exists(bsp_qcomm_bin_dir):
        shutil.rmtree(bsp_qcomm_bin_dir)
    os.makedirs(bsp_qcomm_bin_dir)
    # create bsp_qcomm_lib directory
    bsp_qcomm_lib_dir = os.path.join(builddir, "../../release/bsp-qcomm/lib")
    if os.path.exists(bsp_qcomm_lib_dir):
        shutil.rmtree(bsp_qcomm_lib_dir)
    os.makedirs(bsp_qcomm_lib_dir)
    # create bsp_qcomm_include directory
    bsp_qcomm_include_dir = os.path.join(builddir, "../../release/bsp-qcomm/include")
    if os.path.exists(bsp_qcomm_include_dir):
        shutil.rmtree(bsp_qcomm_include_dir)
    os.makedirs(bsp_qcomm_include_dir)

    #bin
    fdbxclient_src_bin="../release/baic_n50/bin/fdbxclient"
    fdbxclient_dst_bin="../release/bsp-qcomm/bin/fdbxclient"
    fdbxserver_src_bin="../release/baic_n50/bin/fdbxserver"
    fdbxserver_dst_bin="../release/bsp-qcomm/bin/fdbxserver"
    host_server_src_bin="../release/baic_n50/bin/host_server"
    host_server_dst_bin="../release/bsp-qcomm/bin/host_server"
    logsvc_src_bin="../release/baic_n50/bin/logsvc"
    logsvc_dst_bin="../release/bsp-qcomm/bin/logsvc"
    logviewer_src_bin="../release/baic_n50/bin/logviewer"
    logviewer_dst_bin="../release/bsp-qcomm/bin/logviewer"
    name_server_src_bin="../release/baic_n50/bin/name_server"
    name_server_dst_bin="../release/bsp-qcomm/bin/name_server"
    lsclt_src_bin="../release/baic_n50/bin/lsclt"
    lsclt_dst_bin="../release/bsp-qcomm/bin/lsclt"
    lsevt_src_bin="../release/baic_n50/bin/lsevt"
    lsevt_dst_bin="../release/bsp-qcomm/bin/lsevt"
    lshost_src_bin="../release/baic_n50/bin/lshost"
    lshost_dst_bin="../release/bsp-qcomm/bin/lshost"
    lssvc_src_bin="../release/baic_n50/bin/lssvc"
    lssvc_dst_bin="../release/bsp-qcomm/bin/lssvc"
    vehicleservice_src_bin="../release/baic_n50/bin/vehicleservice"
    vehicleservice_dst_bin="../release/bsp-qcomm/bin/vehicleservice"
    #lib
    release_fdbus_src_lib="../release/baic_n50/lib/libfdbus.so"
    release_fdbus_dst_lib="../release/bsp-qcomm/lib/libfdbus.so"
    release_fdbusclib_src_lib="../release/baic_n50/lib/libfdbus-clib.so"
    release_fdbusclib_dst_lib="../release/bsp-qcomm/lib/libfdbus-clib.so"
    release_protobuflite_src_lib="../release/baic_n50/lib/libprotobuf-lite.so.********"
    release_protobuflite_dst_lib="../release/bsp-qcomm/lib/libprotobuf-lite.so.********"
    release_protobuf_src_lib="../release/baic_n50/lib/libprotobuf.so.********"
    release_protobuf_dst_lib="../release/bsp-qcomm/lib/libprotobuf.so.********"
    vehicleclient_src_lib="../release/baic_n50/lib/libautolink_vehicleclient_qnx.so"
    vehicleclient_dst_lib="../release/bsp-qcomm/lib/libautolink_vehicleclient_qnx.so"
    #include
    protobuf_src_include="../../external/protobuf/include"
    protobuf_dst_include="../release/bsp-qcomm/include/protobuf"
    fdbus_src_include="../../infrastructure/fdbus/public/fdbus"
    fdbus_dst_include="../release/bsp-qcomm/include/fdbus"
    #vehicleservice
    vehicleservice_src_include="../../hal/vehicleservice"
    vehicleservice_dst_include="../release/bsp-qcomm/include/vehicleservice"

    shutil.copy2(fdbxclient_src_bin, fdbxclient_dst_bin)
    shutil.copy2(fdbxserver_src_bin, fdbxserver_dst_bin)
    shutil.copy2(host_server_src_bin, host_server_dst_bin)
    shutil.copy2(logsvc_src_bin, logsvc_dst_bin)
    shutil.copy2(logviewer_src_bin, logviewer_dst_bin)
    shutil.copy2(name_server_src_bin, name_server_dst_bin)
    shutil.copy2(lsclt_src_bin, lsclt_dst_bin)
    shutil.copy2(lsevt_src_bin, lsevt_dst_bin)
    shutil.copy2(lshost_src_bin, lshost_dst_bin)
    shutil.copy2(lssvc_src_bin, lssvc_dst_bin)
    shutil.copy2(vehicleservice_src_bin, vehicleservice_dst_bin)
    shutil.copy2(release_fdbus_src_lib, release_fdbus_dst_lib)
    shutil.copy2(release_fdbusclib_src_lib, release_fdbusclib_dst_lib)
    shutil.copy2(release_protobuflite_src_lib, release_protobuflite_dst_lib)
    shutil.copy2(release_protobuf_src_lib, release_protobuf_dst_lib)
    shutil.copy2(vehicleclient_src_lib, vehicleclient_dst_lib)
    shutil.copytree(protobuf_src_include, protobuf_dst_include)
    shutil.copytree(fdbus_src_include, fdbus_dst_include)
    shutil.copytree(vehicleservice_src_include, vehicleservice_dst_include)
    shutil.rmtree("../release/bsp-qcomm/include/vehicleservice/impl")
    shutil.move("../release/bsp-qcomm/include/vehicleservice/autolink.viclient.proto", "../release/bsp-qcomm/include/vehicleservice/autolink.vehicle.proto")
    shutil.move("../release/bsp-qcomm/include/vehicleservice/autolink.viclient.pb.cc", "../release/bsp-qcomm/include/vehicleservice/autolink.vehicle.pb.cc")
    shutil.move("../release/bsp-qcomm/include/vehicleservice/autolink.viclient.pb.h", "../release/bsp-qcomm/include/vehicleservice/autolink.vehicle.pb.h")

    os.chdir("../release/bsp-qcomm/lib")
    os.symlink("libprotobuf-lite.so.********", "libprotobuf-lite.so")
    os.symlink("libprotobuf.so.********", "libprotobuf.so")
    os.chdir("../../../scripts")

def __sdkrelease_conan_new(user, channel, version):
    cmakedir = __pro.builder.get("cmakedir")
    builddir = __pro.builder.get("builddir")
    
    conan_file_dir = os.path.abspath(builddir) + "/../../release/conanfile.py"
    print("conan_file_dir: %s" % conan_file_dir)
    if os.path.exists(conan_file_dir):
        print("conanfile.py exists, need remove it!")
        try:
            os.remove(conan_file_dir)
            print("conanfile.py delete success!")
        except Exception as e:
            print("conanfile.py delete exception!")
    else:
        print("conanfile.py is not exist!")

    conan_new_cmd = "cd " + os.path.abspath(cmakedir) +  "/../release" + " && conan new bsp-qcomm/" + version + "@" + str(user) + "/" + str(channel) + \
                        " --bare && cd " + os.path.abspath(cmakedir) +  "/../scripts/"

    subprocess.check_call(
        conan_new_cmd,
        shell=True,
        stderr=subprocess.STDOUT,
        cwd=builddir
    )

    with open(conan_file_dir, "r") as file:
        lines = [file.readlines() for _ in xrange(6)]

    with open(conan_file_dir, "a") as file:
        file.write("\n")
        file.write("    user = \"" + user + "\"" + "\n")
        file.write("    channel = \"" + channel + "\"" + "\n")

def __sdkrelease_conan_export():
    cmakedir = __pro.builder.get("cmakedir")
    builddir = __pro.builder.get("builddir")

    conan_new_cmd = "cd " + os.path.abspath(cmakedir) +  "/../release" + " && conan export-pkg -pf bsp-qcomm conanfile.py -f && cd " + os.path.abspath(cmakedir) +  "/../scripts/"
    subprocess.check_call(
        conan_new_cmd,
        shell=True,
        stderr=subprocess.STDOUT,
        cwd=builddir
    )

def __sdkrelease_conan_upload(user, channel, version):
    cmakedir = __pro.builder.get("cmakedir")
    builddir = __pro.builder.get("builddir")

    conan_upload_cmd = "cd " + os.path.abspath(cmakedir) +  "/../release" + " && conan upload  bsp-qcomm/" + version + "@" + user + "/" + channel + " -r=al --all && cd " \
                        + os.path.abspath(cmakedir) +  "/../scripts/"

    subprocess.check_call(
        conan_upload_cmd,
        shell=True,
        stderr=subprocess.STDOUT,
        cwd=builddir
    )

def __sdkautodrive_copy():
    builddir = __pro.builder.get("builddir")
    # create bsp_qcomm directory
    bsp_qcomm_dir = os.path.join(builddir, "../../release/bsp-qcomm")
    if os.path.exists(bsp_qcomm_dir):
        shutil.rmtree(bsp_qcomm_dir)
    os.makedirs(bsp_qcomm_dir)

    # create bsp_qcomm_lib directory
    bsp_qcomm_lib_dir = os.path.join(builddir, "../../release/bsp-qcomm/lib")
    if os.path.exists(bsp_qcomm_lib_dir):
        shutil.rmtree(bsp_qcomm_lib_dir)
    os.makedirs(bsp_qcomm_lib_dir)
    # create bsp_qcomm_include directory
    bsp_qcomm_include_dir = os.path.join(builddir, "../../release/bsp-qcomm/include")
    if os.path.exists(bsp_qcomm_include_dir):
        shutil.rmtree(bsp_qcomm_include_dir)
    os.makedirs(bsp_qcomm_include_dir)

    if __pro.vehicletype == "n50":
        #lib
        shutil.copy("../release/baic_n50/lib/libal_cm.so", "../release/bsp-qcomm/lib/libal_cm.so")
        shutil.copy("../release/baic_n50/lib/libal_log.so", "../release/bsp-qcomm/lib/libal_log.so")
        shutil.copy("../release/baic_n50/lib/libfdbus.so", "../release/bsp-qcomm/lib/libfdbus.so")
        shutil.copy("../release/baic_n50/lib/libfdbus-clib.so", "../release/bsp-qcomm/lib/libfdbus-clib.so")
        shutil.copy("../release/baic_n50/lib/libprotobuf-lite.so.********", "../release/bsp-qcomm/lib/libprotobuf-lite.so.********")
        shutil.copy("../release/baic_n50/lib/libprotobuf.so.********", "../release/bsp-qcomm/lib/libprotobuf.so.********")
    elif __pro.vehicletype == "n80":
        #lib
        shutil.copy("../release/baic_n80/lib/libal_cm.so", "../release/bsp-qcomm/lib/libal_cm.so")
        shutil.copy("../release/baic_n80/lib/libal_log.so", "../release/bsp-qcomm/lib/libal_log.so")
        shutil.copy("../release/baic_n80/lib/libfdbus.so", "../release/bsp-qcomm/lib/libfdbus.so")
        shutil.copy("../release/baic_n80/lib/libfdbus-clib.so", "../release/bsp-qcomm/lib/libfdbus-clib.so")
        shutil.copy("../release/baic_n80/lib/libprotobuf-lite.so.********", "../release/bsp-qcomm/lib/libprotobuf-lite.so.********")
        shutil.copy("../release/baic_n80/lib/libprotobuf.so.********", "../release/bsp-qcomm/lib/libprotobuf.so.********")

    # include
    shutil.copy("../../frameworks/cm/carpropertymanager/car_property_manager.h", "../release/bsp-qcomm/include/car_property_manager.h")
    shutil.copy("../../frameworks/cm/carpropertymanager/car_property_types.h", "../release/bsp-qcomm/include/car_property_types.h")
    shutil.copy("../../frameworks/cm/carpropertymanager/car_property_value.h", "../release/bsp-qcomm/include/car_property_value.h")
    shutil.copy("../../frameworks/cm/carpropertymanager/readme.md", "../release/bsp-qcomm/readme.md")
    shutil.copy("../../frameworks/cm/carpropertymanager/version_info", "../release/bsp-qcomm/version_info")

    os.chdir("../release/bsp-qcomm/lib")
    os.symlink("libprotobuf-lite.so.********", "libprotobuf-lite.so")
    os.symlink("libprotobuf.so.********", "libprotobuf.so")
    os.chdir("../../../scripts")


def mk(pro):
    parser = optparse.OptionParser()
    parser.add_option("-r", "--rebuild",
                    action="store_true", dest="rebuild", default=False,
                    help="clean and rebuild")
    parser.add_option("-p", "--upload",
                    action="store_true", dest="upload", default=False,
                    help="using conan compile and upload")
    parser.add_option("-u", "--user",
                    dest="user", default="AL", help="conan upload user")
    parser.add_option("-c", "--channel",
                    dest="channel", default="release", help="conan upload channel")
    parser.add_option("-t", "--tools",
                    dest="tools", default="es11", help="select build tools version")
    parser.add_option("-v", "--version",
                    dest="version", help="conan upload sdk version")
    parser.add_option("-l", "--vehicletype",
                    dest="vehicletype", default="n50", help="select vehicle type, default is n50")

    (options, args) = parser.parse_args()

    global __pro
    __pro = pro

    os.chdir(os.path.abspath(os.path.dirname(__file__)))

    __initsdp(options.tools)
    __genFdbus()

    __export_environment()

    #if __docker_get_enable() and not __docker_inside():
    #    return __run_in_docker(__script_cmd())
    __compile(options.rebuild)
    __generate_version_info()
    __collect_commit_info()
    __pack()
    __prebuilt_copy()
    if (options.upload):
        if options.version:
            #__sdkrelease_copy()
            __collect_carpropertymanager_commit_info(options.version)
            __sdkautodrive_copy()
            __sdkrelease_conan_new(options.user, options.channel, options.version)
            __sdkrelease_conan_export()
            __sdkrelease_conan_upload(options.user, options.channel, options.version)
        else:
            print("Please input sdk version first!!!")
            return
    else:
        return
