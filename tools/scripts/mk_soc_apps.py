#!/usr/bin/python2
import os
import mk
import sys
import subprocess

pro = mk.Product()

pro.workspace = "../"


pro.kanzihome = "/opt/Kanzi710"

pro.mountdirectory = "/mnt/autolink/apps/al-qnx-apps"

pro.vehicletype = "n50"

pro.builder = {
    "cc"          : "ntoaarch64-gcc",
    "cxx"         : "ntoaarch64-g++",
    "ccflags"     : "-D_QNX_SOURCE -D__QNXNTO__ -D_GLIBCXX_USE_C99",
    "cxxflags"    : "-std=c++14 -D_QNX_SOURCE -D__QNXNTO__ -D_GLIBCXX_USE_C99",
    "ldflags"     : "-L $QNX_TARGET/aarch64le/io-sock/lib",
    "cmakedir"    : "../cmake/",
    "builddir"    : "../build/baic_n50",
    "jobs"        : 8,
    "verbose"     : True
}

pro.docker = {
    "enable" : True
}

pro.packager = {
    "dst_prefix" : "../release/baic_n50",
    "files" : {
        # infrastructure
        # "../build/baic_n50/autolink/doip_uds_fdb"                  : "bin/doip_uds_fdb",
        "../build/baic_n50/autolink/lib/libfdbus.so"        : "lib/libfdbus.so",
        "../build/baic_n50/autolink/lib/libfdbus-clib.so"   : "lib/libfdbus-clib.so",
        "../build/baic_n50/autolink/bin/fdbxserver"         : "bin/fdbxserver",
        "../build/baic_n50/autolink/bin/fdbxclient"         : "bin/fdbxclient",
        "../build/baic_n50/autolink/bin/host_server"        : "bin/host_server",
        "../build/baic_n50/autolink/bin/name_server"        : "bin/name_server",
        "../build/baic_n50/autolink/bin/logsvc"             : "bin/logsvc",
        "../build/baic_n50/autolink/bin/logviewer"          : "bin/logviewer",
        "../build/baic_n50/autolink/bin/lsclt"              : "bin/lsclt",
        "../build/baic_n50/autolink/bin/lsevt"              : "bin/lsevt",
        "../build/baic_n50/autolink/bin/lshost"             : "bin/lshost",
        "../build/baic_n50/autolink/bin/lssvc"              : "bin/lssvc",
        "../build/baic_n50/autolink/lib/libalcomeclient.so" : "lib/libalcomeclient.so",
        "../build/baic_n50/autolink/bin/psis_server"        : "bin/psis_server",
        "../build/baic_n50/autolink/lib/libal_psis.so"      : "lib/libal_psis.so",
        "../build/baic_n50/autolink/bin/psis_client"        : "bin/psis_client",
        "../build/baic_n50/autolink/lib/libal_log.so"       : "lib/libal_log.so",
        "../../../../vendor/autolink/infrastructure/log/qnx-scene-capture.sh" : "qnx-scene-capture.sh",
        # hal
        "../build/baic_n50/autolink/lib/libautolink_vehicleclient_qnx.so"    : "lib/libautolink_vehicleclient_qnx.so",
        "../build/baic_n50/autolink/bin/vehicleservice"    : "bin/vehicleservice",
        "../build/baic_n50/autolink/bin/vehicle_test"      : "bin/vehicle_test",
        "../build/baic_n50/autolink/bin/vehicledbg"        : "bin/vehicledbg",
        "../build/baic_n50/autolink/bin/alpowerhal"        : "bin/alpowerhal",
        "../build/baic_n50/autolink/bin/aldisplayhal"        : "bin/aldisplayhal",
        "../build/baic_n50/autolink/bin/variant_server"    : "bin/variant_server",
        "../build/baic_n50/autolink/bin/variant_tool"    : "bin/variant_tool",
        # framework
        "../build/baic_n50/autolink/lib/libal_core.so"   : "lib/libal_core.so",
        "../build/baic_n50/autolink/lib/libal_pa.so"     : "lib/libal_pa.so",
        "../build/baic_n50/autolink/lib/libal_cm.so"     : "lib/libal_cm.so",
        # midware
        "../build/baic_n50/autolink/bin/cluster_early_midware"         : "bin/cluster_early_midware",
        "../build/baic_n50/autolink/bin/avas_midware"                  : "bin/avas_midware",
        "../build/baic_n50/autolink/bin/anim_midware"                  : "bin/anim_midware",
        "../build/baic_n50/autolink/bin/cluster_midware"               : "bin/cluster_midware",
        "../build/baic_n50/autolink/bin/diag_midware"                  : "bin/diag_midware",
        "../build/baic_n50/autolink/bin/facreset_midware"              : "bin/facreset_midware",
        "../build/baic_n50/autolink/bin/baic_uac"            : "bin/baic_uac",
        "../build/baic_n50/autolink/bin/baic_installer"            : "bin/baic_installer",
        "../../../../vendor/autolink/midware/resources/animation"          : "midware_resources/animation",
        "../../../../vendor/autolink/midware/resources/uac"                : "midware_resources/uac",
        "../../../../vendor/autolink/midware/resources/sounds"             : "midware_resources/sounds",
        "../../../../vendor/autolink/midware/resources/test_sounds"        : "midware_resources/test_sounds",
        #"../../../../vendor/autolink/midware/resources/car_cfg_d01_v1"     : "midware_resources/car_cfg_d01_v1",
        # dms
        #"../../../../vendor/sensetime/dms/data"                            : "midware_resources/dms/data",
        #"../../../../vendor/sensetime/dms/lib"                             : "lib/dms_lib",
        #"../build/baic_n50/autolink/dms_midware"                   : "bin/dms_midware",
        # oms
        #"../../../../vendor/sensetime/oms/data"                            : "midware_resources/oms/data",
        #"../../../../vendor/sensetime/oms/lib"                             : "lib/dms_lib",
        #"../build/baic_n50/autolink/oms_midware"                   : "bin/oms_midware",
        # avm
        #"../build/baic_n50/autolink/ex_avm_midware"                : "bin/ex_avm_midware",
        # drm
        # "../build/baic_n50/autolink/drm_midware"                   : "bin/drm_midware",
        # hmi
        "/opt/Kanzi710/Engine/lib/qnx710_screen_aarch64/ES3_Release/libkzcore.so"   : "lib/libkzcore.so",
        "/opt/Kanzi710/Engine/lib/qnx710_screen_aarch64/ES3_Release/libkzcoreui.so" : "lib/libkzcoreui.so",
        "/opt/Kanzi710/Engine/lib/qnx710_screen_aarch64/ES3_Release/libkzui.so"     : "lib/libkzui.so",
        "../build/baic_n50/autolink/bin/EarlyTT"         : "bin/EarlyTT",
        "../build/baic_n50/autolink/bin/cluster-hmi"     : "hmi_resource/cluster-hmi",
        #"../build/baic_n50/autolink/bin/avm-hmi"         : "avm_resource/avm-hmi",
        "../build/baic_n50/autolink/lib/libClusterFunction.so"   : "lib/libClusterFunction.so",
        #"../build/baic_n50/autolink/lib/libFunctionAVM.so"       : "lib/libFunctionAVM.so",
        "../../../../vendor/autolink/hmi/N50/Cluster/ClusterFunction/Application/bin" : "hmi_resource",
        # "../../../../vendor/autolink/hmi/N50/AVM/Application/bin"                     : "avm_resource",
        # # startup
        "../startups/startup.sh"             : "startup.sh",
        "../startups/baic/apps.list"      : "apps.list",
        "../startups/baic/apps_idcu.list" : "apps_idcu.list",
        # version
        "../build/baic_n50/socVersion"  : "./socVersion",
        #"../build/baic_n50/commit_info" : "./commit_info",
        # uac
        #"../vendor/autolink/midware/uac/extlib/sail_updater/lib/liblogapi.so"                : "lib/uac_lib/",
        #"../vendor/autolink/midware/uac/extlib/sail_updater/lib/libpmem_client.so"           : "lib/uac_lib/",
        #"../vendor/autolink/midware/uac/extlib/sail_updater/lib/libqcmicrc.so"               : "lib/uac_lib/",
        #"../vendor/autolink/midware/uac/extlib/sail_updater/lib/libsailupdate.so"            : "lib/uac_lib/",
        #"../vendor/autolink/midware/uac/extlib/sail_updater/lib/libsscdclient.so"            : "lib/uac_lib/",
        #"../vendor/autolink/midware/uac/extlib/sail_updater/lib/libswupdate_sail_updater.so" : "lib/uac_lib/",
        #"../vendor/autolink/midware/uac/extlib/installer/lib/libabinstaller.so"              : "lib/uac_lib/",
        #"../vendor/autolink/midware/uac/extlib/installer/lib/libabinstallerpl.so"            : "lib/uac_lib/",
        # lionOta
        # "../external/lionOta/bin"                : "bin/",
        # "../external/lionOta/lib"                : "lib/lion_lib/",
        # "../external/lionOta/etc"                : "etc/",
        # someip
        "../../../../vendor/autolink/frameworks/pa/ISomeIP/impl/hirain/ThirdPart/ara_com/bin/ara_com_daemon" : "bin/ara_com_daemon",
        "../../../../vendor/autolink/frameworks/pa/ISomeIP/impl/hirain/ThirdPart/lib" : "lib/someip",
        "../../../../vendor/autolink/frameworks/pa/ISomeIP/impl/hirain/ThirdPart/manifest/ICC_QNX/machine_manifest.toml"        : "midware_resources/someip/machine_manifest.toml",
        "../../../../vendor/autolink/frameworks/pa/ISomeIP/impl/hirain/ThirdPart/ara_com/etc/MANIFEST_ara_com_daemon.toml"      : "midware_resources/someip/MANIFEST_ara_com_daemon.toml",
        "../../../../vendor/autolink/frameworks/pa/ISomeIP/impl/hirain/ThirdPart/icc_qnx_app/etc/MANIFEST_icc_qnx.toml"         : "midware_resources/someip/MANIFEST_icc_qnx.toml",
        "../../../../vendor/autolink/frameworks/pa/ISomeIP/impl/hirain/ThirdPart/icc_qnx_app/etc/service_instance_icc_qnx.toml" : "midware_resources/someip/service_instance_icc_qnx.toml",
        # debugger
        "../build/baic_n50/autolink/bin/carpropmgrdbgr"  : "bin/carpropmgrdbgr",
        "../build/baic_n50/autolink/bin/iscreendbgr"     : "bin/iscreendbgr",
        "../build/baic_n50/autolink/bin/audiodbgr"       : "bin/audiodbgr",
        "../build/baic_n50/autolink/bin/videodbgr"       : "bin/videodbgr",
        "../build/baic_n50/autolink/bin/adasSender"       : "bin/adasSender",
        "../build/baic_n50/autolink/bin/perfProber"       : "bin/perfProber",
        "../build/baic_n50/autolink/bin/apiVerifier"       : "bin/apiVerifier",
        "../../frameworks/cm/carpropertymanager/debug/perfProber/stresstest.sh"       : "stresstest.sh",
        # avas
        "../../supplier/dayintec/avas/avas/sound_type_1/AVAS1_type_classics.wav"     :   "avas/sound_type_1/AVAS1_type_classics.wav",
        "../../supplier/dayintec/avas/avas/sound_type_1/tuning_N50AB_F03.txt"        :   "avas/sound_type_1/tuning_N50AB_F03.txt",
        "../../supplier/dayintec/avas/avas/sound_type_1/tuning_N50AS.txt"            :   "avas/sound_type_1/tuning_N50AS.txt",
        "../../supplier/dayintec/avas/avas/sound_type_1/tuning_N51AB_C02.txt"        :   "avas/sound_type_1/tuning_N51AB_C02.txt",
        "../../supplier/dayintec/avas/avas/sound_type_1/tuning_r_N50AB_F03.txt"      :   "avas/sound_type_1/tuning_r_N50AB_F03.txt",
        "../../supplier/dayintec/avas/avas/sound_type_1/tuning_r_N50AS.txt"          :   "avas/sound_type_1/tuning_r_N50AS.txt",
        "../../supplier/dayintec/avas/avas/sound_type_1/tuning_r_N51AB_C02.txt"      :   "avas/sound_type_1/tuning_r_N51AB_C02.txt",
        "../../supplier/dayintec/avas/avas/sound_type_2/AVAS2_type_sciencefiction.wav"   :   "avas/sound_type_2/AVAS2_type_sciencefiction.wav",
        "../../supplier/dayintec/avas/avas/sound_type_2/tuning_N50AB_F03.txt"        :   "avas/sound_type_2/tuning_N50AB_F03.txt",
        "../../supplier/dayintec/avas/avas/sound_type_2/tuning_N50AS.txt"            :   "avas/sound_type_2/tuning_N50AS.txt",
        "../../supplier/dayintec/avas/avas/sound_type_2/tuning_N51AB_C02.txt"        :   "avas/sound_type_2/tuning_N51AB_C02.txt",
        "../../supplier/dayintec/avas/avas/sound_type_2/tuning_r_N50AB_F03.txt"      :   "avas/sound_type_2/tuning_r_N50AB_F03.txt",
        "../../supplier/dayintec/avas/avas/sound_type_2/tuning_r_N50AS.txt"          :   "avas/sound_type_2/tuning_r_N50AS.txt",
        "../../supplier/dayintec/avas/avas/sound_type_2/tuning_r_N51AB_C02.txt"      :   "avas/sound_type_2/tuning_r_N51AB_C02.txt",
        "../../supplier/dayintec/avas/avas/Config_N50AB_F03.txt"                     :   "avas/Config_N50AB_F03.txt",
        "../../supplier/dayintec/avas/avas/Config_N50AS.txt"                         :   "avas/Config_N50AS.txt",
        "../../supplier/dayintec/avas/avas/Config_N51AB.txt"                         :   "avas/Config_N51AB.txt",
        "../../supplier/dayintec/avas/avas/libavas_1.8.so"                           :   "avas/libavas_1.8.so",
        "../../supplier/dayintec/avas/avas/libYin1.8_Logic.so"                       :   "avas/libYin1.8_Logic.so",

        # idps
        # "../external/idps/bin"                : "idps/bin/",
        # "../external/idps/lib64"              : "idps/lib64/",
        # "../external/idps/etc"                : "idps/etc/",
        # launcher
        "../build/baic_n50/autolink/bin/launcher"       : "bin/launcher",
        "../build/baic_n50/autolink/bin/applauncher"       : "bin/applauncher",
        "../build/baic_n50/autolink/bin/sysmonitor"       : "bin/sysmonitor",
        # external
        "../../external/protobuf/lib/libprotobuf-lite.so.3.19.5.0"          : "lib/libprotobuf-lite.so.3.19.5.0",
        "../../external/protobuf/lib/libprotobuf-lite.so"                   : "lib/libprotobuf-lite.so",
        "../../external/protobuf/lib/libprotobuf.so.3.19.5.0"               : "lib/libprotobuf.so.3.19.5.0",
        "../../external/protobuf/lib/libprotobuf.so"                        : "lib/libprotobuf.so",
        "../../hal/vehicleservice/files/vehicle_prop_value.json"            : "etc/vehicle/vehicle_prop_value.json",
        "../../hal/vehicleservice/files/vehicle_prop_config.json"           : "etc/vehicle/vehicle_prop_config.json",
        "../../infrastructure/psis/impl/io/al_zyt_psis_property.json"       : "etc/psis/al_zyt_psis_property.json",
        "../../infrastructure/healthymanager/config/phm.json"                : "etc/phm/phm.json",
        # diag_subsystem
        "../build/baic_n50/autolink/bin/diagserver"                         : "bin/diagserver",
        "../build/baic_n50/autolink/lib/libtpmanager.so"                    : "lib/libtpmanager.so",
    }
}

pro.environment = {
    "BUILD_PRODUCT": "BAIC_N50",
    "BUILD_PATH": "baic_n50",
    "BUILD_VERSION": os.environ.get("BUILD_VERSION", "unknow")
}

if __name__ == '__main__':
    ret = mk.mk(pro)
    sys.exit(ret)
