# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/DaYin2/qnx/vendor/autolink/tools/cmake

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50/CMakeFiles /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50/autolink/midware_swp/uac//CMakeFiles/progress.marks
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 autolink/midware_swp/uac/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 autolink/midware_swp/uac/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 autolink/midware_swp/uac/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 autolink/midware_swp/uac/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
autolink/midware_swp/uac/CMakeFiles/build_proto_source.dir/rule:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 autolink/midware_swp/uac/CMakeFiles/build_proto_source.dir/rule
.PHONY : autolink/midware_swp/uac/CMakeFiles/build_proto_source.dir/rule

# Convenience name for target.
build_proto_source: autolink/midware_swp/uac/CMakeFiles/build_proto_source.dir/rule
.PHONY : build_proto_source

# fast build rule for target.
build_proto_source/fast:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/build_proto_source.dir/build.make autolink/midware_swp/uac/CMakeFiles/build_proto_source.dir/build
.PHONY : build_proto_source/fast

# Convenience name for target.
autolink/midware_swp/uac/CMakeFiles/NativeIf.dir/rule:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 autolink/midware_swp/uac/CMakeFiles/NativeIf.dir/rule
.PHONY : autolink/midware_swp/uac/CMakeFiles/NativeIf.dir/rule

# Convenience name for target.
NativeIf: autolink/midware_swp/uac/CMakeFiles/NativeIf.dir/rule
.PHONY : NativeIf

# fast build rule for target.
NativeIf/fast:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/NativeIf.dir/build.make autolink/midware_swp/uac/CMakeFiles/NativeIf.dir/build
.PHONY : NativeIf/fast

# Convenience name for target.
autolink/midware_swp/uac/CMakeFiles/baic_installer.dir/rule:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 autolink/midware_swp/uac/CMakeFiles/baic_installer.dir/rule
.PHONY : autolink/midware_swp/uac/CMakeFiles/baic_installer.dir/rule

# Convenience name for target.
baic_installer: autolink/midware_swp/uac/CMakeFiles/baic_installer.dir/rule
.PHONY : baic_installer

# fast build rule for target.
baic_installer/fast:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_installer.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_installer.dir/build
.PHONY : baic_installer/fast

# Convenience name for target.
autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/rule:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/rule
.PHONY : autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/rule

# Convenience name for target.
baic_uac: autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/rule
.PHONY : baic_uac

# fast build rule for target.
baic_uac/fast:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/build
.PHONY : baic_uac/fast

home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.o: home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.cc.o
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.o

# target to build an object file
home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.cc.o:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_installer.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_installer.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.cc.o
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.cc.o
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.cc.o

home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.i: home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.cc.i
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.i

# target to preprocess a source file
home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.cc.i:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_installer.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_installer.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.cc.i
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.cc.i
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.cc.i

home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.s: home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.cc.s
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.s

# target to generate assembly for a file
home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.cc.s:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_installer.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_installer.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.cc.s
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.cc.s
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.cc.s

home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.o: home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.cpp.o
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.o

# target to build an object file
home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.cpp.o:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.cpp.o
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.cpp.o

home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.i: home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.cpp.i
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.i

# target to preprocess a source file
home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.cpp.i:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.cpp.i
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.cpp.i

home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.s: home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.cpp.s
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.s

# target to generate assembly for a file
home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.cpp.s:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.cpp.s
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.cpp.s

home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.o: home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.cpp.o
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.o

# target to build an object file
home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.cpp.o:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.cpp.o
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.cpp.o

home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.i: home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.cpp.i
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.i

# target to preprocess a source file
home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.cpp.i:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.cpp.i
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.cpp.i

home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.s: home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.cpp.s
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.s

# target to generate assembly for a file
home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.cpp.s:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.cpp.s
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.cpp.s

home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.o: home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.cpp.o
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.o

# target to build an object file
home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.cpp.o:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.cpp.o
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.cpp.o

home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.i: home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.cpp.i
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.i

# target to preprocess a source file
home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.cpp.i:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.cpp.i
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.cpp.i

home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.s: home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.cpp.s
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.s

# target to generate assembly for a file
home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.cpp.s:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.cpp.s
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.cpp.s

home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.o: home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.cpp.o
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.o

# target to build an object file
home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.cpp.o:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.cpp.o
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.cpp.o

home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.i: home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.cpp.i
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.i

# target to preprocess a source file
home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.cpp.i:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.cpp.i
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.cpp.i

home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.s: home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.cpp.s
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.s

# target to generate assembly for a file
home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.cpp.s:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.cpp.s
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.cpp.s

home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.o: home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.cpp.o
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.o

# target to build an object file
home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.cpp.o:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_installer.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_installer.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.cpp.o
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.cpp.o

home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.i: home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.cpp.i
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.i

# target to preprocess a source file
home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.cpp.i:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_installer.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_installer.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.cpp.i
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.cpp.i

home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.s: home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.cpp.s
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.s

# target to generate assembly for a file
home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.cpp.s:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_installer.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_installer.dir/home/<USER>/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.cpp.s
.PHONY : home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... NativeIf"
	@echo "... build_proto_source"
	@echo "... baic_installer"
	@echo "... baic_uac"
	@echo "... home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.o"
	@echo "... home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.i"
	@echo "... home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/idl/idl-gen/NativeIf.pb.s"
	@echo "... home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.o"
	@echo "... home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.i"
	@echo "... home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/baic_uac.s"
	@echo "... home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.o"
	@echo "... home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.i"
	@echo "... home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_if.s"
	@echo "... home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.o"
	@echo "... home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.i"
	@echo "... home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_midware.s"
	@echo "... home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.o"
	@echo "... home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.i"
	@echo "... home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/src/uac_module.s"
	@echo "... home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.o"
	@echo "... home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.i"
	@echo "... home/xietiantian/DaYin2/qnx/vendor/autolink/midware_swp/uac/test/baic_installer.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50 && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

