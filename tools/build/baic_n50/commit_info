20250807155136

... A new version of repo (2.40) is available.
... New version is available at: /home/<USER>/DaYin2/.repo/repo/repo
... The launcher is run from: /usr/bin/repo
!!! The launcher is not writable.  Please talk to your sysadmin or distro
!!! to get an update installed.

6fa2c9513367e4870b22d1aefe64fb8f377369b8 Merge "【change】BAIC-1828 diag模块proto文件放到统一位置【1/3】" into al_dev
290c62f0601a8fca09baa408e6f0015128143a41 Initial empty repository
de26db3742335931b1a551b85daf58e15707a7af 【feature】添加V3.19.5.0 protobuf库
3816324d7e372bd932dd5362c68df184a9beace7 【Feature】D01-35743 : 全量同步D01 videoplayer和VIDC代码到北汽分支【2/2】
20e8f0b0cabdd35234b3e2e9473a5cd5f92f5e44 【change】D01-00000 Core::Timer OnTimeout 时序
b317c765b1c379f9724debcbede727d49c777fe9 【bug/change/feature】【2/4】BAIC-000 : log模块的代码仓库放到infrastruct下,删除framework下log实现,留头文件
49d0e293aac41b456c5d7fe952061e52366e9d3a 【Feature】D01-35743 : 全量同步D01 videoplayer和VIDC代码到北汽分支【1/2】
61a09b4b3f1a04a62ff52d89f14c634a2d6f923d Initial empty repository
7c4ff4d637bb33a93b10256e86e6927a3cf25851 【bug】【BAIC-1367】亮灭屏的状态没有上抛异常
386611e970a32789618eb2fff9a30668a2a4741e 【feature】：BAIC-2009 增加STR唤醒后主动唤醒android系统动作
cb33ec55bba07525c73dfa853630ac6c3ea94908 feature：Rename ALPowerHal to alpowerhal
233f16d14715f537e2591b1a3d7cd42bd62d81b7 【feature】适配新的DBC信号
4efabcbec5ef0bfc098dcdd52d2150f116ccc768 【change】BAIC-1828 move public proto files out and change header file name【2/3】
242f0048030b6fc2c00f4068b3832488afe693aa 【feature】BAIC-304 support release map signal of N80_vehicle_interafce_V1.0.3.xlsx
d0d4247c74714579782cccde250233997ff35d97 【Feature】【N50】【HMI+EarlyTT】【BAIC-0000】:N50调整燃油指示灯位置最后绘制
99cd094933b76481d39aa580ec16e7a87cd3d2f5 【Feature】【BAIC-000000】【N80】【TT】N80轮询灯左右两组，逻辑差异化开发 【原因分析】【N80】【TT】N80轮询灯左右两组，逻辑差异化开发 【解决方案】【N80】【TT】N80轮询灯左右两组，逻辑差异化开发 【自测用例】模拟信号发送,切换不同的信号值,观察显示效果是否正确 【自测方法】10次------------- 【影响范围】N80和N50的TT模块,不涉及其他模块 【代码修改量】100行---------------- 【提交项目/分支】al_dev 【体现版本】2025年8月7日及之后的版本
5ade0bd34fbfa881bbff7040f5a135d8adb4a59f 【change】BAIC-000:【N50/N80】alcom 压测问题修复，优化调整 patch 3
d83f1285810878b3810a1da6e36fad8e3ad68fd9 【bug/change/feature】BAIC-0000：修复dtc上报问题 【原因分析】dtc上报健康管理消息ID错误 【解决方案】修改dtc上报健康管理的消息ID 【自测用例】台架推包，测试程序模拟故障上报，观察上报结果 【自测方法】台架模拟故障上报，观察上报结果 【影响范围】北汽诊断dtc故障上报 【代码修改量】<10 【提交项目/分支】al_dev 【体现版本】2025.07.29
828a2677051a34abb07abac7b4905195232dcdcf 【bug】BAIC-0000: qnx下logsvc scanDir函数失效
e04960e7def6d6973967741e82a4ca19659cdbbd 【feature】BAIC-0000 新增avas_midware的保活
3b2bb92b53766495a40b4382c5a3c681fe3d0d12  【feature】【2/4】BAIC-0000 launcher增加waitfor打印
2450aeea54a3ccd7d052de7dbc0c38797aed5899 【bug/change/feature】BAIC-000 : log日志添加脚本给卓驭使用, 获取信息
8897a64f3359d6001403b8d803244d0428290993 【change】BAIC-000:【N50】alcom 放开log打印定位问题，更新成果物
6d770e9b7ef521ac4252131407bd00f3f5133003 【feature】 Baic-000 psis功能
412f14ac5cb7b839d836925181d017ab787a8dbf 【bug/change/feature】D01-000: someip 2.3通信协议更新
15659b76ed8ff27d56fc93b778f30df6c8b860ab 【bug/change/feature】【BAIC-0000】tpmanager version update【1/3】 【原因分析】tpmanager update 【解决方案】tpmanager update 【自测用例】tpmanager update sync from 8255dev 【自测方法】tpmanager update 【影响范围】tpmanager update 【代码修改量】>100 【提交项目/分支】al_dev 【体现版本】2025-07-02
3c33724756df321800c4f4702f34809c26e0f51a 【bug/change/feature】
c6bf7be09c74fadfa63f1f00bfa501d8b189ce57 BAIC-2183 | 仲裁逻辑修改，提示类型的声音到来，如果不是实时播放，后续不需要出声
3b639a1c0af605ca36aabbb395611ecfe6551f1c Merge "BAIC-1978 | 需求变更,驾驶模式-选择个性化 仪表界面需要显示图标" into al_dev
4d29d7e1eef3afdc3e0a736d922413af636496f6 【feature】BAIC-0000 avas模块的功能实现及增加avas的编译实现代码
331f59e7c357ac1cb95323610afa9c5ccf360d18 【Feature】【N50】【HMI+state】【N50-0000】:增加N80按键页面迁移
49624122a87433697690856b232d6eb126d4da5c Merge "Feature : [BAIC-49]: WHUD开机动画结束标志读取&外发功能" into al_dev
5038b5d263a3c7d5acecdc0fd0bf447e80bbc1f6 【Bug】BAIC-870 : 增加整车电源状态传递
b4e0944763571768e9782fcc537cc4d2f9795371 【Feature】BAIC-49 : 恢复出厂设置动画
ac82337bee5f5660a19fe33f0c1d480c30b84028 Feature : [BAIC-49]:scccom新增燃油低报警功能
ab30f003b9b6470252307da1ec97d915d10e659a 【Feature】BAIC-49 : WHUD六项设置内容更新，下电记忆
0c1641b40c3158961db078a49b5cba5d97df5ee2 【Bug】BAIC-1418 : [启动性能优化] 目前脚本 startup 提前拉起就会出现 2. anim_midware 必挂，且后续拉起后必挂 问题
4298ecf5e7029dbf4216e17aae2272e0d0a03848 【feature】【D01国际】【SOC】【KeyModule】#【D01-INT-10086】:长按右侧方控左按键+语音按键>2s,系统自动恢复到所销售国家的默认语言
f5f2047c4824e9e4cadbdf88dd39825b49f7b52f 【bug】BAIC-2505:充电模式下，不显示加油提示灯
f9143f2f3b57824423f4d460cff092f16934517f 【feature】BAIC-0000 仅后排安全带为系时且车速<25时用户无法取消
88a3aea02bb8ef31e848c9813d27e7e7ca66f5ff update rtkservice 202507291440
65372d8e1b13e20cac844b81d396098ec6caa943 【feature】BAIC-967：迁移diag_midware模块代码从midware到midware_swp【2/3】
a02a9ad9e039dd3034f45e289531aeee4a0a3998 【feature】BAIC-762 add factory reset module 【1/4】
ed9c6007f6de3a9a9be354e307a9b3f38a0ff5a4 【change】  BAIC-1146：卓驭OTA升级接口结构变动
d61603395acd7f3a69c217343515884476f9d706 【bug/change/feature】BAIC-0000：avas调试版本初版N50AS、N50AB_F03、N51AB
449ddde52d5a3ebaf060f2cbcfcd0d2aa36ce1ae 【feature】BAIC-0000 avas功能模块增加，修改对应的cmake文件 【原因分析】avas功能模块增加，修改对应的cmake文件 【解决方案】avas功能模块增加，修改对应的cmake文件 【自测用例】修改对应cmake文件，检查是否正常生成avas_mideware 【自测方法】修改对应cmake文件，检查是否正常生成avas_mideware 【影响范围】avas模块等 【代码修改量】20行 【提交项目/分支】al_dev 【体现版本】2025-8-7日及以后版本
4f3c1adf2223aa3ff70e7f1c870a9e66d0d0294b 【feature】BAIC-0000 avas功能模块编译生成avas_midware后的mk_soc_apps.py修改 【原因分析】avas功能模块编译生成avas_midware后的mk_soc_apps.py修改 【解决方案】avas功能模块编译生成avas_midware后的mk_soc_apps.py修改 【自测用例】拉取最新代码后运行编译脚本查看avas_midware是否存在bin目录下 【自测方法】拉取最新代码后运行编译脚本查看avas_midware是否存在bin目录下 【影响范围】mk_soc_apps.py 【代码修改量】1行 【提交项目/分支】al_dev 【体现版本】2025年8月7日及以后的版本
e307221ff3e3f9e0d8277dad56c9a2c21d24709a Merge "【feature】BAIC-0000 avas功能模块实现后对应的启动脚本的修改" into al_dev
