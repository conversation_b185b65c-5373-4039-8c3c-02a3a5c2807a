# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/DaYin2/qnx/vendor/autolink/tools/cmake

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50/CMakeFiles /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named al_core

# Build rule for target.
al_core: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 al_core
.PHONY : al_core

# fast build rule for target.
al_core/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/al_core.dir/build.make autolink/CMakeFiles/al_core.dir/build
.PHONY : al_core/fast

#=============================================================================
# Target rules for targets named al_core_ut

# Build rule for target.
al_core_ut: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 al_core_ut
.PHONY : al_core_ut

# fast build rule for target.
al_core_ut/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/al_core_ut.dir/build.make autolink/CMakeFiles/al_core_ut.dir/build
.PHONY : al_core_ut/fast

#=============================================================================
# Target rules for targets named al_pa

# Build rule for target.
al_pa: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 al_pa
.PHONY : al_pa

# fast build rule for target.
al_pa/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/al_pa.dir/build.make autolink/CMakeFiles/al_pa.dir/build
.PHONY : al_pa/fast

#=============================================================================
# Target rules for targets named al_cm

# Build rule for target.
al_cm: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 al_cm
.PHONY : al_cm

# fast build rule for target.
al_cm/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/al_cm.dir/build.make autolink/CMakeFiles/al_cm.dir/build
.PHONY : al_cm/fast

#=============================================================================
# Target rules for targets named launcher

# Build rule for target.
launcher: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 launcher
.PHONY : launcher

# fast build rule for target.
launcher/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/launcher.dir/build.make autolink/CMakeFiles/launcher.dir/build
.PHONY : launcher/fast

#=============================================================================
# Target rules for targets named sysmonitor

# Build rule for target.
sysmonitor: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sysmonitor
.PHONY : sysmonitor

# fast build rule for target.
sysmonitor/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/sysmonitor.dir/build.make autolink/CMakeFiles/sysmonitor.dir/build
.PHONY : sysmonitor/fast

#=============================================================================
# Target rules for targets named applauncher

# Build rule for target.
applauncher: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 applauncher
.PHONY : applauncher

# fast build rule for target.
applauncher/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/applauncher.dir/build.make autolink/CMakeFiles/applauncher.dir/build
.PHONY : applauncher/fast

#=============================================================================
# Target rules for targets named cluster_early_midware

# Build rule for target.
cluster_early_midware: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 cluster_early_midware
.PHONY : cluster_early_midware

# fast build rule for target.
cluster_early_midware/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/cluster_early_midware.dir/build.make autolink/CMakeFiles/cluster_early_midware.dir/build
.PHONY : cluster_early_midware/fast

#=============================================================================
# Target rules for targets named avas_midware

# Build rule for target.
avas_midware: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 avas_midware
.PHONY : avas_midware

# fast build rule for target.
avas_midware/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/avas_midware.dir/build.make autolink/CMakeFiles/avas_midware.dir/build
.PHONY : avas_midware/fast

#=============================================================================
# Target rules for targets named anim_midware

# Build rule for target.
anim_midware: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 anim_midware
.PHONY : anim_midware

# fast build rule for target.
anim_midware/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/anim_midware.dir/build.make autolink/CMakeFiles/anim_midware.dir/build
.PHONY : anim_midware/fast

#=============================================================================
# Target rules for targets named cluster_midware

# Build rule for target.
cluster_midware: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 cluster_midware
.PHONY : cluster_midware

# fast build rule for target.
cluster_midware/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/cluster_midware.dir/build.make autolink/CMakeFiles/cluster_midware.dir/build
.PHONY : cluster_midware/fast

#=============================================================================
# Target rules for targets named EarlyTT

# Build rule for target.
EarlyTT: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 EarlyTT
.PHONY : EarlyTT

# fast build rule for target.
EarlyTT/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/EarlyTT.dir/build.make autolink/CMakeFiles/EarlyTT.dir/build
.PHONY : EarlyTT/fast

#=============================================================================
# Target rules for targets named ClusterFunction

# Build rule for target.
ClusterFunction: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ClusterFunction
.PHONY : ClusterFunction

# fast build rule for target.
ClusterFunction/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/ClusterFunction.dir/build.make autolink/CMakeFiles/ClusterFunction.dir/build
.PHONY : ClusterFunction/fast

#=============================================================================
# Target rules for targets named cluster-hmi

# Build rule for target.
cluster-hmi: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 cluster-hmi
.PHONY : cluster-hmi

# fast build rule for target.
cluster-hmi/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/cluster-hmi.dir/build.make autolink/CMakeFiles/cluster-hmi.dir/build
.PHONY : cluster-hmi/fast

#=============================================================================
# Target rules for targets named carpropmgrdbgr

# Build rule for target.
carpropmgrdbgr: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 carpropmgrdbgr
.PHONY : carpropmgrdbgr

# fast build rule for target.
carpropmgrdbgr/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/carpropmgrdbgr.dir/build.make autolink/CMakeFiles/carpropmgrdbgr.dir/build
.PHONY : carpropmgrdbgr/fast

#=============================================================================
# Target rules for targets named adasSender

# Build rule for target.
adasSender: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 adasSender
.PHONY : adasSender

# fast build rule for target.
adasSender/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/adasSender.dir/build.make autolink/CMakeFiles/adasSender.dir/build
.PHONY : adasSender/fast

#=============================================================================
# Target rules for targets named perfProber

# Build rule for target.
perfProber: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 perfProber
.PHONY : perfProber

# fast build rule for target.
perfProber/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/perfProber.dir/build.make autolink/CMakeFiles/perfProber.dir/build
.PHONY : perfProber/fast

#=============================================================================
# Target rules for targets named apiVerifier

# Build rule for target.
apiVerifier: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apiVerifier
.PHONY : apiVerifier

# fast build rule for target.
apiVerifier/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/apiVerifier.dir/build.make autolink/CMakeFiles/apiVerifier.dir/build
.PHONY : apiVerifier/fast

#=============================================================================
# Target rules for targets named carpropmgrtools

# Build rule for target.
carpropmgrtools: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 carpropmgrtools
.PHONY : carpropmgrtools

# fast build rule for target.
carpropmgrtools/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/carpropmgrtools.dir/build.make autolink/CMakeFiles/carpropmgrtools.dir/build
.PHONY : carpropmgrtools/fast

#=============================================================================
# Target rules for targets named audiodbgr

# Build rule for target.
audiodbgr: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 audiodbgr
.PHONY : audiodbgr

# fast build rule for target.
audiodbgr/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/audiodbgr.dir/build.make autolink/CMakeFiles/audiodbgr.dir/build
.PHONY : audiodbgr/fast

#=============================================================================
# Target rules for targets named videodbgr

# Build rule for target.
videodbgr: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 videodbgr
.PHONY : videodbgr

# fast build rule for target.
videodbgr/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/videodbgr.dir/build.make autolink/CMakeFiles/videodbgr.dir/build
.PHONY : videodbgr/fast

#=============================================================================
# Target rules for targets named iscreendbgr

# Build rule for target.
iscreendbgr: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 iscreendbgr
.PHONY : iscreendbgr

# fast build rule for target.
iscreendbgr/fast:
	$(MAKE) $(MAKESILENT) -f autolink/CMakeFiles/iscreendbgr.dir/build.make autolink/CMakeFiles/iscreendbgr.dir/build
.PHONY : iscreendbgr/fast

#=============================================================================
# Target rules for targets named alcomeclient

# Build rule for target.
alcomeclient: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 alcomeclient
.PHONY : alcomeclient

# fast build rule for target.
alcomeclient/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/CMakeFiles/alcomeclient.dir/build.make autolink/infrastructure/CMakeFiles/alcomeclient.dir/build
.PHONY : alcomeclient/fast

#=============================================================================
# Target rules for targets named psis_server

# Build rule for target.
psis_server: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 psis_server
.PHONY : psis_server

# fast build rule for target.
psis_server/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/CMakeFiles/psis_server.dir/build.make autolink/infrastructure/CMakeFiles/psis_server.dir/build
.PHONY : psis_server/fast

#=============================================================================
# Target rules for targets named al_psis

# Build rule for target.
al_psis: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 al_psis
.PHONY : al_psis

# fast build rule for target.
al_psis/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/CMakeFiles/al_psis.dir/build.make autolink/infrastructure/CMakeFiles/al_psis.dir/build
.PHONY : al_psis/fast

#=============================================================================
# Target rules for targets named psis_client

# Build rule for target.
psis_client: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 psis_client
.PHONY : psis_client

# fast build rule for target.
psis_client/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/CMakeFiles/psis_client.dir/build.make autolink/infrastructure/CMakeFiles/psis_client.dir/build
.PHONY : psis_client/fast

#=============================================================================
# Target rules for targets named al_log

# Build rule for target.
al_log: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 al_log
.PHONY : al_log

# fast build rule for target.
al_log/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/CMakeFiles/al_log.dir/build.make autolink/infrastructure/CMakeFiles/al_log.dir/build
.PHONY : al_log/fast

#=============================================================================
# Target rules for targets named uninstall

# Build rule for target.
uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 uninstall
.PHONY : uninstall

# fast build rule for target.
uninstall/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/fdbus/CMakeFiles/uninstall.dir/build.make autolink/infrastructure/fdbus/CMakeFiles/uninstall.dir/build
.PHONY : uninstall/fast

#=============================================================================
# Target rules for targets named fdbus

# Build rule for target.
fdbus: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 fdbus
.PHONY : fdbus

# fast build rule for target.
fdbus/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/fdbus/CMakeFiles/fdbus.dir/build.make autolink/infrastructure/fdbus/CMakeFiles/fdbus.dir/build
.PHONY : fdbus/fast

#=============================================================================
# Target rules for targets named name_server

# Build rule for target.
name_server: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 name_server
.PHONY : name_server

# fast build rule for target.
name_server/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/fdbus/CMakeFiles/name_server.dir/build.make autolink/infrastructure/fdbus/CMakeFiles/name_server.dir/build
.PHONY : name_server/fast

#=============================================================================
# Target rules for targets named host_server

# Build rule for target.
host_server: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 host_server
.PHONY : host_server

# fast build rule for target.
host_server/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/fdbus/CMakeFiles/host_server.dir/build.make autolink/infrastructure/fdbus/CMakeFiles/host_server.dir/build
.PHONY : host_server/fast

#=============================================================================
# Target rules for targets named lssvc

# Build rule for target.
lssvc: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lssvc
.PHONY : lssvc

# fast build rule for target.
lssvc/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/fdbus/CMakeFiles/lssvc.dir/build.make autolink/infrastructure/fdbus/CMakeFiles/lssvc.dir/build
.PHONY : lssvc/fast

#=============================================================================
# Target rules for targets named lshost

# Build rule for target.
lshost: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lshost
.PHONY : lshost

# fast build rule for target.
lshost/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/fdbus/CMakeFiles/lshost.dir/build.make autolink/infrastructure/fdbus/CMakeFiles/lshost.dir/build
.PHONY : lshost/fast

#=============================================================================
# Target rules for targets named lsclt

# Build rule for target.
lsclt: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lsclt
.PHONY : lsclt

# fast build rule for target.
lsclt/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/fdbus/CMakeFiles/lsclt.dir/build.make autolink/infrastructure/fdbus/CMakeFiles/lsclt.dir/build
.PHONY : lsclt/fast

#=============================================================================
# Target rules for targets named logsvc

# Build rule for target.
logsvc: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 logsvc
.PHONY : logsvc

# fast build rule for target.
logsvc/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/fdbus/CMakeFiles/logsvc.dir/build.make autolink/infrastructure/fdbus/CMakeFiles/logsvc.dir/build
.PHONY : logsvc/fast

#=============================================================================
# Target rules for targets named logviewer

# Build rule for target.
logviewer: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 logviewer
.PHONY : logviewer

# fast build rule for target.
logviewer/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/fdbus/CMakeFiles/logviewer.dir/build.make autolink/infrastructure/fdbus/CMakeFiles/logviewer.dir/build
.PHONY : logviewer/fast

#=============================================================================
# Target rules for targets named fdbxclient

# Build rule for target.
fdbxclient: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 fdbxclient
.PHONY : fdbxclient

# fast build rule for target.
fdbxclient/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/fdbus/CMakeFiles/fdbxclient.dir/build.make autolink/infrastructure/fdbus/CMakeFiles/fdbxclient.dir/build
.PHONY : fdbxclient/fast

#=============================================================================
# Target rules for targets named fdbxserver

# Build rule for target.
fdbxserver: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 fdbxserver
.PHONY : fdbxserver

# fast build rule for target.
fdbxserver/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/fdbus/CMakeFiles/fdbxserver.dir/build.make autolink/infrastructure/fdbus/CMakeFiles/fdbxserver.dir/build
.PHONY : fdbxserver/fast

#=============================================================================
# Target rules for targets named ntfcenter

# Build rule for target.
ntfcenter: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ntfcenter
.PHONY : ntfcenter

# fast build rule for target.
ntfcenter/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/fdbus/CMakeFiles/ntfcenter.dir/build.make autolink/infrastructure/fdbus/CMakeFiles/ntfcenter.dir/build
.PHONY : ntfcenter/fast

#=============================================================================
# Target rules for targets named lsevt

# Build rule for target.
lsevt: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lsevt
.PHONY : lsevt

# fast build rule for target.
lsevt/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/fdbus/CMakeFiles/lsevt.dir/build.make autolink/infrastructure/fdbus/CMakeFiles/lsevt.dir/build
.PHONY : lsevt/fast

#=============================================================================
# Target rules for targets named lsdp

# Build rule for target.
lsdp: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lsdp
.PHONY : lsdp

# fast build rule for target.
lsdp/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/fdbus/CMakeFiles/lsdp.dir/build.make autolink/infrastructure/fdbus/CMakeFiles/lsdp.dir/build
.PHONY : lsdp/fast

#=============================================================================
# Target rules for targets named fdbus-clib

# Build rule for target.
fdbus-clib: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 fdbus-clib
.PHONY : fdbus-clib

# fast build rule for target.
fdbus-clib/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/fdbus/CMakeFiles/fdbus-clib.dir/build.make autolink/infrastructure/fdbus/CMakeFiles/fdbus-clib.dir/build
.PHONY : fdbus-clib/fast

#=============================================================================
# Target rules for targets named alcom

# Build rule for target.
alcom: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 alcom
.PHONY : alcom

# fast build rule for target.
alcom/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/alcom/CMakeFiles/alcom.dir/build.make autolink/infrastructure/alcom/CMakeFiles/alcom.dir/build
.PHONY : alcom/fast

#=============================================================================
# Target rules for targets named alcdbgr

# Build rule for target.
alcdbgr: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 alcdbgr
.PHONY : alcdbgr

# fast build rule for target.
alcdbgr/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/alcom/CMakeFiles/alcdbgr.dir/build.make autolink/infrastructure/alcom/CMakeFiles/alcdbgr.dir/build
.PHONY : alcdbgr/fast

#=============================================================================
# Target rules for targets named tpmanager

# Build rule for target.
tpmanager: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 tpmanager
.PHONY : tpmanager

# fast build rule for target.
tpmanager/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/diag_subsystem/CMakeFiles/tpmanager.dir/build.make autolink/infrastructure/diag_subsystem/CMakeFiles/tpmanager.dir/build
.PHONY : tpmanager/fast

#=============================================================================
# Target rules for targets named diagproxy

# Build rule for target.
diagproxy: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 diagproxy
.PHONY : diagproxy

# fast build rule for target.
diagproxy/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/diag_subsystem/diagserver/CMakeFiles/diagproxy.dir/build.make autolink/infrastructure/diag_subsystem/diagserver/CMakeFiles/diagproxy.dir/build
.PHONY : diagproxy/fast

#=============================================================================
# Target rules for targets named dtcproxy

# Build rule for target.
dtcproxy: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 dtcproxy
.PHONY : dtcproxy

# fast build rule for target.
dtcproxy/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/diag_subsystem/diagserver/CMakeFiles/dtcproxy.dir/build.make autolink/infrastructure/diag_subsystem/diagserver/CMakeFiles/dtcproxy.dir/build
.PHONY : dtcproxy/fast

#=============================================================================
# Target rules for targets named diagserver

# Build rule for target.
diagserver: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 diagserver
.PHONY : diagserver

# fast build rule for target.
diagserver/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/diag_subsystem/diagserver/CMakeFiles/diagserver.dir/build.make autolink/infrastructure/diag_subsystem/diagserver/CMakeFiles/diagserver.dir/build
.PHONY : diagserver/fast

#=============================================================================
# Target rules for targets named diagserver_debugger

# Build rule for target.
diagserver_debugger: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 diagserver_debugger
.PHONY : diagserver_debugger

# fast build rule for target.
diagserver_debugger/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/diag_subsystem/diagserver/CMakeFiles/diagserver_debugger.dir/build.make autolink/infrastructure/diag_subsystem/diagserver/CMakeFiles/diagserver_debugger.dir/build
.PHONY : diagserver_debugger/fast

#=============================================================================
# Target rules for targets named ex_dcm

# Build rule for target.
ex_dcm: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ex_dcm
.PHONY : ex_dcm

# fast build rule for target.
ex_dcm/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/diag_subsystem/diagserver/CMakeFiles/ex_dcm.dir/build.make autolink/infrastructure/diag_subsystem/diagserver/CMakeFiles/ex_dcm.dir/build
.PHONY : ex_dcm/fast

#=============================================================================
# Target rules for targets named ex_diag

# Build rule for target.
ex_diag: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ex_diag
.PHONY : ex_diag

# fast build rule for target.
ex_diag/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/diag_subsystem/diagserver/CMakeFiles/ex_diag.dir/build.make autolink/infrastructure/diag_subsystem/diagserver/CMakeFiles/ex_diag.dir/build
.PHONY : ex_diag/fast

#=============================================================================
# Target rules for targets named ex_dtc

# Build rule for target.
ex_dtc: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ex_dtc
.PHONY : ex_dtc

# fast build rule for target.
ex_dtc/fast:
	$(MAKE) $(MAKESILENT) -f autolink/infrastructure/diag_subsystem/diagserver/CMakeFiles/ex_dtc.dir/build.make autolink/infrastructure/diag_subsystem/diagserver/CMakeFiles/ex_dtc.dir/build
.PHONY : ex_dtc/fast

#=============================================================================
# Target rules for targets named vehicleservice

# Build rule for target.
vehicleservice: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 vehicleservice
.PHONY : vehicleservice

# fast build rule for target.
vehicleservice/fast:
	$(MAKE) $(MAKESILENT) -f autolink/hal/CMakeFiles/vehicleservice.dir/build.make autolink/hal/CMakeFiles/vehicleservice.dir/build
.PHONY : vehicleservice/fast

#=============================================================================
# Target rules for targets named vehicledbg

# Build rule for target.
vehicledbg: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 vehicledbg
.PHONY : vehicledbg

# fast build rule for target.
vehicledbg/fast:
	$(MAKE) $(MAKESILENT) -f autolink/hal/CMakeFiles/vehicledbg.dir/build.make autolink/hal/CMakeFiles/vehicledbg.dir/build
.PHONY : vehicledbg/fast

#=============================================================================
# Target rules for targets named autolink_vehicleclient_qnx

# Build rule for target.
autolink_vehicleclient_qnx: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 autolink_vehicleclient_qnx
.PHONY : autolink_vehicleclient_qnx

# fast build rule for target.
autolink_vehicleclient_qnx/fast:
	$(MAKE) $(MAKESILENT) -f autolink/hal/CMakeFiles/autolink_vehicleclient_qnx.dir/build.make autolink/hal/CMakeFiles/autolink_vehicleclient_qnx.dir/build
.PHONY : autolink_vehicleclient_qnx/fast

#=============================================================================
# Target rules for targets named vehicle_test

# Build rule for target.
vehicle_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 vehicle_test
.PHONY : vehicle_test

# fast build rule for target.
vehicle_test/fast:
	$(MAKE) $(MAKESILENT) -f autolink/hal/CMakeFiles/vehicle_test.dir/build.make autolink/hal/CMakeFiles/vehicle_test.dir/build
.PHONY : vehicle_test/fast

#=============================================================================
# Target rules for targets named alpowerhal

# Build rule for target.
alpowerhal: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 alpowerhal
.PHONY : alpowerhal

# fast build rule for target.
alpowerhal/fast:
	$(MAKE) $(MAKESILENT) -f autolink/hal/CMakeFiles/alpowerhal.dir/build.make autolink/hal/CMakeFiles/alpowerhal.dir/build
.PHONY : alpowerhal/fast

#=============================================================================
# Target rules for targets named variant_server

# Build rule for target.
variant_server: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 variant_server
.PHONY : variant_server

# fast build rule for target.
variant_server/fast:
	$(MAKE) $(MAKESILENT) -f autolink/hal/CMakeFiles/variant_server.dir/build.make autolink/hal/CMakeFiles/variant_server.dir/build
.PHONY : variant_server/fast

#=============================================================================
# Target rules for targets named variant_tool

# Build rule for target.
variant_tool: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 variant_tool
.PHONY : variant_tool

# fast build rule for target.
variant_tool/fast:
	$(MAKE) $(MAKESILENT) -f autolink/hal/CMakeFiles/variant_tool.dir/build.make autolink/hal/CMakeFiles/variant_tool.dir/build
.PHONY : variant_tool/fast

#=============================================================================
# Target rules for targets named aldisplayhal

# Build rule for target.
aldisplayhal: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 aldisplayhal
.PHONY : aldisplayhal

# fast build rule for target.
aldisplayhal/fast:
	$(MAKE) $(MAKESILENT) -f autolink/hal/CMakeFiles/aldisplayhal.dir/build.make autolink/hal/CMakeFiles/aldisplayhal.dir/build
.PHONY : aldisplayhal/fast

#=============================================================================
# Target rules for targets named powerclient

# Build rule for target.
powerclient: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 powerclient
.PHONY : powerclient

# fast build rule for target.
powerclient/fast:
	$(MAKE) $(MAKESILENT) -f autolink/hal/CMakeFiles/powerclient.dir/build.make autolink/hal/CMakeFiles/powerclient.dir/build
.PHONY : powerclient/fast

#=============================================================================
# Target rules for targets named build_proto_source

# Build rule for target.
build_proto_source: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 build_proto_source
.PHONY : build_proto_source

# fast build rule for target.
build_proto_source/fast:
	$(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/build_proto_source.dir/build.make autolink/midware_swp/uac/CMakeFiles/build_proto_source.dir/build
.PHONY : build_proto_source/fast

#=============================================================================
# Target rules for targets named NativeIf

# Build rule for target.
NativeIf: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 NativeIf
.PHONY : NativeIf

# fast build rule for target.
NativeIf/fast:
	$(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/NativeIf.dir/build.make autolink/midware_swp/uac/CMakeFiles/NativeIf.dir/build
.PHONY : NativeIf/fast

#=============================================================================
# Target rules for targets named baic_installer

# Build rule for target.
baic_installer: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 baic_installer
.PHONY : baic_installer

# fast build rule for target.
baic_installer/fast:
	$(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_installer.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_installer.dir/build
.PHONY : baic_installer/fast

#=============================================================================
# Target rules for targets named baic_uac

# Build rule for target.
baic_uac: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 baic_uac
.PHONY : baic_uac

# fast build rule for target.
baic_uac/fast:
	$(MAKE) $(MAKESILENT) -f autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/build.make autolink/midware_swp/uac/CMakeFiles/baic_uac.dir/build
.PHONY : baic_uac/fast

#=============================================================================
# Target rules for targets named facreset_midware

# Build rule for target.
facreset_midware: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 facreset_midware
.PHONY : facreset_midware

# fast build rule for target.
facreset_midware/fast:
	$(MAKE) $(MAKESILENT) -f autolink/midware_swp/factoryreset/CMakeFiles/facreset_midware.dir/build.make autolink/midware_swp/factoryreset/CMakeFiles/facreset_midware.dir/build
.PHONY : facreset_midware/fast

#=============================================================================
# Target rules for targets named diag_midware

# Build rule for target.
diag_midware: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 diag_midware
.PHONY : diag_midware

# fast build rule for target.
diag_midware/fast:
	$(MAKE) $(MAKESILENT) -f autolink/midware_swp/diagnosis/CMakeFiles/diag_midware.dir/build.make autolink/midware_swp/diagnosis/CMakeFiles/diag_midware.dir/build
.PHONY : diag_midware/fast

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... NativeIf"
	@echo "... build_proto_source"
	@echo "... uninstall"
	@echo "... ClusterFunction"
	@echo "... EarlyTT"
	@echo "... adasSender"
	@echo "... al_cm"
	@echo "... al_core"
	@echo "... al_core_ut"
	@echo "... al_log"
	@echo "... al_pa"
	@echo "... al_psis"
	@echo "... alcdbgr"
	@echo "... alcom"
	@echo "... alcomeclient"
	@echo "... aldisplayhal"
	@echo "... alpowerhal"
	@echo "... anim_midware"
	@echo "... apiVerifier"
	@echo "... applauncher"
	@echo "... audiodbgr"
	@echo "... autolink_vehicleclient_qnx"
	@echo "... avas_midware"
	@echo "... baic_installer"
	@echo "... baic_uac"
	@echo "... carpropmgrdbgr"
	@echo "... carpropmgrtools"
	@echo "... cluster-hmi"
	@echo "... cluster_early_midware"
	@echo "... cluster_midware"
	@echo "... diag_midware"
	@echo "... diagproxy"
	@echo "... diagserver"
	@echo "... diagserver_debugger"
	@echo "... dtcproxy"
	@echo "... ex_dcm"
	@echo "... ex_diag"
	@echo "... ex_dtc"
	@echo "... facreset_midware"
	@echo "... fdbus"
	@echo "... fdbus-clib"
	@echo "... fdbxclient"
	@echo "... fdbxserver"
	@echo "... host_server"
	@echo "... iscreendbgr"
	@echo "... launcher"
	@echo "... logsvc"
	@echo "... logviewer"
	@echo "... lsclt"
	@echo "... lsdp"
	@echo "... lsevt"
	@echo "... lshost"
	@echo "... lssvc"
	@echo "... name_server"
	@echo "... ntfcenter"
	@echo "... perfProber"
	@echo "... powerclient"
	@echo "... psis_client"
	@echo "... psis_server"
	@echo "... sysmonitor"
	@echo "... tpmanager"
	@echo "... variant_server"
	@echo "... variant_tool"
	@echo "... vehicle_test"
	@echo "... vehicledbg"
	@echo "... vehicleservice"
	@echo "... videodbgr"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

