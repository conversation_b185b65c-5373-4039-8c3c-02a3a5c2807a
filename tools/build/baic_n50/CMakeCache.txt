# This is the CMakeCache file.
# For build in directory: /home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50
# It was generated by CMake: /usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/opt/qnx/qnx710_es11/host/linux/x86_64/usr/bin/ntoaarch64-addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/opt/qnx/qnx710_es11/host/linux/x86_64/usr/bin/ntoaarch64-ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/opt/qnx/qnx710_es11/host/linux/x86_64/usr/bin/ntoaarch64-g++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/opt/qnx/qnx710_es11/host/linux/x86_64/usr/bin/ntoaarch64-gcc-ar

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/opt/qnx/qnx710_es11/host/linux/x86_64/usr/bin/ntoaarch64-gcc-ranlib

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=-std=c++14 -D_QNX_SOURCE -D__QNXNTO__ -D_GLIBCXX_USE_C99

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/opt/qnx/qnx710_es11/host/linux/x86_64/usr/bin/ntoaarch64-gcc

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=/opt/qnx/qnx710_es11/host/linux/x86_64/usr/bin/ntoaarch64-gcc-ar

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=/opt/qnx/qnx710_es11/host/linux/x86_64/usr/bin/ntoaarch64-gcc-ranlib

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=-L /opt/qnx/qnx710_es11/target/qnx7/aarch64le/io-sock/lib

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Path to a program.
CMAKE_LINKER:FILEPATH=/opt/qnx/qnx710_es11/host/linux/x86_64/usr/bin/ntoaarch64-ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/gmake

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=-L /opt/qnx/qnx710_es11/target/qnx7/aarch64le/io-sock/lib

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/opt/qnx/qnx710_es11/host/linux/x86_64/usr/bin/ntoaarch64-nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/opt/qnx/qnx710_es11/host/linux/x86_64/usr/bin/ntoaarch64-objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/opt/qnx/qnx710_es11/host/linux/x86_64/usr/bin/ntoaarch64-objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=Project

//Path to a program.
CMAKE_RANLIB:FILEPATH=/opt/qnx/qnx710_es11/host/linux/x86_64/usr/bin/ntoaarch64-ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/opt/qnx/qnx710_es11/host/linux/x86_64/usr/bin/ntoaarch64-readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=-L /opt/qnx/qnx710_es11/target/qnx7/aarch64le/io-sock/lib

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/opt/qnx/qnx710_es11/host/linux/x86_64/usr/bin/ntoaarch64-strip

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Dependencies for the target
ClusterFunction_LIB_DEPENDS:STATIC=general;al_core;general;al_log;general;al_cm;

//relase env
DEPLOYMENT:BOOL=ON

//enable audio function
ENABLE_AUDIO:BOOL=ON

//enable camera function
ENABLE_CAMERA:BOOL=ON

//enable carproperty manager function
ENABLE_CARPROPERTY_MANAGER:BOOL=ON

//enable customproperty function
ENABLE_CUSTOM_PROPERTY:BOOL=ON

//enable ivi function
ENABLE_IVI:BOOL=ON

//enable pps function
ENABLE_PPS:BOOL=ON

//enable psis function
ENABLE_PSIS:BOOL=ON

//enable screen function
ENABLE_SCREEN:BOOL=ON

//enable someip function
ENABLE_SOMEIP:BOOL=OFF

//enable vehicle function
ENABLE_VEHICLE:BOOL=ON

//enable video function
ENABLE_VIDC:BOOL=ON

//compile proto files manually
MANUAL_COMPILE_PROTO:BOOL=ON

//Value Computed by CMake
Project_BINARY_DIR:STATIC=/home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50

//Value Computed by CMake
Project_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
Project_SOURCE_DIR:STATIC=/home/<USER>/DaYin2/qnx/vendor/autolink/tools/cmake

//cross compile for qnx platform
QNX:BOOL=ON

//build rb_installer to test communication
TEST_INSTALLER:BOOL=ON

//Dependencies for the target
al_cm_LIB_DEPENDS:STATIC=general;al_pa;general;al_core;general;al_log;general;json;

//Dependencies for the target
al_log_LIB_DEPENDS:STATIC=general;slog2;

//Dependencies for the target
al_pa_LIB_DEPENDS:STATIC=general;al_core;general;al_log;general;fdbus;general;libsocket.so.4;general;protobuf;general;autolink_vehicleclient_qnx;general;alcomeclient;general;screen;general;OSAbstraction;general;libstd;general;pmem_client;general;ioctlClient;general;AACParserLib;general;AC3ParserLib;general;AMRNBParserLib;general;AMRWBParserLib;general;ASFParserLib;general;AVIParserLib;general;FileDemux_Common;general;EVRCBParserLib;general;EVRCWBParserLib;general;FLACParserLib;general;ID3Lib;general;MP2ParserLib;general;MP3ParserLib;general;OGGParserLib;general;QCPParserLib;general;RawParserLib;general;SeekLib;general;SeekTableLib;general;VideoFMTReaderLib;general;WAVParserLib;general;ISOBaseFileLib;general;MKAVParserLib;general;AIFFParserLib;general;FileBaseLib;general;FileSource;general;pmem_client;general;qcxosal;general;OSAbstraction;general;qcxclient;general;asound;

//Dependencies for the target
al_psis_LIB_DEPENDS:STATIC=general;fdbus;general;libsocket.so.4;general;protobuf;general;al_log;

//Value Computed by CMake
alcom_BINARY_DIR:STATIC=/home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50/autolink/infrastructure/alcom

//Value Computed by CMake
alcom_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
alcom_SOURCE_DIR:STATIC=/home/<USER>/DaYin2/qnx/vendor/autolink/tools/cmake/autolink/infrastructure/alcom

//Dependencies for the target
autolink_vehicleclient_qnx_LIB_DEPENDS:STATIC=general;fdbus;general;libsocket.so.4;general;protobuf;general;al_log;

//Value Computed by CMake
cluster-hmi_BINARY_DIR:STATIC=/home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50/autolink

//Value Computed by CMake
cluster-hmi_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
cluster-hmi_SOURCE_DIR:STATIC=/home/<USER>/DaYin2/qnx/vendor/autolink/tools/cmake/autolink

//Dependencies for the target
diagproxy_LIB_DEPENDS:STATIC=general;protobuf;general;fdbus;

//Dependencies for the target
dtcproxy_LIB_DEPENDS:STATIC=general;protobuf;general;fdbus;

//Dependencies for the target
fdbus-clib_LIB_DEPENDS:STATIC=general;fdbus;general;slog2;

//Allocate port number by system rather than by name server
fdbus_ALLOC_PORT_BY_SYSTEM:BOOL=OFF

//build Android version
fdbus_ANDROID:BOOL=OFF

//Value Computed by CMake
fdbus_BINARY_DIR:STATIC=/home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50/autolink/infrastructure/fdbus

//build library for C
fdbus_BUILD_CLIB:BOOL=ON

//Build jni
fdbus_BUILD_JNI:BOOL=OFF

//Enable log
fdbus_ENABLE_LOG:BOOL=ON

//Force localhost for local communication
fdbus_FORCE_LOCALHOST:BOOL=OFF

//forced to build without rtti
fdbus_FORCE_NO_RTTI:BOOL=ON

//Value Computed by CMake
fdbus_IS_TOP_LEVEL:STATIC=OFF

//Dependencies for the target
fdbus_LIB_DEPENDS:STATIC=general;slog2;general;libsocket.so.4;

//specify -lpthread to link
fdbus_LINK_PTHREAD_LIB:BOOL=ON

//specify -lsocket to link
fdbus_LINK_SOCKET_LIB:BOOL=OFF

//Log to qnx slog2
fdbus_LOG_TO_QNX_SLOG2:BOOL=OFF

//Log to stdout
fdbus_LOG_TO_STDOUT:BOOL=OFF

//Do not use event FD
fdbus_PIPE_AS_EVENTFD:BOOL=OFF

//QNX style directory entry
fdbus_QNX_DIRENT:BOOL=OFF

//QNX link io socket lib 4
fdbus_QNX_IO_SOCKET_4:BOOL=OFF

//QNX style keepalive for TCP
fdbus_QNX_KEEPALIVE:BOOL=ON

//Enable security of FDBus
fdbus_SECURITY:BOOL=OFF

//Using SSL for security
fdbus_SEC_USING_SSL:BOOL=OFF

//Enable peercred of socket
fdbus_SOCKET_QNX_PEERCRED:BOOL=OFF

//Value Computed by CMake
fdbus_SOURCE_DIR:STATIC=/home/<USER>/DaYin2/qnx/vendor/autolink/tools/cmake/autolink/infrastructure/fdbus

//using abstract address for UDS
fdbus_UDS_ABSTRACT:BOOL=OFF

//using ZIP Lib to compress logs
fdbus_USING_ZIP:BOOL=OFF

//Dependencies for the target
tpmanager_LIB_DEPENDS:STATIC=general;doip;general;socket;


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/DaYin2/qnx/vendor/autolink/tools/build/baic_n50
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=22
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=1
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/DaYin2/qnx/vendor/autolink/tools/cmake
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=12
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/share/cmake-3.22
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1

