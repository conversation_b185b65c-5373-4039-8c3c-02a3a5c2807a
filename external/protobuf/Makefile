CURRENT_MK_DIR  := $(patsubst %/,%,$(dir $(abspath $(lastword $(MAKEFILE_LIST)))))
INSTALL_ROOT := $(CURRENT_MK_DIR)/
DEPLOY_ROOT_BIN  := $(VENDOR_INSTALL_ROOT_nto)/bin
DEPLOY_ROOT_LIB := $(VENDOR_INSTALL_ROOT_nto)/lib


MKDIR := mkdir -p
CP    := cp -arfp
RM    := rm -rf

.PHONY: all
all:install
.PHONY: install
install: clean
	$(MKDIR) $(DEPLOY_ROOT_BIN)
	$(MKDIR) $(DEPLOY_ROOT_LIB)
	$(CP) ${INSTALL_ROOT}/lib/libprotobuf-lite.so $(DEPLOY_ROOT_LIB)/
	$(CP) ${INSTALL_ROOT}/lib/libprotobuf-lite.so.3.19.5.0 $(DEPLOY_ROOT_LIB)/
	$(CP) ${INSTALL_ROOT}/lib/libprotobuf.so $(DEPLOY_ROOT_LIB)/
	$(CP) ${INSTALL_ROOT}/lib/libprotobuf.so.3.19.5.0 $(DEPLOY_ROOT_LIB)/

.PHONY: clean
clean:
	echo nop
