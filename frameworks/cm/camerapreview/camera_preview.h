#ifndef APPFW_CM_CAMERA_PREVIEW_H
#define APPFW_CM_CAMERA_PREVIEW_H
#include "autolink/frameworks/cm/camerapreview/impl/camera_window.h"
#include <chrono>
namespace AutoLink {
namespace Frameworks {
namespace CM {

class CameraPreview : public CameraWindow {
public:
    enum Status {
        STATUS_NORMAL,       // 或正在绘制图像
        STATUS_FRAME_TIMEOUT // 帧超时
    };
    enum TouchEvent {
        EVENT_TOUCH,
        EVENT_MOVE,
        EVENT_RELEASE
    };
    struct FrameData {
        void* data;
        uint64_t timestamp;
        uint32_t width;
        uint32_t height;
    };
    typedef std::function<void(Status)> StatusChangedHandler;
    typedef std::function<void(uint32_t touchId, int32_t posX, int32_t posY, TouchEvent type)> TouchEventHandler;
    typedef std::function<void(const FrameData&)> FrameDataHandler;
public:
    static std::shared_ptr<CameraPreview> Create(uint32_t cameraId, const CameraWindow::WindowParameters &windowParams);
    ~CameraPreview();
    bool Start();
    void Stop(bool destroy = false);
    bool SetVisible(bool visible);
    bool SetWindowGeometry(uint32_t posX, uint32_t posY, uint32_t width, uint32_t height);
    bool SetWindowZorder(uint32_t zorder);
    bool SetWindowDisplayId(uint32_t displayId);
    void RegisterStatusChangedHandler(StatusChangedHandler handler);
    void UnRegisterStatusChangedHandler();
    void RegistTouchEventHandler(TouchEventHandler handler);
    void UnRegistTouchEventHandler();
    void RegistFrameDataHandler(FrameDataHandler handler);
    void UnRegistFrameDataHandler();
private:
    CameraPreview(uint32_t cameraId, const CameraWindow::WindowParameters &windowParams);
    void SetStatus(Status status);
private:
    void OnCameraFrameReady();
    void OnFrameTimeout();
    void OnFrameRunningTimeout();
private:
    struct CameraPreviewPrivate *p_;
};
} // namespace CM
} // namespace Frameworks
} // namespace AutoLink
#endif