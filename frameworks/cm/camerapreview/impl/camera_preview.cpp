
#include "autolink/frameworks/cm/camerapreview/camera_preview.h"
#include "autolink/frameworks/log/log.h"
#include "autolink/frameworks/pa/ICamera/ICamera_context.h"
#include "autolink/frameworks/pa/IScreen/IScreenWindow.h"
#include "autolink/frameworks/core/timer.h"
#include "autolink/frameworks/core/async.h"
#include "pmem.h"

// #include <libyuv/convert_argb.h>

#define LOG_TAG "CameraPreview"

namespace AutoLink {
namespace Frameworks {
namespace CM {
static constexpr uint32_t CAMERA_BUFFERS_COUNT = 4;
static constexpr uint32_t FRAME_LOST_TIMER_INTERVAL = 1000;


class CameraPreview;
class CameraPreviewTouchEvent : public AutoLink::Frameworks::PA::TouchEventCallbacks{
    public:
        void SetIScreenWindow(AutoLink::Frameworks::CM::CameraPreview::TouchEventHandler p){
            if (p){
                p_ = p;
            }
        }

        void OnTouchPressed(uint32_t touchId, int32_t x, int32_t y){
            if (p_){
                LOG_TAG_INFO("OnTouchPressed: %d, %d", x, y);
                p_(touchId, x, y, CameraPreview::TouchEvent::EVENT_TOUCH);
            }
        }

        void OnTouchMoving(uint32_t touchId, int32_t x, int32_t y){
            if (p_){
                LOG_TAG_INFO("OnTouchMoving: %d, %d", x, y);
                p_(touchId, x, y, CameraPreview::TouchEvent::EVENT_MOVE);
            }
        }

        void OnTouchReleased(uint32_t touchId, int32_t x, int32_t y){
            if (p_){
                LOG_TAG_INFO("OnTouchReleased: %d, %d", x, y);
                p_(touchId, x, y, CameraPreview::TouchEvent::EVENT_RELEASE);
            }
        }
    private:
        AutoLink::Frameworks::CM::CameraPreview::TouchEventHandler p_{nullptr};
};

struct CameraPreviewPrivate {
    uint32_t cameraId;
    AutoLink::Frameworks::PA::ICamera* camera{nullptr};
    AutoLink::Frameworks::PA::IScreenWindow *window{nullptr};
    AutoLink::Frameworks::PA::RenderBuffer **windowBuffers{nullptr};

    std::shared_ptr<AutoLink::Frameworks::Core::Timer> frameTimer{nullptr};
    std::shared_ptr<AutoLink::Frameworks::Core::Timer> frameRunningTimer{nullptr};
    uint32_t frameCounter{0};
    uint32_t frameRunningCounter{0};
    AutoLink::Frameworks::CM::CameraPreview::StatusChangedHandler statusChangedHandler;
    AutoLink::Frameworks::CM::CameraPreview::TouchEventHandler touchEventHandler;
    AutoLink::Frameworks::CM::CameraPreview::FrameDataHandler frameDataHandler;

    CameraPreviewTouchEvent camPreviewTouchEvt;
    AutoLink::Frameworks::PA::IScreenWindow* TouchEventCallbacks{nullptr};
    int status{AutoLink::Frameworks::CM::CameraPreview::STATUS_NORMAL};
    bool started{false};
    bool inited{false};
};

std::shared_ptr<CameraPreview> CameraPreview::Create(uint32_t cameraId, const CameraWindow::WindowParameters &windowParams){
    return std::shared_ptr<CameraPreview>(new CameraPreview(cameraId, windowParams));
}

CameraPreview::CameraPreview(uint32_t cameraId, const WindowParameters &windowParams) : CameraWindow(windowParams), p_(new CameraPreviewPrivate){
    p_->cameraId = cameraId;
    p_->frameTimer = AutoLink::Frameworks::Core::Timer::Create("FrameStartTimer", 1 * 1000, [&](){OnFrameTimeout();});
    p_->frameRunningTimer = AutoLink::Frameworks::Core::Timer::Create("FrameRunningTimer", 1 * 2000, [&](){OnFrameRunningTimeout();});
    p_->window = (AutoLink::Frameworks::PA::IScreenWindow *)GetPlatformWindow();
}

CameraPreview::~CameraPreview(){
    Stop();
    delete p_;
    p_ = nullptr;
}

bool CameraPreview::Start(){
    if (p_->started) {
        return true;
    }

    if (!p_->inited) {

        p_->camera = AutoLink::Frameworks::PA::ICameraContext::Get().CreateCamera();
        if (!p_->camera) {
            return false;
        }

        if (!p_->camera->Open(p_->cameraId)) {
            AutoLink::Frameworks::PA::ICameraContext::Get().DestroyCamera(p_->camera);
            p_->camera = nullptr;
            return false;
        }

        auto cameraInfo = p_->camera->GetInfo();
        int32_t screenFmt;
        switch (cameraInfo.fmt) {
            case QCARCAM_FMT_MIPIRAW_8: {screenFmt = AutoLink::Frameworks::PA::ISCREEN_FORMAT_BYTE;}    break;
            case QCARCAM_FMT_RGB_888:   {screenFmt = AutoLink::Frameworks::PA::ISCREEN_FORMAT_RGB888;}  break;
            case QCARCAM_FMT_YUYV_8:    {screenFmt = AutoLink::Frameworks::PA::ISCREEN_FORMAT_YUY2;}    break;
            case QCARCAM_FMT_UYVY_8:    {screenFmt = AutoLink::Frameworks::PA::ISCREEN_FORMAT_UYVY;}    break;
            default:                    {screenFmt = AutoLink::Frameworks::PA::ISCREEN_FORMAT_RGB565;}  break;
        }
        p_->window->SetColorFormat(screenFmt);

        p_->windowBuffers = p_->window->CreateRenderBuffers(cameraInfo.width, cameraInfo.height, CAMERA_BUFFERS_COUNT);
        p_->window->PostRenderBuffer(p_->windowBuffers[0]);

        if (p_->windowBuffers) {
            AutoLink::Frameworks::PA::ICamera::Buffer cameraBuffers[CAMERA_BUFFERS_COUNT]{};
            for (int i = 0; i < CAMERA_BUFFERS_COUNT; ++i) {
                AutoLink::Frameworks::PA::ICamera::Buffer &cameraBuffer = cameraBuffers[i];
                cameraBuffer.planesNum = 1;
                cameraBuffer.planes[0].width = p_->windowBuffers[i]->width;
                cameraBuffer.planes[0].height = p_->windowBuffers[i]->height;
                cameraBuffer.planes[0].stride = p_->windowBuffers[i]->stride[0];
                cameraBuffer.planes[0].size = p_->windowBuffers[i]->stride[0] * p_->windowBuffers[0]->height;
                cameraBuffer.planes[0].memHndl = (uint64_t)p_->windowBuffers[i]->memHandle;
                cameraBuffer.planes[0].offset = (uint64_t)p_->windowBuffers[i]->offset[0];
            }
            p_->camera->SetBuffers(cameraBuffers);
        }

        AutoLink::Frameworks::PA::ICamera::EventCallbacks callbacks{
            .frameReadyCb = std::bind(&CameraPreview::OnCameraFrameReady, this),
            .signalValidChangedCb = nullptr,
            .errorCb = nullptr
        };
        p_->camera->SetEventCallback(callbacks);
        p_->inited = true;

    }

    if (p_->camera && p_->camera->Start()) {
        p_->status = -1;
        p_->started = true;
        p_->frameCounter = 0;
        if (!(p_->frameTimer->IsRunning())){
            p_->frameTimer->Start();
        }
        return true;
    }

    return false;
}

void CameraPreview::Stop(bool destroy){
    if (p_->camera && p_->started){
        p_->started = false;
        p_->camera->Stop();
    }

    if (destroy) {

        if (p_->camera) {
            p_->camera->Close();
            AutoLink::Frameworks::PA::ICameraContext::Get().DestroyCamera(p_->camera);
        }

        if (p_->windowBuffers && p_->window) {
            p_->window->DestroyRenderBuffers();
            p_->windowBuffers = nullptr;
        }

        p_->inited = false;

    }
}

bool CameraPreview::SetVisible(bool visible){
    if (p_->window){
        return p_->window->SetVisible(visible);
    }
    return false;
}

void CameraPreview::RegisterStatusChangedHandler(StatusChangedHandler handler){
    p_->statusChangedHandler = handler;
}

void CameraPreview::UnRegisterStatusChangedHandler(){
    p_->statusChangedHandler = nullptr;
}

void CameraPreview::SetStatus(Status status){
    if (status != p_->status){
        p_->status = status;
        AutoLink::Frameworks::Core::Async::Submit("frameReady", [&, status](){
            if (p_->statusChangedHandler){
                p_->statusChangedHandler(status);
            }
        });
    }
}

bool CameraPreview::SetWindowGeometry(uint32_t posX, uint32_t posY, uint32_t width, uint32_t height){
    if (p_->window)
    {
        if ((0 == p_->window->SetRectangle(posX, posY, width, height)) &&
            (0 == p_->window->SetSourceRectangle(0, 0, width, height)))
        {
            return true;
        }
    }

    return false;
}

bool CameraPreview::SetWindowZorder(uint32_t zorder){
    if (p_->window && (0 == p_->window->SetZorder(zorder))){
        return true;
    }

    return false;
}

bool CameraPreview::SetWindowDisplayId(uint32_t displayId){
    if (p_->window && (0 == p_->window->SetDisplayId(displayId))){
        return true;
    }

    return false;
}

void CameraPreview::RegistTouchEventHandler(TouchEventHandler handler){
    p_->touchEventHandler = handler;
    if (!p_->window)
        return;

    p_->camPreviewTouchEvt.SetIScreenWindow(p_->touchEventHandler);
    p_->window->RegisterTouchEventCallbacks(&p_->camPreviewTouchEvt);
}

void CameraPreview::UnRegistTouchEventHandler(){
    p_->touchEventHandler = nullptr;
    LOG_TAG_INFO("UnRegistTouchEventHandler done");
}

void CameraPreview::RegistFrameDataHandler(FrameDataHandler handler){
    if(p_->frameDataHandler)
        return;

    p_->frameDataHandler = handler;
    LOG_TAG_INFO("RegistFrameDataHandler done, handler: %p", handler);
}

void CameraPreview::UnRegistFrameDataHandler(){
    p_->frameDataHandler = nullptr;
    LOG_TAG_INFO("UnRegistFrameDataHandler done");
}

void CameraPreview::OnCameraFrameReady(){
    AutoLink::Frameworks::PA::ICamera::Frame* frame = p_->camera->GetFrame();
    if (frame) {
        p_->window->PostRenderBuffer(p_->windowBuffers[frame->bufferIdx]);
        if (p_->frameDataHandler){
            FrameData data{};
            data.data = p_->windowBuffers[frame->bufferIdx]->ptr;
            data.timestamp = frame->timestamp;
            data.width = p_->camera->GetInfo().width;
            data.height = p_->camera->GetInfo().height;
            p_->frameDataHandler(data);
        }
        p_->camera->ReleaseFrame(frame);
        SetStatus(STATUS_NORMAL);
        if (!(p_->frameRunningTimer->IsRunning())){
            p_->frameRunningTimer->Start();
        }
        p_->frameCounter++;
        p_->frameRunningCounter++;
    }
}

void CameraPreview::OnFrameTimeout(){
    if (p_->frameCounter > 0)
        p_->frameCounter == 0;
    else {
        SetStatus(STATUS_FRAME_TIMEOUT);
    }
}
void CameraPreview::OnFrameRunningTimeout(){
    if (p_->frameRunningCounter > 0){
        p_->frameRunningCounter = 0;
    }
    else {
        LOG_TAG_WARNING("camera: %d frame interrupt!!!", p_->cameraId);
    }
}

} // namespace CM
} // namespace Frameworks
} // namespace AutoLink