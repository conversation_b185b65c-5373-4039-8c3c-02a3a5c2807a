#include "autolink/frameworks/cm/camerapreview/impl/camera_window.h"
#include "autolink/frameworks/pa/IScreen/IScreen.h"
#include "autolink/frameworks/pa/IScreen/IScreenWindow.h"
#include "autolink/frameworks/log/log.h"
#include <vector>

#define LOG_TAG "CameraWindow"

#define SCREEN_WINDOW_BUFFERS_COUNT 2

namespace AutoLink {
namespace Frameworks {
namespace CM {

struct CameraWindowPrivate {
    CameraWindow::WindowParameters windowParams;
    AutoLink::Frameworks::PA::IScreenWindow *window{nullptr};
    AutoLink::Frameworks::PA::RenderBuffer *renderBuffer{nullptr};
    uint32_t renderBufferIndex;
    uint32_t drawWidth;
    uint32_t drawHeight;
};

CameraWindow::CameraWindow(const WindowParameters &windowParams) : p_(new CameraWindowPrivate)
{
    p_->windowParams = windowParams;
    AutoLink::Frameworks::PA::IScreen::Get().SetScreenEventDemmand(!!p_->windowParams.demmand);
    p_->window = AutoLink::Frameworks::PA::IScreen::Get().CreateWindow();
    if (p_->window) {
        p_->window->SetDisplayId(p_->windowParams.displayId);
        p_->window->SetZorder(p_->windowParams.zorder);
        p_->window->SetRectangle(p_->windowParams.x, p_->windowParams.y, p_->windowParams.width, p_->windowParams.height);
        p_->window->SetVisible(false);
    }
}

CameraWindow::~CameraWindow()
{
    AutoLink::Frameworks::PA::IScreen::Get().DestroyWindow(p_->window);
    delete p_;
}

void CameraWindow::SetVisible(bool visible)
{
    if (p_->window)
        p_->window->SetVisible(visible);
}

void CameraWindow::SetBounds(int32_t x, int32_t y, uint32_t width, uint32_t height)
{
    if (p_->window)
        p_->window->SetRectangle(x, y, width, height);
}

void *CameraWindow::GetPlatformWindow()
{
    return p_->window;
}

} // namespace CM
} // namespace Frameworks
} // namespace AutoLink