#ifndef APPFW_CM_CAMERA_WINDOW_H
#define APPFW_CM_CAMERA_WINDOW_H

#include <string>
#include <functional>
#include <memory>

namespace AutoLink {
namespace Frameworks {
namespace CM {
class CameraWindow {
public:
    struct WindowParameters {
        uint32_t displayId{0};
        uint32_t zorder{0};
        int32_t x{0};
        int32_t y{0};
        uint32_t width{0};
        uint32_t height{0};
        uint32_t demmand{0};
    };

    CameraWindow(const WindowParameters &windowParams);

    virtual ~CameraWindow();

    void SetVisible(bool visible);

    void SetBounds(int32_t x, int32_t y, uint32_t width, uint32_t height);

protected:
    void *GetPlatformWindow();

private:
    struct CameraWindowPrivate *p_;
};
} // namespace CM
} // namespace Frameworks
} // namespace AutoLink
#endif