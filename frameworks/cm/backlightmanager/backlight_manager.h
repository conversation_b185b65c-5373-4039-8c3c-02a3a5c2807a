#ifndef APPFW_CM_BACKLIGHT_MANAGER_MANAGER_H
#define APPFW_CM_BACKLIGHT_MANAGER_MANAGER_H

#include <functional>
#include <memory>

namespace AutoLink {
namespace Frameworks {
namespace CM {

class BacklightManager {
public:
    enum BacklightMode {
        BACKLIGHT_MODE_DAY_MODE,  // 白天模式
        BACKLIGHT_MODE_NIGHT_MODE // 黑夜模式
    };

    struct BacklightStatus {
        uint32_t brightness;
        bool onOff;
    };
    
    typedef std::function<void(BacklightMode)> BacklightModeNotifyHandler;
    typedef std::function<void(BacklightStatus)> BacklightStatusNotifyHandler;

public:
    /**
     * 单例接口
     */
    static BacklightManager &Get();

    BacklightManager(const BacklightManager &copy) = delete;

    BacklightManager &operator=(const BacklightManager &other) = delete;

    ~BacklightManager();

    /// @brief 设置仪表屏背光亮度
    /// @param dispId 显示设备 id
    /// @param brightness 背光亮度值 有效范围: [1, 10]
    void SetBacklightBrightness(uint32_t dispId, uint32_t brightness);

    /// @brief 设置仪表背光开关
    /// @param onoff 背光开关,true为开，false为关
    void SetBacklightOnOff(uint32_t dispId, bool onoff);

    /// @brief 注册监听仪表白天黑夜模式切换通知
    /// @param handler 通知回调
    /// @param handlerOwner 通知回调拥有者，同一个拥有者只能注册一个handler
    void RegisterBacklightModeNotifyHandler(BacklightModeNotifyHandler handler, void *handlerOwner);

    /// @brief 注册监听仪表背光状态变化通知，包括亮度，开关
    /// @param handler 通知回调
    /// @param handlerOwner 通知回调拥有者，同一个拥有者只能注册一个handler
    void RegisterClusterBacklightStatusNotifyHandler(BacklightStatusNotifyHandler handler, void *handlerOwner);

private:
    BacklightManager();

private:
    void OnBacklightModeNotify(BacklightMode mode);

    void OnBacklightStatusNotify(const BacklightStatus &status);

private:
    struct BacklightManagerPrivate *p_;
};

} // namespace CM
} // namespace Frameworks
} // namespace AutoLink
#endif