#include <map>
#include "autolink/frameworks/cm/backlightmanager/backlight_manager.h"
#include "autolink/frameworks/pa/IDispCtrl/IDispCtrl.h"
#include "autolink/frameworks/pa/ILCM/ILCM.h"
#include "autolink/frameworks/core/async.h"

namespace AutoLink {
namespace Frameworks {
namespace CM {

struct BacklightManagerPrivate {
    using IDispCtrl = AutoLink::Frameworks::PA::IDispCtrl;
    IDispCtrl dispCtrl;
    using ILCM = AutoLink::Frameworks::PA::ILCM;
    ILCM lcm;
    std::map<void *, BacklightManager::BacklightModeNotifyHandler> backlightModeNotifyHandlers;
    std::map<void *, BacklightManager::BacklightStatusNotifyHandler> backlightStatusNotifyHandlers;
};

BacklightManager &BacklightManager::Get()
{
    static BacklightManager mgr;
    return mgr;
}

BacklightManager::BacklightManager() : p_(new BacklightManagerPrivate)
{
    
    p_->dispCtrl.RegisterDisplayDayNightModeChangedHandler(
        [&](AutoLink::Frameworks::PA::IDispCtrlBacklightMode mode) {
            if (mode == AutoLink::Frameworks::PA::IDispCtrlBacklightMode::BACKLIGHT_MODE_DAY_MODE)
                OnBacklightModeNotify(BACKLIGHT_MODE_DAY_MODE);
            else if (mode == AutoLink::Frameworks::PA::IDispCtrlBacklightMode::BACKLIGHT_MODE_NIGHT_MODE)
                OnBacklightModeNotify(BACKLIGHT_MODE_NIGHT_MODE);
        });

    p_->dispCtrl.RegisterDisplayStatusChangedHandler(
        [&](const AutoLink::Frameworks::PA::IDispCtrlBacklightStatus &status) {
            if (status.dispId == AutoLink::Frameworks::PA::IDispCtrlDispType::DISP_CLUSTER)
                OnBacklightStatusNotify(BacklightStatus{.brightness = status.brightness, .onOff = status.onOff});
        });
}

BacklightManager::~BacklightManager()
{
    delete p_;
}

void BacklightManager::SetBacklightBrightness(uint32_t dispId, uint32_t brightness)
{
    p_->dispCtrl.SetBrightness(dispId, brightness);
}

void BacklightManager::SetBacklightOnOff(uint32_t dispId, bool onoff)
{
    p_->lcm.SetDispState(dispId, onoff);
}

void BacklightManager::RegisterBacklightModeNotifyHandler(BacklightModeNotifyHandler handler, void *handlerOwner)
{
    p_->backlightModeNotifyHandlers[handlerOwner] = handler;
}

void BacklightManager::RegisterClusterBacklightStatusNotifyHandler(BacklightStatusNotifyHandler handler, void *handlerOwner)
{
    p_->backlightStatusNotifyHandlers[handlerOwner] = handler;
}

void BacklightManager::OnBacklightModeNotify(BacklightMode mode)
{
    using namespace AutoLink::Frameworks;
     Core::Async::Submit("OnBacklightModeNotify", [this, mode]() {
        for (auto p : p_->backlightModeNotifyHandlers) {
            if (p.second)
                p.second(mode);
        }
    });

}

void BacklightManager::OnBacklightStatusNotify(const BacklightStatus &status)
{
    using namespace AutoLink::Frameworks;
    Core::Async::Submit("OnBacklightStatusNotify", [this, status]() {
        for (auto p : p_->backlightStatusNotifyHandlers) {
            if (p.second)
                p.second(status);
        }
    });
}

} // namespace CM
} // namespace Frameworks
} // namespace AutoLink