#ifndef APPFW_CM_AUDIO_PLAYBACK_H
#define APPFW_CM_AUDIO_PLAYBACK_H

#include "autolink/frameworks/cm/audioplayer/audio_player_types.h"

namespace AutoLink {
namespace Frameworks {
namespace CM {

class AudioPlayback final {
public:
    explicit AudioPlayback(StreamType type);
    ~AudioPlayback();

    void Play(const PlayParams& playParam,
              const SourceAudioParams& audioParams);
    void Stop();

    void Suspend();
    void Resume();

    int32_t SetVolumeLevel(const uint8_t& volumeLevel);

private:
    struct AudioPlaybackPrivate* p_;
};

}  // namespace CM
}  // namespace Frameworks
}  // namespace AutoLink

#endif