#include "audio_playback.h"

#include "autolink/frameworks/cm/audioplayer/audio_player.h"
#include "autolink/frameworks/core/impl/taskmanager.h"

#include <unistd.h>

using namespace AutoLink::Frameworks::Core;
using namespace AutoLink::Frameworks::CM;

namespace AutoLink {
namespace Frameworks {
namespace CM {

struct AudioPlaybackPrivate {
    std::shared_ptr<AudioPlayer> player;

    void OnPlayerStart(const uint32_t& objID) {
        printf("PlayerStart %u\n", objID);
    }
    void OnPlayerStop(uint32_t objID, uint32_t playedTimes, uint32_t stopReason) {
        printf("PlayerStop %u %u %u\n", objID, playedTimes, stopReason);

        usleep(1000000);

        TaskManager::Get().Cancel();
    }
};

AudioPlayback::AudioPlayback(StreamType type)
    : p_(new AudioPlaybackPrivate) {
    p_->player = AudioPlayer::Create(type);

    (p_->player)->RegisterStartHandler(
                std::bind(
                    &AudioPlaybackPrivate::OnPlayerStart,
                    p_,
                    std::placeholders::_1));

    (p_->player)->RegisterStopHandler(
                std::bind(
                    &AudioPlaybackPrivate::OnPlayerStop,
                    p_,
                    std::placeholders::_1,
                    std::placeholders::_2,
                    std::placeholders::_3));
}
AudioPlayback::~AudioPlayback() {
    if (nullptr != p_) {
        delete p_;
        p_ = nullptr;
    }
}

void AudioPlayback::Play(const PlayParams& playParam,
                         const SourceAudioParams& audioParams) {
    if (nullptr != p_->player) {
        p_->player->Play(1, audioParams, playParam);

        TaskManager::Get().Run();
    }
}

void AudioPlayback::Stop() {
    (p_->player)->Stop(0);
}

void AudioPlayback::Suspend() {
    (p_->player)->Suspend();
}

void AudioPlayback::Resume() {
    (p_->player)->Resume();
}

int32_t AudioPlayback::SetVolumeLevel(const uint8_t& volumeLevel) {
    return (p_->player)->SetVolumeLevel(volumeLevel);
}

}  // namespace CM
}  // namespace Frameworks
}  // namespace AutoLink
