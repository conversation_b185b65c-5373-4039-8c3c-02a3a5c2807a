#ifndef APPFW_CM_AUDIO_DEBUGER_COMMANDER_H
#define APPFW_CM_AUDIO_DEBUGER_COMMANDER_H

namespace AutoLink {
namespace Frameworks {
namespace CM {

class AudioDebugCommander final {
public:
    AudioDebugCommander(int argc, char **argv);
    ~AudioDebugCommander();

    void Run();
    void Exit();

private:
    struct AudioDebugCommanderPrivate *p_;
};

}  // namespace CM
}  // namespace Frameworks
}  // namespace AutoLink

#endif