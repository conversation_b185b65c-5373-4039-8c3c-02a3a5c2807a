#include "commander.h"

#include <dirent.h>
#include <getopt.h>
#include <stdint.h>

#include <iostream>
#include <vector>

#include "autolink/frameworks/cm/audioplayer/audio_wav_file.h"
#include "autolink/frameworks/cm/audioplayer/debug/playback/audio_playback.h"

namespace AutoLink {
namespace Frameworks {
namespace CM {

typedef enum AudioDebugOperation : uint8_t {
    OP_PARSE_WAV = 0x1,
    OP_WAV_D = 0x2,
    OP_WAV_F = 0x4,
    OP_PB = 0x8,
} AudioDebugOperation_;

struct AudioDebugCommanderPrivate {
    int argc;
    char **argv;

    std::string wavDir;
    std::string wavFile;

    PlayParams playParam{1,0,0,0x3F};

    uint32_t playTimes{1};
    uint16_t playInterval{0};
    uint16_t playMinimum{0};
    uint8_t playVolume{0};
    uint8_t testCase{0};
    StreamType type{STREAM_TYPE_CHIME};

    uint32_t op{0};

    std::vector<std::shared_ptr<AudioWavFile>> wavFiles;
    AudioPlayback *pb;

    void ParseArgs() {
        const char *optstr = "wd:f:pt:i:m:v:s:c:";
        struct option lopts[] = {
            {"ParseWav", no_argument, NULL, 'w'},
            {"WavDirectory", required_argument, NULL, 'd'},
            {"WavFile", required_argument, NULL, 'f'},
            {"Playback", required_argument, NULL, 'p'},
            {"PlayYimes", required_argument, NULL, 't'},
            {"PlayInterval", required_argument, NULL, 'i'},
            {"PlayMinimum", required_argument, NULL, 'm'},
            {"PlayVolume", required_argument, NULL, 'v'},
            {"StreamType", required_argument, NULL, 's'},
            {"TestCase", required_argument, NULL, 'c'},
            {NULL, 0, NULL, 0},
        };

        uint32_t command;
        while ((command = getopt_long(argc, argv, optstr, lopts, NULL)) != -1) {
            switch (command) {
            case 'w':
                op |= OP_PARSE_WAV;
                break;
            case 'd':
                wavDir = optarg;
                op |= OP_WAV_D;
                break;
            case 'f':
                wavFile = optarg;
                op |= OP_WAV_F;
                break;
            case 't':
                playParam.Times = atoi(optarg);
                break;
            case 'i':
                playParam.Interval = atoi(optarg);
                break;
            case 'm':
                playParam.Minimum = atoi(optarg);
                break;
            case 'v':
                playVolume = atoi(optarg);
                break;
            case 'p':
                op |= OP_PB;
                break;
            case 's':
                type = (StreamType)atoi(optarg);
                break;
            case 'c':
                testCase = atoi(optarg);
                break;
            default:
                break;
            }
        }
    }

    void CommandProcess() {
        if (OP_PARSE_WAV == (op & OP_PARSE_WAV)) {
            ParseWav();
        }

        if (OP_PB == (op & OP_PB)) {
            PlaybackWav();
        }

        printf("process end\n");
    }

    void ParseWav() {
        if (OP_WAV_D == (op & OP_WAV_D)) {
            DIR *dir;
            struct dirent *ptr;
            char filepath[100];

            dir = opendir(wavDir.c_str());
            if (NULL != dir) {
                while ((ptr = readdir(dir)) != NULL) {
                    if ((strcmp(ptr->d_name, ".") == 0) ||
                        (strcmp(ptr->d_name, "..") == 0)) {
                        continue;
                    }

                    snprintf(filepath, sizeof(filepath), "%s/%s", wavDir.c_str(), ptr->d_name);

                    wavFiles.push_back(AudioWavFile::Create(filepath));
                }
                closedir(dir);
            }

            DumpWavInfo();
        } else if (OP_WAV_F == (op & OP_WAV_F)) {
            wavFiles = std::vector<std::shared_ptr<AudioWavFile>>();
            wavFiles.push_back(AudioWavFile::Create(wavFile.c_str()));
            DumpWavInfo();
        } else {
        }
    }

    void PlaybackWav() {
        if (nullptr != pb) {
            pb->SetVolumeLevel(playVolume);

            SourceAudioParams audioParams;
            audioParams.WavParams = wavFiles[0]->GetAudioParams();

            if (1 == audioParams.WavParams.Channels) {
                audioParams.ChannelStrtg = STRATEGY_MONO_TO_STEREO_ALL;
            } else {
                audioParams.ChannelStrtg = STRATEGY_INVALID;
            }

            pb->Play(playParam, audioParams);
        }
    }

    void DumpWavInfo() {
        for (auto wavFile : wavFiles) {
            printf("------ wav file info ------\n");

            printf("file          : %s\n", wavFile->FileDescri().c_str());

            AudioWavParams params = wavFile->GetAudioParams();

            printf("SampleRate    : %u\n", params.SampleRate);     // 采样频率
            printf("BitsPerSample : %u\n", params.BitsPerSample);  // 每个采样点所需的位数
            printf("Channels      : %u\n", params.Channels);       // 数据通道数
            printf("DataBuf       : %p\n", params.DataBuffer);     // 数据偏移
            printf("DataSize      : %u\n", params.DataSize);       // 可用数据大小
        }
    }
};

AudioDebugCommander::AudioDebugCommander(int argc, char **argv)
    : p_(new AudioDebugCommanderPrivate()) {
    p_->argc = argc;
    p_->argv = argv;

}
AudioDebugCommander::~AudioDebugCommander() {
    if (nullptr != p_->pb) {
        delete p_->pb;
        p_->pb = nullptr;
    }

    if (nullptr != p_) {
        delete p_;
        p_ = nullptr;
    }
}

void AudioDebugCommander::Run() {
    p_->ParseArgs();
    printf("start run, type is %d\n", p_->type);

    p_->pb = new AudioPlayback(p_->type);

    if (p_->testCase == 0) {
        p_->CommandProcess();
    }
    else if (p_->testCase == 1) {
        p_->CommandProcess();
        (p_->pb)->Stop();
        p_->CommandProcess();
    }
    else if (p_->testCase == 2) {
        p_->CommandProcess();
        (p_->pb)->Stop();
        (p_->pb)->Suspend();
        (p_->pb)->Resume();
        p_->CommandProcess();
    }
    else if (p_->testCase == 3) {
        (p_->pb)->Suspend();
        (p_->pb)->Resume();
        p_->CommandProcess();
    }
    else if (p_->testCase == 4) {
        p_->CommandProcess();
        (p_->pb)->Stop();
        (p_->pb)->Suspend();
        (p_->pb)->Resume();
        (p_->pb)->Suspend();
        (p_->pb)->Resume();
        (p_->pb)->Suspend();
        (p_->pb)->Resume();
        (p_->pb)->Stop();
        p_->CommandProcess();
        (p_->pb)->Stop();
        p_->CommandProcess();
    }
    else if (p_->testCase == 5) {
        while(true) {
            p_->CommandProcess();
            (p_->pb)->Stop();
            (p_->pb)->Suspend();
            (p_->pb)->Resume();
        }
    }
    else if (p_->testCase == 6) {
        while(true) {
            p_->CommandProcess();
            (p_->pb)->Stop();
        }
    }

}

void AudioDebugCommander::Exit() {
    if (nullptr != p_->pb) {
        (p_->pb)->Stop();
    }
}

}  // namespace CM
}  // namespace Frameworks
}  // namespace AutoLink
