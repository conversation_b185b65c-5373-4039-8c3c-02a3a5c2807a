#include "wav_file.h"

#include "fstream"

namespace AutoLink {
namespace Frameworks {
namespace CM {

#define WaveFileRIFFLenght (4u)
#define WaveFileSIZELenght (4u)
#define WaveFileWAVELenght (4u)

#define WaveFileChunkNameLenght (4u)
#define WaveFileChunkSizeLenght (4u)

#define WaveFileFmtAudioFormatLenght (2u)
#define WaveFileFmtChannelsLenght (2u)
#define WaveFileFmtSampleRateLenght (4u)
#define WaveFileFmtByteRateLenght (4u)
#define WaveFileFmtBlockAlignLenght (2u)
#define WaveFileFmtBitsPerSampleLenght (2u)

#define WaveFileFmtLenght (WaveFileFmtAudioFormatLenght + \
                           WaveFileFmtChannelsLenght +    \
                           WaveFileFmtSampleRateLenght +  \
                           WaveFileFmtByteRateLenght +    \
                           WaveFileFmtBlockAlignLenght +  \
                           WaveFileFmtBitsPerSampleLenght)

struct AudioWavFilePrivate {
    std::string path;

    SourceAudioParams audioParams{{0, 0, 0, nullptr, 0}, STRATEGY_INVALID};

    uint32_t fileSize{0};

    uint32_t wavDataOffset{0};
    uint32_t wavDuration{0};

    void ParseWav() {
        std::fstream fsFile;

        fsFile.open(path.c_str(), std::ios::in | std::ios::binary);

        // bool is_open(); C++98
        // bool is_open() const; C++11
        if (!fsFile.is_open()) {
            printf("file %s missing\n", path.c_str());

            fsFile.close();
            return;
        }

        uint32_t pos = 0;

        fsFile.seekg(pos, fsFile.end);
        fileSize = fsFile.tellg();

        fsFile.seekg(pos, fsFile.beg);

        char sRIFF[WaveFileRIFFLenght] = {0};
        fsFile.read(sRIFF, WaveFileRIFFLenght);

        if (sRIFF[0] != 'R' ||
            sRIFF[1] != 'I' ||
            sRIFF[2] != 'F' ||
            sRIFF[3] != 'F') {
            printf("%s no RIFF chunk\n", path.c_str());

            fsFile.close();
            return;
        }

        pos += WaveFileRIFFLenght;
        fsFile.seekg(pos, fsFile.beg);

        char sSize[WaveFileSIZELenght] = {0};
        fsFile.read(sSize, WaveFileSIZELenght);

        pos += WaveFileSIZELenght;
        fsFile.seekg(pos, fsFile.beg);

        char sWAVE[WaveFileWAVELenght] = {0};
        fsFile.read(sWAVE, WaveFileWAVELenght);

        if (sWAVE[0] != 'W' ||
            sWAVE[1] != 'A' ||
            sWAVE[2] != 'V' ||
            sWAVE[3] != 'E') {
            printf("%s not WAVE\n", path.c_str());

            fsFile.close();
            return;
        }

        pos += WaveFileWAVELenght;

        uint32_t WaveDataLen = sSize[3] * 0x1000000 + sSize[2] * 0x10000 + sSize[1] * 0x100 + sSize[0] + WaveFileRIFFLenght + WaveFileSIZELenght;
        if (WaveDataLen != fileSize) {
            printf("file %s size error, file Read: %u, wav info: %u\n", path.c_str(), fileSize, WaveDataLen);

            fsFile.close();
            return;
        }

        char sChunkName[WaveFileChunkNameLenght] = {0};
        char sChunkSize[WaveFileChunkSizeLenght] = {0};
        uint32_t ChunkSize = 0;

        while (pos < fileSize) {
            fsFile.seekg(pos, fsFile.beg);
            fsFile.read(sChunkName, WaveFileChunkNameLenght);

            pos += WaveFileChunkNameLenght;

            if (pos >= fileSize) {
                printf("file %s chunk overflow: %u >= %u\n", path.c_str(), pos, fileSize);

                fsFile.close();
                return;
            }

            fsFile.seekg(pos, fsFile.beg);
            fsFile.read(sChunkSize, WaveFileChunkSizeLenght);

            ChunkSize = sChunkSize[3] * 0x1000000 + sChunkSize[2] * 0x10000 + sChunkSize[1] * 0x100 + sChunkSize[0];

            pos += WaveFileChunkSizeLenght;

            if (pos >= fileSize) {
                printf("file %s chunk overflow: %u >= %u\n", path.c_str(), pos, fileSize);

                fsFile.close();
                return;
            }

            if (sChunkName[0] == 'f' &&
                sChunkName[1] == 'm' &&
                sChunkName[2] == 't' &&
                sChunkName[3] == ' ') {
                pos += WaveFileFmtAudioFormatLenght;

                char sChannels[WaveFileFmtChannelsLenght] = {0};
                fsFile.seekg(pos, fsFile.beg);
                fsFile.read(sChannels, WaveFileFmtChannelsLenght);
                audioParams.WavParams.Channels = sChannels[0] + sChannels[1] * 0x100;

                pos += WaveFileFmtChannelsLenght;

                char sSampleRate[WaveFileFmtSampleRateLenght] = {0};
                fsFile.seekg(pos, fsFile.beg);
                fsFile.read(sSampleRate, WaveFileFmtSampleRateLenght);
                audioParams.WavParams.SampleRate = sSampleRate[3] * 0x1000000 + sSampleRate[2] * 0x10000 + sSampleRate[1] * 0x100 + sSampleRate[0];

                pos += WaveFileFmtSampleRateLenght;
                pos += WaveFileFmtByteRateLenght;
                pos += WaveFileFmtBlockAlignLenght;

                char sBitsPerSample[WaveFileFmtBitsPerSampleLenght] = {0};
                fsFile.seekg(pos, fsFile.beg);
                fsFile.read(sBitsPerSample, WaveFileFmtBitsPerSampleLenght);
                audioParams.WavParams.BitsPerSample = sBitsPerSample[0] + sBitsPerSample[1] * 0x100;

                pos += WaveFileFmtBitsPerSampleLenght;
                pos += (ChunkSize - WaveFileFmtLenght);
            } else if (sChunkName[0] == 'd' &&
                       sChunkName[1] == 'a' &&
                       sChunkName[2] == 't' &&
                       sChunkName[3] == 'a') {
                wavDataOffset = pos;

                audioParams.WavParams.DataBuffer = new int8_t[ChunkSize];
                fsFile.seekg(pos, fsFile.beg);
                fsFile.read((char*)audioParams.WavParams.DataBuffer, ChunkSize);

                audioParams.WavParams.DataSize = ChunkSize;
                printf("data chunk size %u \n", ChunkSize);

                break;
            } else {
                pos += ChunkSize;
            }
        }

        fsFile.close();

        wavDuration = audioParams.WavParams.DataSize * 1000 / (audioParams.WavParams.SampleRate * audioParams.WavParams.BitsPerSample * audioParams.WavParams.Channels / 8);

        if (1 == audioParams.WavParams.Channels) {
            audioParams.ChannelStrtg = STRATEGY_MONO_TO_STEREO_ALL;
        }
    }
};

AudioWavFile::AudioWavFile(std::string filePath) : p_(new AudioWavFilePrivate) {
    p_->path = filePath;

    p_->ParseWav();
}
AudioWavFile::~AudioWavFile() {
    if (nullptr != p_->audioParams.WavParams.DataBuffer) {
        delete[] p_->audioParams.WavParams.DataBuffer;
        p_->audioParams.WavParams.DataBuffer = nullptr;
    }

    if (nullptr != p_) {
        delete p_;
        p_ = nullptr;
    }
}

SourceAudioParams AudioWavFile::GetAudioParams() {
    return p_->audioParams;
}

void AudioWavFile::DumpInfo() {
    printf("------ wav file info ------\n");

    printf("file          : %s\n", p_->path.c_str());
    printf("size          : %u bytes\n", p_->fileSize);                       // 文件大小
    printf("duration      : %u ms\n", p_->wavDuration);                       // 时长
    printf("SampleRate    : %u\n", p_->audioParams.WavParams.SampleRate);     // 采样频率
    printf("BitsPerSample : %u\n", p_->audioParams.WavParams.BitsPerSample);  // 每个采样点所需的位数
    printf("Channels      : %u\n", p_->audioParams.WavParams.Channels);       // 数据通道数
    printf("ChannelStrtg  : %u\n", p_->audioParams.ChannelStrtg);             // 声道转换策略
    printf("DataOffset    : %u\n", p_->wavDataOffset);                        // 数据偏移
    printf("DataBuf       : %p\n", p_->audioParams.WavParams.DataBuffer);     // 数据偏移
    printf("DataSize      : %u\n", p_->audioParams.WavParams.DataSize);       // 可用数据大小
}

}  // namespace CM
}  // namespace Frameworks
}  // namespace AutoLink
