#ifndef APPFW_CM_WAV_FILE_H
#define APPFW_CM_WAV_FILE_H

#include "autolink/frameworks/cm/audioplayer/audio_player_types.h"

namespace AutoLink {
namespace Frameworks {
namespace CM {

class AudioWavFile final {
public:
    explicit AudioWavFile(std::string filePath);
    ~AudioWavFile();

    SourceAudioParams GetAudioParams();

    void DumpInfo();

private:
    struct AudioWavFilePrivate* p_;
};

}  // namespace CM
}  // namespace Frameworks
}  // namespace AutoLink

#endif