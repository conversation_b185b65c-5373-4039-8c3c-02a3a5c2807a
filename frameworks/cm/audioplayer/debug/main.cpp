﻿#include "autolink/frameworks/cm/audioplayer/debug/commander/commander.h"

#include <thread>
#include <signal.h>
#include <unistd.h>

using namespace AutoLink::Frameworks::CM;

int main(int argc, char *argv[])
{
    AudioDebugCommander cmdr(argc, argv);

    sigset_t mask;
    sigemptyset(&mask);
    sigaddset(&mask, SIGABRT);
    sigaddset(&mask, SIGTERM);
    sigaddset(&mask, SIGSEGV);
    sigaddset(&mask, SIGINT);
    if (sigprocmask(SIG_BLOCK, &mask, NULL) == -1)
    {
        printf("sigprocmask failed: %s\n",strerror(errno));
        return -1;
    }

    std::thread thrdSignal = std::thread(
        [&]()
        {
            int sigNum;
            sigwait(&mask, &sigNum);

            printf("Application abnormally exit by signal %d\n", sigNum);

            cmdr.Exit();

            sigprocmask(SIG_UNBLOCK, &mask, NULL);
            kill(getpid(), sigNum); });
    thrdSignal.detach();

    cmdr.Run();

    printf("Application after run\n");

    return 0;
}
