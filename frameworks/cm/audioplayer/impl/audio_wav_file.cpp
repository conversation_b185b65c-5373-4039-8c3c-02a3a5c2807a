#include "autolink/frameworks/cm/audioplayer/audio_wav_file.h"

#include "fstream"

#include "autolink/frameworks/log/log.h"

using namespace AutoLink::Frameworks::Log;

namespace AutoLink {
namespace Frameworks {
namespace CM {

#define WaveFileRIFFLenght (4u)
#define WaveFileSIZELenght (4u)
#define WaveFileWAVELenght (4u)

#define WaveFileChunkNameLenght (4u)
#define WaveFileChunkSizeLenght (4u)

#define WaveFileFmtAudioFormatLenght (2u)
#define WaveFileFmtChannelsLenght (2u)
#define WaveFileFmtSampleRateLenght (4u)
#define WaveFileFmtByteRateLenght (4u)
#define WaveFileFmtBlockAlignLenght (2u)
#define WaveFileFmtBitsPerSampleLenght (2u)

#define WaveFileFmtLenght (WaveFileFmtAudioFormatLenght + \
                           WaveFileFmtChannelsLenght +    \
                           WaveFileFmtSampleRateLenght +  \
                           WaveFileFmtByteRateLenght +    \
                           WaveFileFmtBlockAlignLenght +  \
                           WaveFileFmtBitsPerSampleLenght)

struct AudioWavFilePrivate {
    std::string path;

    AudioWavParams audioParams{0, 0, 0, nullptr, 0};

    uint32_t fileSize{0};

    uint32_t wavDataOffset{0};
    uint32_t wavDuration{0};

    void ParseWav() {
        std::fstream fsFile;

        fsFile.open(path.c_str(), std::ios::in | std::ios::binary);

        // bool is_open(); C++98
        // bool is_open() const; C++11
        if (!fsFile.is_open()) {
            printf("file %s missing\n", path.c_str());

            fsFile.close();
            return;
        }

        uint32_t pos = 0;

        fsFile.seekg(pos, fsFile.end);
        fileSize = fsFile.tellg();

        fsFile.seekg(pos, fsFile.beg);

        char sRIFF[WaveFileRIFFLenght] = {0};
        fsFile.read(sRIFF, WaveFileRIFFLenght);

        if (sRIFF[0] != 'R' ||
            sRIFF[1] != 'I' ||
            sRIFF[2] != 'F' ||
            sRIFF[3] != 'F') {
            printf("%s no RIFF chunk\n", path.c_str());

            fsFile.close();
            return;
        }

        pos += WaveFileRIFFLenght;
        fsFile.seekg(pos, fsFile.beg);

        char sSize[WaveFileSIZELenght] = {0};
        fsFile.read(sSize, WaveFileSIZELenght);

        pos += WaveFileSIZELenght;
        fsFile.seekg(pos, fsFile.beg);

        char sWAVE[WaveFileWAVELenght] = {0};
        fsFile.read(sWAVE, WaveFileWAVELenght);

        if (sWAVE[0] != 'W' ||
            sWAVE[1] != 'A' ||
            sWAVE[2] != 'V' ||
            sWAVE[3] != 'E') {
            printf("%s not WAVE\n", path.c_str());

            fsFile.close();
            return;
        }

        pos += WaveFileWAVELenght;

        uint32_t WaveDataLen = sSize[3] * 0x1000000 + sSize[2] * 0x10000 + sSize[1] * 0x100 + sSize[0] + WaveFileRIFFLenght + WaveFileSIZELenght;
        if (WaveDataLen != fileSize) {
            printf("file %s size error, file Read: %u, wav info: %u\n", path.c_str(), fileSize, WaveDataLen);

            fsFile.close();
            return;
        }

        char sChunkName[WaveFileChunkNameLenght] = {0};
        char sChunkSize[WaveFileChunkSizeLenght] = {0};
        uint32_t ChunkSize = 0;

        while (pos < fileSize) {
            fsFile.seekg(pos, fsFile.beg);
            fsFile.read(sChunkName, WaveFileChunkNameLenght);

            pos += WaveFileChunkNameLenght;

            if (pos >= fileSize) {
                printf("file %s chunk overflow: %u >= %u\n", path.c_str(), pos, fileSize);

                fsFile.close();
                return;
            }

            fsFile.seekg(pos, fsFile.beg);
            fsFile.read(sChunkSize, WaveFileChunkSizeLenght);

            ChunkSize = sChunkSize[3] * 0x1000000 + sChunkSize[2] * 0x10000 + sChunkSize[1] * 0x100 + sChunkSize[0];

            pos += WaveFileChunkSizeLenght;

            if (pos >= fileSize) {
                printf("file %s chunk overflow: %u >= %u\n", path.c_str(), pos, fileSize);

                fsFile.close();
                return;
            }

            if (sChunkName[0] == 'f' &&
                sChunkName[1] == 'm' &&
                sChunkName[2] == 't' &&
                sChunkName[3] == ' ') {
                pos += WaveFileFmtAudioFormatLenght;

                char sChannels[WaveFileFmtChannelsLenght] = {0};
                fsFile.seekg(pos, fsFile.beg);
                fsFile.read(sChannels, WaveFileFmtChannelsLenght);
                audioParams.Channels = sChannels[0] + sChannels[1] * 0x100;

                pos += WaveFileFmtChannelsLenght;

                char sSampleRate[WaveFileFmtSampleRateLenght] = {0};
                fsFile.seekg(pos, fsFile.beg);
                fsFile.read(sSampleRate, WaveFileFmtSampleRateLenght);
                audioParams.SampleRate = sSampleRate[3] * 0x1000000 + sSampleRate[2] * 0x10000 + sSampleRate[1] * 0x100 + sSampleRate[0];

                pos += WaveFileFmtSampleRateLenght;
                pos += WaveFileFmtByteRateLenght;
                pos += WaveFileFmtBlockAlignLenght;

                char sBitsPerSample[WaveFileFmtBitsPerSampleLenght] = {0};
                fsFile.seekg(pos, fsFile.beg);
                fsFile.read(sBitsPerSample, WaveFileFmtBitsPerSampleLenght);
                audioParams.BitsPerSample = sBitsPerSample[0] + sBitsPerSample[1] * 0x100;

                pos += WaveFileFmtBitsPerSampleLenght;
                pos += (ChunkSize - WaveFileFmtLenght);
            } else if (sChunkName[0] == 'd' &&
                       sChunkName[1] == 'a' &&
                       sChunkName[2] == 't' &&
                       sChunkName[3] == 'a') {
                wavDataOffset = pos;

                audioParams.DataBuffer = new int8_t[ChunkSize];
                fsFile.seekg(pos, fsFile.beg);
                fsFile.read((char*)audioParams.DataBuffer, ChunkSize);

                audioParams.DataSize = ChunkSize;

                break;
            } else {
                pos += ChunkSize;
            }
        }

        fsFile.close();

        wavDuration = audioParams.DataSize * 1000 / (audioParams.SampleRate * audioParams.BitsPerSample * audioParams.Channels / 8);
    }
};

std::shared_ptr<AudioWavFile> AudioWavFile::Create(const char* filePath) {
    return std::shared_ptr<AudioWavFile>(new AudioWavFile(filePath));
}

AudioWavFile::AudioWavFile(std::string filePath) : p_(new AudioWavFilePrivate) {
    p_->path = filePath;

    p_->ParseWav();
}
AudioWavFile::~AudioWavFile() {
    if (nullptr != p_->audioParams.DataBuffer) {
        delete[] p_->audioParams.DataBuffer;
        p_->audioParams.DataBuffer = nullptr;
    }

    if (nullptr != p_) {
        delete p_;
        p_ = nullptr;
    }
}

AudioWavParams AudioWavFile::GetAudioParams() {
    return p_->audioParams;
}

std::string AudioWavFile::FileDescri() {
    return p_->path;
}

}  // namespace CM
}  // namespace Frameworks
}  // namespace AutoLink
