#ifndef APPFW_CM_AUDIO_SOURCE_H
#define APPFW_CM_AUDIO_SOURCE_H

#include "autolink/frameworks/cm/audioplayer/audio_player_types.h"
#include "autolink/frameworks/pa/IAudio/audio_types.h"

using namespace AutoLink::Frameworks::PA;

namespace AutoLink {
namespace Frameworks {
namespace CM {

enum IoStatus : uint8_t {
    IO_START,  // 供给周期开始
    IO_END,    // 供给周期结束
};

typedef std::function<void(const IoStatus&)> IoStatusCallback;

class AudioSource final {
public:
    explicit AudioSource(IoStatusCallback statusCB);
    ~AudioSource();

    void IoRequest(const SourceAudioParams& audioParams,
                   bool bFade = false);
    void IoRequest(bool bFade = false);
    void StopIo();

    void OnDataDelivery(const SinkAudioParams& sinkAudioParams,
                        DataChBufPtr leftOrMonoChPtr,
                        DataChBufPtr rightChPtr,
                        const uint32_t& sizePerCh);

private:
    struct AudioSourcePrivate* p_;
};

}  // namespace CM
}  // namespace Frameworks
}  // namespace AutoLink

#endif