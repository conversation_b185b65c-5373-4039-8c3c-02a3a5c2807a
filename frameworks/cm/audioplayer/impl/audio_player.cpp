#include "autolink/frameworks/cm/audioplayer/audio_player.h"

#include "audio_source.h"
#include "autolink/frameworks/cm/audioplayer/audio_player_types.h"
#include "autolink/frameworks/core/async.h"
#include "autolink/frameworks/core/timer.h"
#include "autolink/frameworks/log/log.h"
#include "autolink/frameworks/pa/IAudio/IAudio.h"

using namespace AutoLink::Frameworks::Log;
using namespace AutoLink::Frameworks::Core;

namespace AutoLink {
namespace Frameworks {
namespace CM {

#define LOG_TAG "AudioPlayer"

#define CHIME_VOLUME_LEVEL_NONE (0)
#define CHIME_VOLUME_LEVEL_LOW (1)
#define CHIME_VOLUME_LEVEL_MID (2)
#define CHIME_VOLUME_LEVEL_HIGH (3)

#define CHIME_LOCATION_ALL (0x3F)

struct AudioPlayerPrivate {
    StreamType strmType;
    AudioSource* pSource{nullptr};
    IAudio* pIAudio{nullptr};

    StatusStartHandler startCB{nullptr};
    StatusStopHandler stopCB{nullptr};

    struct timespec startTime;

    uint32_t playingID{0};
    uint32_t playedTimes{0};
    uint32_t playStopReason{0};

    std::shared_ptr<Timer> timer{nullptr};

    PlayParams playParams{0, 0, 0x3F};

    volatile bool bStopping{false};

    void DataDeliveryCallback(const SinkAudioParams& sinkAudioParams,
                              DataChBufPtr leftOrMonoChPtr,
                              DataChBufPtr rightChPtr,
                              const uint32_t& sizePerCh) {
        return pSource->OnDataDelivery(sinkAudioParams,
                                       leftOrMonoChPtr,
                                       rightChPtr,
                                       sizePerCh);
    }

    void OnIoStatusCallback(const IoStatus& inStatus) {
        LOG_TAG_INFO("ST[%u] ID[%u] IoStatus status[%d] stopping[%d] times[%u]/[%u] Interval[%u]",
                     strmType,
                     playingID,
                     inStatus,
                     bStopping,
                     playedTimes, playParams.Times,
                     playParams.Interval);

        if (IO_START == inStatus) {
            playedTimes++;
        } else if (IO_END == inStatus) {
            // 未接到停止的指令
            // 无次数限制的播放 或者 播放次数未满
            // 无播放间隔
            if ((!bStopping) &&
                ((0 == playParams.Times) ||
                 (playedTimes != playParams.Times)) &&
                (0 == playParams.Interval)) {
                // 无间断
                pSource->IoRequest();

                return;
            }
        }

        Async::Submit("IoStatus", [this, inStatus]() {
            switch (inStatus) {
            case IO_START:
                if (1 == playedTimes) {
                    // this is the start time
                    clock_gettime(CLOCK_MONOTONIC, &startTime);

                    if (nullptr != startCB) {
                        startCB(playingID);
                    }
                }

                break;
            case IO_END:
                if (bStopping) {
                    if (nullptr != pIAudio) {
                        LOG_TAG_INFO("IAudio Stop");
                        pIAudio->Stop();
                    }

                    if (nullptr != stopCB) {
                        uint32_t tmpPlayingID = playingID;
                        playingID = 0;
                        stopCB(tmpPlayingID, playedTimes, playStopReason);
                    }

                } else {
                    // 播放次数已满
                    if (playedTimes == playParams.Times) {
                        if (nullptr != pIAudio) {
                            LOG_TAG_INFO("IAudio Stop");
                            pIAudio->Stop();

                        }
                        if (nullptr != stopCB) {
                            uint32_t tmpPlayingID = playingID;
                            playingID = 0;
                            stopCB(tmpPlayingID, playedTimes, playStopReason);
                        }

                    } else {
                        StartTimer(playParams.Interval);
                    }
                }
                break;

            default:
                break;
            }
        });
    }

    void StartTimer(uint64_t interval) {


        if (nullptr != timer) {
            timer->Start(interval);
        }
    }

    void OnPlayerTimeFunc() {
        pSource->IoRequest();
    }
};

std::shared_ptr<AudioPlayer> AudioPlayer::Create(const StreamType& strmType) {
    return std::shared_ptr<AudioPlayer>(new AudioPlayer(strmType));
}

AudioPlayer::AudioPlayer(const StreamType& strmType) : p_(new AudioPlayerPrivate) {
    p_->strmType = strmType;
    AudioType type = AUDIO_TYPE_CHIME;

    switch (strmType) {
    case STREAM_TYPE_CHIME:
        type = AUDIO_TYPE_CHIME;
        break;
    case STREAM_TYPE_CHIME_3D:
        type = AUDIO_TYPE_CHIME_3D;
        break;
    case STREAM_TYPE_BEEP:
        type = AUDIO_TYPE_BEEP;
        break;
    case STREAM_TYPE_CUE:
        type = AUDIO_TYPE_CUE;
        break;
    case STREAM_TYPE_AVAS:
        type = AUDIO_TYPE_AVAS;
        break;
    case STREAM_TYPE_ESE:
        type = AUDIO_TYPE_ESE;
        break;
    default:
        break;
    }
    LOG_TAG_INFO("%s, AudioType = %d", __func__, type);

    p_->pSource = new AudioSource(std::bind(&AudioPlayerPrivate::OnIoStatusCallback,
                                            p_,
                                            std::placeholders::_1));
    p_->pIAudio = new IAudio(type,
                                CHIME_VOLUME_LEVEL_MID,
                                CHIME_LOCATION_ALL,
                                std::bind(&AudioPlayerPrivate::DataDeliveryCallback,
                                        p_,
                                        std::placeholders::_1,
                                        std::placeholders::_2,
                                        std::placeholders::_3,
                                        std::placeholders::_4));

    p_->timer = Timer::Create("AudioPlayer",
                              p_->playParams.Interval,
                              std::bind(&AudioPlayerPrivate::OnPlayerTimeFunc,
                                        p_));

    if ((nullptr == p_->pIAudio) || (nullptr == p_->pSource) || (nullptr == p_->timer)) {
        throw std::logic_error("no enough component");
    }

    (p_->timer)->SetSingleShot(true);
}
AudioPlayer::~AudioPlayer() {
    if (nullptr != p_->pIAudio) {
        delete p_->pIAudio;
        p_->pIAudio = nullptr;
    }
    if (nullptr != p_->pSource) {
        delete p_->pSource;
        p_->pSource = nullptr;
    }
    if (nullptr != p_) {
        delete p_;
        p_ = nullptr;
    }
}

bool AudioPlayer::PlayerValid()
{
    return p_->pIAudio->ClientValid();
}

bool AudioPlayer::Play(const uint32_t& objID,
                       const SourceAudioParams& audioParams,
                       const PlayParams& playParams) {
    LOG_TAG_INFO("[%u] > Play ID[%d] t[%u] i[%u] m[%u] l[%u]",
                 p_->strmType,
                 objID,
                 playParams.Times,
                 playParams.Interval,
                 playParams.Minimum,
                 playParams.Location);

    p_->playingID = objID;
    p_->playParams = playParams;

    p_->playedTimes = 0;
    p_->bStopping = false;
    p_->playStopReason = 0;

    if (STREAM_TYPE_BEEP == p_->strmType) {
        p_->pSource->IoRequest(audioParams, true);
    } else {
        p_->pSource->IoRequest(audioParams);
    }

    if (nullptr != p_->pIAudio) {

        SinkAudioParams params {
            audioParams.WavParams.SampleRate,
            audioParams.WavParams.BitsPerSample,
            audioParams.WavParams.Channels
        };
        LOG_TAG_INFO("samplerate = %d, bits = %d, channel = %d, databuffer = %p, dataSize = %u",
        audioParams.WavParams.SampleRate,
        audioParams.WavParams.BitsPerSample,
        audioParams.WavParams.Channels,
        audioParams.WavParams.DataBuffer,
        audioParams.WavParams.DataSize);
        p_->pIAudio->SetParams(params);
        if (nullptr != p_->pIAudio) {
            if (p_->pIAudio->Start() != 0) {
                p_->playingID = 0;
                LOG_TAG_ERROR("AudioPlayer start failed, set playid 0");
                return false;
            }
            else {
                LOG_TAG_INFO("AudioPlayer start success");
            }
            return true;
        }
    }

    p_->playingID = 0;
    LOG_TAG_ERROR("pIAudio is nullptr, set playid 0");
    return true;
}

void AudioPlayer::Stop(uint32_t iStopReason, bool bImme) {
    LOG_TAG_INFO("[%u] > Stop ID[%d] f[%d]",
                 p_->strmType,
                 p_->playingID,
                 bImme);

    if (/*(STREAM_TYPE_BEEP == p_->strmType) ||*/ bImme) {
        p_->pSource->StopIo();
    }

    p_->bStopping = true;
    p_->playStopReason = iStopReason;
}

int32_t AudioPlayer::SetVolumeLevel(uint8_t volumeLevel) {
    if (nullptr != p_->pIAudio) {
        // AudioType audioType = AUDIO_TYPE_CHIME_3D;

        // switch (p_->strmType) {
        // case STREAM_TYPE_CHIME:
        //     audioType = AUDIO_TYPE_CHIME;
        //     break;
        // case STREAM_TYPE_BEEP:
        //     audioType = AUDIO_TYPE_BEEP;
        //     break;
        // case STREAM_TYPE_CUE:
        //     audioType = AUDIO_TYPE_CUE;
        //     break;
        // default:
        //     break;
        // }

        return p_->pIAudio->SetVolumeLevel(volumeLevel, (AudioType)p_->strmType);
    }

    return -1;
}

void AudioPlayer::Suspend() {
    if (nullptr != p_->pIAudio) {
        p_->pIAudio->Suspend();
    }
}
void AudioPlayer::Resume() {
    if (nullptr != p_->pIAudio) {
        p_->pIAudio->Resume();
    }
}

uint32_t AudioPlayer::GetCurPlayingID() {
    return p_->playingID;
}
uint32_t AudioPlayer::GetTimeElapsed() {
    struct timespec curTime;
    clock_gettime(CLOCK_MONOTONIC, &curTime);

    uint32_t elapsedTime = ((curTime.tv_sec * 1000000000 + curTime.tv_nsec) -
                            (p_->startTime.tv_sec * 1000000000 + p_->startTime.tv_nsec)) /
                           1000000;

    return elapsedTime;
}
bool AudioPlayer::IsStopping() {
    return p_->bStopping;
}

void AudioPlayer::RegisterStartHandler(StatusStartHandler hndl) {
    p_->startCB = hndl;
}

void AudioPlayer::RegisterStopHandler(StatusStopHandler hndl) {
    p_->stopCB = hndl;
}

}  // namespace CM
}  // namespace Frameworks
}  // namespace AutoLink
