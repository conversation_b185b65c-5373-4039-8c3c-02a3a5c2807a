#include "audio_source.h"

#include <mutex>

#include "autolink/frameworks/log/log.h"

using namespace AutoLink::Frameworks::Log;

namespace AutoLink {
namespace Frameworks {
namespace CM {

#define LOG_TAG "AudioSource"

enum IoFade : uint8_t {
    IO_NO_FADE = 0,
    IO_FADE_IN,
    IO_FADE_OUT
};

struct AudioSourcePrivate {
    IoStatusCallback statusCB{nullptr};
    SourceAudioParams auParams{{0, 0, 0, nullptr, 0}, STRATEGY_INVALID};
    IoStatus curStatus{IO_END};

    std::mutex lckData;

    volatile uint32_t remainSize{0};

    volatile IoFade ioFade{IO_NO_FADE};

    uint32_t framesCntFade{0};
    uint32_t framesCurrFade{0};

    void FadePreSeq16BPS(uint32_t remianBytesPerChSink) {
        if (IO_NO_FADE == ioFade) {
            return;
        }

        uint16_t bytesPerSample = auParams.WavParams.BitsPerSample / 8;
        uint32_t remianBytesPerChSource = remainSize / auParams.WavParams.Channels;

        framesCntFade =
            (remianBytesPerChSink < remianBytesPerChSource) ? (remianBytesPerChSink / bytesPerSample) : (remianBytesPerChSource / bytesPerSample);

        framesCurrFade = 0;
    }

    bool FadeExecSeq16BPSFromMono(DataChBufPtr sourceBuf, DataChBufPtr sinkBufL, DataChBufPtr sinkBufR) {
        if (IO_NO_FADE == ioFade) {
            return false;
        }

        // 获得源数据
        int16_t sampleDataFade;
        ::memcpy(&sampleDataFade, sourceBuf, sizeof(int16_t));

        // 根据fade的类型决定数据的变换方式
        if (IO_FADE_IN == ioFade) {
            sampleDataFade *= (float)(framesCntFade - framesCurrFade - 1) / framesCntFade;
        } else if (IO_FADE_OUT == ioFade) {
            sampleDataFade *= (float)framesCurrFade / framesCntFade;
        }

        // 使用变换后的数据
        if (nullptr != sinkBufL) {
            ::memcpy(sinkBufL,
                     &sampleDataFade,
                     sizeof(int16_t));
        }

        if (nullptr != sinkBufR) {
            ::memcpy(sinkBufR,
                     &sampleDataFade,
                     sizeof(int16_t));
        }

        // 当前帧结束
        framesCurrFade++;

        return true;
    }

    bool FadeExecSeq16BPSFromStero(DataChBufPtr sourceBufL, DataChBufPtr sourceBufR,
                                   DataChBufPtr sinkBufL, DataChBufPtr sinkBufR) {
        if (IO_NO_FADE == ioFade) {
            return false;
        }

        // 获得源数据
        int16_t sampleDataFadeL;
        ::memcpy(&sampleDataFadeL, sourceBufL, sizeof(int16_t));
        int16_t sampleDataFadeR;
        ::memcpy(&sampleDataFadeR, sourceBufR, sizeof(int16_t));

        // 根据fade的类型决定数据的变换方式
        if (IO_FADE_IN == ioFade) {
            sampleDataFadeL *= (float)(framesCntFade - framesCurrFade - 1) / framesCntFade;
            sampleDataFadeR *= (float)(framesCntFade - framesCurrFade - 1) / framesCntFade;
        } else if (IO_FADE_OUT == ioFade) {
            sampleDataFadeL *= (float)framesCurrFade / framesCntFade;
            sampleDataFadeR *= (float)framesCurrFade / framesCntFade;
        }

        // 使用变换后的数据
        if (nullptr != sinkBufL) {
            ::memcpy(sinkBufL,
                     &sampleDataFadeL,
                     sizeof(int16_t));
        }

        if (nullptr != sinkBufR) {
            ::memcpy(sinkBufR,
                     &sampleDataFadeR,
                     sizeof(int16_t));
        }

        // 当前帧结束
        framesCurrFade++;

        return true;
    }

    void FadePostSeq(uint32_t& fadeRemainSize) {
        if (IO_NO_FADE == ioFade) {
            return;
        }

        if (framesCurrFade == framesCntFade) {
            if (IO_FADE_OUT == ioFade) {
                fadeRemainSize = 0;
            }

            ioFade = IO_NO_FADE;
        }
    }
};

AudioSource::AudioSource(IoStatusCallback statusCB) : p_(new AudioSourcePrivate) {
    p_->statusCB = statusCB;
}
AudioSource::~AudioSource() {
    if (nullptr != p_) {
        delete p_;
        p_ = nullptr;
    }
}

void AudioSource::IoRequest(const SourceAudioParams& newParams, bool bFade) {
    p_->lckData.lock();

    p_->auParams = newParams;

    // p_->auParams.WavParams.SampleRate = newParams.SampleRate;
    // p_->auParams.WavParams.BitsPerSample = newParams.BitsPerSample;
    // p_->auParams.WavParams.Channels = newParams.Channels;
    // p_->auParams.ChannelStrtg = newParams.ChannelStrtg;
    // p_->auParams.WavParams.DataBuffer = newParams.DataBuffer;
    // p_->auParams.WavParams.DataSize = newParams.DataSize;

    p_->remainSize = newParams.WavParams.DataSize;

    p_->ioFade = bFade ? IO_FADE_IN : IO_NO_FADE;

    p_->lckData.unlock();

    LOG_TAG_INFO("IoRequest buf[%p] size[%u] fade[%d]",
                 newParams.WavParams.DataBuffer,
                 newParams.WavParams.DataSize,
                 bFade);
}

void AudioSource::IoRequest(bool bFade) {
    p_->lckData.lock();

    p_->remainSize = p_->auParams.WavParams.DataSize;

    p_->ioFade = bFade ? IO_FADE_IN : IO_NO_FADE;

    p_->lckData.unlock();

    LOG_TAG_INFO("IoRequest fade[%d]", bFade);
}

void AudioSource::StopIo() {
    p_->lckData.lock();

    p_->ioFade = IO_FADE_OUT;

    p_->lckData.unlock();

    LOG_TAG_INFO("StopIo");
}

void AudioSource::OnDataDelivery(const SinkAudioParams& sinkAudioParams,
                                 DataChBufPtr leftOrMonoChPtr,
                                 DataChBufPtr rightChPtr,
                                 const uint32_t& sizePerCh) {
    if ((sinkAudioParams.SampleRate != p_->auParams.WavParams.SampleRate) ||
        (sinkAudioParams.BitsPerSample != p_->auParams.WavParams.BitsPerSample) ||
        (nullptr == p_->auParams.WavParams.DataBuffer) ||
        (0 == p_->auParams.WavParams.DataSize)) {
        if (nullptr != leftOrMonoChPtr) {
            ::memset(leftOrMonoChPtr, 0, sizePerCh);
        }

        if (nullptr != rightChPtr) {
            ::memset(rightChPtr, 0, sizePerCh);
        }

        return;
    }

    uint16_t sinkBytesPerSample = sinkAudioParams.BitsPerSample / 8;
    uint32_t remainSizeLocal = 0;
    uint32_t sourceDataOffsetPerCh = 0;
    uint32_t sinkDataOffsetPerCh = 0;

    uint32_t remainSizePerCh = sizePerCh;
    while (0 < remainSizePerCh) {
        p_->lckData.lock();

        p_->FadePreSeq16BPS(remainSizePerCh);

        if (0 == p_->remainSize) {
            sinkDataOffsetPerCh = sizePerCh - remainSizePerCh;

            if (nullptr != leftOrMonoChPtr) {
                ::memset(leftOrMonoChPtr + sinkDataOffsetPerCh,
                         0,
                         sinkBytesPerSample);
            }

            if (nullptr != rightChPtr) {
                ::memset(rightChPtr + sinkDataOffsetPerCh,
                         0,
                         sinkBytesPerSample);
            }

            // if ((nullptr != p_->statusCB) && (IO_END != p_->curStatus)) {
            //     p_->curStatus = IO_END;
            //     p_->statusCB(IO_END);
            // }

            if (remainSizePerCh > sinkBytesPerSample) {
                remainSizePerCh -= sinkBytesPerSample;
            } else {
                remainSizePerCh = 0;
            }

            p_->lckData.unlock();

            continue;
        }

        if (p_->auParams.WavParams.DataSize == p_->remainSize) {
            if ((nullptr != p_->statusCB) && (IO_START != p_->curStatus)) {
                p_->curStatus = IO_START;
                p_->statusCB(IO_START);
            }
        }

        remainSizeLocal = p_->remainSize;
        sourceDataOffsetPerCh = p_->auParams.WavParams.DataSize - remainSizeLocal;
        sinkDataOffsetPerCh = sizePerCh - remainSizePerCh;

        if (p_->auParams.WavParams.Channels < sinkAudioParams.Channels) {
            // enough data
            if (sinkBytesPerSample <= remainSizeLocal) {
                if (ChannelStrategy::STRATEGY_MONO_TO_STEREO_RIGHT == p_->auParams.ChannelStrtg) {
                    ::memset(leftOrMonoChPtr + sinkDataOffsetPerCh,
                             0,
                             sinkBytesPerSample);

                    if (!p_->FadeExecSeq16BPSFromMono(p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh,
                                                      nullptr,
                                                      rightChPtr + sinkDataOffsetPerCh)) {
                        ::memcpy(rightChPtr + sinkDataOffsetPerCh,
                                 p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh,
                                 sinkBytesPerSample);
                    }

                    remainSizeLocal -= sinkBytesPerSample;
                } else if (ChannelStrategy::STRATEGY_MONO_TO_STEREO_LEFT == p_->auParams.ChannelStrtg) {
                    if (!p_->FadeExecSeq16BPSFromMono(p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh,
                                                      leftOrMonoChPtr + sinkDataOffsetPerCh,
                                                      nullptr)) {
                        ::memcpy(leftOrMonoChPtr + sinkDataOffsetPerCh,
                                 p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh,
                                 sinkBytesPerSample);
                    }

                    ::memset(rightChPtr + sinkDataOffsetPerCh,
                             0,
                             sinkBytesPerSample);

                    remainSizeLocal -= sinkBytesPerSample;
                } else {
                    // STRATEGY_MONO_TO_STEREO_ALL
                    if (!p_->FadeExecSeq16BPSFromMono(p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh,
                                                      leftOrMonoChPtr + sinkDataOffsetPerCh,
                                                      rightChPtr + sinkDataOffsetPerCh)) {
                        ::memcpy(leftOrMonoChPtr + sinkDataOffsetPerCh,
                                 p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh,
                                 sinkBytesPerSample);
                        ::memcpy(rightChPtr + sinkDataOffsetPerCh,
                                 p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh,
                                 sinkBytesPerSample);
                    }

                    remainSizeLocal -= sinkBytesPerSample;
                }
            } else {
                ::memset(leftOrMonoChPtr + sinkDataOffsetPerCh,
                         0,
                         sinkBytesPerSample);
                ::memset(rightChPtr + sinkDataOffsetPerCh,
                         0,
                         sinkBytesPerSample);

                remainSizeLocal = 0;
            }
        } else if (p_->auParams.WavParams.Channels > sinkAudioParams.Channels) {
            // enough data
            if ((p_->auParams.WavParams.Channels * sinkBytesPerSample) <= remainSizeLocal) {
                if (ChannelStrategy::STRATEGY_STEREO_RIGHT_TO_MONO == p_->auParams.ChannelStrtg) {
                    if (!p_->FadeExecSeq16BPSFromStero(p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh,
                                                       p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh + sinkBytesPerSample,
                                                       nullptr,
                                                       leftOrMonoChPtr + sinkDataOffsetPerCh)) {
                        ::memcpy(leftOrMonoChPtr + sinkDataOffsetPerCh,
                                 p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh + sinkBytesPerSample,
                                 sinkBytesPerSample);
                    }

                    remainSizeLocal -= (p_->auParams.WavParams.Channels * sinkBytesPerSample);
                } else {
                    // STRATEGY_STEREO_LEFT_TO_MONO
                    if (!p_->FadeExecSeq16BPSFromStero(p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh,
                                                       p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh + sinkBytesPerSample,
                                                       leftOrMonoChPtr + sinkDataOffsetPerCh,
                                                       nullptr)) {
                        ::memcpy(leftOrMonoChPtr + sinkDataOffsetPerCh,
                                 p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh,
                                 sinkBytesPerSample);
                    }
                    remainSizeLocal -= (p_->auParams.WavParams.Channels * sinkBytesPerSample);
                }
            } else {
                ::memset(leftOrMonoChPtr + sinkDataOffsetPerCh,
                         0,
                         sinkBytesPerSample);

                remainSizeLocal = 0;
            }
        } else {
            if (2 == p_->auParams.WavParams.Channels) {
                // enough data
                if ((p_->auParams.WavParams.Channels * sinkBytesPerSample) <= remainSizeLocal) {
                    if (ChannelStrategy::STRATEGY_STEREO_TO_STEREO_SWAP == p_->auParams.ChannelStrtg) {
                        if (!p_->FadeExecSeq16BPSFromStero(p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh,
                                                           p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh + sinkBytesPerSample,
                                                           rightChPtr + sinkDataOffsetPerCh,
                                                           leftOrMonoChPtr + sinkDataOffsetPerCh)) {
                            ::memcpy(rightChPtr + sinkDataOffsetPerCh,
                                     p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh,
                                     sinkBytesPerSample);
                            ::memcpy(leftOrMonoChPtr + sinkDataOffsetPerCh,
                                     p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh + sinkBytesPerSample,
                                     sinkBytesPerSample);
                        }
                        remainSizeLocal -= (p_->auParams.WavParams.Channels * sinkBytesPerSample);
                    } else {
                        if (!p_->FadeExecSeq16BPSFromStero(p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh,
                                                           p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh + sinkBytesPerSample,
                                                           leftOrMonoChPtr + sinkDataOffsetPerCh,
                                                           rightChPtr + sinkDataOffsetPerCh)) {
                            ::memcpy(leftOrMonoChPtr + sinkDataOffsetPerCh,
                                     p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh,
                                     sinkBytesPerSample);
                            ::memcpy(rightChPtr + sinkDataOffsetPerCh,
                                     p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh + sinkBytesPerSample,
                                     sinkBytesPerSample);
                        }
                        remainSizeLocal -= (p_->auParams.WavParams.Channels * sinkBytesPerSample);
                    }
                } else {
                    ::memset(leftOrMonoChPtr + sinkDataOffsetPerCh,
                             0,
                             sinkBytesPerSample);
                    ::memset(rightChPtr + sinkDataOffsetPerCh,
                             0,
                             sinkBytesPerSample);
                    remainSizeLocal = 0;
                }
            } else {
                // Mono
                // enough data
                if (sinkBytesPerSample <= remainSizeLocal) {
                    if (!p_->FadeExecSeq16BPSFromMono(p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh,
                                                      leftOrMonoChPtr + sinkDataOffsetPerCh,
                                                      nullptr)) {
                        ::memcpy(leftOrMonoChPtr + sinkDataOffsetPerCh,
                                 p_->auParams.WavParams.DataBuffer + sourceDataOffsetPerCh,
                                 sinkBytesPerSample);
                    }
                    remainSizeLocal -= sinkBytesPerSample;
                } else {
                    ::memset(leftOrMonoChPtr + sinkDataOffsetPerCh,
                             0,
                             sinkBytesPerSample);

                    remainSizeLocal = 0;
                }
            }
        }
        if (remainSizePerCh > sinkBytesPerSample) {
            remainSizePerCh -= sinkBytesPerSample;
        } else {
            remainSizePerCh = 0;
        }

        p_->FadePostSeq(remainSizeLocal);

        p_->remainSize = remainSizeLocal;

        p_->lckData.unlock();

        if (0 == remainSizeLocal) {
            if ((nullptr != p_->statusCB) && (IO_END != p_->curStatus)) {
                p_->curStatus = IO_END;

                // this should be beyond the lock area
                p_->statusCB(IO_END);
            }
        }
    }
}

}  // namespace CM
}  // namespace Frameworks
}  // namespace AutoLink
