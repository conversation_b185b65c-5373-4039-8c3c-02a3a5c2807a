#ifndef APPFW_CM_AUDIO_WAV_TYPE_H
#define APPFW_CM_AUDIO_WAV_TYPE_H

#include <stdint.h>

namespace AutoLink {
namespace Frameworks {
namespace CM {

typedef struct AudioWavParams {
    uint32_t SampleRate;     // 采样频率
    uint16_t BitsPerSample;  // 每个采样点所需的位数
    uint8_t Channels;        // 数据通道数
    int8_t* DataBuffer;      // chunk data 数据地址
    uint32_t DataSize;       // chunk data 数据大小
} AudioWavParams_;

}  // namespace CM
}  // namespace Frameworks
}  // namespace AutoLink

#endif
