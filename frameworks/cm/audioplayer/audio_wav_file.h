#ifndef APPFW_CM_AUDIO_WAV_FILE_H
#define APPFW_CM_AUDIO_WAV_FILE_H

#include "audio_wav_types.h"

#include <memory>

namespace AutoLink {
namespace Frameworks {
namespace CM {

class AudioWavFile final {
public:
    static std::shared_ptr<AudioWavFile> Create(const char* filePath);
    ~AudioWavFile();

    AudioWavParams GetAudioParams();

    std::string FileDescri();

private:
    AudioWavFile(std::string filePath);

private:
    struct AudioWavFilePrivate* p_;
};

}  // namespace CM
}  // namespace Frameworks
}  // namespace AutoLink

#endif