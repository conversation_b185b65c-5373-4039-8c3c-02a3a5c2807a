#ifndef APPFW_CM_AUDIO_PLAYER_H
#define APPFW_CM_AUDIO_PLAYER_H

#include <memory>

#include "audio_player_types.h"
#include "autolink/frameworks/pa/IAudio/audio_types.h"

using namespace AutoLink::Frameworks::PA;

namespace AutoLink {
namespace Frameworks {
namespace CM {

class AudioPlayer final {
public:
    static std::shared_ptr<AudioPlayer> Create(const StreamType& strmType);

    ~AudioPlayer();

    bool PlayerValid();

    bool Play(const uint32_t& objID,
              const SourceAudioParams& audioParams,
              const PlayParams& playParams);

    /// @brief 
    /// @param iStopReason : 0 will be set when playing for default setting
    /// @param bImme : set true to stop immediately
    void Stop(uint32_t iStopReason, bool bImme = false);

    /// @brief
    /// @param volumeLevel
    ///     IAUDIO_VOLUME_LEVEL_LOW
    ///     IAUDIO_VOLUME_LEVEL_MID
    ///     IAUDIO_VOLUME_LEVEL_HIGH
    /// @return
    int32_t SetVolumeLevel(uint8_t volumeLevel);

    void Suspend();
    void Resume();

    uint32_t GetCurPlayingID();
    uint32_t GetTimeElapsed();
    bool IsStopping();

    void RegisterStartHandler(StatusStartHandler hndl);
    void RegisterStopHandler(StatusStopHandler hndl);

private:
    explicit AudioPlayer(const StreamType& strmType);

    AudioPlayer(const AudioPlayer&) = delete;
    AudioPlayer(AudioPlayer&&) = delete;
    const AudioPlayer& operator=(const AudioPlayer&) = delete;

private:
    struct AudioPlayerPrivate* p_;
};

}  // namespace CM
}  // namespace Frameworks
}  // namespace AutoLink

#endif