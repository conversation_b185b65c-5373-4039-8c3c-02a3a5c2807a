#ifndef APPFW_CM_AUDIO_PLAYER_TYPE_H
#define APPFW_CM_AUDIO_PLAYER_TYPE_H

#include <functional>

#include "audio_wav_types.h"

namespace AutoLink {
namespace Frameworks {
namespace CM {

enum StreamType : uint8_t {
    STREAM_TYPE_CHIME = 0,
    STREAM_TYPE_CHIME_3D = 1,  // Reserved
    STREAM_TYPE_BEEP = 2,
    STREAM_TYPE_CUE = 3,
    STREAM_TYPE_AVAS = 4,
    STREAM_TYPE_ESE = 5,
};

enum ChannelStrategy : uint8_t {
    STRATEGY_INVALID = 0,
    STRATEGY_STEREO_LEFT_TO_MONO,   // 立体声左声道 > 单声道，默认
    STRATEGY_STEREO_RIGHT_TO_MONO,  // 立体声右声道 > 单声道
    STRATEGY_MONO_TO_STEREO_LEFT,   // 单声道 > 立体声左声道
    STRATEGY_MONO_TO_STEREO_RIGHT,  // 单声道 > 立体声右声道
    STRATEGY_MONO_TO_STEREO_ALL,    // 单声道 > 立体声左声道 + 右声道 副本，默认
    STRATEGY_STEREO_TO_STEREO_SWAP  // 立体声 > 立体声 声道转换，默认不交换
};

typedef struct PlayParams {
    uint32_t Times;     // 播放次数，0 for no limit
    uint16_t Interval;  // 当次播放开始距前次播放结束的时间间隔（单位：ms）
    uint16_t Minimum;   // 播放最小时间（单位：ms）

    uint8_t Location;  // 播放位置
} PlayParams_;

typedef struct SourceAudioParams {
    AudioWavParams WavParams;      // WAV 格式
    ChannelStrategy ChannelStrtg;  // 声道转换策略
} SourceAudioParams_;

typedef std::function<void(uint32_t objID)> StatusStartHandler;
typedef std::function<void(uint32_t objID, uint32_t playedTimes, uint32_t stopReason)> StatusStopHandler;

}  // namespace CM
}  // namespace Frameworks
}  // namespace AutoLink

#endif
