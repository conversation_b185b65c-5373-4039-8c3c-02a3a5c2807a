/*    Copyright(C) 2024 Autolink Information Technology Co.Ltd. All rights reserved.   */
/*                                                                                     */
/*                         The file is automatically generated.                        */
/*                      !!!!!! Manual changes are prohibited !!!!!                     */
/*                                                                                     */
/* latest change:                                                                      */
/*
      2025/08/05

      Vehicle_n80.rx 增加初版TC、ETC信号列表
*/

#ifndef APPFW_CM_CAR_PROPERTY_CFG_H
#define APPFW_CM_CAR_PROPERTY_CFG_H

#include "autolink/frameworks/cm/carpropertymanager/impl/common/car_property_def.h"

namespace AutoLink {
namespace Frameworks {
namespace CM {

const char* CAR_PROPERTY_CFG_GEN_TIME = "GEN_2025_08_06_103020";

const bool ProcessHost = 0;

const uint32_t ConfigNum = 96;

const CarPropertyConfig gCarProperyConfigArray[ConfigNum] = {
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_DAYNIGHT_MODE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_LANGUAGE_SETTING" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_STARTUP_STS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_DISPLAY_STS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_FACTORY_RESET" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_FULL_SCREEN" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_BATTERY_LEVEL_UNIT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_TIME_FORMAT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "1", "ivi", "rx", "I2C_WARN_VOLUME_LEVEL" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_ESE_SWITCH" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_ESE_VOLUME_LEVEL" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_ESE_MODE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_AVAS_STATUS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "1", "ivi", "rx", "I2C_HUD_BSD_SWITCH" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_STRING, """", "ivi", "rx", "I2C_NAVI_GUIDE_INFO" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_STRING, """", "ivi", "rx", "I2C_CALL_CONTACT_INFO" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_STRING, """", "ivi", "rx", "I2C_NAVI_LANE_INFO" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_STRING, """", "ivi", "rx", "I2C_TMC_SEGMENT_INFO" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_CLUSTER_SOUND_TEST" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_SMART_KEY_SWITCH" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_STRING, """", "ivi", "rx", "I2C_SOFTWARE_VERSION" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_HUD_HOMETHEATRE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_NTC_TEMP_HIGH_LIGHT_DOWN" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_NTC_TEMP_HIGH_OFF_SCREEN" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_NAVI_STATUS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_CALL_STATUS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_LOG_EXPORT_STATUS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_TRUNK_DISABLE_CLICKED" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_ARRAY, "[ , , ]", "ivi", "tx", "C2I_WARN_HISTORY_LIST_STR" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_STRING, """", "ivi", "tx", "C2I_SOC_VERSION" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "tx", "C2I_DAYNIGHT_MODE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "tx", "C2I_LANGUAGE_SETTING" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "tx", "C2I_CHARGE_CENTER_REQUEST" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "tx", "C2I_MIRRCMD_DIS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "tx", "C2I_BATTERY_LEVEL_UNIT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "tx", "C2I_TIME_FORMAT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "1", "ivi", "tx", "C2I_HUD_BSD_SWITCH" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "2", "ivi", "tx", "C2I_HMI_MENU_PAGE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "tx", "C2I_HUD_HOMETHEATRE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "tx", "C2I_CHIME_ALARM_STATUS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_ARRAY, "[ , , ]", "ivi", "tx", "C2I_TT_STATUS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "DRVMODSIG" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "DRVMODSHIFTMISOPER" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "DRVSTYLE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "LNCHCTRLMODDIRMN" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "LNCHCTRLTRIGRMN" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "ACLRTIREQ" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "CHRGSTSDISP" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "VEHEXTDCHASTS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "RESVCHRGSTSDISP" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "CHRGDISCHRGCRTDISP" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_FLOAT, "0", "custom", "drivinginfo", "CHRGDCHAPWRDISP" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "RMNGCHRGTIDISPLY" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "PWRCOLOR" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "DOORLOCK_BTNSTS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "DOORLOCK_BTNGRAY" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "CHRGPORTDOOR_BTNSTS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "CHRGPORTDOOR_BTNGRAY" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TRUNK_BTNSTS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TRUNK_BTNGRAY" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "MIRRCMD_BTNSTS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "MIRRCMD_BTNGRAY" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "FUELDOOR_BTNSTS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "FUELDOOR_BTNGRAY" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TBOX_RESVACCHRGSTRTTI_HR" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TBOX_RESVACCHRGSTRTTI_MINS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TBOX_RESVACCHRGENDTI_HR" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TBOX_RESVACCHRGENDTI_MINS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TBOX_MONRESVACCHRGREPSTRT_SET" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "OUTDT_VALUE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "CHARG_EFFICIENCY_FLAG" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "CHARG_EFFICIENCY" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TIRERIFRNTP_WARN" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TIRELEFRNT_WARN" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TIRELEREP_WARN" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TIRERIREP_WARN" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "LERETIRE_T" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "LEFRNTTIRE_T" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "RIFRNTTIRE_T" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "RIRETIRE_T" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_FLOAT, "0", "custom", "drivinginfo", "LEFRNT_P" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_FLOAT, "0", "custom", "drivinginfo", "RIFRNT_P" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_FLOAT, "0", "custom", "drivinginfo", "RIRE_P" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_FLOAT, "0", "custom", "drivinginfo", "LERE_P" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TIMER_HOUR" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TIMER_MINUTE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "power", "CLUSTER_ANIMATION_STATUS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "power", "IVI_ANIMATION_STATUS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "power", "POWER_MODE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "power", "READY" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_STRING, "“”", "custom", "power", "STR_REGISTER" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_STRING, "“”", "custom", "power", "STR_SUSPEND_PREPARE_ACK" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "custom", "power", "STR_SUSPEND_STATUS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_STRING, """", "custom", "power", "STR_SUSPEND_RESUME_ACK" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "power", "POWER_STATE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "power", "WHUD_HMI_READY" },
};

} // namespace CM
} // namespace Frameworks
} // namespace AutoLink

#endif