/*    Copyright(C) 2024 Autolink Information Technology Co.Ltd. All rights reserved.   */
/*                                                                                     */
/*                         The file is automatically generated.                        */
/*                      !!!!!! Manual changes are prohibited !!!!!                     */
/*                                                                                     */
/* latest change:                                                                      */
/*
      2025/08/05

      Vehicle_n80.rx 增加初版TC、ETC信号列表
*/

#ifndef APPFW_CM_CAR_PROPERTY_CFG_H
#define APPFW_CM_CAR_PROPERTY_CFG_H

#include "autolink/frameworks/cm/carpropertymanager/impl/common/car_property_def.h"

namespace AutoLink {
namespace Frameworks {
namespace CM {

const char* CAR_PROPERTY_CFG_GEN_TIME = "GEN_2025_08_06_103020";

const bool ProcessHost = 0;

const uint32_t ConfigNum = 13;

const CarPropertyConfig gCarProperyConfigArray[ConfigNum] = {
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "usr_cfg", "USR_LANGUAGE_SETTING" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "usr_cfg", "USR_DAYNIGHT_MODE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_BYTES, "0", "psis", "usr_cfg", "USR_EXTERNAL_APP_ACTIVE_CODE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "usr_cfg", "USR_BATTERY_LEVEL_UNIT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "usr_cfg", "USR_TIME_FORMAT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "1", "psis", "usr_cfg", "USR_HUD_BSD_SWITCH" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "1", "psis", "usr_cfg", "USR_HUD_SWITCH" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "5", "psis", "usr_cfg", "USR_HUD_HEIADJ" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "5", "psis", "usr_cfg", "USR_HUD_ILLADJ" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "1", "psis", "usr_cfg", "USR_HUD_MODSWT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "1", "psis", "usr_cfg", "USR_HUD_SNOWMODESWTSTS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "usr_cfg", "USR_HUD_CRTLANGUAGE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "2", "psis", "usr_cfg", "USR_HMI_PAGEINDEX" },
};

} // namespace CM
} // namespace Frameworks
} // namespace AutoLink

#endif