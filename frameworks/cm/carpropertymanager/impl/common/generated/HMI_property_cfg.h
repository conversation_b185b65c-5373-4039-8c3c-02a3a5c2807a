/*    Copyright(C) 2024 Autolink Information Technology Co.Ltd. All rights reserved.   */
/*                                                                                     */
/*                         The file is automatically generated.                        */
/*                      !!!!!! Manual changes are prohibited !!!!!                     */
/*                                                                                     */
/* latest change:                                                                      */
/*
      2025/08/05

      Vehicle_n80.rx 增加初版TC、ETC信号列表
*/

#ifndef APPFW_CM_CAR_PROPERTY_CFG_H
#define APPFW_CM_CAR_PROPERTY_CFG_H

#include "autolink/frameworks/cm/carpropertymanager/impl/common/car_property_def.h"

namespace AutoLink {
namespace Frameworks {
namespace CM {

const char* CAR_PROPERTY_CFG_GEN_TIME = "GEN_2025_08_06_103020";

const bool ProcessHost = 0;

const uint32_t ConfigNum = 311;

const CarPropertyConfig gCarProperyConfigArray[ConfigNum] = {
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_HUD_TYPE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_AEB" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_FCW" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_ACC" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_ICA" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_TSR" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_ISA" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_LDW" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_LCA" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_DOW" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_FCTA" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_RCTA" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_TLA" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_HWA" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_STEERING_WHEEL_HEATING" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_FACE_RECOGNITION" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_DMS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_APA" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_WPC" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_UVGI" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_FRAGRANCE_FUNTION" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_DRIVER_SEAT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_DRIVER_SEAT_LUMBAR_SUPPORT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_DRIVER_SEAT_HEATING" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_PASSENGER_SEAT_MEMORY" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_PASSENGER_SEAT_HEATING" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_SECOND_ROW_SEATBELT_UNFASTENED_WARNING" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_REMOTE_CONTROL_PARKING" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_REMOTE_KEY" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_HI_CAR" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_CAR_PLAY" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_CAR_BIT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_FCTB" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_RCTB" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_RCW" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_ISLC" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_WELCOME_CEREMONY_LIGHTING_FUNCTION" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_INTELLIGENT_HEADLIGHT_CONTROL" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_DYNAMIC_TAILLIGHT_ANIMATION" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_INTERIOR_AMBIENT_LIGHT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_EXTERNAL_REARVIEW_MIRROR_ELECTRIC_HEATING" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_REARVIEW_MIRROR_FOLDS_AUTOMATICALLY" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_CUSHION_ADJUSTMENT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_CC" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_SECOND_ROW_SEAT_HEATING" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_ELECTRIC_TAIL" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_POWER_LIFTGATE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_NUMBER_OF_SPEAKERS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_RAGE_MODE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_TRACK_MODE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_AUXILIARY_INSTRUMENT_PANEL_AMBIENT_LIGHT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_DEFAULT_LANGUAGE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_DRIVER_SEAT_VENTILATION" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_PASSENGER_SEAT_VENTILATION" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_VITAL_SIGNS_MONITORING" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_INTELLIGENT_HIGH_BEAM" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_PM2_5_MONITORING_DISPLAY" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_DRIVER_SEAT_MASSAGE_FUNCTION" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_PASSENGER_SEAT_MASSAGE_FUNCTION" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_RADIO" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_CAR_PAINT_COLOR" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_EXHAUST_OUTLET" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_CHARGING_PORT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_REARVIEW_MIRROR_HEAT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_CENTRAL_CONTROL_SCREEN" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_CAR_MOUNTED_CHIP" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_ABAT_VENT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_ZERO_GRAVITY_PASSENGER_SEAT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_LEG_REST_PASSENGER_SEAT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_MASSAGE_DRIVER_SEAT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_MASSAGE_PASSENGER_SEAT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_FRONT_SEAT_VIBRATION_UNIT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_LEG_INFRARED_HEAT_PASSENGER_SEAT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_EXTERNAL_REARVIEW_MIRROR_MEMORY" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_ENGINE_TYPE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_EXTEND_RANGE_FUEL_TANK" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_MODEL_CODE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_AVP" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_NOA" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_POWERTRAIN_SYS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_DRIVE_TYPE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_BRAKE_MODEL" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_CALIPER" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_TIRE_SIZE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "car_cfg", "CAR_CFG_CURB_WEIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "usr_cfg", "USR_LANGUAGE_SETTING" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "usr_cfg", "USR_DAYNIGHT_MODE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_BYTES, "0", "psis", "usr_cfg", "USR_EXTERNAL_APP_ACTIVE_CODE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "usr_cfg", "USR_BATTERY_LEVEL_UNIT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "usr_cfg", "USR_TIME_FORMAT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "1", "psis", "usr_cfg", "USR_HUD_BSD_SWITCH" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "1", "psis", "usr_cfg", "USR_HUD_SWITCH" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "5", "psis", "usr_cfg", "USR_HUD_HEIADJ" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "5", "psis", "usr_cfg", "USR_HUD_ILLADJ" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "1", "psis", "usr_cfg", "USR_HUD_MODSWT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "1", "psis", "usr_cfg", "USR_HUD_SNOWMODESWTSTS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "psis", "usr_cfg", "USR_HUD_CRTLANGUAGE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "2", "psis", "usr_cfg", "USR_HMI_PAGEINDEX" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_DAYNIGHT_MODE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_LANGUAGE_SETTING" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_STARTUP_STS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_DISPLAY_STS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_FACTORY_RESET" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_FULL_SCREEN" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_BATTERY_LEVEL_UNIT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_TIME_FORMAT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "1", "ivi", "rx", "I2C_WARN_VOLUME_LEVEL" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_ESE_SWITCH" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_ESE_VOLUME_LEVEL" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_ESE_MODE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_AVAS_STATUS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "1", "ivi", "rx", "I2C_HUD_BSD_SWITCH" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_STRING, """", "ivi", "rx", "I2C_NAVI_GUIDE_INFO" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_STRING, """", "ivi", "rx", "I2C_CALL_CONTACT_INFO" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_STRING, """", "ivi", "rx", "I2C_NAVI_LANE_INFO" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_STRING, """", "ivi", "rx", "I2C_TMC_SEGMENT_INFO" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_CLUSTER_SOUND_TEST" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_SMART_KEY_SWITCH" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_STRING, """", "ivi", "rx", "I2C_SOFTWARE_VERSION" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_HUD_HOMETHEATRE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_NTC_TEMP_HIGH_LIGHT_DOWN" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_NTC_TEMP_HIGH_OFF_SCREEN" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_NAVI_STATUS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_CALL_STATUS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_LOG_EXPORT_STATUS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_TRUNK_DISABLE_CLICKED" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_ARRAY, "[ , , ]", "ivi", "tx", "C2I_WARN_HISTORY_LIST_STR" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_STRING, """", "ivi", "tx", "C2I_SOC_VERSION" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "tx", "C2I_DAYNIGHT_MODE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "tx", "C2I_LANGUAGE_SETTING" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "tx", "C2I_CHARGE_CENTER_REQUEST" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "tx", "C2I_MIRRCMD_DIS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "tx", "C2I_BATTERY_LEVEL_UNIT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "tx", "C2I_TIME_FORMAT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "1", "ivi", "tx", "C2I_HUD_BSD_SWITCH" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "2", "ivi", "tx", "C2I_HMI_MENU_PAGE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "tx", "C2I_HUD_HOMETHEATRE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "tx", "C2I_CHIME_ALARM_STATUS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_ARRAY, "[ , , ]", "ivi", "tx", "C2I_TT_STATUS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "mcu", "SPEED_VALUE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "mcu", "POWER_VALUE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "mcu", "POWER_PERCENT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "mcu", "GEAR_TYPE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "mcu", "FUEL_PERCENT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "mcu", "EVDTE_VALUE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "mcu", "EVDTE_DISP_TYPE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "mcu", "DRV_PWRLIM_PERC" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "mcu", "EGYFB_PWRLIM_PERC" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "mcu", "DTE_VALUE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "mcu", "DTE_UNIT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "mcu", "THISDRIVERTIME_MINUTE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "mcu", "THISTRIPVALUE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "mcu", "GPIOHW_KL15_STATE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "mcu", "TOTDTE_VALUE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "mcu", "MTDISTANCE_VALUE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "mcu", "ODOVAL" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "mcu", "ODOVAL_ML" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "mcu", "FUELLOW_WARNING_STATE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_FLOAT, "0", "custom", "mcu", "TCTRIP1_AEC" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_FLOAT, "0", "custom", "mcu", "TCTRIP1_AFC" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "DRVMODSIG" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "DRVMODSHIFTMISOPER" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "DRVSTYLE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "LNCHCTRLMODDIRMN" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "LNCHCTRLTRIGRMN" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "ACLRTIREQ" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "CHRGSTSDISP" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "VEHEXTDCHASTS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "RESVCHRGSTSDISP" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "CHRGDISCHRGCRTDISP" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_FLOAT, "0", "custom", "drivinginfo", "CHRGDCHAPWRDISP" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "RMNGCHRGTIDISPLY" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "PWRCOLOR" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "DOORLOCK_BTNSTS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "DOORLOCK_BTNGRAY" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "CHRGPORTDOOR_BTNSTS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "CHRGPORTDOOR_BTNGRAY" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TRUNK_BTNSTS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TRUNK_BTNGRAY" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "MIRRCMD_BTNSTS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "MIRRCMD_BTNGRAY" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "FUELDOOR_BTNSTS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "FUELDOOR_BTNGRAY" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TBOX_RESVACCHRGSTRTTI_HR" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TBOX_RESVACCHRGSTRTTI_MINS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TBOX_RESVACCHRGENDTI_HR" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TBOX_RESVACCHRGENDTI_MINS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TBOX_MONRESVACCHRGREPSTRT_SET" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "OUTDT_VALUE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "CHARG_EFFICIENCY_FLAG" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "CHARG_EFFICIENCY" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TIRERIFRNTP_WARN" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TIRELEFRNT_WARN" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TIRELEREP_WARN" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TIRERIREP_WARN" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "LERETIRE_T" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "LEFRNTTIRE_T" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "RIFRNTTIRE_T" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "RIRETIRE_T" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_FLOAT, "0", "custom", "drivinginfo", "LEFRNT_P" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_FLOAT, "0", "custom", "drivinginfo", "RIFRNT_P" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_FLOAT, "0", "custom", "drivinginfo", "RIRE_P" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_FLOAT, "0", "custom", "drivinginfo", "LERE_P" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TIMER_HOUR" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "drivinginfo", "TIMER_MINUTE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "POSITION_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "REAR_FOG_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "LOW_BEAM_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "HIGH_BEAM_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "VCU_AUTHENTICATION_FAILED_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "PDCU_ENERGY_STOP_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "CHARGE_DISCHARGE_CONNECT_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "LIMIT_DRIVE_POWER_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "COMMON_BREAK_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "SINGLE_PEDAL_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "DOOR_OPEN_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "LEFT_TURN_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "RIGHT_TURN_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "SYSBRAKE_FAIL_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "DANGER_WARN_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "AIRBAG_WARN_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "AIRBAG_WARN_BLINK" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "ABS_FAIL_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "SYS_CHARGE_FAIL_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "EPB_BRAKE_MODE_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "EPB_BRAKE_MODE_BLINK" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "EPB_FAIL_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "ESP_MODE_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "ESP_MODE_BLINK" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "ESP_CLOSE_LIGH" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "NOT_SAFETY_BELT_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "CHARGE_REMIND_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "TPMS_WARN_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "TPMS_WARN_BLINK_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "SYSTEM_WARN_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "READY_MODE_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "HIGH_POWER_BREAK_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "EBD_BREAK_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "AUTO_HOLD_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "HDC_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "HDC_BLINK" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "EPS_BREAK_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "VCU_PULL_MODE_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "ENGINE_FAIL_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "MOTOR_HOT_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "MOTOR_SYSTEM_HOT_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "MOTOR_BRAKE_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "REFUE_NOTICE_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "OIL_PRESSURE_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "EV_MODE_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "WATER_TEMP_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "CHARGE_NOTICE_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "ENERGY_MODE_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "ADAS_BREAK_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "ADAS_DEMARCATE_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "ADAS_SYS_TAKE_OVER_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "FCW_AEB_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "ACC_CRUISE_VAL_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "ACC_CRUISE_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "LCC_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "NOA_STATUS_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "LDW_RDP_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "BSC_LCA_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "ASL_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "SMART_HIGH_BEAM_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "SLA_SPEED_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "TSR_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "TLA_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "TLA_COLOR_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "TLA_COLOR_COUNTDOWN_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "TT_SELFCHECK_STS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "TT_BRKLAMP_STS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "TT_RVSLAMP_STS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "E_TT_TLA_COLOR_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "telltale", "E_TT_TLA_COLOR_COUNTDOWN_LIGHT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "warning", "WARNING_DISPLAY_POP" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "warning", "HOOD_DOOR_STS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "warning", "DRIVER_DOOR_STS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "warning", "PSNGER_DOOR_STS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "warning", "RHR_DOOR_STS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "warning", "LHR_DOOR_STS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "warning", "TRUNK_DOOR_STS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "warning", "ENERGY_MODE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "warning", "WARNING_SHOW_STS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "warning", "SECUBLT_DRIVER" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "warning", "SECUBLT_PSNGER" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "warning", "SECUBLT_RL" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "warning", "SECUBLT_RM" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "warning", "SECUBLT_RR" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "warning", "BACKLIGHT_REQ" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "warning", "ADAS_NOAU_ODD_DIST_INFO" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "warning", "WARNING_DISPLAY_POP_M" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "warning", "WARNING_DISPLAY_POP_R" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "warning", "MAINTENANCE_MILEAGE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "power", "CLUSTER_ANIMATION_STATUS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "power", "IVI_ANIMATION_STATUS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "power", "POWER_MODE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "power", "READY" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_STRING, "“”", "custom", "power", "STR_REGISTER" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_STRING, "“”", "custom", "power", "STR_SUSPEND_PREPARE_ACK" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "custom", "power", "STR_SUSPEND_STATUS" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_STRING, """", "custom", "power", "STR_SUSPEND_RESUME_ACK" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "power", "POWER_STATE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "power", "WHUD_HMI_READY" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "hmi", "HMI_RENDER_STATE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "custom", "hmi", "STANDBY_BUTTON" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "custom", "hmi", "TOUCH_BAREA_BUTTON" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "2", "custom", "hmi", "HMI_MENU_PAGE" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "hmi", "HMI_MENU_COUNT" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "hmi", "HMI_MENU_INDEX" },
    { CAR_PROPERTY_RW, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "custom", "hmi", "HMI_MENU_BAR_STATE" },
};

} // namespace CM
} // namespace Frameworks
} // namespace AutoLink

#endif