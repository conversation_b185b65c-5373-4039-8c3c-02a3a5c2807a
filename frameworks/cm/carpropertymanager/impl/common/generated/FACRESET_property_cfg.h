/*    Copyright(C) 2024 Autolink Information Technology Co.Ltd. All rights reserved.   */
/*                                                                                     */
/*                         The file is automatically generated.                        */
/*                      !!!!!! Manual changes are prohibited !!!!!                     */
/*                                                                                     */
/* latest change:                                                                      */
/*
      2025/08/05

      Vehicle_n80.rx 增加初版TC、ETC信号列表
*/

#ifndef APPFW_CM_CAR_PROPERTY_CFG_H
#define APPFW_CM_CAR_PROPERTY_CFG_H

#include "autolink/frameworks/cm/carpropertymanager/impl/common/car_property_def.h"

namespace AutoLink {
namespace Frameworks {
namespace CM {

const char* CAR_PROPERTY_CFG_GEN_TIME = "GEN_2025_08_06_103020";

const bool ProcessHost = 0;

const uint32_t ConfigNum = 28;

const CarPropertyConfig gCarProperyConfigArray[ConfigNum] = {
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_DAYNIGHT_MODE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_LANGUAGE_SETTING" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_STARTUP_STS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_DISPLAY_STS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_FACTORY_RESET" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_FULL_SCREEN" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_BATTERY_LEVEL_UNIT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_TIME_FORMAT" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "1", "ivi", "rx", "I2C_WARN_VOLUME_LEVEL" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_ESE_SWITCH" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_ESE_VOLUME_LEVEL" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_ESE_MODE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_AVAS_STATUS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "1", "ivi", "rx", "I2C_HUD_BSD_SWITCH" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_STRING, """", "ivi", "rx", "I2C_NAVI_GUIDE_INFO" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_STRING, """", "ivi", "rx", "I2C_CALL_CONTACT_INFO" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_STRING, """", "ivi", "rx", "I2C_NAVI_LANE_INFO" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_STRING, """", "ivi", "rx", "I2C_TMC_SEGMENT_INFO" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_CLUSTER_SOUND_TEST" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_SMART_KEY_SWITCH" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_STRING, """", "ivi", "rx", "I2C_SOFTWARE_VERSION" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_HUD_HOMETHEATRE" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_NTC_TEMP_HIGH_LIGHT_DOWN" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_NTC_TEMP_HIGH_OFF_SCREEN" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_NAVI_STATUS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_CALL_STATUS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_ONCHANGED, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_LOG_EXPORT_STATUS" },
    { CAR_PROPERTY_R, CAR_PROPERTY_NOTIFY_EVENT, 0, CAR_PROPERTY_INT32, "0", "ivi", "rx", "I2C_TRUNK_DISABLE_CLICKED" },
};

} // namespace CM
} // namespace Frameworks
} // namespace AutoLink

#endif