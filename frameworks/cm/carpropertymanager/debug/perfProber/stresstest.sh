#!/bin/sh

# Initialize variables
process_count=1
module_count=1

# Define the function to show script usage
show_usage() {
    echo "Usage: $0 [-p <number of processes>] [-m <number of modules>] [-h]"
    echo "  -p <number of processes>  Specify the number of perfProber processes to start, default is 1."
    echo "  -m <number of modules>    Specify the number of modules in perfProber to start, default is 1."
    echo "  -h                        Display this help message."
}

# Parse command-line arguments
while getopts ":p:m:h" opt; do
  case $opt in
    p)
      process_count="$OPTARG"
      ;;
    m)
      module_count="$OPTARG"
      ;;
    h)
      show_usage
      exit 0
      ;;
    \?)
      echo "Invalid option: -$OPTARG" >&2
      show_usage
      exit 1
      ;;
    :)
      echo "Option -$OPTARG requires an argument." >&2
      show_usage
      exit 1
      ;;
  esac
done

# Start perfProber processes
i=1
while [ $i -le $process_count ]
do
    if [ -n "$module_count" ]; then
        ./bin/perfProber -p 0 -m $module_count &
    else
        ./bin/perfProber -p 0 &
    fi
    i=$(expr $i + 1)
done

# Wait for all background processes to complete
#wait