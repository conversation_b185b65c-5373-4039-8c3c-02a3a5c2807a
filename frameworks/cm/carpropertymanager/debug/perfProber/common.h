#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include <mutex>
#include <sstream>

uint32_t moduleNum = 1;
uint32_t bRecord = 0;
std::string propFullNameToPrint;
uint32_t pubTimes = 0;
uint32_t pubValue = 0;

// 记录延迟
void record_latency(const struct timespec &start, const struct timespec &end)
{
    long long latency = (end.tv_sec - start.tv_sec) * 1000000000LL + (end.tv_nsec - start.tv_nsec);
    std::cout << "Latency: " << latency << " nanoseconds" << std::endl;
}

// 使用说明
void printUsage()
{
    std::stringstream ss;
    ss << "\n====================================================================================\n"
       << "                  CPM 性能压测仿真工具  v1.1\n"
       << "  A tool for performance/stress testing of the CarPropertyManager \n"
       << "  module.  Feel free to contact HUALEI for any usage issues or feedback. \n"
       << "          Copyright Autolink.  All Rights Reserved.          \n"
       << "===================================================================================\n"
       << "Usage: perfProber [options]\n"
       << "Options:\n"
       << "  -h, --help                             显示此帮助信息并退出。\n"
       << "  -m, --module N                         设置压力测试的module数量。默认值为 " << moduleNum << ".\n"
       << "  -r, --recordTime N                     设置1表示打印时延。默认值为 " << bRecord << ".\n"
       << "  -p, --property <propFullName>          打印指定属性的值。默认打印全部属性;若设0,则不打印.\n"
       << "  -t, --times N                          设置发布属性值的次数。默认值为 " << pubTimes << ".\n"
       << "  -v, --value N                          设置发布的属性值。默认值为 " << pubValue << ".\n"
       << "Example:\n"
       << "  ./perfProber -m 4                        进程中创建4个模块,全量订阅所有属性\n"
       << "  ./perfProber -p vehicle.rx.PDU_TPMS_589  仅打印 PDU_TPMS_589 的值\n"
       << "  ./perfProber -t 5 -v 100                 下发控制属性值5次,属性值为100\n"
       << "  ./perfProber -m 3 -t 20                  创建3个模块,下发控制属性值20次,属性值均为默认值.\n"
       << "====================================================================================\n";
    std::cout << ss.str() << std::endl;
}

// 解析命令行参数
bool parseCommandLineArgs(int argc, char *argv[])
{
    for (int i = 1; i < argc; ++i)
    {
        if (std::string(argv[i]) == "-h" || std::string(argv[i]) == "--help")
        {
            printUsage();
            return false;
        }
        else if (std::string(argv[i]) == "-m" || std::string(argv[i]) == "--module")
        {
            if (i + 1 < argc && std::string(argv[i + 1]).find_first_not_of("0123456789") == std::string::npos)
            {
                moduleNum = std::stoi(argv[++i]);
            }
            else
            {
                std::cerr << "Error: Invalid argument for " << argv[i - 1] << std::endl;
                return false;
            }
        }
        else if (std::string(argv[i]) == "-r" || std::string(argv[i]) == "--bRecord")
        {
            if (i + 1 < argc && std::string(argv[i + 1]).find_first_not_of("0123456789") == std::string::npos)
            {
                bRecord = std::stoi(argv[++i]);
            }
            else
            {
                std::cerr << "Error: Invalid argument for " << argv[i - 1] << std::endl;
                return false;
            }
        }
        else if (std::string(argv[i]) == "-p" || std::string(argv[i]) == "--property")
        {
            if (i + 1 < argc)
            {
                propFullNameToPrint = argv[++i];
            }
            else
            {
                std::cerr << "Error: Invalid argument for " << argv[i - 1] << std::endl;
                return false;
            }
        }
        else if (std::string(argv[i]) == "-t" || std::string(argv[i]) == "--times")
        {
            if (i + 1 < argc && std::string(argv[i + 1]).find_first_not_of("0123456789") == std::string::npos)
            {
                pubTimes = std::stoi(argv[++i]);
            }
            else
            {
                std::cerr << "Error: Invalid argument for " << argv[i - 1] << std::endl;
                return false;
            }
        }
        else if (std::string(argv[i]) == "-v" || std::string(argv[i]) == "--value")
        {
            if (i + 1 < argc)
            {
                pubValue = std::stoi(argv[++i]);
            }
            else
            {
                std::cerr << "Error: Invalid argument for " << argv[i - 1] << std::endl;
                return false;
            }
        }
        else
        {
            std::cerr << "Error: Unknown option " << argv[i] << std::endl;
            return false;
        }
    }
    return true;
}