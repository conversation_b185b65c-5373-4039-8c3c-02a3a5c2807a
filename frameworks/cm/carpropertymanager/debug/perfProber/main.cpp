#include "autolink/frameworks/core/module.h"
#include "autolink/frameworks/core/application.h"
#include "autolink/frameworks/cm/carpropertymanager/car_property_manager.h"
#include "autolink/frameworks/cm/carpropertymanager/impl/common/generated/CEM_property_cfg.h"
#include "autolink/frameworks/cm/carpropertymanager/debug/perfProber/common.h"

#include <string>
#include <pthread.h>
#include <sstream>
#include <iostream>
#include <time.h>
#include <unistd.h>
#include <vector>
#include <mutex>
#include <functional>

using namespace AutoLink::Frameworks;

std::mutex mtx;

class Subscriber : public AutoLink::Frameworks::Core::Module
{
public:
    Subscriber();
    void OnInit() override;

private:
    static void OnCbProp(const std::string &name, const CM::CarPropertyValue &propValue);
};

// 回调函数
void Subscriber::OnCbProp(const std::string &name, const CM::CarPropertyValue &propValue)
{
    if (propFullNameToPrint == "0")
    {
        return;
    }
    if (propFullNameToPrint.empty() || name == propFullNameToPrint)
    {
        std::lock_guard<std::mutex> lck(mtx);
        std::cout << "propName: " << name.c_str()
                  << "    -isvalid: " << propValue.IsValid();
        switch (propValue.GetCarPropertyValueType())
        {
        case CM::ValueType::CAR_PROPERTY_INT32:
            std::cout << "    -value: " << propValue.ToInt() << std::endl;
            break;
        case CM::ValueType::CAR_PROPERTY_UINT32:
            std::cout << "    -value: " << propValue.ToUInt() << std::endl;
            break;
        case CM::ValueType::CAR_PROPERTY_INT64:
            std::cout << "    -value: " << propValue.ToFloat() << std::endl;
            break;
        case CM::ValueType::CAR_PROPERTY_UINT64:
            std::cout << "    -value: " << propValue.ToFloat() << std::endl;
            break;
        case CM::ValueType::CAR_PROPERTY_FLOAT:
            std::cout << "    -value: " << propValue.ToFloat() << std::endl;
            break;
        case CM::ValueType::CAR_PROPERTY_STRING:
            std::cout << "    -value: " << propValue.ToString() << std::endl;
            break;
        case CM::ValueType::CAR_PROPERTY_BYTES:
        {
            auto bytes = propValue.ToBytes();
            std::cout << "    -value: ";
            for (auto byte : bytes)
            {
                std::cout << std::hex << static_cast<int>(byte);
            }
            std::cout << std::dec << std::endl;
            break;
        }
        case CM::ValueType::CAR_PROPERTY_ARRAY:
        {
            auto vec = propValue.ToValueVector();
            std::cout << "    -value: [";
            for (size_t i = 0; i < vec.size(); ++i)
            {
                if (i > 0)
                {
                    std::cout << ", ";
                }
                std::cout << vec[i].ToInt();
            }
            std::cout << "]" << std::endl;
            break;
        }
        case CM::ValueType::CAR_PROPERTY_MAP:
        {
            auto map = propValue.ToValueMap();
            std::cout << "    -value: {";
            size_t index = 0;
            for (const auto &pair : map)
            {
                if (index > 0)
                {
                    std::cout << ", ";
                }
                std::cout << pair.first << ": " << pair.second.ToInt();
                ++index;
            }
            std::cout << "}" << std::endl;
            break;
        }
        default:
            break;
        }
    }
}

Subscriber::Subscriber() // : moduleNum(1), iterations(STRESS_TEST_ITERATIONS)
{
    // global_describer = this;
}

void Subscriber::OnInit()
{
    struct timespec start, end;
    clock_gettime(CLOCK_MONOTONIC, &start);

    std::vector<std::string> propFullNameVec;

    for (int j = 0; j < CM::ConfigNum; ++j)
    {
        const auto &propertyConfig = CM::gCarProperyConfigArray[j];
        char propFullName[128];
        snprintf(propFullName, sizeof(propFullName), "%s.%s.%s",
                 propertyConfig.descriDomain,
                 propertyConfig.descriGroup,
                 propertyConfig.descriName);

        if (propertyConfig.descriGroup == "tx")
        {
            if (propertyConfig.descriDomain == "Vehicle")
            {
                propFullNameVec.emplace_back(propFullName);
            }
            continue;
        }

        CM::CarPropertyManager::Get().SubscribeProperty(propFullName,
                                                        [propFullName](const CM::CarPropertyValue &value)
                                                        { OnCbProp(propFullName, value); });
    }

    // TRACE_EVENT(_NTO_TRACE_CPULOAD, "CriticalEnd", 0);

    clock_gettime(CLOCK_MONOTONIC, &end);
    if (bRecord)
    {
        record_latency(start, end);
    }

    for (int t = 0; t < pubTimes; ++t)
    {
        for (auto name : propFullNameVec)
        {
            CM::CarPropertyManager::Get().SetPropertyValue(name.c_str(),
                                                           CM::CarPropertyValue(pubValue));
        }
    }

    // std::cout << "CPU % : " << get_cpu_usage() << std::endl;
}

class ProberApplication : public Core::Application
{
public:
    ProberApplication(int argc, char **argv) : Core::Application("perfProber", argc, argv) {}

    virtual ~ProberApplication() {}

protected:
    virtual void OnInit()
    {
    }

    virtual void OnRegisterModule()
    {
        std::string moduleName = "Subscriber";
        for (int i = 0; i < moduleNum; ++i)
        {
            std::string name = moduleName + std::to_string(i);
            RegisterModule<Subscriber>(name.c_str());
        }
    }
};

int main(int argc, char *argv[])
{
    if (!parseCommandLineArgs(argc, argv))
    {
        return 0;
    }

    CARPROPERTYMANAGER_INIT(CM::gCarProperyConfigArray, CM::ConfigNum, CM::ProcessHost);
    ProberApplication app(argc, argv);
    return app.Run();
}