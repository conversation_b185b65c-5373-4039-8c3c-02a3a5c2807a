﻿#include "autolink/frameworks/cm/carpropertymanager/debug/carpropmgrdbgr/commander/commander.h"

#include "autolink/frameworks/log/log.h"

#include <thread>
#include <signal.h>
#include <unistd.h>

using namespace AutoLink::Frameworks::Log;
using namespace AutoLink::Frameworks::CM;

#define LOG_TAG "main"

int main(int argc, char *argv[])
{
    DebugCommander cmdr(argc, argv);

    sigset_t mask;
    sigemptyset(&mask);
    sigaddset(&mask, SIGABRT);
    sigaddset(&mask, SIGTERM);
    sigaddset(&mask, SIGSEGV);
    sigaddset(&mask, SIGINT);
    if (sigprocmask(SIG_BLOCK, &mask, NULL) == -1)
    {
        LOG_ERROR("sigprocmask failed: %s", strerror(errno));
        return -1;
    }

    std::thread thrdSignal = std::thread(
        [&]()
        {
            int sigNum;
            sigwait(&mask, &sigNum);
            LOG_ERROR("Application abnormally exit by signal %d", sigNum);

            cmdr.Exit();

            sigprocmask(SIG_UNBLOCK, &mask, NULL);
            kill(getpid(), sigNum); });
    thrdSignal.detach();

    cmdr.Run();

    return 0;
}
