#include "commander.h"

#include "autolink/frameworks/cm/carpropertymanager/debug/carpropmgrdbgr/playback/car_property_playback.h"
#include "autolink/frameworks/cm/carpropertymanager/debug/carpropmgrdbgr/recorder/car_property_rec.h"

#include <stdint.h>
#include <getopt.h>

#include <iostream>

void printusage()
{
    std::cout << "\n=====================================================================\n";
    std::cout << "               Car Property Manager Debug Tool v1.0              \n\n";
    std::cout << " This tool is used for debugging car property management functions.\n";
    std::cout << " It exclusively interacts with PPS and does not support interaction\n";
    std::cout << " with PSIS.\n";
    std::cout << "=====================================================================\n\n";
    std::cout << "Usage: [options]\n"
              << "Options:\n"
              << "  -s, --SetProperty         Set a property.\n"
              << "  -g, --GetProperty         Get a property.\n"
              << "  -n, --PropertyName <name> Specify the property name.\n"
              << "  -v, --PropertyValue <val> Specify the property value.\n"
              << "  -r, --Record              Start recording properties.\n"
              << "  -p, --Playback <file>     Playback properties from a file.\n"
              << "  -h, --Help                Show this help message.\n\n"
              << "Examples:\n"
              << "  Set a property:          ./carpropmgrdbgr -s -n vehicle.rx.ADAS_AUDIOWARN_32C -v 3\n"
              << "  Get a property:          ./carpropmgrdbgr -g -n vehicle.rx.ADAS_AUDIOWARN_32C\n"
              << "  Start recording:         ./carpropmgrdbgr -r\n"
              << "  Playback from a file:    ./carpropmgrdbgr -p exampleFile\n"
              << "  Show help:               ./carpropmgrdbgr -h\n\n";
}
namespace AutoLink
{
    namespace Frameworks
    {
        namespace CM
        {
            typedef enum DebugOperation : uint8_t
            {
                OP_SET = 0x1,
                OP_GET = 0x2,
                OP_TOP = 0x4,
                OP_REC = 0x8,
                OP_PB = 0x10,
            } DebugOperation_;

            struct DebugCommanderPrivate
            {
                CarPropertyPlayback pb;
                CarPropertyRec rec;

                int argc;
                char **argv;

                std::string propName;
                std::string propValue;

                std::string pbFile;

                uint32_t topNum{0};

                uint32_t op{0};

                void ParseArgs()
                {
                    const char *optstr = "sgn:v:rp:h";
                    struct option lopts[] = {
                        {"SetProperty", no_argument, NULL, 's'},
                        {"GetProperty", no_argument, NULL, 'g'},
                        {"PropertyName", required_argument, NULL, 'n'},
                        {"PropertyValue", required_argument, NULL, 'v'},
                        // {"Top", required_argument, NULL, 't'},
                        {"Record", no_argument, NULL, 'r'},
                        {"Playback", required_argument, NULL, 'p'},
                        // 添加长选项 "Help"
                        {"Help", no_argument, NULL, 'h'},
                        {NULL, 0, NULL, 0},
                    };

                    uint32_t command;
                    while ((command = getopt_long(argc, argv, optstr, lopts, NULL)) != -1)
                    {
                        switch (command)
                        {
                        case 'n':
                            propName = optarg;
                            break;
                        case 'v':
                            propValue = optarg;
                            break;
                        case 's':
                            op |= OP_SET;
                            break;
                        case 'g':
                            op |= OP_GET;
                            break;
                        case 't':
                            op |= OP_TOP;
                            topNum = atoi(optarg);
                            break;
                        case 'r':
                            op |= OP_REC;
                            break;
                        case 'p':
                            op |= OP_PB;
                            pbFile = optarg;
                            break;
                        case 'h':
                            printusage();
                            break;
                        default:
                            break;
                        }
                    }
                }

                void CommandProcess()
                {
                    if (OP_SET == (op & OP_SET))
                    {
                        pb.SetProperty(propName, propValue);
                    }
                    else if (OP_GET == (op & OP_GET))
                    {
                        std::cout << "Command accepted: GET " << propName.c_str() << std::endl;
                        pb.DumpProperty(propName);
                    }
                    else if (OP_REC == (op & OP_REC))
                    {
                        rec.StartRec();
                    }
                    // else if (OP_TOP == (op & OP_TOP))
                    // {
                    //     rec.TopCounter(topNum);
                    // }
                    else if (OP_PB == (op & OP_PB))
                    {
                        pb.PlaybackProperty(pbFile.c_str());
                    }
                    else
                    {
                    }
                }
            };

            DebugCommander::DebugCommander(int argc, char **argv)
                : p_(new DebugCommanderPrivate())
            {
                p_->argc = argc;
                p_->argv = argv;
            }
            DebugCommander::~DebugCommander()
            {
                delete p_;
            }

            void DebugCommander::Run()
            {
                // init recorder
                p_->rec.Init();

                // init playback

                p_->ParseArgs();

                p_->CommandProcess();
            }

            void DebugCommander::Exit()
            {
                // Exit playback
                p_->pb.Exit();

                // Exit recorder
                p_->rec.Exit();
            }

        } // namespace CM
    } // namespace Frameworks
} // namespace AutoLink
