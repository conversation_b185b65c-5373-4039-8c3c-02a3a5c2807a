#ifndef APPFW_CM_CPM_DEBUGER_COMMANDER_H
#define APPFW_CM_CPM_DEBUGER_COMMANDER_H

namespace AutoLink
{
    namespace Frameworks
    {
        namespace CM
        {
            class DebugCommander final
            {
            public:
                DebugCommander(int argc, char **argv);
                ~DebugCommander();

                void Run();
                void Exit();

            private:
                struct DebugCommanderPrivate *p_;
            };

        } // namespace CM
    } // namespace Frameworks
} // namespace AutoLink

#endif