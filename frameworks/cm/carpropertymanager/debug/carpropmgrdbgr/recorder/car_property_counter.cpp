#include "car_property_counter.h"

#include <vector>

#include <iostream>

namespace AutoLink
{
    namespace Frameworks
    {
        namespace CM
        {
            typedef struct CarPropertyCounterData
            {
                uint32_t counter{0};

                // from 1 on
                uint32_t prev{0};
                uint32_t next{0};
            } CarPropertyCounterData_;

            struct CarPropertyCounterPrivate
            {
                // from 1 on
                uint32_t first{0};
                uint32_t last{0};

                CarPropertyCounterFunction cb;

                // from 0 on
                std::vector<CarPropertyCounterData *> list;
            };

            CarPropertyCounter::CarPropertyCounter(CarPropertyCounterFunction func)
                : p_(new CarPropertyCounterPrivate())
            {
                p_->cb = func;
            }
            CarPropertyCounter::~CarPropertyCounter()
            {
                for (auto &data : p_->list)
                {
                    delete data;
                }

                delete p_;
            }

            void CarPropertyCounter::OnInit(const uint32_t &propIdx)
            {
                p_->list.push_back(new CarPropertyCounterData());
            }
            void CarPropertyCounter::OnIncrease(const uint32_t &propIdx)
            {
                // std::cout << "OnIncrease [" << propIdx << "]:[" << p_->list[propIdx]->counter
                //           << "] F[" << p_->first
                //           << "] L[" << p_->last << "]" << std::endl;

                ++(p_->list[propIdx]->counter);

                // empty
                if (0 == p_->first)
                {
                    p_->first = propIdx + 1;
                    p_->last = propIdx + 1;

                    p_->list[propIdx]->next = 0;
                    p_->list[propIdx]->prev = 0;

                    return;
                }

                // I am the first
                if (propIdx == (p_->first - 1))
                {
                    return;
                }

                uint32_t myPos;
                if (0 == p_->list[propIdx]->prev)
                {
                    // I am not in the queue
                    myPos = p_->last - 1;

                    if (p_->list[propIdx]->counter <= p_->list[myPos]->counter)
                    {
                        p_->list[myPos]->next = propIdx + 1;

                        p_->list[propIdx]->next = 0;
                        p_->list[propIdx]->prev = p_->last;

                        p_->last = propIdx + 1;

                        return;
                    }
                }
                else
                {
                    // I am in the queue
                    myPos = p_->list[propIdx]->prev - 1;

                    if (p_->list[propIdx]->counter <= p_->list[myPos]->counter)
                    {
                        return;
                    }

                    if (p_->last == (propIdx + 1))
                    {
                        p_->last = p_->list[propIdx]->prev;
                    }

                    p_->list[myPos]->next = p_->list[propIdx]->next;
                }

                while (p_->list[propIdx]->counter > p_->list[myPos]->counter)
                {
                    // I am larger than the first
                    if (myPos == (p_->first - 1))
                    {
                        p_->list[myPos]->next = p_->list[propIdx]->next;
                        p_->list[myPos]->prev = propIdx + 1;

                        p_->list[propIdx]->next = p_->first;
                        p_->list[propIdx]->prev = 0;

                        p_->first = propIdx + 1;

                        return;
                    }

                    myPos = p_->list[myPos]->prev - 1;
                }

                p_->list[propIdx]->prev = myPos + 1;
                p_->list[propIdx]->next = p_->list[myPos]->next;

                p_->list[myPos]->next = propIdx + 1;

                if (0 != p_->list[propIdx]->next)
                {
                    p_->list[p_->list[propIdx]->next - 1]->prev = propIdx + 1;
                }
            }
            void CarPropertyCounter::OnTop(const uint32_t &num)
            {
                if (0 == p_->first)
                {
                    return;
                }

                uint32_t dataPos = p_->first;

                for (size_t i = 0; i < num; i++)
                {
                    // no data left in the queue
                    if (0 == dataPos)
                    {
                        break;
                    }

                    // display the counter
                    uint32_t propIdx = dataPos - 1;
                    p_->cb(propIdx, p_->list[propIdx]->counter);

                    dataPos = p_->list[propIdx]->next;
                }
            }

        } // namespace CM
    } // namespace Frameworks
} // namespace AutoLink
