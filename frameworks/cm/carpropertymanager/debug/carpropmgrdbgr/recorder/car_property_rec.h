#ifndef APPFW_CM_CPM_PROPERTY_REC_H
#define APPFW_CM_CPM_PROPERTY_REC_H

#include <stdint.h>

namespace AutoLink
{
    namespace Frameworks
    {
        namespace CM
        {
            class CarPropertyRec final
            {
            public:
                CarPropertyRec();
                ~CarPropertyRec();

                void Init();
                void Exit();

                void StartRec();

                void InitCounter();
                void TopCounter(const uint32_t &num);

            private:
                struct CarPropertyRecPrivate *p_;
            };

        } // namespace CM
    } // namespace Frameworks
} // namespace AutoLink

#endif