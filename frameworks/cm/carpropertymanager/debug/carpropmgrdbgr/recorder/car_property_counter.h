#ifndef APPFW_CM_CPM_PROPERTY_COUNTER_H
#define APPFW_CM_CPM_PROPERTY_COUNTER_H

#include <stdint.h>
#include <functional>

namespace AutoLink
{
    namespace Frameworks
    {
        namespace CM
        {
            typedef std::function<void(const uint32_t &, const uint32_t &)> CarPropertyCounterFunction;

            class CarPropertyCounter final
            {
            public:
                CarPropertyCounter(CarPropertyCounterFunction func);
                ~CarPropertyCounter();

                void OnInit(const uint32_t &propIdx);

                void OnIncrease(const uint32_t &propIdx);

                void OnTop(const uint32_t &num);

            private:
                struct CarPropertyCounterPrivate *p_;
            };

        } // namespace CM
    } // namespace Frameworks
} // namespace AutoLink

#endif