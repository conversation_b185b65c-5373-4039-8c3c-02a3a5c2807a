#include "car_property_rec.h"

#include <stdio.h>
#include <time.h>

#include <fstream>
#include <iostream>
#include <mutex>

#include "autolink/frameworks/cm/carpropertymanager/debug/carpropmgrdbgr/assistant/car_property_assistant.h"
#include "autolink/frameworks/cm/carpropertymanager/impl/common/generated/DBG_property_cfg.h"
#include "autolink/frameworks/core/impl/taskmanager.h"
#include "autolink/frameworks/core/timer.h"
#include "car_property_counter.h"

using namespace AutoLink::Frameworks::Core;

namespace AutoLink {
namespace Frameworks {
namespace CM {
const char *sinkFilePath = "/tmp";

struct CarPropertyRecPrivate {
    bool propBegin{false};

    std::ofstream sink;
    std::mutex lckSink;

    CarPropertyCounter *counter;
    std::shared_ptr<Timer> timer;

    const char *GenPropName(const uint32_t &propIdx) {
        static char propFull[128];
        snprintf(propFull,
                 128,
                 "%s.%s.%s",
                 gCarProperyConfigArray[propIdx].descriDomain,
                 gCarProperyConfigArray[propIdx].descriGroup,
                 gCarProperyConfigArray[propIdx].descriName);

        return propFull;
    }

    void StartRec() {
        if (sink.is_open()) {
            return;
        }

        struct timespec ts;
        clock_gettime(CLOCK_REALTIME, &ts);

        time_t realSec = ts.tv_sec;
        struct tm realTime;
        localtime_r(&realSec, &realTime);

        char sinkFile[48];
        snprintf(sinkFile,
                 48,
                 "%s/CarProperty_%4d%02d%02d_%02d%02d%02d.rec",
                 sinkFilePath,
                 realTime.tm_year + 1900,
                 realTime.tm_mon + 1,
                 realTime.tm_mday,
                 realTime.tm_hour,
                 realTime.tm_min,
                 realTime.tm_sec);

        sink.open(sinkFile, std::ios::out | std::ios::trunc);

        if (sink.is_open()) {
            // start object of header
            sink << "{";

            struct timespec startTime;
            clock_gettime(CLOCK_MONOTONIC, &startTime);
            int32_t startTimestamp = (startTime.tv_sec * 1000000000 + startTime.tv_nsec) / 1000000;

            char sStart[16];
            snprintf(sStart, 16, "%d", startTimestamp);
            sink << "\"start_timestamp\":\"" << sStart << "\",";

            propBegin = true;

            // start array of prop_list
            sink << "\"prop_list\":[";
        }

        if (nullptr != timer) {
            timer->Start();
        }

        TaskManager::Get().Run();
    }
    void StopRec() {
        std::lock_guard<std::mutex> guard(lckSink);

        if (sink.is_open()) {
            // end array of prop_list
            sink << "],";

            struct timespec ts;
            clock_gettime(CLOCK_REALTIME, &ts);

            time_t realSec = ts.tv_sec;
            struct tm realTime;
            localtime_r(&realSec, &realTime);

            char sEnd[32];
            snprintf(sEnd,
                     32,
                     "%4d/%02d/%02d %02d:%02d:%02d",
                     realTime.tm_year + 1900,
                     realTime.tm_mon + 1,
                     realTime.tm_mday,
                     realTime.tm_hour,
                     realTime.tm_min,
                     realTime.tm_sec);

            sink << "\"end_timestamp\":\"" << sEnd << "\"";

            // end object of footer
            sink << "}";
        } else {
            return;
        }

        sink.close();

        if (nullptr != timer) {
            timer->Stop();
        }

        TaskManager::Get().Cancel();
    }

    void OnTopTimer() {
        if (nullptr != counter) {
            counter->OnTop(5);
        }
    }

    void OnPropertyDisplayCounter(const uint32_t &propIdx,
                                  const uint32_t &propCounter) {
        std::cout << GenPropName(propIdx) << " " << propCounter << " times" << std::endl;
    }

    void OnPropertyChanged(const uint32_t &propIdx,
                           const std::string &propValue) {
        // std::cout << "OnPropertyChanged [" << GenPropName(propIdx) << "] " << propValue
        //           << " constructing " << constructing << std::endl;
        counter->OnIncrease(propIdx);

        std::lock_guard<std::mutex> guard(lckSink);

        if (sink.is_open()) {
            if (!propBegin) {
                sink << ",";
            }

            // start object of prop
            sink << "{";

            static char sCurrent[16];

            // current change time
            struct timespec curTime;
            clock_gettime(CLOCK_MONOTONIC, &curTime);

            // unit : ms
            int32_t curTimestamp = (curTime.tv_sec * 1000000000 + curTime.tv_nsec) / 1000000;

            snprintf(sCurrent, 16, "%d", curTimestamp);
            sink << "\"prop_timestamp\":\"" << sCurrent << "\",";

            sink << "\"prop_domain\":\"" << gCarProperyConfigArray[propIdx].descriDomain << "\",";
            sink << "\"prop_group\":\"" << gCarProperyConfigArray[propIdx].descriGroup << "\",";
            sink << "\"prop_name\":\"" << gCarProperyConfigArray[propIdx].descriName << "\",";

            sink << "\"prop_value\":\"" << propValue << "\"";

            // end object of prop
            sink << "}";

            propBegin = false;
        }
    }
};

CarPropertyRec::CarPropertyRec()
    : p_(new CarPropertyRecPrivate()) {
    p_->counter = new CarPropertyCounter(
        std::bind(&CarPropertyRecPrivate::OnPropertyDisplayCounter,
                  p_,
                  std::placeholders::_1,
                  std::placeholders::_2));

    p_->timer = Timer::Create("CarPropertyManagerDbgr",
                              5000,
                              std::bind(&CarPropertyRecPrivate::OnTopTimer, p_));
}
CarPropertyRec::~CarPropertyRec() {
    if (nullptr != p_->counter) {
        delete p_->counter;
    }

    delete p_;
}

void CarPropertyRec::Init() {
    for (size_t i = 0; i < ConfigNum; i++) {
        p_->counter->OnInit(i);
        CarPropertyAssistant::Get().SubscribeProperty(gCarProperyConfigArray[i].descriDomain,
                                                      gCarProperyConfigArray[i].descriGroup,
                                                      gCarProperyConfigArray[i].descriName,
                                                      i,
                                                      gCarProperyConfigArray[i].valType);
    }

    CarPropertyAssistant::Get().RegistChangeNotify(
        std::bind(&CarPropertyRecPrivate::OnPropertyChanged,
                  p_,
                  std::placeholders::_1,
                  std::placeholders::_2));
}

void CarPropertyRec::Exit() {
    p_->StopRec();
}

void CarPropertyRec::StartRec() {
    p_->StartRec();
}

void CarPropertyRec::InitCounter() {
    delete p_->counter;
    p_->counter = new CarPropertyCounter(
        std::bind(&CarPropertyRecPrivate::OnPropertyDisplayCounter,
                  p_,
                  std::placeholders::_1,
                  std::placeholders::_2));

    for (size_t i = 0; i < ConfigNum; i++) {
        p_->counter->OnInit(i);
    }
}
void CarPropertyRec::TopCounter(const uint32_t &num) {
    p_->counter->OnTop(num);
}

}  // namespace CM
}  // namespace Frameworks
}  // namespace AutoLink