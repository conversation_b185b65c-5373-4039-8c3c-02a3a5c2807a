#include "car_property_assistant.h"

#include <map>
#include <unordered_map>

#include "autolink/frameworks/cm/carpropertymanager/impl/data/property_object_cfg.h"
#include "autolink/frameworks/cm/carpropertymanager/car_property_value.h"
#include "autolink/frameworks/core/ioeventnotifier.h"
#include "autolink/frameworks/pa/IPPS/IPPS.h"

using namespace AutoLink::Frameworks::Core;
using namespace AutoLink::Frameworks::PA;

namespace AutoLink {
namespace Frameworks {
namespace CM {
const int32_t ppsEnableTimeout = 100;

struct CarPropertyData {
    uint32_t idx;
    uint8_t valType;
};

struct CarPropertyPPS {
    PPSHandle *hndlPPSWrite{nullptr};
    PPSHandle *hndlPPSSubscrb{nullptr};

    std::shared_ptr<IOEventNotifier> notifier;

    std::unordered_map<std::string, CarPropertyData> props;
};

struct CarPropertyAssistantPrivate {
    AssistantNotifyFunction cb{nullptr};

    std::map<std::string, CarPropertyPPS *> objs;
};

CarPropertyAssistant::CarPropertyAssistant()
    : p_(new CarPropertyAssistantPrivate()) {
}
CarPropertyAssistant::~CarPropertyAssistant() {
    delete p_;
}

CarPropertyAssistant &CarPropertyAssistant::Get() {
    static CarPropertyAssistant asstnt;
    return asstnt;
}

void CarPropertyAssistant::RegistChangeNotify(AssistantNotifyFunction func) {
    p_->cb = func;
}

void CarPropertyAssistant::SubscribeProperty(const std::string &propDomain,
                                             const std::string &propGroup,
                                             const std::string &propName,
                                             uint32_t propIdx,
                                             uint8_t propValType) {
    std::string ppsObjName = propDomain + "_" + propGroup;

    auto iter = p_->objs.find(ppsObjName);
    if (iter != p_->objs.end()) {
        CarPropertyData propData = {propIdx, propValType};
        iter->second->props.insert(std::make_pair(propName, propData));
    } else {
        auto iterPropCfg = gPropObjectCfgMap.find(ppsObjName);
        if (iterPropCfg != gPropObjectCfgMap.end()) {
            CarPropertyPPS *ppsObj = new CarPropertyPPS();

            ppsObj->hndlPPSWrite = PPSCreateHandle(ppsObjName.c_str(),
                                                   HandleType::HANDLE_WRITE,
                                                   nullptr,
                                                   ppsEnableTimeout,
                                                   128);  // write buffer size default 128 bytes

            uint32_t ppsObjSize = iterPropCfg->second.dataCounter * 30;
            ppsObj->hndlPPSSubscrb =
                PPSCreateHandle(
                    ppsObjName.c_str(),
                    HandleType::HANDLE_SUBSCRIBE,
                    [this, ppsObj](const std::string &propName, const std::string &propValue) {
                                    if (nullptr == p_->cb)
                                    {
                                        return;
                                    }

                                    auto iter = ppsObj->props.find(propName);
                                    if (iter != ppsObj->props.end())
                                    {
                                        p_->cb(iter->second.idx, propValue);
                                    } },
                    ppsEnableTimeout,
                    ppsObjSize);

            PPSReadData(ppsObj->hndlPPSSubscrb);

            if (nullptr != ppsObj->hndlPPSSubscrb) {
                ppsObj->notifier = IOEventNotifier::Create(ppsObjName.c_str(),
                                                           ppsObj->hndlPPSSubscrb->fd,
                                                           IOEventNotifier::IO_EVENT_INPUT,
                                                           [this, ppsObj](IOEventNotifier::IOEvent event) {
                                                               PPSReadData(ppsObj->hndlPPSSubscrb);
                                                           });
            }

            CarPropertyData propData = {propIdx, propValType};
            ppsObj->props.insert(std::make_pair(propName, propData));

            p_->objs.insert(std::make_pair(ppsObjName, ppsObj));
        }
    }
}
void CarPropertyAssistant::UnSubscribeProperty(const std::string &propDomain,
                                               const std::string &propGroup,
                                               const std::string &propName) {
    std::string ppsObjName = propDomain + "_" + propGroup;

    auto iterObj = p_->objs.find(ppsObjName);
    if (iterObj != p_->objs.end()) {
        auto iterProp = iterObj->second->props.find(propName);
        if (iterProp != iterObj->second->props.end()) {
            iterObj->second->props.erase(iterProp);
        }
    }
}

void CarPropertyAssistant::SetPropertyValue(const std::string &propDomain,
                                            const std::string &propGroup,
                                            const std::string &propName,
                                            const std::string &propValue) {
    const char *SINGLE_VALUE_MASK = "%d[%s";
    const char *MULTI_VALUE_MASK = "%d[%s]";

    static char val[100];

    std::string ppsObjName = propDomain + "_" + propGroup;

    auto iterObj = p_->objs.find(ppsObjName);
    if (iterObj != p_->objs.end())
    {
        memset(val, 0, sizeof(val));

        auto iterProp = iterObj->second->props.find(propName);
        if (iterProp != iterObj->second->props.end()) {
            if ((CAR_PROPERTY_INT32 <= iterProp->second.valType) &&
                (CAR_PROPERTY_STRING >= iterProp->second.valType)) {
                snprintf(val, sizeof(val), SINGLE_VALUE_MASK, iterProp->second.valType, propValue.c_str());
            } else if ((CAR_PROPERTY_BYTES <= iterProp->second.valType) &&
                       (CAR_PROPERTY_MAP >= iterProp->second.valType)) {
                snprintf(val, sizeof(val), MULTI_VALUE_MASK, iterProp->second.valType, propValue.c_str());
            } else {
                snprintf(val, sizeof(val), "%s", propValue.c_str());
            }

            if (PPSWriteData(iterObj->second->hndlPPSWrite,
                             AttributeSyntax::ATTRIBUTE_SYNTAX_NULL_DECODING,
                             propName,
                             val))
            {
                std::cout << "Command accepted: SET "
                          << propDomain.c_str() << "."
                          << propGroup.c_str() << "."
                          << propName.c_str() << " = "
                          << propValue.c_str() << std::endl;
            }
        }
    }
}

void CarPropertyAssistant::GetPropertyValue(const std::string &propDomain,
                                            const std::string &propGroup,
                                            const std::string &propName,
                                            AssistantGetFunction func) {
    std::string ppsObjName = propDomain + "_" + propGroup;
    std::cout << "GetPropertyValue, Domain_Group [" << ppsObjName.c_str() << "]." << std::endl;

    auto iterObj = p_->objs.find(ppsObjName);
    if (iterObj != p_->objs.end()) {
        auto iterProp = gPropObjectCfgMap.find(ppsObjName);
        if (iterProp != gPropObjectCfgMap.end()) {
            uint32_t ppsObjSize = iterProp->second.dataCounter * 30;
            PPSHandle *hndlPPSRead =
                PPSCreateHandle(
                    ppsObjName.c_str(),
                    HandleType::HANDLE_READ,
                    [this, propName, func](const std::string &curName, const std::string &curValue) {
                        if (curName == propName) {
                            func(curValue);
                        }
                    },
                    ppsEnableTimeout,
                    ppsObjSize);

            PPSReadData(hndlPPSRead);

            PPSDestroyHandle(hndlPPSRead);
        }
        else
        {
            std::cerr << "GetPropertyValue, KeyName = [" << propName.c_str() << "] not found." << std::endl;
        }
    }
    else
    {
        std::cerr << "GetPropertyValue, KeyName = [" << ppsObjName.c_str() << "] not found." << std::endl;
    }
}

}  // namespace CM
}  // namespace Frameworks
}  // namespace AutoLink
