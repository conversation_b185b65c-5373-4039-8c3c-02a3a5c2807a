#ifndef APPFW_CM_CPM_PROPERTY_ASSISTANT_H
#define APPFW_CM_CPM_PROPERTY_ASSISTANT_H

#include <stdint.h>
#include <string>
#include <functional>
#include <iostream>

namespace AutoLink
{
    namespace Frameworks
    {
        namespace CM
        {
            typedef std::function<void(const uint32_t &, const std::string &)> AssistantNotifyFunction;
            typedef std::function<void(const std::string &)> AssistantGetFunction;

            class CarPropertyAssistant final
            {
            public:
                static CarPropertyAssistant &Get();

                void RegistChangeNotify(AssistantNotifyFunction func);

                void SubscribeProperty(const std::string &propDomain,
                                       const std::string &propGroup,
                                       const std::string &propName,
                                       uint32_t propIdx,
                                       uint8_t propValType);
                void UnSubscribeProperty(const std::string &propDomain,
                                         const std::string &propGroup,
                                         const std::string &propName);

                void SetPropertyValue(const std::string &propDomain,
                                      const std::string &propGroup,
                                      const std::string &propName,
                                      const std::string &propValue);

                void GetPropertyValue(const std::string &propDomain,
                                      const std::string &propGroup,
                                      const std::string &propName,
                                      AssistantGetFunction func);

            private:
                explicit CarPropertyAssistant(/* args */);
                ~CarPropertyAssistant();

                CarPropertyAssistant(const CarPropertyAssistant &) = delete;
                CarPropertyAssistant(CarPropertyAssistant &&) = delete;
                const CarPropertyAssistant &operator=(const CarPropertyAssistant &) = delete;

                struct CarPropertyAssistantPrivate *p_;
            };

        } // namespace CM
    } // namespace Frameworks
} // namespace AutoLink

#endif