#ifndef APPFW_CM_CPM_PARSERJSON_H
#define APPFW_CM_CPM_PARSERJSON_H

#include "autolink/frameworks/cm/carpropertymanager/debug/carpropmgrdbgr/playback/parser/jsoncpp/json/json.h"

#include <functional>

namespace AutoLink
{
    namespace Frameworks
    {
        namespace CM
        {

            typedef enum ItemStatus : uint8_t
            {
                STATUS_INVALID = 0,
                STATUS_IGNORE,
                STATUS_ACCEPT,
            } ItemStatus_;

            // const char *inItemName, ItemStatus &outStatus
            typedef std::function<void(const char *, ItemStatus &)> ItemStartCB;

            // const char *inItemName, const char *inAttriName, const char *inAttriValue
            typedef std::function<void(const char *, const char *, const char *)> ItemAttriCB;

            // const char *inItemName, const char *inItemContent
            typedef std::function<void(const char *, const char *)> ItemContentCB;

            // const char *inItemName
            typedef std::function<void(const char *)> ItemEndCB;

            typedef struct ParserOut
            {
                ItemStartCB itemStart;
                ItemAttriCB itemAttri;
                ItemContentCB itemContent;
                ItemEndCB itemEnd;
            } ParserOut_;

            class ParserJson final
            {
            public:
                explicit ParserJson();
                ~ParserJson();

                bool OnParseFile(const char *uriFile,
                                 const ParserOut &listener);
                bool OnParseString(const char *sContent,
                                   const ParserOut &listener);

            private:
                void Parse(Json::Value jsonNode, const ParserOut &listener);
            };
        } // namespace CM
    } // namespace Frameworks
} // namespace AutoLink

#endif