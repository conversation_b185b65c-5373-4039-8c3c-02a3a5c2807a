#include "ParserJson.h"

#include "fstream"

namespace AutoLink
{
    namespace Frameworks
    {
        namespace CM
        {

            ParserJson::Parser<PERSON><PERSON>()
            {
            }

            ParserJson::~ParserJson()
            {
            }

            bool ParserJson::OnParseFile(const char *uriFile,
                                         const ParserOut &listener)
            {
                if (nullptr == uriFile)
                {
                    return false;
                }

                std::ifstream fsJsonFile(uriFile);
                if (!fsJsonFile.is_open())
                {
                    return false;
                }

                Json::Value rootNode;
                Json::CharReaderBuilder builder;
                builder["collectComments"] = true;
                JSONCPP_STRING errs;
                if (!parseFromStream(builder, fsJsonFile, &rootNode, &errs))
                {
                    return false;
                }

                fsJsonFile.close();

                if (rootNode.isObject())
                {
                    Parse(rootNode, listener);
                }

                return true;
            }

            bool ParserJson::OnParseString(const char *sContent,
                                           const ParserOut &listener)
            {
                return false;
            }

            void ParserJson::Parse(Json::Value jsonNode, const ParserOut &listener)
            {
                if (jsonNode.empty())
                {
                    return;
                }

                Json::Value::Members nodeMembers = jsonNode.getMemberNames();
                for (auto iterMember = nodeMembers.begin(); iterMember != nodeMembers.end(); iterMember++)
                {
                    Json::Value &curNode = jsonNode[*iterMember];

                    ItemStatus itemStatus = STATUS_INVALID;

                    if (curNode.isObject())
                    {
                        Parse(curNode, listener);
                    }
                    else if (curNode.isArray())
                    {
                        listener.itemStart((*iterMember).c_str(), itemStatus);

                        if (STATUS_ACCEPT != itemStatus)
                        {
                            continue;
                        }

                        uint32_t arrSize = curNode.size();
                        for (uint32_t idx = 0; idx < arrSize; idx++)
                        {
                            Parse(curNode[idx], listener);
                        }

                        listener.itemEnd((*iterMember).c_str());
                    }
                    else if (curNode.isString())
                    {
                        listener.itemStart((*iterMember).c_str(), itemStatus);

                        if (STATUS_ACCEPT != itemStatus)
                        {
                            continue;
                        }

                        listener.itemContent((*iterMember).c_str(), curNode.asString().c_str());

                        listener.itemEnd((*iterMember).c_str());
                    }
                    else
                    {
                    }
                }
            }
        } // namespace CM
    } // namespace Frameworks
} // namespace AutoLink