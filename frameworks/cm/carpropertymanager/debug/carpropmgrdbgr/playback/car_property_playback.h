#ifndef APPFW_CM_CPM_PROPERTY_PLAYBACK_H
#define APPFW_CM_CPM_PROPERTY_PLAYBACK_H

#include <string>

namespace AutoLink
{
    namespace Frameworks
    {
        namespace CM
        {
            class CarPropertyPlayback final
            {
            public:
                CarPropertyPlayback();
                ~CarPropertyPlayback();

                void Init();
                void Exit();

                void PlaybackProperty(const char * sourceFile);

                void SetProperty(const std::string &propFullName,
                                 const std::string &propValue);
                void DumpProperty(const std::string &propFullName);

            private:
                struct CarPropertyPlaybackPrivate *p_;
            };

        } // namespace CM
    } // namespace Frameworks
} // namespace AutoLink

#endif