#include "car_property_playback.h"

#include "autolink/frameworks/cm/carpropertymanager/impl/common/car_property_def.h"
#include "autolink/frameworks/cm/carpropertymanager/debug/carpropmgrdbgr/assistant/car_property_assistant.h"

#include "autolink/frameworks/cm/carpropertymanager/debug/carpropmgrdbgr/playback/parser/ParserJson.h"

#include <chrono>
#include <thread>
#include <vector>

#include "autolink/frameworks/log/log.h"
using namespace AutoLink::Frameworks::Log;

namespace AutoLink
{
    namespace Frameworks
    {
        namespace CM
        {
#define LOG_TAG "CarPropertyPb"

            struct PlaybackItem
            {
                std::string propDomain;
                std::string propGroup;
                std::string propName;
                std::string propValue;
                int32_t propStamp;
            };

            struct CarPropertyPlaybackPrivate
            {
                PlaybackItem curItem;

                int32_t startStamp{0};

                std::vector<PlaybackItem> playlist;

                bool EqualString(const char *s1, const char *s2)
                {
                    int32_t idx;

                    for (idx = 0;
                         (s1[idx] != 0) && (s2[idx] != 0);
                         idx++)
                    {
                        if (s1[idx] != s2[idx])
                        {
                            return false;
                        }
                    }

                    return ((s1[idx] == 0) && (s2[idx] == 0));
                }

                void onItemStart(const char *inItemName, ItemStatus &outStatus)
                {
                    // LOG_TAG_INFO("onItemStart [%s]", inItemName);

                    if (EqualString(inItemName, "end_timestamp"))
                    {
                        outStatus = STATUS_IGNORE;
                    }
                    else
                    {
                        outStatus = STATUS_ACCEPT;
                    }
                }

                void onItemAttri(const char *inItemName,
                                 const char *inAttriName,
                                 const char *inAttriValue)
                {
                    // LOG_TAG_INFO("onItemAttri [%s][%s][%s]", inItemName, inAttriName, inAttriValue);
                }

                void onItemContent(const char *inItemName,
                                   const char *inItemContent)
                {
                    LOG_TAG_INFO("onItemContent [%s][%s]", inItemName, inItemContent);
                    if (EqualString(inItemName, "prop_domain"))
                    {
                        curItem.propDomain = inItemContent;
                    }
                    else if (EqualString(inItemName, "prop_group"))
                    {
                        curItem.propGroup = inItemContent;
                    }
                    else if (EqualString(inItemName, "prop_name"))
                    {
                        curItem.propName = inItemContent;
                    }
                    else if (EqualString(inItemName, "prop_value"))
                    {
                        curItem.propValue = inItemContent;

                        playlist.push_back(curItem);
                    }
                    else if (EqualString(inItemName, "prop_timestamp"))
                    {
                        curItem.propStamp = atoi(inItemContent);
                    }
                    else if (EqualString(inItemName, "start_timestamp"))
                    {
                        startStamp = atoi(inItemContent);
                    }
                    else
                    {
                        LOG_TAG_INFO("unknown [%s]", inItemName);
                    }
                }

                void onItemEnd(const char *inItemName)
                {
                    // LOG_TAG_INFO("onItemEnd [%s]", inItemName);
                }

                void OnGetProperty(const std::string &propValue)
                {
                    LOG_TAG_INFO("DumpProperty value [%s]", propValue.c_str());
                    std::cout << "DumpProperty value [" << propValue.c_str() << "]" << std::endl;
                }
            };

            CarPropertyPlayback::CarPropertyPlayback()
                : p_(new CarPropertyPlaybackPrivate())
            {
            }
            CarPropertyPlayback::~CarPropertyPlayback()
            {
                delete p_;
            }

            void CarPropertyPlayback::PlaybackProperty(const char *sourceFile)
            {
                LOG_TAG_INFO("PlaybackProperty %s", sourceFile);

                ParserJson *parser = new ParserJson();
                if (nullptr == parser)
                {
                    LOG_TAG_INFO("create parser failed");

                    return;
                }

                // ParserOut stCB;

                parser->OnParseFile(sourceFile,
                                    {.itemStart = std::bind(
                                         &CarPropertyPlaybackPrivate::onItemStart,
                                         p_,
                                         std::placeholders::_1,
                                         std::placeholders::_2),
                                     .itemAttri = std::bind(
                                         &CarPropertyPlaybackPrivate::onItemAttri,
                                         p_,
                                         std::placeholders::_1,
                                         std::placeholders::_2,
                                         std::placeholders::_3),
                                     .itemContent = std::bind(
                                         &CarPropertyPlaybackPrivate::onItemContent,
                                         p_,
                                         std::placeholders::_1,
                                         std::placeholders::_2),
                                     .itemEnd = std::bind(
                                         &CarPropertyPlaybackPrivate::onItemEnd,
                                         p_,
                                         std::placeholders::_1)});

                delete parser;

                int32_t lastStamp = p_->startStamp;
                int32_t propInterval = 0;

                for (size_t i = 0; i < p_->playlist.size(); i++)
                {
                    propInterval = p_->playlist[i].propStamp - lastStamp;
                    if (0 < propInterval)
                    {
                        std::this_thread::sleep_for(std::chrono::milliseconds(propInterval));
                    }

                    lastStamp = p_->playlist[i].propStamp;

                    CarPropertyAssistant::Get().SetPropertyValue(p_->playlist[i].propDomain,
                                                                 p_->playlist[i].propGroup,
                                                                 p_->playlist[i].propName,
                                                                 p_->playlist[i].propValue);
                }
            }

            void CarPropertyPlayback::Init()
            {
            }

            void CarPropertyPlayback::Exit()
            {
            }

            void CarPropertyPlayback::SetProperty(const std::string &propFullName,
                                                  const std::string &propValue)
            {
                LOG_TAG_INFO("SetProperty [%s]:[%s]", propFullName.c_str(), propValue.c_str());

                CarPropertyParser propParser;
                std::string propKeys[CAR_PROPERTY_UBOUND];

                if (!propParser.ParseKey(propFullName.c_str(), propKeys))
                {
                    return;
                }

                CarPropertyAssistant::Get().SetPropertyValue(propKeys[CAR_PROPERTY_DOMAIN],
                                                             propKeys[CAR_PROPERTY_GROUP],
                                                             propKeys[CAR_PROPERTY_PROP],
                                                             propValue);
            }
            void CarPropertyPlayback::DumpProperty(const std::string &propFullName)
            {
                CarPropertyParser propParser;
                std::string propKeys[CAR_PROPERTY_UBOUND];

                if (!propParser.ParseKey(propFullName.c_str(), propKeys))
                {
                    std::cerr << "DumpProperty ParseKey [" << propFullName.c_str() << "] failed." << std::endl;
                    return;
                }

                CarPropertyAssistant::Get().GetPropertyValue(
                    propKeys[CAR_PROPERTY_DOMAIN],
                    propKeys[CAR_PROPERTY_GROUP],
                    propKeys[CAR_PROPERTY_PROP],
                    std::bind(&CarPropertyPlaybackPrivate::OnGetProperty,
                              p_,
                              std::placeholders::_1));
            }

        } // namespace CM
    } // namespace Frameworks
} // namespace AutoLink
