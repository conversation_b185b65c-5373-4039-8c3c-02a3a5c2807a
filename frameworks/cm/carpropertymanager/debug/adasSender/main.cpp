﻿#include "autolink/frameworks/log/log.h"
#include "autolink/frameworks/pa/IADAS/IADAS_client.h"

#include <iostream>
#include <string>
#include <cctype>
#include <vector>
#include <algorithm>
#include <climits>
#include <cerrno>
#include <cstdlib>

using namespace AutoLink::Frameworks::PA;

#define PROCESS_NAME "adasSender"

struct Parameter
{
    std::string name;
    int value;
};

std::vector<Parameter> parameters;

void printUsage(const std::string &processName)
{
    std::cout << "\n===============================================================\n";
    std::cout << "                  ADAS TT 消息发送模拟器 v1.0\n";
    std::cout << "          Copyright Autolink.  All Rights Reserved.          \n";
    std::cout << "  Feel free to contact HUALEI for any usage issues or feedback. \n";
    std::cout << "===============================================================\n";
    std::cout << "用法: 在提示符下输入: <keyname> <value>\n";
    std::cout << "支持: \n";
    std::cout << "  1. 支持参数校验\n";
    std::cout << "  2. 支持特殊命令: 'exit'、'list'、'clear'\n";

    std::cout << "\n输入格式:\n";
    std::cout << "  " << PROCESS_NAME << "> keyname value\n";

    std::cout << "\n特殊命令:\n";
    std::cout << "  exit    - 退出程序\n";
    std::cout << "  list    - 显示所有已输入的参数\n";
    std::cout << "  clear   - 清除所有参数\n";
    std::cout << "  help    - 显示本帮助信息\n";

    std::cout << "\n参数要求:(参考car_property_list_bq_8775表格中的adas.rx页)\n";
    std::cout << "  - <keyname> 必须是有效的属性名\n";
    std::cout << "  - <value> 必须是有效的整数（范围: " << INT_MIN << " 到 " << INT_MAX << ")\n";

    std::cout << "\n示例:\n";
    std::cout << "  " << PROCESS_NAME << "> ADAS_RDA_RCW_STATUS_LIGHT_INFO 1\n";
    std::cout << "  " << PROCESS_NAME << "> ADAS_SYS_FAILED_INFO 0\n";
    std::cout << "  " << PROCESS_NAME << "> ADAS_HMA_STATUS_LIGHT_INFO 3\n";
    std::cout << "=========================================================\n";
}

bool isValidName(const std::string &name)
{
    if (name.empty())
        return false;

    if (name == "exit" || name == "list" || name == "clear" || name == "help")
    {
        return true;
    }

    if (!std::isalpha(name[0]) && name[0] != '_')
    {
        return false;
    }

    for (char c : name)
    {
        if (!std::isalnum(c) && c != '_')
        {
            return false;
        }
    }

    return true;
}

bool safeStringToInt(const std::string &str, int &result)
{
    if (str.empty())
    {
        return false;
    }

    // 使用strtol进行转换（包含范围检查）
    char *endPtr;
    errno = 0;
    long longValue = std::strtol(str.c_str(), &endPtr, 10);

    if (errno == ERANGE)
    {
        // out of range
        return false;
    }

    // 检查整个字符串是否被转换
    if (*endPtr != '\0')
    {
        return false;
    }

    if (longValue < INT_MIN || longValue > INT_MAX)
    {
        return false;
    }

    result = static_cast<int>(longValue);
    return true;
}

bool handleSpecialCommand(const std::string &command)
{
    if (command == "exit")
    {
        std::cout << "退出程序...\n";
        return true;
    }

    if (command == "list")
    {
        std::cout << "\n当前参数列表:\n";
        if (parameters.empty())
        {
            std::cout << "  (无参数)\n";
        }
        else
        {
            for (const auto &param : parameters)
            {
                std::cout << "  " << param.name << " = " << param.value << "\n";
            }
        }
        return true;
    }

    if (command == "clear")
    {
        parameters.clear();
        std::cout << "所有参数已清除\n";
        return true;
    }

    if (command == "help")
    {
        printUsage(PROCESS_NAME);
        return true;
    }

    return false;
}

void handleParameterInput(const std::string &name, const std::string &valueStr)
{
    int value;
    if (!safeStringToInt(valueStr, value))
    {
        std::cerr << "Error: Value '" << valueStr << "' is not an integer.\n";
        return;
    }

    parameters.push_back({name, value});
    std::cout << "Accepted : " << name << " = " << value << "\n";

    static IADASClient client;
    client.OnSendAdasInfo(name, value);
}

void mainLoop()
{
    printUsage(PROCESS_NAME);

    std::cout << "\n"
              << PROCESS_NAME << "> ";
    std::string line;

    while (std::getline(std::cin, line))
    {
        // 去除首尾空白字符
        line.erase(line.begin(), std::find_if(line.begin(), line.end(), [](int ch)
                                              { return !std::isspace(ch); }));
        line.erase(std::find_if(line.rbegin(),
                                line.rend(),
                                [](int ch)
                                { return !std::isspace(ch); })
                       .base(),
                   line.end());

        if (line.empty())
        {
            std::cout << PROCESS_NAME << "> ";
            continue;
        }

        // 分割输入
        size_t pos = line.find(' ');
        if (pos == std::string::npos)
        {
            if (handleSpecialCommand(line))
            {
                if (line == "exit")
                {
                    return;
                }
                std::cout << PROCESS_NAME << "> ";
                continue;
            }

            std::cerr << "Error: Invalid usage.\n";
            std::cout << PROCESS_NAME << "> ";
            continue;
        }

        std::string name = line.substr(0, pos);
        std::string valueStr = line.substr(pos + 1);

        // 处理特殊命令（可能带参数）
        if (handleSpecialCommand(name))
        {
            if (name == "exit")
            {
                return;
            }
            std::cout << PROCESS_NAME << "> ";
            continue;
        }

        if (!isValidName(name))
        {
            std::cerr << "Error: Invalid KeyName '" << name << "'.\n";
            std::cout << PROCESS_NAME << "> ";
            continue;
        }

        handleParameterInput(name, valueStr);

        std::cout << PROCESS_NAME << "> ";
    }
}

int main()
{
    try
    {
        mainLoop();
    }
    catch (const std::exception &e)
    {
        std::cerr << "\nError: Process exception : " << e.what() << "\n";
        return 1;
    }
    catch (...)
    {
        std::cerr << "\nError: Unknown exception, program terminated.\n";
        return 1;
    }

    return 0;
}