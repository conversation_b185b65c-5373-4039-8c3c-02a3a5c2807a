# 车辆配置头文件生成工具

此工具用于根据 `vehicleConfigWordList.xlsx` Excel 文件生成 `baic_n5_cfg_cal.h` C++ 头文件。

## 功能概述
该 Python 脚本会读取 `vehicleConfigWordList.xlsx` 文件中的车辆配置信息，将其转换为 C++ 头文件中的位域结构体声明，最终生成 `baic_n5_cfg_cal.h` 头文件。
`vehicleConfigWordList.xlsx` 文件从https://t83dfrspj4.feishu.cn/sheets/NC9GsxyTJhLqU5tPHkOcTnKInFR 获取。

## 如何使用
1. 确保你已经安装了 Python 3.x、openpyxl。
2. 将 `vehicleConfigWordList.xlsx` 文件放置在脚本所在的目录中。
3. 打开终端或命令提示符，进入脚本所在的目录。
4. 运行脚本：
   ```bash
   python vehicleConfigGen++.py
   ```
5. 脚本将会生成 `baic_n5_cfg_cal.h` 头文件。

## 目录结构
```plaintext
.
├── vehicleConfigWordList.xlsx  # 输入的 Excel 配置文件
├── vehicleConfigGen++.py       # 生成头文件的 Python 脚本
├── baic_n5_cfg_cal.h           # 生成的 C++ 头文件（运行脚本后生成）
└── README.md                   # 本说明文件
