import openpyxl
import datetime

def generate_header_file(excel_file, output_file):
    # 获取当前日期和时间
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # 定义头文件的基本结构，包含注释信息
    header_content = [
        "/**",
        "* @{",
        f"* @file {output_file}",
        "* @brief This header file generates variable, structure, and enumeration definitions related to vehicle configuration based on an Excel table.",
        f"* <AUTHOR>
        f"* @date {now}",
        f"* @table {excel_file}",
        "* @copyright (C) 2025 Auto-link.",
        "*                Unauthorized reproduction, distribution, and use of this file, as well as disclosure of its contents to others, are prohibited.",
        "*                Violators will be held liable. All rights reserved in case of patent, utility model, or design authorization.",
        "* @}",
        "**/",
        "",
        "/**",
        "* DO NOT MODIFY THIS FILE MANUALLY!!!",
        "* This file is automatically generated by the script.",
        "**/",
        f"#ifndef _BAIC_N5_CFG_CAL_H /* _BAIC_N5_CFG_CAL_H */",
        f"#define _BAIC_N5_CFG_CAL_H /* _BAIC_N5_CFG_CAL_H */",
        "",
        "#ifdef __cplusplus",
        "extern \"C\" {",
        "#endif /* __cplusplus */",
        "#include <stdint.h>",
        "#define DPT_TABLE_VER \"V2.8 2025-06-19\"",
        "",
        "/**",
        "* DID: 0xf011",
        "* DID LEN: 24",
        "* DID DESC(EN): DID0xf011",
        "* DID DESC(EN):",
        "**/",
        "struct Did0xf011",
        "{",
    ]

    # 打开 Excel 文件
    workbook = openpyxl.load_workbook(excel_file)
    sheet = workbook.active

    # 全局 RESERVED_ 计数器
    reserved_counter = 1

    # 用于存储每个 byte 内的变量信息
    byte_variables = {}

    # 从第二行开始遍历，跳过表头
    for row in sheet.iter_rows(min_row=2, values_only=True):
        # 确保前四列数据不为 None 且不为空字符串
        if any(cell is None or cell == "" for cell in row[:4]):
            continue

        byte_num = row[0]  # A 列：BYTE
        bit_width = row[1]  # B 列：BIT
        var_type = row[2]  # C 列：TYPE
        var_name = row[3]  # D 列：CONGIF_ITEM_NAME

        try:
            byte_num = int(byte_num)
            if not 0 <= byte_num <= 23:
                print(f"Warning: Byte number {byte_num} is not in the range of 0~23, skipping variable {var_name}")
                continue
        except ValueError:
            print(f"Warning: Byte number {byte_num} is not a valid integer, skipping variable {var_name}")
            continue

        try:
            bit_width = int(bit_width)
            if not 1 <= bit_width <= 8:
                print(f"Warning: Bit width {bit_width} is not in the range of 1~8, skipping variable {var_name}")
                continue
        except ValueError:
            print(f"Warning: Bit width {bit_width} is not a valid integer, skipping variable {var_name}")
            continue

        # 处理 RESERVED_ 名称
        if var_name == "RESERVED_":
            new_var_name = f"RESERVED_{reserved_counter}"
            reserved_counter += 1
            var_name = new_var_name

        # 将变量信息添加到对应的 byte 列表中
        if byte_num not in byte_variables:
            byte_variables[byte_num] = []
        byte_variables[byte_num].append((var_type, var_name, bit_width))

    # 按 byte 顺序遍历，将每个 byte 内的变量倒序添加到 header_content
    for byte_num in sorted(byte_variables.keys()):
        for var_type, var_name, bit_width in reversed(byte_variables[byte_num]):
            header_content.append(f"    {var_type} {var_name} : {bit_width}; // Byte {byte_num}")

    header_content.extend([
        "};",
        "",
        "#ifdef __cplusplus",
        "}",
        "#endif /* __cplusplus */",
        "",
        "#endif /* _BAIC_IDCPRO_N5_CFG_CAL_H */"
    ])

    # 将内容写入头文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(header_content))

    print(f"头文件已成功生成: {output_file}")

if __name__ == "__main__":
    excel_file = "vehicleConfigWordList.xlsx"
    output_file = "baic_n5_cfg_cal.h"
    generate_header_file(excel_file, output_file)