/**
* @{
* @file baic_n5_cfg_cal.h
* @brief This header file generates variable, structure, and enumeration definitions related to vehicle configuration based on an Excel table.
* <AUTHOR>
* @date 2025-07-31 16:13:41
* @table vehicleConfigWordList.xlsx
* @copyright (C) 2025 Auto-link.
*                Unauthorized reproduction, distribution, and use of this file, as well as disclosure of its contents to others, are prohibited.
*                Violators will be held liable. All rights reserved in case of patent, utility model, or design authorization.
* @}
**/

/**
* DO NOT MODIFY THIS FILE MANUALLY!!!
* This file is automatically generated by the script.
**/
#ifndef _BAIC_N5_CFG_CAL_H /* _BAIC_N5_CFG_CAL_H */
#define _BAIC_N5_CFG_CAL_H /* _BAIC_N5_CFG_CAL_H */

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */
#include <stdint.h>
#define DPT_TABLE_VER "V2.8 2025-06-19"

/**
* DID: 0xf011
* DID LEN: 24
* DID DESC(EN): DID0xf011
* DID DESC(EN):
**/
struct Did0xf011
{
    uint8_t ISA : 1; // Byte 0
    uint8_t TSR : 1; // Byte 0
    uint8_t ICA : 1; // Byte 0
    uint8_t ACC : 1; // Byte 0
    uint8_t FCW : 1; // Byte 0
    uint8_t AEB : 1; // Byte 0
    uint8_t HUD_TYPE : 2; // Byte 0
    uint8_t STEERING_WHEEL_HEATING : 1; // Byte 1
    uint8_t HWA : 1; // Byte 1
    uint8_t TLA : 1; // Byte 1
    uint8_t RCTA : 1; // Byte 1
    uint8_t FCTA : 1; // Byte 1
    uint8_t DOW : 1; // Byte 1
    uint8_t LCA : 1; // Byte 1
    uint8_t LDW : 1; // Byte 1
    uint8_t FRAGRANCE_FUNTION : 1; // Byte 2
    uint8_t UVGI : 1; // Byte 2
    uint8_t WPC : 1; // Byte 2
    uint8_t APA : 1; // Byte 2
    uint8_t DMS : 2; // Byte 2
    uint8_t FACE_RECOGNITION : 2; // Byte 2
    uint8_t REMOTE_KEY : 1; // Byte 3
    uint8_t REMOTE_CONTROL_PARKING : 1; // Byte 3
    uint8_t SECOND_ROW_SEATBELT_UNFASTENED_WARNING : 1; // Byte 3
    uint8_t PASSENGER_SEAT_HEATING : 1; // Byte 3
    uint8_t PASSENGER_SEAT_MEMORY : 1; // Byte 3
    uint8_t DRIVER_SEAT_HEATING : 1; // Byte 3
    uint8_t DRIVER_SEAT_LUMBAR_SUPPORT : 1; // Byte 3
    uint8_t DRIVER_SEAT : 1; // Byte 3
    uint8_t WELCOME_CEREMONY_LIGHTING_FUNCTION : 1; // Byte 4
    uint8_t ISLC : 1; // Byte 4
    uint8_t RCW : 1; // Byte 4
    uint8_t RCTB : 1; // Byte 4
    uint8_t FCTB : 1; // Byte 4
    uint8_t CAR_BIT : 1; // Byte 4
    uint8_t CAR_PLAY : 1; // Byte 4
    uint8_t HI_CAR : 1; // Byte 4
    uint8_t SECOND_ROW_SEAT_HEATING : 1; // Byte 5
    uint8_t CC : 1; // Byte 5
    uint8_t CUSHION_ADJUSTMENT : 1; // Byte 5
    uint8_t REARVIEW_MIRROR_FOLDS_AUTOMATICALLY : 1; // Byte 5
    uint8_t EXTERNAL_REARVIEW_MIRROR_ELECTRIC_HEATING : 1; // Byte 5
    uint8_t INTERIOR_AMBIENT_LIGHT : 1; // Byte 5
    uint8_t DYNAMIC_TAILLIGHT_ANIMATION : 1; // Byte 5
    uint8_t INTELLIGENT_HEADLIGHT_CONTROL : 1; // Byte 5
    uint8_t AUXILIARY_INSTRUMENT_PANEL_AMBIENT_LIGHT : 1; // Byte 6
    uint8_t TRACK_MODE : 1; // Byte 6
    uint8_t RAGE_MODE : 1; // Byte 6
    uint8_t NUMBER_OF_SPEAKERS : 3; // Byte 6
    uint8_t POWER_LIFTGATE : 1; // Byte 6
    uint8_t ELECTRIC_TAIL : 1; // Byte 6
    uint8_t DEFAULT_LANGUAGE : 8; // Byte 7
    uint8_t RADIO : 1; // Byte 8
    uint8_t PASSENGER_SEAT_MASSAGE_FUNCTION : 1; // Byte 8
    uint8_t DRIVER_SEAT_MASSAGE_FUNCTION : 1; // Byte 8
    uint8_t PM2_5_MONITORING_DISPLAY : 1; // Byte 8
    uint8_t INTELLIGENT_HIGH_BEAM : 1; // Byte 8
    uint8_t VITAL_SIGNS_MONITORING : 1; // Byte 8
    uint8_t PASSENGER_SEAT_VENTILATION : 1; // Byte 8
    uint8_t DRIVER_SEAT_VENTILATION : 1; // Byte 8
    uint8_t CAR_PAINT_COLOR : 8; // Byte 9
    uint8_t CAR_MOUNTED_CHIP : 2; // Byte 10
    uint8_t CENTRAL_CONTROL_SCREEN : 2; // Byte 10
    uint8_t REARVIEW_MIRROR_HEAT : 1; // Byte 10
    uint8_t CHARGING_PORT : 1; // Byte 10
    uint8_t EXHAUST_OUTLET : 1; // Byte 10
    uint8_t RESERVED_1 : 1; // Byte 10
    uint8_t FRONT_SEAT_VIBRATION_UNIT : 1; // Byte 11
    uint8_t MASSAGE_PASSENGER_SEAT : 2; // Byte 11
    uint8_t MASSAGE_DRIVER_SEAT : 2; // Byte 11
    uint8_t LEG_REST_PASSENGER_SEAT : 1; // Byte 11
    uint8_t ZERO_GRAVITY_PASSENGER_SEAT : 1; // Byte 11
    uint8_t ABAT_VENT : 1; // Byte 11
    uint8_t RESERVED_4 : 1; // Byte 12
    uint8_t RESERVED_3 : 1; // Byte 12
    uint8_t RESERVED_2 : 1; // Byte 12
    uint8_t EXTEND_RANGE_FUEL_TANK : 2; // Byte 12
    uint8_t ENGINE_TYPE : 1; // Byte 12
    uint8_t EXTERNAL_REARVIEW_MIRROR_MEMORY : 1; // Byte 12
    uint8_t LEG_INFRARED_HEAT_PASSENGER_SEAT : 1; // Byte 12
    uint8_t RESERVED_5 : 8; // Byte 13
    uint8_t RESERVED_6 : 8; // Byte 14
    uint8_t MODEL_CODE : 8; // Byte 15
    uint8_t RESERVED_7 : 1; // Byte 16
    uint8_t DRIVE_TYPE : 2; // Byte 16
    uint8_t POWERTRAIN_SYS : 3; // Byte 16
    uint8_t NOA : 1; // Byte 16
    uint8_t AVP : 1; // Byte 16
    uint8_t CURB_WEIGHT : 3; // Byte 17
    uint8_t TIRE_SIZE : 2; // Byte 17
    uint8_t CALIPER : 1; // Byte 17
    uint8_t BRAKE_MODEL : 2; // Byte 17
    uint8_t RESERVED_8 : 8; // Byte 18
    uint8_t RESERVED_9 : 8; // Byte 19
    uint8_t RESERVED_10 : 8; // Byte 20
    uint8_t RESERVED_11 : 8; // Byte 21
    uint8_t RESERVED_12 : 8; // Byte 22
    uint8_t RESERVED_13 : 8; // Byte 23
};

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* _BAIC_IDCPRO_N5_CFG_CAL_H */