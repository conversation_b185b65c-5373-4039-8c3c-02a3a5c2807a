#define LOG_TAG "carpropmgrtools"
#include <algorithm>
#include <vector>
#include <log.h>
#include <unistd.h>
#include <iostream>
#include <chrono>
#include <thread>
#include <string_view>
#include "autolink/frameworks/cm/carpropertymanager/impl/common/generated/CM_property_cfg.h"
//#include "autolink/frameworks/cm/carpropertymanager/impl/common/generated/DIAG_property_cfg.h"
#include "autolink/frameworks/cm/carpropertymanager/car_property_manager.h"
#include "autolink/frameworks/cm/carpropertymanager/car_property_value.h"
#include "autolink/frameworks/core/module.h"
#include "autolink/frameworks/core/impl/taskmanager.h"
#include "autolink/frameworks/core/application.h"

using namespace std;
using namespace AutoLink::Frameworks;
using namespace AutoLink::Frameworks::CM;
using namespace AutoLink::Frameworks::Log;

std::mutex mutex_;
std::condition_variable condition_variable_;
int subIndex = 1;

class CarPropTest : public AutoLink::Frameworks::Core::Module {
public:
    CarPropTest();
    ~CarPropTest();
    void OnInit() override;
    void subscrible();
    void onCallback(const CarPropertyValue &propValue);

};

CarPropTest::CarPropTest()  {}
CarPropTest::~CarPropTest(){}

void CarPropTest::OnInit(){
    subscrible();
}

class CarPropTestApplication : public Core::Application
{
public:
    CarPropTestApplication(int argc, char **argv) : Core::Application("diag_midware", argc, argv) {}

    virtual ~CarPropTestApplication() {}

protected:
    virtual void OnInit()
    {
    }

    virtual void OnRegisterModule()
    {
        // Reigster our modules,eg
        RegisterModule<CarPropTest>("diagMidware");
    }
};



void usage(int argc, char **argv) {

  printf("usage:\n");

  printf("Example: %s -s 1 -h 1\n", argv[0]);
  printf("Example: %s -n vehicle.tx.EHU_CENTRLOCKCTRL_4E -v 2\n", argv[0]);
}

void CarPropTest::onCallback(const CarPropertyValue &propValue) {
    int value = propValue.IsValid() ? propValue.ToInt() : 65535;
    LOG_TAG_INFO("onCallback val:%d ", value);
    //CarPropertyValue prop = CarPropertyManager::Get().GetPropertyValue("vehicle.rx.VCU_DRVMODSIG_102");
    //value = propValue.IsValid() ? propValue.ToInt() : 65535;
    //LOG_TAG_INFO("vehicle.rx.VCU_DRVMODSIG_102 onCallback val:%d ", value);
}

void CarPropTest::subscrible() {
    LOG_TAG_INFO("subscrible start");


    switch(subIndex) {
        case 1:
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.VCU_DRVMODSIG_102", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1)); 
            break;
        case 2:
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.VCU_DRVSTYLEFACTOR_3A2", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));             // 驾驶风格
            break;
        case 3:
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.BMS_CHRGSTSDISP_49D", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));                   // 充电状态（显示）
            break;
        case 4:
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.VCU_RESVCHRGSTSDISP_503", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));           // 充电预约状态（显示）
            break;
        case 5:
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.VCU_CHRGDISCHRGCRTDISP_52C", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));     // 充放电电流（显示）
            break;
        case 6:
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.BMS_RMNGCHRGTIDISPLY_29F", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));         // 剩余充电时间（显示）
            break;
        case 7:
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.BMS_VEHEXTDCHASTS_363", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));               // 车辆对外放电状态
            break;
        case 8:
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.VCU_CHRGINDCRLAMP_505", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));               //充电提醒指示灯(纯电)
            break;
        case 9:
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.ADAS_AUDIOWARN_32C", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));             //充电提醒指示灯(混动)
            break;
        case 10:
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.ADAS_FLTINDCR_340", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));             //充电提醒指示灯(混动)
            break;
        case 11:
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.ADAS_SPDLIM_ASL_347", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));             //充电提醒指示灯(混动)
            break;
        case 12:
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.ADAS_SPDLIMSTS_ASL_347", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));             //充电提醒指示灯(混动)
            break;
        case 13:
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.ADAS_STS_AEB_33C", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));             //充电提醒指示灯(混动)
            break;
        case 14:
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.ADAS_STS_FCW_33C", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));             //充电提醒指示灯(混动)
            break;
        case 15:
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.BCM_FRNTHOODLIDSTS_343", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));             //充电提醒指示灯(混动)
            break;
        case 16:
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.VCU_DRVMODSIG_102", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));                       // 驾驶模式信号  0x17FD
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.VCU_DRVSTYLEFACTOR_3A2", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));             // 驾驶风格

            //充放电
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.BMS_CHRGSTSDISP_49D", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));                   // 充电状态（显示）
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.VCU_RESVCHRGSTSDISP_503", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));           // 充电预约状态（显示）
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.VCU_CHRGDISCHRGCRTDISP_52C", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));     // 充放电电流（显示）
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.BMS_RMNGCHRGTIDISPLY_29F", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));         // 剩余充电时间（显示）
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.BMS_VEHEXTDCHASTS_363", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));               // 车辆对外放电状态

            //电量显示
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.VCU_CHRGINDCRLAMP_505", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));               //充电提醒指示灯(纯电)
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.ADAS_AUDIOWARN_32C", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));             //充电提醒指示灯(混动)
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.ADAS_FLTINDCR_340", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));             //充电提醒指示灯(混动)
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.ADAS_SPDLIM_ASL_347", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));             //充电提醒指示灯(混动)
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.ADAS_SPDLIMSTS_ASL_347", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));             //充电提醒指示灯(混动)
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.ADAS_STS_AEB_33C", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));             //充电提醒指示灯(混动)
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.ADAS_STS_FCW_33C", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));             //充电提醒指示灯(混动)
            CarPropertyManager::Get().SubscribeProperty("vehicle.rx.BCM_FRNTHOODLIDSTS_343", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));             //充电提醒指示灯(混动)
            break;
         default: break;

    }
    //车型配置
    //CarPropertyManager::Get().SubscribeProperty("psis.car_cfg.CAR_CFG_MODEL_CODE", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));
    //CarPropertyManager::Get().SubscribeProperty("custom.mcu.POWER_VALUE", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));                            //剩余电量百分比
    //CarPropertyManager::Get().SubscribeProperty("custom.drivinginfo.DRVMODSIG", std::bind(&CarPropTest::onCallback, this, std::placeholders::_1));                            //剩余电量百分比
    LOG_TAG_INFO("subscrible data, index = %d", index);
}

bool setprop(char* name, int val) {
    //CarPropertyManager::Get().SetPropertyValue("vehicle.tx.EHU_CENTRLOCKCTRL_4E", 2);
    CarPropertyManager::Get().SetPropertyValue("custom.drivinginfo.DRVMODSIG", 3);
    CarPropertyManager::Get().SetPropertyValue(name, val);
    return true;
}

int main(int argc, char *argv[]) {
    LOG_TAG_INFO("carproptest start");

    int opt;
    int val = 0;
    char* propName = "vehicle";
    int32_t propId = 0;
    bool isSubscrible = false;
    bool isHost = false;

    while ((opt = getopt(argc, argv, "s:n:v:h:")) != -1) {
        switch (opt) {
        case 's':
            isSubscrible = true;
            subIndex = std::stoi(optarg);
            printf("subscrible data, subIndex = %d\n", subIndex);
            break;
        case 'n':
            printf("setprop name =%s\n", optarg);
            propName = optarg;
            break;
        case 'v':
            val = std::stoi(optarg);
            printf("value=%d\n", val);
            break;
        case 'h':
            isHost = true;
            break;
        case '?':
            printf("unkonwn option: %c\n", optopt);
            break;
        default:
            break;
        }
    }
    CARPROPERTYMANAGER_INIT(CM::gCarProperyConfigArray, CM::ConfigNum, isHost);
 
    std::this_thread::sleep_for(std::chrono::milliseconds(500));


    if (isSubscrible) {
        CarPropTestApplication app(argc, argv);
        printf("carpropManagerTools run");
        return app.Run();
    }
    else {
        bool ret = setprop(propName, val);
        if(!ret) {
            printf("%s: set data for %s failed.", __func__, propName);
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    }
    LOG_TAG_INFO("carproptest end");
    return 0;
}

