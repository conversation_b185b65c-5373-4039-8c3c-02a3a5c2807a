#ifndef APIVERIFIER_H
#define APIVERIFIER_H

#include "autolink/frameworks/core/ioeventnotifier.h"
#include "autolink/frameworks/pa/IPPS/IPPS.h"
#include <string>
#include <iostream>
#include <sstream>
#include <cstring>
#include <vector>
#include <algorithm>
#include <memory>
#include <thread>

using namespace AutoLink::Frameworks::Core;
using namespace AutoLink::Frameworks::PA;
#define DEFAULT_BUFFER_SIZE 1024

std::string ppsRoot = "/tmp/pps_al";
std::string objName = "AUTOLINK";
std::string propName = "HUALEI";
std::string propValue = "SUPERMAN";
int bufferSize = DEFAULT_BUFFER_SIZE;
AttributeSyntax syntax = AttributeSyntax::ATTRIBUTE_SYNTAX_NULL_DECODING;
HandleType writeHandleType = HandleType::HANDLE_WRITE;
HandleType readHandleType = HandleType::HANDLE_READ;

std::shared_ptr<IOEventNotifier> pNotifier;
PPSHandle *readHandle = nullptr;
PPSHandle *writeHandle = nullptr;

// 定义 PPS 通知回调函数
void ppsNotifyCallback(const std::string &propName, const std::string &propValue)
{
    std::cout << "Read  result.    " << propName << " = " << propValue << std::endl;
}

// 打印使用说明
void printUsage()
{
    std::stringstream ss;
    ss << "\n===============================================================================\n"
       << "                        IPPS 接口验证工具  apiVerifier v1.0\n"
       << "  A tool for testing IPPS interfaces. \n"
       << "  Feel free to contact HUALEI for any usage issues or feedback. \n"
       << "          Copyright Autolink.  All Rights Reserved.          \n"
       << "===============================================================================\n"
       << "Usage: ./apiVerifier [options]\n"
       << "Options:\n"
       << "  -h                             Show this help message and exit.\n"
       << "  -o, --objName <name>           Set the PPS object name. Default: " << objName << "\n"
       << "  -n, --propName <name>          Set the property name. Default: " << propName << "\n"
       << "  -v, --propValue <value>        Set the property value. Default: " << propValue << "\n"
       << "  -s, --bufferSize <size>        Set the buffer size. Default: " << DEFAULT_BUFFER_SIZE << "\n"
       << "  -p, --path <path>              Set the specified path. Default: " << ppsRoot << "\n"
       << "  -w, --write <type>             Set the write handle type. Default: 1\n"
       << "  -r, --read  <type>             Set the read handle type. Default: 0\n"
       << "                                 Available handle types: \n"
       << "                                     0 - HANDLE_READ\n"
       << "                                     1 - HANDLE_WRITE\n"
       << "                                     2 - HANDLE_WRITE_PERSIST\n"
       << "                                     3 - HANDLE_WRITE_TRUNC\n"
       << "                                     4 - HANDLE_SUBSCRIBE\n"
       << "  -t, --syntax <type>            Set the attribute syntax type. Default: 0\n "
       << "                                 Available syntax types: \n"
       << "                                     0 - ATTRIBUTE_SYNTAX_NULL_DECODING\n"
       << "                                     1 - ATTRIBUTE_SYNTAX_C_LAN_ESCAPE_SEQUENCE\n"
       << "                                     2 - ATTRIBUTE_SYNTAX_BOOLEAN\n"
       << "                                     3 - ATTRIBUTE_SYNTAX_NUMERIC\n"
       << "                                     4 - ATTRIBUTE_SYNTAX_BASE64\n"
       << "                                     5 - ATTRIBUTE_SYNTAX_JSON\n"
       << "Examples:\n"
       << "  ./apiVerifier -h\n"
       << "  ./apiVerifier -t 5\n"
       << "  ./apiVerifier -o my_pps_obj -n my_prop -v 123 -s 2048 -t 1\n"
       << "  ./apiVerifier -p /path/to/directory -o my_pps_obj\n"
       << "===============================================================================\n";
    std::cout << ss.str() << std::endl;
}

// 解析命令行参数的函数
bool parseCommandLineArgs(int argc, char *argv[])
{
    for (int i = 1; i < argc; ++i)
    {
        if (std::strcmp(argv[i], "-h") == 0)
        {
            printUsage();
            return false;
        }
        else if ((std::strcmp(argv[i], "-o") == 0 || std::strcmp(argv[i], "--objName") == 0) && i + 1 < argc)
        {
            objName = argv[++i];
        }
        else if ((std::strcmp(argv[i], "-n") == 0 || std::strcmp(argv[i], "--propName") == 0) && i + 1 < argc)
        {
            propName = argv[++i];
        }
        else if ((std::strcmp(argv[i], "-v") == 0 || std::strcmp(argv[i], "--propValue") == 0) && i + 1 < argc)
        {
            propValue = argv[++i];
        }
        else if ((std::strcmp(argv[i], "-s") == 0 || std::strcmp(argv[i], "--bufferSize") == 0) && i + 1 < argc)
        {
            bufferSize = std::stoi(argv[++i]);
        }
        else if ((std::strcmp(argv[i], "-t") == 0 || std::strcmp(argv[i], "--syntax") == 0) && i + 1 < argc)
        {
            try
            {
                int syntaxValue = std::stoi(argv[++i]);
                switch (syntaxValue)
                {
                case 0:
                    syntax = AttributeSyntax::ATTRIBUTE_SYNTAX_NULL_DECODING;
                    break;
                case 1:
                    syntax = AttributeSyntax::ATTRIBUTE_SYNTAX_C_LAN_ESCAPE_SEQUENCE;
                    break;
                case 2:
                    syntax = AttributeSyntax::ATTRIBUTE_SYNTAX_BOOLEAN;
                    break;
                case 3:
                    syntax = AttributeSyntax::ATTRIBUTE_SYNTAX_NUMERIC;
                    break;
                case 4:
                    syntax = AttributeSyntax::ATTRIBUTE_SYNTAX_BASE64;
                    break;
                case 5:
                    syntax = AttributeSyntax::ATTRIBUTE_SYNTAX_JSON;
                    break;
                default:
                    std::cerr << "Error: Invalid syntax type." << std::endl;
                    return false;
                }
            }
            catch (...)
            {
                std::cerr << "Error: Invalid syntax type." << std::endl;
                return false;
            }
        }
        else if ((std::strcmp(argv[i], "-p") == 0 || std::strcmp(argv[i], "--path") == 0) && i + 1 < argc)
        {
            ppsRoot = argv[++i];
        }
        else if ((std::strcmp(argv[i], "-w") == 0 || std::strcmp(argv[i], "--write") == 0) && i + 1 < argc)
        {
            int tmp = std::stoi(argv[++i]);
            switch (tmp)
            {
            case 1:
                writeHandleType = HandleType::HANDLE_WRITE;
                break;
            case 2:
                writeHandleType = HandleType::HANDLE_WRITE_PERSIST;
                break;
            case 3:
                writeHandleType = HandleType::HANDLE_WRITE_TRUNC;
                break;
            default:
                std::cerr << "Error: Invalid write handle type." << std::endl;
                return false;
            }
        }
        else if ((std::strcmp(argv[i], "-r") == 0 || std::strcmp(argv[i], "--read") == 0) && i + 1 < argc)
        {
            int tmp = std::stoi(argv[++i]);
            switch (tmp)
            {
            case 0:
                readHandleType = HandleType::HANDLE_READ;
                break;
            case 4:
                readHandleType = HandleType::HANDLE_SUBSCRIBE;
                break;
            default:
                std::cerr << "Error: Invalid read handle type." << std::endl;
                return false;
            }
        }
        else
        {
            std::cerr << "Error: Unknown option: " << argv[i] << std::endl;
            return false;
        }
    }
    return true;
}

void OnIOEventNotify(IOEventNotifier::IOEvent event)
{
    PPSReadData(readHandle, syntax);
}

int OnInit()
{
    // 创建 PPS 句柄用于写入操作
    writeHandle = PPSCreateHandle(objName, writeHandleType, nullptr, 0, bufferSize, ppsRoot);
    if (!writeHandle)
    {
        std::cerr << "Error: API PPSCreateHandle failed to create write handle." << std::endl;
        return 1;
    }

    // 创建 PPS 句柄用于读取操作
    readHandle = PPSCreateHandle(objName, readHandleType, ppsNotifyCallback, 0, bufferSize, ppsRoot);
    if (!readHandle)
    {
        std::cerr << "Error: API PPSCreateHandle failed to create read handle." << std::endl;
        PPSDestroyHandle(writeHandle);
        return 1;
    }
    else
    {
        pNotifier = IOEventNotifier::Create(objName.c_str(),
                                            readHandle->fd,
                                            IOEventNotifier::IO_EVENT_INPUT,
                                            std::bind(&OnIOEventNotify,
                                                      std::placeholders::_1));
    }
    return 0;
}

int OnWrite()
{
    for (int i = 0; i < 5; i++)
    {
        if (PPSWriteData(writeHandle, syntax, propName, propValue + "_" + std::to_string(i)))
        {
            std::cout << "Write Accepted.  " << propName << " = " << propValue + "_" + std::to_string(i) << std::endl;
        }
        else
        {
            std::cerr << "Error: API PPSWriteData returns false." << std::endl;
            return 1;
        }
    }
    return 0;
}

int OnDestroy()
{
    // 销毁 PPS 句柄
    PPSDestroyHandle(writeHandle);
    PPSDestroyHandle(readHandle);
    return 0;
}

#endif // APIVERIFIER_H