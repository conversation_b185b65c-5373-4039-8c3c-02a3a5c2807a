#ifndef ISOMEIP_H
#define ISOMEIP_H

#include <string>
#include <functional>
#include <vector>
#include <map>
#include "autolink/frameworks/log/log.h"

namespace AutoLink {
namespace Frameworks {
namespace PA {

enum ValueType : int32_t
{
    INT32 = 1,
    FLOAT = 2,
};

struct SomeipPropRxValue
{
    ValueType valTYpe{INT32};
    union
    {
        int32_t i{0};
        float f;
    } value;

    SomeipPropRxValue(int32_t i_)
    {
        valTYpe = INT32;
        value.i = i_;
    }

    SomeipPropRxValue(double f_)
    {
        valTYpe = FLOAT;
        value.f = static_cast<float>(f_);
    }
};

struct SomeipPropRxData
{
    std::string prop{""};
    bool isValid{false};
    std::vector<SomeipPropRxValue> vecValue;

    SomeipPropRxData(std::string prop_)
    {
        prop = prop_;
    }

    SomeipPropRxData(std::string prop_, bool isValid_)
    {
        prop = prop_;
        isValid = isValid_;
    }

    SomeipPropRxData(std::string prop_, bool isValid_, std::vector<SomeipPropRxValue> vecValue_)
    {
        prop = prop_;
        isValid = isValid_;
        vecValue = vecValue_;
    }

    SomeipPropRxData(std::string prop_, int i_)
    {
        prop = prop_;
        isValid = true;
        SomeipPropRxValue val(i_);
        vecValue.push_back(val);
    }

    SomeipPropRxData(std::string prop_, double f_)
    {
        prop = prop_;
        isValid = true;
        SomeipPropRxValue val(f_);
        vecValue.push_back(val);
    }

    SomeipPropRxData(std::string prop_, std::vector<SomeipPropRxValue> vecValue_)
    {
        prop = prop_;
        isValid = true;
        vecValue = vecValue_;
    }
};

typedef std::function<void(const SomeipPropRxData &propRxData)> SomeipPropDataHandler;

class ISomeIP
{
public:
    ISomeIP();
    ~ISomeIP();

    static void BlockSignal();
    static void RestoreSignal();
    void RegisterPropValueHandler(SomeipPropDataHandler handler);
    void Start();

private:
    struct ISomeIPPrivate *p_;
};

} // namespace PA
} // namespace Frameworks
} // namespace AutoLink

#endif
