#ifndef SOMEIP_INTERFACE_H
#define SOMEIP_INTERFACE_H

#include <string>
#include "autolink/frameworks/pa/ISomeIP/isomeip.h"


namespace AutoLink {
namespace Frameworks {
namespace PA {

class SomeIPInterface
{
public:
    virtual ~SomeIPInterface() = default;

    virtual void Init() = 0;
    virtual void Start() = 0;
    virtual void Stop() = 0;
    virtual void BlockSignal() = 0;
    virtual void RestoreSignal() = 0;
    virtual void RegisterHandler(SomeipPropDataHandler handler) = 0;

};

} // namespace PA
} // namespace Frameworks
} // namespace AutoLink

#endif
