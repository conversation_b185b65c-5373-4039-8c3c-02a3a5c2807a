///
/// @copyright  This software is the property of HiRain Technologies.
///             Any information contained in this doc should not be reproduced,
///             or used, or disclosed without the written authorization from
///             HiRain Technologies.
///
/// @file       initialization.cc
/// @brief
///
/// <AUTHOR> Mingqing <<EMAIL>>
///
///
#include "ara/core/initialization.h"
#include <cstdlib>

#include "ara/log/runtime/runtime.h"
#include "ara/com/runtime/runtime.h"

#include "ara/core/executor/thread_pool_executor_manager.h"

namespace ara {
namespace core {

namespace {
static constexpr char const *kExecManifsetName = "EXEC_MANIFEST";

} // namespace

/// @brief
///
/// @return Result<void>
///
Result<void> Initialize() {

  ara::log::runtime::Initialize();

  auto com_inited = ara::com::runtime::Initialize();

  return Result<void>::FromValue();
}

Result<void> Deinitialize() {
  //ara::log::runtime::Deinitialize();

  executor::GetThreadPoolExecutorManager().GetIoThreadPoolExecutor()->StopNow();
  return Result<void>::FromValue();
}

} // namespace core
} // namespace ara