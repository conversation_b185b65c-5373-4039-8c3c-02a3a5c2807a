///
/// @copyright  This software is the property of HiRain Technologies.
///             Any information contained in this doc should not be reproduced,
///             or used, or disclosed without the written authorization from
///             HiRain Technologies.
///
/// @file       ADCCDrivingHmiEnv_proxy_impl.cc
/// @brief
///
/// <AUTHOR> Hongyang <<EMAIL>>
///
///
#include "ADCCDrivingHmiEnv_proxy_impl.h"

#include "ara/core/future.h"
#include "ara/core/instance_specifier.h"
#include "ara/core/io/event_loop_manager.h"
#include "ara/core/io/timer.h"
#include <ara/core/runtime/timer.h>

#include <stdexcept>
#include <string>

#define LOG_FATAL() ARA_FATAL(*logger_)
#define LOG_ERROR() ARA_ERROR(*logger_)
#define LOG_WARN() ARA_WARN(*logger_)
#define LOG_INFO() ARA_INFO(*logger_)
#define LOG_DEBUG() ARA_DEBUG(*logger_)
#define LOG_VERBOSE() ARA_VERBOSE(*logger_)

namespace ns{
namespace adcc_drivinghmienv {
ADCCDrivingHmiEnv_proxy_impl::ADCCDrivingHmiEnv_proxy_impl(ara::core::io::EventLoop *loop,
                             ara::core::InstanceSpecifier is)
    : logger_(&ara::log::CreateLogger("ICCA", "")), loop_(loop), is_(is) {

}

ADCCDrivingHmiEnv_proxy_impl::~ADCCDrivingHmiEnv_proxy_impl() {

}

void ADCCDrivingHmiEnv_proxy_impl::ServiceActive() {
    LOG_INFO() << "ServiceActive is called.";
    using proxy::ADCC_DrivingHmiEnvProxy;
    adccdrivinghmienv_finder_ = ADCC_DrivingHmiEnvProxy::StartFindService(
        [this](ara::com::ServiceHandleContainer<ADCC_DrivingHmiEnvProxy::HandleType>
            handles, ara::com::FindServiceHandle) {
            if (!handles.empty()) {
                if (!adccdrivinghmienv_proxy_) {
                    LOG_INFO() << "Found a service instance"
                               << handles[0].GetInstanceId().ToString();

                    adccdrivinghmienv_proxy_ =
                        std::make_shared<ADCC_DrivingHmiEnvProxy>(handles[0]);

                    adccdrivinghmienv_proxy_->NtfDynamicObjectList_Drvg
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The NtfDynamicObjectList_Drvg event's state"
                                           << static_cast<int>(state);
                            });

                    adccdrivinghmienv_proxy_->NtfStaticObjectList_Drvg
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The NtfStaticObjectList_Drvg event's state"
                                           << static_cast<int>(state);
                            });

                    adccdrivinghmienv_proxy_->NtfLaneLineListt_Drvg
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The NtfLaneLineListt_Drvg event's state"
                                           << static_cast<int>(state);
                            });

                    adccdrivinghmienv_proxy_->NtfRoadMarkerList
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The NtfRoadMarkerList event's state"
                                           << static_cast<int>(state);
                            });

                    adccdrivinghmienv_proxy_->NtfLaneLineList_Ext_Drvg
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The NtfLaneLineList_Ext_Drvg event's state"
                                           << static_cast<int>(state);
                            });

                    adccdrivinghmienv_proxy_->NtfRoadStruct_Drvg
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The NtfRoadStruct_Drvg event's state"
                                           << static_cast<int>(state);
                            });

                    adccdrivinghmienv_proxy_->NtfOCCList
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The NtfOCCList event's state"
                                           << static_cast<int>(state);
                            });

                    adccdrivinghmienv_proxy_->NtfDynamicObjectList_Drvg
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive NtfDynamicObjectList_Drvg event";
                            if (timeout) {
                                LOG_INFO() << " NtfDynamicObjectList_Drvg event's data: { Timeout }";
                            } else {
                                adccdrivinghmienv_proxy_->NtfDynamicObjectList_Drvg.GetNewSamples(
                                    [this](proxy::events::NtfDynamicObjectList_Drvg::
                                        EventSamplePtr sample) {
                                        if (!(*sample).empty()) {
                                            LOG_DEBUG() << " NtfDynamicObjectList_Drvg event's data: {{"
                                                       << (*sample)[0].objectId << ", "
                                                       << (*sample)[0].objectType << ", "
                                                       << (*sample)[0].lightStatus << ", "
                                                       << (*sample)[0].objectPoseX << ", "
                                                       << (*sample)[0].objectPoseY << ", "
                                                       << (*sample)[0].objectYaw << ", "
                                                       << (*sample)[0].objectColor << ", "
                                                       << (*sample)[0].objectVelocityX << ", "
                                                       << (*sample)[0].objectVelocityY << ", "
                                                       << (*sample)[0].timestampnSec << ", "
                                                       << (*sample)[0].laneID << "}}";
                                        } else {
                                            LOG_DEBUG() << " NtfDynamicObjectList_Drvg event's data: { empty }";
                                        }
                                    });
                            }
                        });

                    adccdrivinghmienv_proxy_->NtfStaticObjectList_Drvg
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive NtfStaticObjectList_Drvg event";
                            if (timeout) {
                                LOG_INFO() << " NtfStaticObjectList_Drvg event's data: { Timeout }";
                            } else {
                                adccdrivinghmienv_proxy_->NtfStaticObjectList_Drvg.GetNewSamples(
                                    [this](proxy::events::NtfStaticObjectList_Drvg::
                                        EventSamplePtr sample) {
                                        if (!(*sample).empty()) {
                                            LOG_DEBUG() << " NtfStaticObjectList_Drvg event's data: {{"
                                                       << (*sample)[0].objectId << ", "
                                                       << (*sample)[0].objectType << ", "
                                                       << (*sample)[0].objectPoseX << ", "
                                                       << (*sample)[0].objectPoseY << ", "
                                                       << (*sample)[0].objectYaw << ", "
                                                       << (*sample)[0].objectLength << ", "
                                                       << (*sample)[0].objecWidth << ", "
                                                       << (*sample)[0].timestampnSec << ", "
                                                       << (*sample)[0].laneID << "}}";
                                        } else {
                                            LOG_DEBUG() << " NtfStaticObjectList_Drvg event's data: { empty }";
                                        }
                                    });
                            }
                        });

                    adccdrivinghmienv_proxy_->NtfLaneLineListt_Drvg
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive NtfLaneLineListt_Drvg event";
                            if (timeout) {
                                LOG_INFO() << " NtfLaneLineListt_Drvg event's data: { Timeout }";
                            } else {
                                adccdrivinghmienv_proxy_->NtfLaneLineListt_Drvg.GetNewSamples(
                                    [this](proxy::events::NtfLaneLineListt_Drvg::
                                        EventSamplePtr sample) {
                                        if (!(*sample).empty()) {
                                            LOG_DEBUG() << " NtfLaneLineListt_Drvg event's data: {{"
                                                       << (*sample)[0].lineIndex << ", "
                                                       << (*sample)[0].lineId << ", "
                                                       << (*sample)[0].lineColor << ", "
                                                       << (*sample)[0].lineType << ", "
                                                       << (*sample)[0].lineEquation_C0 << ", "
                                                       << (*sample)[0].lineEquation_C1 << ", "
                                                       << (*sample)[0].lineEquation_C2 << ", "
                                                       << (*sample)[0].lineEquation_C3 << ", "
                                                       << (*sample)[0].laneLineWidth << ", "
                                                       << (*sample)[0].lineStart_X << ", "
                                                       << (*sample)[0].lineStart_Y << ", "
                                                       << (*sample)[0].lineEnd_X << ", "
                                                       << (*sample)[0].lineEnd_Y << "}}";
                                        } else {
                                            LOG_DEBUG() << " NtfLaneLineListt_Drvg event's data: { empty }";
                                        }
                                    });
                            }
                        });

                    adccdrivinghmienv_proxy_->NtfRoadMarkerList
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive NtfRoadMarkerList event";
                            if (timeout) {
                                LOG_INFO() << " NtfRoadMarkerList event's data: { Timeout }";
                            } else {
                                adccdrivinghmienv_proxy_->NtfRoadMarkerList.GetNewSamples(
                                    [this](proxy::events::NtfRoadMarkerList::
                                        EventSamplePtr sample) {
                                        if (!(*sample).empty()) {
                                            LOG_DEBUG() << " NtfRoadMarkerList event's data: {{"
                                                       << (*sample)[0].RoadMarkerType << ", "
                                                       << (*sample)[0].RoadMarkerID << ", "
                                                       << (*sample)[0].RoadMarkerTrackingSts << ", "
                                                       << (*sample)[0].RoadMarkerPose1X << ", "
                                                       << (*sample)[0].RoadMarkerPose1Y << ", "
                                                       << (*sample)[0].RoadMarkerPose2X << ", "
                                                       << (*sample)[0].RoadMarkerPose2Y << ", "
                                                       << (*sample)[0].RoadMarkerWidth << ", "
                                                       << (*sample)[0].RoadMarkerAngle << "}}";
                                        } else {
                                            LOG_DEBUG() << " NtfRoadMarkerList event's data: { empty }";
                                        }
                                    });
                            }
                        });

                    adccdrivinghmienv_proxy_->NtfLaneLineList_Ext_Drvg
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive NtfLaneLineList_Ext_Drvg event";
                            if (timeout) {
                                LOG_INFO() << " NtfLaneLineList_Ext_Drvg event's data: { Timeout }";
                            } else {
                                adccdrivinghmienv_proxy_->NtfLaneLineList_Ext_Drvg.GetNewSamples(
                                    [this](proxy::events::NtfLaneLineList_Ext_Drvg::
                                        EventSamplePtr sample) {
                                        if (!(*sample).empty()) {
                                            LOG_DEBUG() << " NtfLaneLineList_Ext_Drvg event's data: {{"
                                                       << (*sample)[0].lineIndex << ", "
                                                       << (*sample)[0].lineId << ", "
                                                       << (*sample)[0].lineColor << ", "
                                                       << (*sample)[0].lineType << ", "
                                                       << (*sample)[0].lineEquation_C0 << ", "
                                                       << (*sample)[0].lineEquation_C1 << ", "
                                                       << (*sample)[0].lineEquation_C2 << ", "
                                                       << (*sample)[0].lineEquation_C3 << ", "
                                                       << (*sample)[0].laneLineWidth << ", "
                                                       << (*sample)[0].lineStart_X << ", "
                                                       << (*sample)[0].lineStart_Y << ", "
                                                       << (*sample)[0].lineEnd_X << ", "
                                                       << (*sample)[0].lineEnd_Y << "}}";
                                        } else {
                                            LOG_DEBUG() << " NtfLaneLineList_Ext_Drvg event's data: { empty }";
                                        }
                                    });
                            }
                        });

                    adccdrivinghmienv_proxy_->NtfRoadStruct_Drvg
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive NtfRoadStruct_Drvg event";
                            if (timeout) {
                                LOG_INFO() << " NtfRoadStruct_Drvg event's data: { Timeout }";
                            } else {
                                adccdrivinghmienv_proxy_->NtfRoadStruct_Drvg.GetNewSamples(
                                    [this](proxy::events::NtfRoadStruct_Drvg::
                                        EventSamplePtr sample) {
                                        if (!(sample->LaneLine).empty() && !(sample->RoadEdge).empty() &&
                                            !(sample->FreeSpace).empty()) {
                                            LOG_DEBUG() << " NtfRoadStruct_Drvg event's data: {{{"
                                                       << ((*sample).LaneLine)[0].LaneLineIndex << ", "
                                                       << ((*sample).LaneLine)[0].LaneLineID << ", {"
                                                       << ((*sample).LaneLine)[0].LaneLineSegments.LaneLineSegmentID << ", "
                                                       << ((*sample).LaneLine)[0].LaneLineSegments.LaneLineType << ", "
                                                       << ((*sample).LaneLine)[0].LaneLineSegments.laneLineColor << ", "
                                                       << ((*sample).LaneLine)[0].LaneLineSegments.LaneLineWidth << ", "
                                                       << ((*sample).LaneLine)[0].LaneLineSegments.LaneLineConfidence << "}}}, {{"
                                                       << ((*sample).RoadEdge)[0].RoadEdgeIndex << ", "
                                                       << ((*sample).RoadEdge)[0].LaneLineID << ", {"
                                                       << ((*sample).RoadEdge)[0].RoadEdgeSegments.RoadEdgeSegmentID << ", "
                                                       << ((*sample).RoadEdge)[0].RoadEdgeSegments.RoadEdgeType << ", "
                                                       << ((*sample).RoadEdge)[0].RoadEdgeSegments.RoadEdgeConfidence << "}}}, {{"
                                                       << ((*sample).FreeSpace)[0].PointData_x << ", "
                                                       << ((*sample).FreeSpace)[0].PointData_y << "}}}";
                                        } else {
                                            LOG_DEBUG() << " NtfRoadStruct_Drvg event's data: { empty }";
                                        }
                                    });
                            }
                        });

                    adccdrivinghmienv_proxy_->NtfOCCList
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive NtfOCCList event";
                            if (timeout) {
                                LOG_INFO() << " NtfOCCList event's data: { Timeout }";
                            } else {
                                adccdrivinghmienv_proxy_->NtfOCCList.GetNewSamples(
                                    [this](proxy::events::NtfOCCList::
                                        EventSamplePtr sample) {
                                        if (!(sample->LaneLine).empty() && !(sample->RoadEdge).empty() &&
                                            !(sample->FreeSpace).empty()) {
                                            LOG_DEBUG() << " NtfOCCList event's data: {{"
                                                       << (*sample)[0].OCCPointData_x << ", "
                                                       << (*sample)[0].OCCPointData_y << ", "
                                                       << (*sample)[0].OCCPointData_z << "}}";
                                        } else {
                                            LOG_DEBUG() << " NtfOCCList event's data: { empty }";
                                        }
                                    });
                            }
                        });

                    auto result = adccdrivinghmienv_proxy_->NtfDynamicObjectList_Drvg.Subscribe(1);
                    if (!result) {
                        LOG_WARN() << "Subscribe NtfDynamicObjectList_Drvg failed, error code"
                                   << result.Error();
                    }

                    result = adccdrivinghmienv_proxy_->NtfStaticObjectList_Drvg.Subscribe(1);
                    if (!result) {
                        LOG_WARN() << "Subscribe NtfStaticObjectList_Drvg failed, error code"
                                   << result.Error();
                    }

                    result = adccdrivinghmienv_proxy_->NtfLaneLineListt_Drvg.Subscribe(1);
                    if (!result) {
                        LOG_WARN() << "Subscribe NtfLaneLineListt_Drvg failed, error code"
                                   << result.Error();
                    }

                    result = adccdrivinghmienv_proxy_->NtfRoadMarkerList.Subscribe(1);
                    if (!result) {
                        LOG_WARN() << "Subscribe NtfRoadMarkerList failed, error code"
                                   << result.Error();
                    }

                    result = adccdrivinghmienv_proxy_->NtfLaneLineList_Ext_Drvg.Subscribe(1);
                    if (!result) {
                        LOG_WARN() << "Subscribe NtfLaneLineList_Ext_Drvg failed, error code"
                                   << result.Error();
                    }

                    result = adccdrivinghmienv_proxy_->NtfRoadStruct_Drvg.Subscribe(1);
                    if (!result) {
                        LOG_WARN() << "Subscribe NtfRoadStruct_Drvg failed, error code"
                                   << result.Error();
                    }

                    result = adccdrivinghmienv_proxy_->NtfOCCList.Subscribe(1);
                    if (!result) {
                        LOG_WARN() << "Subscribe NtfOCCList failed, error code"
                                   << result.Error();
                    }
                }
            } else {
                LOG_INFO() << "************REMOTE-STOP-OFFER************";
            }
        },
        is_);
}

} // namespace adcc_drivinghmienvns
}