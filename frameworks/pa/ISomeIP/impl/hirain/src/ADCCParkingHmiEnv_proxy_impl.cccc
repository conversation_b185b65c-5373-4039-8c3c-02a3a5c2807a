///
/// @copyright  This software is the property of HiRain Technologies.
///             Any information contained in this doc should not be reproduced,
///             or used, or disclosed without the written authorization from
///             HiRain Technologies.
///
/// @file       ADCCParkingHmiEnv_proxy_impl.cc
/// @brief
///
/// <AUTHOR> Hongyang <<EMAIL>>
///
///
#include "ADCCParkingHmiEnv_proxy_impl.h"

#include "ara/core/future.h"
#include "ara/core/instance_specifier.h"
#include "ara/core/io/event_loop_manager.h"
#include "ara/core/io/timer.h"
#include <ara/core/runtime/timer.h>

#include <stdexcept>
#include <string>

#define LOG_FATAL() ARA_FATAL(*logger_)
#define LOG_ERROR() ARA_ERROR(*logger_)
#define LOG_WARN() ARA_WARN(*logger_)
#define LOG_INFO() ARA_INFO(*logger_)
#define LOG_DEBUG() ARA_DEBUG(*logger_)
#define LOG_VERBOSE() ARA_VERBOSE(*logger_)

namespace ns{
namespace adcc_parkinghmienv {
ADCCParkingHmiEnv_proxy_impl::ADCCParkingHmiEnv_proxy_impl(ara::core::io::EventLoop *loop,
                             ara::core::InstanceSpecifier is)
    : logger_(&ara::log::CreateLogger("ICCA", "")), loop_(loop), is_(is) {

}

ADCCParkingHmiEnv_proxy_impl::~ADCCParkingHmiEnv_proxy_impl() {

}

void ADCCParkingHmiEnv_proxy_impl::ServiceActive() {
    LOG_INFO() << "ServiceActive is called.";
    using proxy::ADCC_ParkingHmiEnvProxy;
    adccparkinghmienv_finder_ = ADCC_ParkingHmiEnvProxy::StartFindService(
        [this](ara::com::ServiceHandleContainer<ADCC_ParkingHmiEnvProxy::HandleType>
            handles, ara::com::FindServiceHandle) {
            if (!handles.empty()) {
                if (!adccparkinghmienv_proxy_) {
                    LOG_INFO() << "Found a service instance"
                               << handles[0].GetInstanceId().ToString();

                    adccparkinghmienv_proxy_ =
                        std::make_shared<ADCC_ParkingHmiEnvProxy>(handles[0]);

                    adccparkinghmienv_proxy_->NtfDynamicObjectList_Prkg
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The NtfDynamicObjectList_Prkg event's state"
                                           << static_cast<int>(state);
                            });

                    adccparkinghmienv_proxy_->NtfStaticObjectList_Prkg
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The NtfStaticObjectList_Prkg event's state"
                                           << static_cast<int>(state);
                            });

                    adccparkinghmienv_proxy_->NtfParkingSlot_Prkg
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The NtfParkingSlot_Prkg event's state"
                                           << static_cast<int>(state);
                            });

                    adccparkinghmienv_proxy_->NtfDynamicObjectList_Prkg
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive NtfDynamicObjectList_Prkg event";
                            if (timeout) {
                                LOG_INFO() << " NtfDynamicObjectList_Prkg event's data: { Timeout }";
                            } else {
                                adccparkinghmienv_proxy_->NtfDynamicObjectList_Prkg.GetNewSamples(
                                    [this](proxy::events::NtfDynamicObjectList_Prkg::
                                        EventSamplePtr sample) {
                                        if (!(*sample).empty()) {
                                          LOG_DEBUG() << " NtfDynamicObjectList_Prkg event's data: {{"
                                                     << (*sample)[0].objectId << ", "
                                                     << (*sample)[0].objectType << ", "
                                                     << (*sample)[0].lightStatus << ", "
                                                     << (*sample)[0].objectPoseX << ", "
                                                     << (*sample)[0].objectPoseY << ", "
                                                     << (*sample)[0].objectYaw << ", "
                                                     << (*sample)[0].objectColor << ", "
                                                     << (*sample)[0].objectVelocityX << ", "
                                                     << (*sample)[0].objectVelocityY << ", "
                                                     << (*sample)[0].timestampnSec << ", "
                                                     << (*sample)[0].laneID << "}}";
                                        } else {
                                            LOG_DEBUG() << " NtfDynamicObjectList_Prkg event's data: { empty }";
                                        }
                                    });
                            }
                        });

                    adccparkinghmienv_proxy_->NtfStaticObjectList_Prkg
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive NtfStaticObjectList_Prkg event";
                            if (timeout) {
                                LOG_INFO() << " NtfStaticObjectList_Prkg event's data: { Timeout }";
                            } else {
                                adccparkinghmienv_proxy_->NtfStaticObjectList_Prkg.GetNewSamples(
                                    [this](proxy::events::NtfStaticObjectList_Prkg::
                                        EventSamplePtr sample) {
                                        if (!(*sample).empty()) {
                                            LOG_DEBUG() << " NtfStaticObjectList_Prkg event's data: {{"
                                                       << (*sample)[0].objectId << ", "
                                                       << (*sample)[0].objectType << ", "
                                                       << (*sample)[0].objectPoseX << ", "
                                                       << (*sample)[0].objectPoseY << ", "
                                                       << (*sample)[0].objectYaw << ", "
                                                       << (*sample)[0].objectLength << ", "
                                                       << (*sample)[0].objecWidth << ", "
                                                       << (*sample)[0].timestampnSec << ", "
                                                       << (*sample)[0].laneID << "}}";
                                        } else {
                                            LOG_DEBUG() << " NtfStaticObjectList_Prkg event's data: { empty }";
                                        }
                                    });
                            }
                        });

                    adccparkinghmienv_proxy_->NtfParkingSlot_Prkg
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive NtfParkingSlot_Prkg event";
                            if (timeout) {
                                LOG_INFO() << " NtfParkingSlot_Prkg event's data: { Timeout }";
                            } else {
                                adccparkinghmienv_proxy_->NtfParkingSlot_Prkg.GetNewSamples(
                                    [this](proxy::events::NtfParkingSlot_Prkg::
                                        EventSamplePtr sample) {
                                        if (!(*sample).empty()) {
                                            LOG_DEBUG() << " NtfParkingSlot_Prkg event's data: {{"
                                                       << (*sample)[0].SlotID << ", "
                                                       << (*sample)[0].SlotType << ", "
                                                       << (*sample)[0].SlotStatus << ", {"
                                                       << (*sample)[0].SlotPointTop1.Point_X << ", "
                                                       << (*sample)[0].SlotPointTop1.Point_Y << "}, {"
                                                       << (*sample)[0].SlotPointTop2.Point_X << ", "
                                                       << (*sample)[0].SlotPointTop2.Point_Y << "}, {"
                                                       << (*sample)[0].SlotPointBottom1.Point_X << ", "
                                                       << (*sample)[0].SlotPointBottom1.Point_Y << "}, {"
                                                       << (*sample)[0].SlotPointBottom2.Point_X << ", "
                                                       << (*sample)[0].SlotPointBottom2.Point_Y << "}, "
                                                       << (*sample)[0].SlotNum << "}}";
                                        } else {
                                            LOG_DEBUG() << " NtfParkingSlot_Prkg event's data: { empty }";
                                        }
                                    });
                            }
                        });

                    auto result = adccparkinghmienv_proxy_->NtfDynamicObjectList_Prkg.Subscribe(1);
                    if (!result) {
                        LOG_WARN() << "Subscribe NtfDynamicObjectList_Prkg failed, error code"
                                   << result.Error();
                    }

                    result = adccparkinghmienv_proxy_->NtfStaticObjectList_Prkg.Subscribe(1);
                    if (!result) {
                        LOG_WARN() << "Subscribe NtfStaticObjectList_Prkg failed, error code"
                                   << result.Error();
                    }

                    result = adccparkinghmienv_proxy_->NtfParkingSlot_Prkg.Subscribe(1);
                    if (!result) {
                        LOG_WARN() << "Subscribe NtfParkingSlot_Prkg failed, error code"
                                   << result.Error();
                    }
                }
            } else {
                LOG_INFO() << "************REMOTE-STOP-OFFER************";
            }
        },
        is_);
}

} // namespace adcc_parkinghmienvns
}