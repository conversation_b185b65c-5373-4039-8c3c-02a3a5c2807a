///
/// @copyright  This software is the property of HiRain Technologies.
///             Any information contained in this doc should not be reproduced,
///             or used, or disclosed without the written authorization from
///             HiRain Technologies.
///
/// @file       ADCCAomMMSt_proxy_impl.cc
/// @brief
///
/// <AUTHOR> Hongyang <<EMAIL>>
///
///
#include "ADCCAomMMSt_proxy_impl.h"

#include "ara/core/future.h"
#include "ara/core/instance_specifier.h"
#include "ara/core/io/event_loop_manager.h"
#include "ara/core/io/timer.h"
#include <ara/core/runtime/timer.h>

#include <stdexcept>
#include <string>

#define LOG_FATAL() ARA_FATAL(*logger_)
#define LOG_ERROR() ARA_ERROR(*logger_)
#define LOG_WARN() ARA_WARN(*logger_)
#define LOG_INFO() ARA_INFO(*logger_)
#define LOG_DEBUG() ARA_DEBUG(*logger_)
#define LOG_VERBOSE() ARA_VERBOSE(*logger_)

namespace ns{
namespace adcc_aommmst {
ADCCAomMMSt_proxy_impl::ADCCAomMMSt_proxy_impl(ara::core::io::EventLoop *loop,
                             ara::core::InstanceSpecifier is)
    : logger_(&ara::log::CreateLogger("ICCA", "")), loop_(loop), is_(is) {

}

ADCCAomMMSt_proxy_impl::~ADCCAomMMSt_proxy_impl() {

}

void ADCCAomMMSt_proxy_impl::ServiceActive() {
    LOG_INFO() << "ServiceActive is called";
    using proxy::ADCC_AomMMStProxy;
    adccaommmst_finder_ = ADCC_AomMMStProxy::StartFindService(
        [this](ara::com::ServiceHandleContainer<ADCC_AomMMStProxy::HandleType>
            handles, ara::com::FindServiceHandle) {
            if (!handles.empty()) {
                if (!adccaommmst_proxy_) {
                    LOG_INFO() << "Found a service instance"
                               << handles[0].GetInstanceId().ToString();

                    adccaommmst_proxy_ =
                        std::make_shared<ADCC_AomMMStProxy>(handles[0]);

                    adccaommmst_proxy_->notifyExtMMResWLnk
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The notifyExtMMResWLnk event's state"
                                           << static_cast<int>(state);
                            });

                    adccaommmst_proxy_->notifyExtMMResWLnk
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive notifyExtMMResWLnk event";
                            if (timeout) {
                                LOG_INFO() << " notifyExtMMResWLnk event's data: { Timeout }";
                            } else {
                                adccaommmst_proxy_->notifyExtMMResWLnk.GetNewSamples(
                                    [this](proxy::events::notifyExtMMResWLnk::
                                        EventSamplePtr sample) {
                                        LOG_DEBUG() << " notifyExtMMResWLnk event's data: {"
                                                   << sample->timestamp << ", "
                                                   << sample->mode << ", {"
                                                   << sample->lane.linkId << ","
                                                   << sample->lane.laneGroupId << ","
                                                   << sample->lane.length << ","
                                                   << sample->lane.offset << ","
                                                   << sample->lane.offset_Lateral << ","
                                                   << sample->lane.heading << ","
                                                   << sample->lane.laneNo << "}}";
                                    });
                            }
                        });

                    auto result = adccaommmst_proxy_->notifyExtMMResWLnk.Subscribe(1);
                    if (!result) {
                        LOG_WARN() << "Subscribe notifyExtMMResWLnk failed, error code"
                                   << result.Error();
                    }
                }
            } else {
                LOG_INFO() << "************REMOTE-STOP-OFFER************";
            }
        },
        is_);
}

} // namespace adcc_aommmstns
}