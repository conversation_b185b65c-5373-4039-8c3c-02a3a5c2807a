///
/// @copyright  This software is the property of HiRain Technologies.
///             Any information contained in this doc should not be reproduced,
///             or used, or disclosed without the written authorization from
///             HiRain Technologies.
///
/// @file       ADCCDrivingFuncInfo_proxy_impl.cc
/// @brief
///
/// <AUTHOR> Hongyang <<EMAIL>>
///
///
#include "ADCCDrivingFuncInfo_proxy_impl.h"

#include "ara/core/future.h"
#include "ara/core/instance_specifier.h"
#include "ara/core/io/event_loop_manager.h"
#include "ara/core/io/timer.h"
#include <ara/core/runtime/timer.h>

#include <stdexcept>
#include <string>

#include "autolink/frameworks/log/log.h"
#define LOG_TAG "ADCCDrivingFuncInfo"

#define LOG_FATAL() ARA_FATAL(*logger_)
#define LOG_ERROR() ARA_ERROR(*logger_)
#define LOG_WARN() ARA_WARN(*logger_)
#define LOG_INFO() ARA_INFO(*logger_)
#define LOG_DEBUG() ARA_DEBUG(*logger_)
#define LOG_VERBOSE() ARA_VERBOSE(*logger_)

namespace ns{
namespace adcc_drivingfuncinfo {
ADCCDrivingFuncInfo_proxy_impl::ADCCDrivingFuncInfo_proxy_impl(ara::core::io::EventLoop *loop,
                             ara::core::InstanceSpecifier is)
    : logger_(&ara::log::CreateLogger("ICCA", "")), loop_(loop), is_(is) {

}

ADCCDrivingFuncInfo_proxy_impl::~ADCCDrivingFuncInfo_proxy_impl() {

}

void ADCCDrivingFuncInfo_proxy_impl::RegisterValueHandel(SomeipPropDataHandler handler)
{
    handler_ = handler;
}

void ADCCDrivingFuncInfo_proxy_impl::Start()
{
    ServiceActive();
    return;
}

void ADCCDrivingFuncInfo_proxy_impl::Stop()
{
    proxy::ADCC_DrivingFuncInfoProxy::StopFindService(adccdrivingfuncinfo_finder_);
    return;
}

void ADCCDrivingFuncInfo_proxy_impl::ServiceActive() {
    LOG_INFO() << "ServiceActive is called.";
    using proxy::ADCC_DrivingFuncInfoProxy;
    adccdrivingfuncinfo_finder_ = ADCC_DrivingFuncInfoProxy::StartFindService(
        [this](ara::com::ServiceHandleContainer<ADCC_DrivingFuncInfoProxy::HandleType>
            handles, ara::com::FindServiceHandle) {
            if (!handles.empty()) {
                if (!adccdrivingfuncinfo_proxy_) {
                    LOG_INFO() << "Found a service instance"
                               << handles[0].GetInstanceId().ToString();

                    adccdrivingfuncinfo_proxy_ =
                        std::make_shared<ADCC_DrivingFuncInfoProxy>(handles[0]);

                    adccdrivingfuncinfo_proxy_->NtfDrivingLvl12_Drvg
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The NtfDrivingLvl12_Drvg event's state"
                                           << static_cast<int>(state);
                            });

                    adccdrivingfuncinfo_proxy_->NtfDrivingSafetyFuncInfo_Drvg
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The NtfDrivingSafetyFuncInfo_Drvg event's state"
                                           << static_cast<int>(state);
                            });

                    adccdrivingfuncinfo_proxy_->NtfDrivingLvl2Plus_Drvg
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The NtfDrivingLvl2Plus_Drvg event's state"
                                           << static_cast<int>(state);
                            });

                    adccdrivingfuncinfo_proxy_->CNOARepalyPathInfo
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The CNOARepalyPathInfo event's state"
                                           << static_cast<int>(state);
                            });

                    adccdrivingfuncinfo_proxy_->CNOATrainingPathInfo
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The CNOATrainingPathInfo event's state"
                                           << static_cast<int>(state);
                            });

                    adccdrivingfuncinfo_proxy_->NtfCNOAPathDetail
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The NtfCNOAPathDetail event's state"
                                           << static_cast<int>(state);
                            });

                    adccdrivingfuncinfo_proxy_->NtfOffRoadPathDetail
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The NtfOffRoadPathDetail event's state"
                                       << static_cast<int>(state);
                            });
    
                    adccdrivingfuncinfo_proxy_->NtfOffRoadTrainingPathInfo
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The NtfOffRoadTrainingPathInfo event's state"
                                           << static_cast<int>(state);
                            });
    
                    adccdrivingfuncinfo_proxy_->NtfOffRoadRepalyPathInfo
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The NtfOffRoadRepalyPathInfo event's state"
                                           << static_cast<int>(state);
                            });

                    adccdrivingfuncinfo_proxy_->NtfDrivingLvl12_Drvg
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive NtfDrivingLvl12_Drvg event";
                            if (timeout) {
                                LOG_INFO() << " NtfDrivingLvl12_Drvg event's data: { Timeout }";
                                if(handler_)
                                {
                                    SomeipPropRxData data_AB02_8101("AB02_8101", false);
                                    handler_(data_AB02_8101);

                                    SomeipPropRxData data_LSS_INFO("LSS_INFO", false);
                                    handler_(data_LSS_INFO);

                                    SomeipPropRxData data_DRIVING_COMMON_INFO("DRIVING_COMMON_INFO", false);
                                    handler_(data_DRIVING_COMMON_INFO);

                                    SomeipPropRxData data_ALC_INFO("ALC_INFO", false);
                                    handler_(data_ALC_INFO);
                              }
                            } else {
                                adccdrivingfuncinfo_proxy_->NtfDrivingLvl12_Drvg.GetNewSamples(
                                    [this](proxy::events::NtfDrivingLvl12_Drvg::
                                        EventSamplePtr sample) {

                                          uint8_t e2eRes = static_cast<uint8_t>(sample.GetProfileCheckStatus());
                                          if (0 != e2eRes)
                                          {
                                              LOG_TAG_WARNING("AB02_8101 e2e_check_fail errno:%d ", e2eRes);
                                              // return;
                                          }

                                          std::vector<SomeipPropRxValue> vecValue;
                                          vecValue.push_back(sample->NtfACCInfo_Drvg.ACCSts);
                                          vecValue.push_back(sample->NtfACCInfo_Drvg.ACCTextMessage);
                                          vecValue.push_back(sample->ADAS_strt_DrivingCommon.DrivingCommonSnG);
                                          vecValue.push_back(sample->NtfICAInfo_Drvg.ICATextInfo);
                                          vecValue.push_back(sample->NtfICAInfo_Drvg.ICASts);
                                          vecValue.push_back(sample->NtfICAInfo_Drvg.ICAQuitReason);
                                          vecValue.push_back(sample->NtfISLIInfo_Drvg.ISLIOverSpeedWarn);
                                          vecValue.push_back(sample->NtfISLIInfo_Drvg.ISLISpeedLimitSign);
                                          vecValue.push_back(sample->NtfISLIInfo_Drvg.ISLIWarningModFb);
                                          vecValue.push_back(sample->NtfLSSInfo_Drvg.LKSSts);
                                          vecValue.push_back(sample->NtfLSSInfo_Drvg.ELKSts);
                                          vecValue.push_back(sample->NtfHNOPfunctionInfo_Drvg.HNOPSts);
                                          vecValue.push_back(sample->NtfACCInfo_Drvg.SetSpeed);
                                          vecValue.push_back(sample->NtfTrafficSign_Drvg.TrafficSignType);
                                          vecValue.push_back(sample->NtfACCInfo_Drvg.TimeGapSet);
                                          vecValue.push_back(sample->NtfLSSInfo_Drvg.ELKSts);
                                          vecValue.push_back(sample->NtfLSSInfo_Drvg.ELKLeftActiveSt);
                                          vecValue.push_back(sample->NtfLSSInfo_Drvg.ELKRightActiveSt);
                                          vecValue.push_back(sample->NtfALCInfo_Drvg.ALCStatus);
                                          vecValue.push_back(sample->NtfALCInfo_Drvg.ALCDirIndicator);
                                          vecValue.push_back(sample->NtfALCInfo_Drvg.ALCAbortReason);
                                          vecValue.push_back(sample->NtfALCInfo_Drvg.ALCSoundWarn);
                                          vecValue.push_back(sample->NtfALCInfo_Drvg.ALCTextInfomation);
                                          vecValue.push_back(sample->NtfALCInfo_Drvg.ALCTakeOverRequest);
                                          vecValue.push_back(sample->NtfALCInfo_Drvg.ALCPermitSt);
                                          vecValue.push_back(0); // 25:ADAS_enum_TrafficLightColor(int)
                                          vecValue.push_back(0); // 26:ADAS_val_TrafficLightCountdownNumber(int)
                                          vecValue.push_back(0); // 27:ADAS_enum_TrafficLightType(int)
                                          vecValue.push_back(sample->ADAS_strt_DrivingCommon.DrivingCommonTOR);
                                          vecValue.push_back(sample->NtfACCInfo_Drvg.CruiseAccelerateSts);
                                          vecValue.push_back(sample->NtfALCInfo_Drvg.ALCStatus);
                                          vecValue.push_back(sample->NtfHNOPfunctionInfo_Drvg.NOPQuitReason);
                                          vecValue.push_back(sample->NtfHNOPfunctionInfo_Drvg.NOPLaneChangeMod);
                                          vecValue.push_back(sample->NtfHNOPfunctionInfo_Drvg.NOPLaneChangeReason);
                                          vecValue.push_back(sample->NtfHNOPfunctionInfo_Drvg.NOPLaneChangeInfo);
                                          vecValue.push_back(sample->NtfHNOPfunctionInfo_Drvg.NOPStReminder);
                                          vecValue.push_back(sample->NtfHNOPfunctionInfo_Drvg.HNOPScenariosReminder);
                                          vecValue.push_back(sample->NtfHNOPfunctionInfo_Drvg.NOPDegradeReminder);
                                          vecValue.push_back(sample->NtfIntelligentEvasionInfo_Drvg.iLOATextInfo);
                                          vecValue.push_back(sample->NtfACCInfo_Drvg.FDMDist);
                                          vecValue.push_back(sample->ADAS_strt_DrivingCommon.ODDDistanceQuitReason);
                                          vecValue.push_back(e2eRes);

                                          if (handler_)
                                          {
                                              SomeipPropRxData data("AB02_8101", std::move(vecValue));
                                              handler_(data);
                                          }

                                          std::vector<SomeipPropRxValue> vecLssValue;
                                          vecLssValue.push_back(sample->NtfLSSInfo_Drvg.LKSSts);
                                          vecLssValue.push_back(sample->NtfLSSInfo_Drvg.LKSMod);
                                          vecLssValue.push_back(sample->NtfLSSInfo_Drvg.ELKSts);
                                          vecLssValue.push_back(sample->NtfLSSInfo_Drvg.LKSLeftTrackingSt);
                                          vecLssValue.push_back(sample->NtfLSSInfo_Drvg.LKSRightTrackingSt);
                                          vecLssValue.push_back(sample->NtfLSSInfo_Drvg.ELKLeftActiveSt);
                                          vecLssValue.push_back(sample->NtfLSSInfo_Drvg.ELKRightActiveSt);
                                          vecLssValue.push_back(sample->NtfLSSInfo_Drvg.LKSwarning);
                                          vecLssValue.push_back(e2eRes);
                                          if (handler_)
                                          {
                                              SomeipPropRxData data("LSS_INFO", std::move(vecLssValue));
                                              handler_(data);
                                          }

                                          std::vector<SomeipPropRxValue> veDriComValue;
                                          veDriComValue.push_back(sample->ADAS_strt_DrivingCommon.DrivingCommonTOR);
                                          veDriComValue.push_back(sample->ADAS_strt_DrivingCommon.DrivingCommonSnG);
                                          veDriComValue.push_back(sample->ADAS_strt_DrivingCommon.VoiceRemindSettingModeFb);
                                          veDriComValue.push_back(static_cast<double>(sample->ADAS_strt_DrivingCommon.DrivingDistoEgoCenter));
                                          veDriComValue.push_back(sample->ADAS_strt_DrivingCommon.LKSwarningModFb);
                                          veDriComValue.push_back(sample->ADAS_strt_DrivingCommon.FCTADirection);
                                          veDriComValue.push_back(sample->ADAS_strt_DrivingCommon.RCTADirection);
                                          veDriComValue.push_back(sample->ADAS_strt_DrivingCommon.DistoIntersection);
                                          veDriComValue.push_back(sample->ADAS_strt_DrivingCommon.ODDDistanceQuitReason);
                                          veDriComValue.push_back(sample->ADAS_strt_DrivingCommon.DrivingPlanStopline);
                                          veDriComValue.push_back(static_cast<double>(sample->ADAS_strt_DrivingCommon.DistoNavChangePoint));
                                          veDriComValue.push_back(sample->ADAS_strt_DrivingCommon.SensorFailureSts);
                                          veDriComValue.push_back(sample->ADAS_strt_DrivingCommon.VLMReminder);
                                          veDriComValue.push_back(e2eRes);
                                          if (handler_)
                                          {
                                              SomeipPropRxData data("DRIVING_COMMON_INFO", std::move(veDriComValue));
                                              handler_(data);
                                          }

                                          std::vector<SomeipPropRxValue> veALCValue;
                                          veALCValue.push_back(sample->NtfALCInfo_Drvg.ALCStatus);
                                          veALCValue.push_back(sample->NtfALCInfo_Drvg.ALCDirIndicator);
                                          veALCValue.push_back(sample->NtfALCInfo_Drvg.ALCAbortReason);
                                          veALCValue.push_back(sample->NtfALCInfo_Drvg.ALCSoundWarn);
                                          veALCValue.push_back(sample->NtfALCInfo_Drvg.ALCTextInfomation);
                                          veALCValue.push_back(sample->NtfALCInfo_Drvg.ALCTakeOverRequest);
                                          veALCValue.push_back(sample->NtfALCInfo_Drvg.ALCPermitSt);
                                          veALCValue.push_back(e2eRes);

                                          if (handler_)
                                          {
                                              SomeipPropRxData data("ALC_INFO", std::move(veALCValue));
                                              handler_(data);
                                          }


                                        LOG_DEBUG() << "NtfDrivingLvl12_Drvg e2e result: {"
                                            << static_cast<std::uint8_t>(sample.GetProfileCheckStatus()) << "}";
                                        // close
                                        // LOG_DEBUG() << " NtfDrivingLvl12_Drvg event's data: {{"
                                        //     << sample->NtfTrafficSign_Drvg.TSRSt << ", "
                                        //     << sample->NtfTrafficSign_Drvg.TrafficSignId << ", "
                                        //     << sample->NtfTrafficSign_Drvg.TrafficSignType << ", "
                                        //     << sample->NtfTrafficSign_Drvg.TrafficSignPosX << ", "
                                        //     << sample->NtfTrafficSign_Drvg.TrafficSignPosY << ", "
                                        //     << sample->NtfTrafficSign_Drvg.TrafficSignPosZ << "}, {"
                                        //     << sample->NtfISLIInfo_Drvg.ISLISt << ", "
                                        //     << sample->NtfISLIInfo_Drvg.ISLISpeedLimitSign << ", "
                                        //     << sample->NtfISLIInfo_Drvg.ISLISpeedLimitUnit << ", "
                                        //     << sample->NtfISLIInfo_Drvg.ISLIOverSpeedWarn << ", "
                                        //     << sample->NtfISLIInfo_Drvg.ISLIWarningModFb << ", "
                                        //     << sample->NtfISLIInfo_Drvg.ISLISpdLimType << ", "
                                        //     << sample->NtfISLIInfo_Drvg.ISLICruiseSpdAbsOffsetFb << ", "
                                        //     << sample->NtfISLIInfo_Drvg.ISLICruiseSpdRelOffsetFb << "}}";
                                    });
                            }
                        });

                    adccdrivingfuncinfo_proxy_->NtfDrivingSafetyFuncInfo_Drvg
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive NtfDrivingSafetyFuncInfo_Drvg event";
                            if (timeout) {
                                LOG_INFO() << " NtfDrivingSafetyFuncInfo_Drvg event's data: { Timeout }";
                                if(handler_)
                                {
                                    SomeipPropRxData data_AB02_8102("AB02_8102", false);
                                    handler_(data_AB02_8102);

                                    SomeipPropRxData data_DRIVING_SAFETY_INFO("DRIVING_SAFETY_INFO", false);
                                    handler_(data_DRIVING_SAFETY_INFO);
                              }
                            } else {
                                adccdrivingfuncinfo_proxy_->NtfDrivingSafetyFuncInfo_Drvg.GetNewSamples(
                                    [this](proxy::events::NtfDrivingSafetyFuncInfo_Drvg::
                                        EventSamplePtr sample) {

                                          uint8_t e2eRes = static_cast<uint8_t>(sample.GetProfileCheckStatus());
                                          if (0 != e2eRes)
                                          {
                                              LOG_TAG_WARNING("AB02_8102 e2e_check_fail errno:%d ", e2eRes);
                                              // return;
                                          }

                                          std::vector<SomeipPropRxValue> vecValue;
                                          vecValue.push_back(sample->ActiveSafetyFcnInfo_Drvg.FcwStatus);
                                          vecValue.push_back(sample->ActiveSafetyFcnInfo_Drvg.AebStatus);
                                          vecValue.push_back(sample->ActiveSafetyFcnInfo_Drvg.FCWSettingStsFB);
                                          vecValue.push_back(sample->NtfESSInfo_Drvg.ESSSts);
                                          vecValue.push_back(sample->NtfESSInfo_Drvg.ESSDirection);
                                          vecValue.push_back(sample->NtfESSInfo_Drvg.AESSts);
                                          vecValue.push_back(sample->NtfESSInfo_Drvg.AESDirection);
                                          vecValue.push_back(e2eRes);

                                          if (handler_)
                                          {
                                              SomeipPropRxData data("AB02_8102", std::move(vecValue));
                                              handler_(data);
                                          }

                                          std::vector<SomeipPropRxValue> vecSafetyValue;
                                          vecSafetyValue.push_back(sample->ActiveSafetyFcnInfo_Drvg.FcwStatus);
                                          vecSafetyValue.push_back(sample->ActiveSafetyFcnInfo_Drvg.AebStatus);
                                          vecSafetyValue.push_back(sample->ActiveSafetyFcnInfo_Drvg.FctaSts);
                                          vecSafetyValue.push_back(sample->ActiveSafetyFcnInfo_Drvg.RctaSts);
                                          vecSafetyValue.push_back(sample->ActiveSafetyFcnInfo_Drvg.FCWSettingStsFB);
                                          vecSafetyValue.push_back(sample->ActiveSafetyFcnInfo_Drvg.LDWSensitivityFB);
                                          vecSafetyValue.push_back(e2eRes);
                                          if (handler_)
                                          {
                                              SomeipPropRxData data("DRIVING_SAFETY_INFO", std::move(vecSafetyValue));
                                              handler_(data);
                                          }


                                        LOG_DEBUG() << "NtfDrivingSafetyFuncInfo_Drvg e2e result: {"
                                            << static_cast<std::uint8_t>(sample.GetProfileCheckStatus()) << "}";
                                        // close
                                        // LOG_DEBUG() << " NtfDrivingSafetyFuncInfo_Drvg event's data: {{"
                                        //     << sample->NtfESSInfo_Drvg.ESSSts << ", "
                                        //     << sample->NtfESSInfo_Drvg.ESSDirection << ", "
                                        //     << sample->NtfESSInfo_Drvg.AESSts << ", "
                                        //     << sample->NtfESSInfo_Drvg.AESDirection << "}, {"
                                        //     << sample->ActiveSafetyFcnInfo_Drvg.FcwStatus << ", "
                                        //     << sample->ActiveSafetyFcnInfo_Drvg.AebStatus << ", "
                                        //     << sample->ActiveSafetyFcnInfo_Drvg.FctaSts << ", "
                                        //     << sample->ActiveSafetyFcnInfo_Drvg.RctaSts << ", "
                                        //     << sample->ActiveSafetyFcnInfo_Drvg.FCWSettingStsFB << ", "
                                        //     << sample->ActiveSafetyFcnInfo_Drvg.LDWSensitivityFB << ", {"
                                        //     << sample->ActiveSafetyFcnInfo_Drvg.VehicleEgoPose.VehiclePitchTowardsHorizontal << ", "
                                        //     << sample->ActiveSafetyFcnInfo_Drvg.VehicleEgoPose.VehicleRollTowardsHorizontal << ", "
                                        //     << sample->ActiveSafetyFcnInfo_Drvg.VehicleEgoPose.VehiclePoseTimestamp << "}, "
                                        //     << sample->ActiveSafetyFcnInfo_Drvg.CurRoadSurfaceTexture << ", "
                                        //     << (sample->ActiveSafetyFcnInfo_Drvg.ADAS_strt_ActiveSafetyFcnInfo_Reserved)[0] << "}, "
                                        //     << (sample->ADAS_strt_DrivingSafetyFuncInfo_Reserved)[0] << "}";
                                    });
                            }
                        });

                    adccdrivingfuncinfo_proxy_->NtfDrivingLvl2Plus_Drvg
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive NtfDrivingLvl2Plus_Drvg event";
                            if (timeout) {
                                LOG_INFO() << " NtfDrivingLvl2Plus_Drvg event's data: { Timeout }";
                                if(handler_)
                                {
                                    SomeipPropRxData data_AB02_8103("AB02_8103", false);
                                    handler_(data_AB02_8103);

                                    SomeipPropRxData data_TRAFFIC_LIGHT_MSG("TRAFFIC_LIGHT_MSG", false);
                                    handler_(data_TRAFFIC_LIGHT_MSG);

                                    SomeipPropRxData data_CNOA_INFO("CNOA_INFO", false);
                                    handler_(data_CNOA_INFO);
                                }
                            } else {
                                adccdrivingfuncinfo_proxy_->NtfDrivingLvl2Plus_Drvg.GetNewSamples(
                                    [this](proxy::events::NtfDrivingLvl2Plus_Drvg::
                                        EventSamplePtr sample) {

                                          std::vector<SomeipPropRxValue> vecValue;
                                          vecValue.push_back(sample->NtfHodDmsInfo_Drvg.DriverhandsoffWarning);
                                          vecValue.push_back(0);

                                          if (handler_)
                                          {
                                              SomeipPropRxData data("AB02_8103", std::move(vecValue));
                                              handler_(data);
                                          }


                                          if (!(sample->TrafficLight.empty()))
                                          {
                                              std::vector<SomeipPropRxValue> TrafficLightMsg;

                                              for (auto it : sample->TrafficLight)
                                              {
                                                  TrafficLightMsg.push_back(it.trafficLightType);
                                                  TrafficLightMsg.push_back(it.trafficLightColor);
                                                  TrafficLightMsg.push_back(it.remainingTime);
                                              }
                                              TrafficLightMsg.push_back(0);
                                              if (handler_)
                                              {
                                                  SomeipPropRxData data("TRAFFIC_LIGHT_MSG", std::move(TrafficLightMsg));
                                                  handler_(data);
                                              }
                                          }

                                          std::vector<SomeipPropRxValue> vecCNOAinfoValue;
                                          vecCNOAinfoValue.push_back(sample->CNOAinfo.CityNOASts);
                                          vecCNOAinfoValue.push_back(sample->CNOAinfo.CNOAOperateReminder);
                                          vecCNOAinfoValue.push_back(sample->CNOAinfo.CNOATrainingReminder);
                                          vecCNOAinfoValue.push_back(sample->CNOAinfo.CNOARepalyReminder);
                                          vecCNOAinfoValue.push_back(0);
                                          if (handler_)
                                          {
                                              SomeipPropRxData data("CNOA_INFO", std::move(vecCNOAinfoValue));
                                              handler_(data);
                                          }


                                        // LOG_DEBUG() << " NtfDrivingLvl2Plus_Drvg event's data: {{"
                                        //     << sample->CityNOAFcnInfo_Drvg.CityNOASts << ", "
                                        //     << (sample->CityNOAFcnInfo_Drvg.ADAS_strt_CityNOAFcnInfo_Reserved)[0] << "}, {"
                                        //     << sample->NtfHodDmsInfo_Drvg.DriverhandsoffWarning << ", "
                                        //     << sample->NtfHodDmsInfo_Drvg.DMSWarning << ", "
                                        //     << sample->NtfHodDmsInfo_Drvg.HandsOnReq << ", "
                                        //     << sample->NtfHodDmsInfo_Drvg.ICAplussetting << "}, "
                                        //     << (sample->ADAS_strt_DrivingLvl2Plus_Reserved)[0] << "}";
                                    });
                            }
                        });

                    adccdrivingfuncinfo_proxy_->CNOARepalyPathInfo
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive CNOARepalyPathInfo event";
                            if (timeout) {
                                LOG_INFO() << " CNOARepalyPathInfo event's data: { Timeout }";
                            } else {
                                adccdrivingfuncinfo_proxy_->CNOARepalyPathInfo.GetNewSamples(
                                    [this](proxy::events::CNOARepalyPathInfo::
                                        EventSamplePtr sample) {
                                        // LOG_DEBUG() << " CNOARepalyPathInfo event's data: {"
                                        //     << sample->CNOAReplayPathID << ", "
                                        //     << sample->CNOAReplayPathName << ", "
                                        //     << sample->CNOAReplayPathEndName << ", "
                                        //     << sample->CNOAReplayDrivingDist << ", "
                                        //     << sample->CNOARepayRemainDist << ", "
                                        //     << sample->CNOAReplayPathLabel << ", "
                                        //     << sample->CNOAReplayTurntype << ", ..."
                                        //     << sample->CNOAReplayTurntypeDist << ", "
                                        //     << sample->CNOAReplayOffRoute << ", "
                                        //     << sample->CNOAReplayPercent << "}";
                                    });
                            }
                        });

                    adccdrivingfuncinfo_proxy_->CNOATrainingPathInfo
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive CNOATrainingPathInfo event";
                            if (timeout) {
                                LOG_INFO() << " CNOATrainingPathInfo event's data: { Timeout }";
                                if(handler_)
                                {
                                    SomeipPropRxData data_CNOA_TRAININGPATH_INFO("CNOA_TRAININGPATH_INFO", false);
                                    handler_(data_CNOA_TRAININGPATH_INFO);
                                }
                            } else {
                                adccdrivingfuncinfo_proxy_->CNOATrainingPathInfo.GetNewSamples(
                                    [this](proxy::events::CNOATrainingPathInfo::
                                        EventSamplePtr sample) {

                                        std::vector<SomeipPropRxValue> vecValue;
                                        vecValue.push_back(sample->CNOATrainingPathLength);
                                        if (handler_)
                                        {
                                            SomeipPropRxData data("CNOA_TRAININGPATH_INFO", std::move(vecValue));
                                            handler_(data);
                                        }
                                        // close
                                        // LOG_DEBUG() << " CNOATrainingPathInfo event's data: {"
                                        //     << sample->CNOATrainingPathID << ", "
                                        //     << sample->CNOATrainingPathName << ", "
                                        //     << sample->CNOATrainingPathCurrentDist << ", "
                                        //     << sample->CNOATrainingPathLength << ", "
                                        //     << sample->CNOATrainingisReady << ", "
                                        //     << sample->CNOATrainingLabel << "}";
                                    });
                            }
                        });

                    adccdrivingfuncinfo_proxy_->NtfCNOAPathDetail
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive NtfCNOAPathDetail event";
                            if (timeout) {
                                LOG_INFO() << " NtfCNOAPathDetail event's data: { Timeout }";
                            } else {
                                adccdrivingfuncinfo_proxy_->NtfCNOAPathDetail.GetNewSamples(
                                    [this](proxy::events::NtfCNOAPathDetail::
                                        EventSamplePtr sample) {
                                        if (!(*sample).empty()) {
                                            // LOG_DEBUG() << " NtfCNOAPathDetail event's data: {"
                                            //            << (*sample)[0].CNOAPathID << ", "
                                            //            << (*sample)[0].CNOAPathName << ", "
                                            //            << (*sample)[0].CNOAPathStartName << ", "
                                            //            << (*sample)[0].CNOAPathEndName << ", "
                                            //            << (*sample)[0].CNOAPathLength << ", "
                                            //            << (*sample)[0].CNOAPathLabel << ", "
                                            //            << (*sample)[0].CNOAPathRank << "}";
                                        } else {
                                            LOG_DEBUG() << " NtfCNOAPathDetail event's data: { empty }";
                                        }
                                    });
                            }
                        });

                    adccdrivingfuncinfo_proxy_->NtfOffRoadPathDetail
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive NtfOffRoadPathDetail event";
                            if (timeout) {
                                LOG_INFO() << " NtfOffRoadPathDetail event's data: { Timeout }";
                            } else {
                                adccdrivingfuncinfo_proxy_->NtfOffRoadPathDetail.GetNewSamples(
                                    [this](proxy::events::NtfOffRoadPathDetail::
                                        EventSamplePtr sample) {
                                        if (!(*sample).empty()) {
                                            // LOG_DEBUG() << " NtfOffRoadPathDetail event's data: {"
                                            //            << (*sample)[0].OffRoadPathID << ", "
                                            //            << (*sample)[0].OffRoadPathName << ", "
                                            //            << (*sample)[0].OffRoadPathStartName << ", "
                                            //            << (*sample)[0].OffRoadPathEndName << ", "
                                            //            << (*sample)[0].OffRoadPathLength << ", "
                                            //            << (*sample)[0].OffRoadPathLabel << ", "
                                            //            << (*sample)[0].OffRoadPathRank << ", "
                                            //            << (*sample)[0].OffRoadPathAvailable << "}";
                                        } else {
                                            LOG_DEBUG() << " NtfOffRoadPathDetail event's data: { empty }";
                                        }
                                    });
                            }
                        });

                    adccdrivingfuncinfo_proxy_->NtfOffRoadTrainingPathInfo
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive NtfOffRoadTrainingPathInfo event";
                            if (timeout) {
                                LOG_INFO() << " NtfOffRoadTrainingPathInfo event's data: { Timeout }";
                            } else {
                                adccdrivingfuncinfo_proxy_->NtfOffRoadTrainingPathInfo.GetNewSamples(
                                    [this](proxy::events::NtfOffRoadTrainingPathInfo::
                                        EventSamplePtr sample) {
                                        // LOG_DEBUG() << " NtfOffRoadTrainingPathInfo event's data: {"
                                        //     << sample->OffRoadTrainingPathID << ", "
                                        //     << sample->OffRoadTrainingPathName << ", "
                                        //     << sample->OffRoadTrainingPathCurrentDist << ", "
                                        //     << sample->OffRoadTrainingPathLength << ", "
                                        //     << sample->OffRoadTrainingisReady << ", "
                                        //     << sample->OffRoadTrainingLabel << "}";
                                    });
                            }
                        });

                    adccdrivingfuncinfo_proxy_->NtfOffRoadRepalyPathInfo
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive NtfOffRoadRepalyPathInfo event";
                            if (timeout) {
                                LOG_INFO() << " NtfOffRoadRepalyPathInfo event's data: { Timeout }";
                            } else {
                                adccdrivingfuncinfo_proxy_->NtfOffRoadRepalyPathInfo.GetNewSamples(
                                    [this](proxy::events::NtfOffRoadRepalyPathInfo::
                                        EventSamplePtr sample) {
                                        // LOG_DEBUG() << " NtfOffRoadRepalyPathInfo event's data: {"
                                        //     << sample->OffRoadReplayPathID << ", "
                                        //     << sample->OffRoadReplayPathName << ", "
                                        //     << sample->OffRoadReplayPathEndName << ", "
                                        //     << sample->OffRoadReplayDrivingDist << ", "
                                        //     << sample->OffRoadRepayRemainDist << ", "
                                        //     << sample->OffRoadReplayPathLabel << ", "
                                        //     << sample->OffRoadReplayTurntype << ", "
                                        //     << sample->OffRoadReplayTurntypeDist << ", "
                                        //     << sample->OffRoadReplayOffRoute << ", "
                                        //     << sample->OffRoadReplayPercent << "}";
                                    });
                            }
                        });

                    auto result = adccdrivingfuncinfo_proxy_->NtfDrivingLvl12_Drvg.Subscribe(1);
                    if (!result) {
                    LOG_WARN() << "Subscribe NtfDrivingLvl12_Drvg failed, error code"
                                << result.Error();
                    }

                    result = adccdrivingfuncinfo_proxy_->NtfDrivingSafetyFuncInfo_Drvg.Subscribe(1);
                    if (!result) {
                    LOG_WARN() << "Subscribe NtfDrivingSafetyFuncInfo_Drvg failed, error code"
                                << result.Error();
                    }

                    result = adccdrivingfuncinfo_proxy_->NtfDrivingLvl2Plus_Drvg.Subscribe(1);
                    if (!result) {
                    LOG_WARN() << "Subscribe NtfDrivingLvl2Plus_Drvg failed, error code"
                                << result.Error();
                    }

                    result = adccdrivingfuncinfo_proxy_->CNOARepalyPathInfo.Subscribe(1);
                    if (!result) {
                    LOG_WARN() << "Subscribe CNOARepalyPathInfo failed, error code"
                                << result.Error();
                    }

                    result = adccdrivingfuncinfo_proxy_->CNOATrainingPathInfo.Subscribe(1);
                    if (!result) {
                    LOG_WARN() << "Subscribe CNOATrainingPathInfo failed, error code"
                                << result.Error();
                    }

                    result = adccdrivingfuncinfo_proxy_->NtfCNOAPathDetail.Subscribe(1);
                    if (!result) {
                    LOG_WARN() << "Subscribe NtfCNOAPathDetail failed, error code"
                                << result.Error();
                    }

                    result = adccdrivingfuncinfo_proxy_->NtfOffRoadPathDetail.Subscribe(1);
                    if (!result) {
                    LOG_WARN() << "Subscribe NtfOffRoadPathDetail failed, error code"
                                << result.Error();
                    }

                    result = adccdrivingfuncinfo_proxy_->NtfOffRoadTrainingPathInfo.Subscribe(1);
                    if (!result) {
                    LOG_WARN() << "Subscribe NtfOffRoadTrainingPathInfo failed, error code"
                                << result.Error();
                    }

                    result = adccdrivingfuncinfo_proxy_->NtfOffRoadRepalyPathInfo.Subscribe(1);
                    if (!result) {
                    LOG_WARN() << "Subscribe NtfOffRoadRepalyPathInfo failed, error code"
                                << result.Error();
                    }
                }
                  if (handler_)
                  {
                      SomeipPropRxData data("DRIVING_INFO_STOP_OFFER", 0);
                      handler_(data);
                  }
            } else {
                if (handler_)
                {
                    SomeipPropRxData data("DRIVING_INFO_STOP_OFFER", 1);
                    handler_(data);
                }
              LOG_INFO() << "************REMOTE-STOP-OFFER************";
            }
        },
        is_);

      LOG_TAG_WARNING("AB02_8101 02 03 done ");
}

} // namespace adcc_drivingfuncinfo
} // namespace ns