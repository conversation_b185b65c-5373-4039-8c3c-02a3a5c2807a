///
/// @copyright  This software is the property of HiRain Technologies.
///             Any information contained in this doc should not be reproduced,
///             or used, or disclosed without the written authorization from
///             HiRain Technologies.
///
/// @file       ADCCSensorInfo_proxy_impl.cc
/// @brief
///
/// <AUTHOR> Hongyang <<EMAIL>>
///
///
#include "ADCCSensorInfo_proxy_impl.h"

#include "ara/core/future.h"
#include "ara/core/instance_specifier.h"
#include "ara/core/io/event_loop_manager.h"
#include "ara/core/io/timer.h"
#include <ara/core/runtime/timer.h>

#include <stdexcept>
#include <string>

#define LOG_FATAL() ARA_FATAL(*logger_)
#define LOG_ERROR() ARA_ERROR(*logger_)
#define LOG_WARN() ARA_WARN(*logger_)
#define LOG_INFO() ARA_INFO(*logger_)
#define LOG_DEBUG() ARA_DEBUG(*logger_)
#define LOG_VERBOSE() ARA_VERBOSE(*logger_)

namespace ns{
namespace adcc_sensorinfo {
ADCCSensorInfo_proxy_impl::ADCCSensorInfo_proxy_impl(ara::core::io::EventLoop *loop,
                             ara::core::InstanceSpecifier is)
    : logger_(&ara::log::CreateLogger("ICCA", "")), loop_(loop), is_(is) {

}

ADCCSensorInfo_proxy_impl::~ADCCSensorInfo_proxy_impl() {

}

void ADCCSensorInfo_proxy_impl::RegisterValueHandel(SomeipPropDataHandler handler)
{
    handler_ = handler;
}

void ADCCSensorInfo_proxy_impl::Start()
{
    ServiceActive();
    return;
}

void ADCCSensorInfo_proxy_impl::Stop()
{
    proxy::ADCC_SensorInfoProxy::StopFindService(adccsensorinfo_finder_);
    return;
}

void ADCCSensorInfo_proxy_impl::ServiceActive() {
    LOG_INFO() << "ServiceActive is called.";
    using proxy::ADCC_SensorInfoProxy;
    adccsensorinfo_finder_ = ADCC_SensorInfoProxy::StartFindService(
        [this](ara::com::ServiceHandleContainer<ADCC_SensorInfoProxy::HandleType>
            handles, ara::com::FindServiceHandle) {
            if (!handles.empty()) {
                if (!adccsensorinfo_proxy_) {
                    LOG_INFO() << "Found a service instance"
                               << handles[0].GetInstanceId().ToString();

                    adccsensorinfo_proxy_ =
                        std::make_shared<ADCC_SensorInfoProxy>(handles[0]);

                    adccsensorinfo_proxy_->NtfSensorStAll
                        .SetSubscriptionStateChangeHandler(
                            [this](ara::com::SubscriptionState state) {
                                LOG_INFO() << "The NtfSensorStAll event's state"
                                           << static_cast<int>(state);
                            });

                    adccsensorinfo_proxy_->NtfSensorStAll
                        .SetReceiveHandler([this](bool timeout) {
                            LOG_INFO() << "Receive NtfSensorStAll event";
                            if (timeout) {
                                LOG_INFO() << " NtfSensorStAll event's data: { Timeout }";
                                if(handler_)
                                {
                                    SomeipPropRxData data_AB01_8010("AB01_8010", false);
                                    handler_(data_AB01_8010);
                                }
                            } else {
                                adccsensorinfo_proxy_->NtfSensorStAll.GetNewSamples(
                                    [this](proxy::events::NtfSensorStAll::
                                        EventSamplePtr sample) {

                                          std::vector<SomeipPropRxValue> vecValue;
                                          vecValue.push_back(sample->SensorWorkingSts.ADASSysSts);
                                          vecValue.push_back(sample->SensorWorkingSts.FLCameraSysSts);
                                          vecValue.push_back(sample->SensorWorkingSts.FLCameraCalSts);
                                          vecValue.push_back(sample->SensorWorkingSts.FRCameraSysSts);
                                          vecValue.push_back(sample->SensorWorkingSts.FRCameraCalSts);
                                          vecValue.push_back(sample->SensorWorkingSts.RLCameraSysSts);
                                          vecValue.push_back(sample->SensorWorkingSts.RLCameraCalSts);
                                          vecValue.push_back(sample->SensorWorkingSts.RRCameraSysSts);
                                          vecValue.push_back(sample->SensorWorkingSts.RRCameraCalSts);
                                          vecValue.push_back(sample->SensorWorkingSts.RearCameraSysSts);
                                          vecValue.push_back(sample->SensorWorkingSts.RearCameraCalSts);
                                          vecValue.push_back(sample->SensorWorkingSts.FrontFarCameraSysSts);
                                          vecValue.push_back(sample->SensorWorkingSts.FrontFarCameraCalSts);
                                          vecValue.push_back(sample->SensorWorkingSts.FrontNearCameraSysSts);
                                          vecValue.push_back(sample->SensorWorkingSts.FrontNearCameraCalSts);
                                          vecValue.push_back(sample->SensorWorkingSts.MainLidarSysSts);
                                          vecValue.push_back(sample->SensorWorkingSts.MainLidarCalSts);
                                          vecValue.push_back(sample->SensorWorkingSts.FLLidarSysSts);
                                          vecValue.push_back(sample->SensorWorkingSts.FLLidarCalSts);
                                          vecValue.push_back(sample->SensorWorkingSts.FRLidarSysSts);
                                          vecValue.push_back(sample->SensorWorkingSts.FRLidarCalSts);
                                          vecValue.push_back(sample->SensorWorkingSts.SVSFrontCameraSysSts);
                                          vecValue.push_back(sample->SensorWorkingSts.SVSLeftCameraSysSts);
                                          vecValue.push_back(sample->SensorWorkingSts.SVSRightCameraSysSts);
                                          vecValue.push_back(sample->SensorWorkingSts.SVSRearCameraSysSts);
                                          vecValue.push_back(sample->SensorWorkingSts.SVSFrontCameraCalSts);
                                          vecValue.push_back(sample->SensorWorkingSts.SVSLeftCameraCalSts);
                                          vecValue.push_back(sample->SensorWorkingSts.SVSRightCameraCalSts);
                                          vecValue.push_back(sample->SensorWorkingSts.SVSRearCameraCalSts);
                                          vecValue.push_back(sample->RadarWorkingSts.FrontRadarCalSts);
                                          vecValue.push_back(sample->RadarWorkingSts.FrontLeftRadarCalSts);
                                          vecValue.push_back(sample->RadarWorkingSts.FrontRightRadarCalSts);
                                          vecValue.push_back(sample->RadarWorkingSts.RearLeftRadarCalSts);
                                          vecValue.push_back(sample->RadarWorkingSts.RearRightRadarCalSts);
                                          vecValue.push_back(0);

                                          if (handler_)
                                          {
                                              SomeipPropRxData data("AB01_8010", std::move(vecValue));
                                              handler_(data);
                                          }
                                        // LOG_DEBUG() << " NtfSensorStAll event's data: {{"
                                        //            << sample->SensorWorkingSts.ADASSysSts << ", "
                                        //            << sample->SensorWorkingSts.FLCameraSysSts << ", "
                                        //            << sample->SensorWorkingSts.FLCameraCalSts << ","
                                        //            << sample->SensorWorkingSts.FRCameraSysSts << ","
                                        //            << sample->SensorWorkingSts.FRCameraCalSts << ","
                                        //            << sample->SensorWorkingSts.RLCameraSysSts << ","
                                        //            << sample->SensorWorkingSts.RLCameraCalSts << ","
                                        //            << sample->SensorWorkingSts.RRCameraSysSts << ","
                                        //            << sample->SensorWorkingSts.RRCameraCalSts << ","
                                        //            << sample->SensorWorkingSts.RearCameraSysSts << ","
                                        //            << sample->SensorWorkingSts.RearCameraCalSts << ","
                                        //            << sample->SensorWorkingSts.FrontFarCameraSysSts << ","
                                        //            << sample->SensorWorkingSts.FrontFarCameraCalSts << ","
                                        //            << sample->SensorWorkingSts.FrontNearCameraSysSts << ","
                                        //            << sample->SensorWorkingSts.FrontNearCameraCalSts << ","
                                        //            << sample->SensorWorkingSts.MainLidarSysSts << ","
                                        //            << sample->SensorWorkingSts.MainLidarCalSts << ","
                                        //            << sample->SensorWorkingSts.FLLidarSysSts << ","
                                        //            << sample->SensorWorkingSts.FLLidarCalSts << ","
                                        //            << sample->SensorWorkingSts.FRLidarSysSts << ","
                                        //            << sample->SensorWorkingSts.FRLidarCalSts << ","
                                        //            << sample->SensorWorkingSts.SVSFrontCameraSysSts << ","
                                        //            << sample->SensorWorkingSts.SVSLeftCameraSysSts << ","
                                        //            << sample->SensorWorkingSts.SVSRightCameraSysSts << ","
                                        //            << sample->SensorWorkingSts.SVSRearCameraSysSts << ","
                                        //            << sample->SensorWorkingSts.SVSFrontCameraCalSts << ","
                                        //            << sample->SensorWorkingSts.SVSLeftCameraCalSts << ","
                                        //            << sample->SensorWorkingSts.SVSRightCameraCalSts << ","
                                        //            << sample->SensorWorkingSts.SVSRearCameraCalSts << "}, {"
                                        //            << sample->RadarWorkingSts.FrontRadarCalSts << ","
                                        //            << sample->RadarWorkingSts.FrontLeftRadarCalSts << ","
                                        //            << sample->RadarWorkingSts.FrontRightRadarCalSts << ","
                                        //            << sample->RadarWorkingSts.RearLeftRadarCalSts << ","
                                        //            << sample->RadarWorkingSts.RearRightRadarCalSts << "}}";
                                    });
                            }
                        });

                    auto result = adccsensorinfo_proxy_->NtfSensorStAll.Subscribe(1);
                    if (!result) {
                        LOG_WARN() << "Subscribe NtfSensorStAll failed, error code"
                                   << result.Error();
                    }
                }
                    if (handler_)
                    {
                        SomeipPropRxData data("SENSOR_INFO_STOP_OFFER", 0);
                        handler_(data);
                    }
                } else {
                    if (handler_)
                    {
                        SomeipPropRxData data("SENSOR_INFO_STOP_OFFER", 1);
                        handler_(data);
                    }
                LOG_DEBUG() << "************REMOTE-STOP-OFFER************";
                }
            },
            is_);
}

} // namespace adcc_sensorinfons
}