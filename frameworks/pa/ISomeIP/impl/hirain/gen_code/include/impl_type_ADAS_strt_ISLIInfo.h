///
/// @file       impl_type_ADAS_strt_ISLIInfo.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_ISLIINFO_H_
#define IMPL_TYPE_ADAS_STRT_ISLIINFO_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_enum_ISLISt.h"
#include "impl_type_ADAS_enum_ISLISpeedLimitSign.h"
#include "impl_type_ADAS_enum_ISLISpeedLimitUnit.h"
#include "impl_type_ADAS_enum_ISLIOverSpeedWarn.h"
#include "impl_type_ADAS_enum_ISLIWarningMod.h"
#include "impl_type_ADAS_enum_ISLISpdLimType.h"
#include "impl_type_ADAS_val_SpdLimitAbsOffset.h"
#include "impl_type_ADAS_val_SpdLimitRelOffset.h"



struct ADAS_strt_ISLIInfo {
  ADAS_enum_ISLISt ISLISt;
  ADAS_enum_ISLISpeedLimitSign ISLISpeedLimitSign;
  ADAS_enum_ISLISpeedLimitUnit ISLISpeedLimitUnit;
  ADAS_enum_ISLIOverSpeedWarn ISLIOverSpeedWarn;
  ADAS_enum_ISLIWarningMod ISLIWarningModFb;
  ADAS_enum_ISLISpdLimType ISLISpdLimType;
  ADAS_val_SpdLimitAbsOffset ISLICruiseSpdAbsOffsetFb;
  ADAS_val_SpdLimitRelOffset ISLICruiseSpdRelOffsetFb;
};

#endif // IMPL_TYPE_ADAS_STRT_ISLIINFO_H_
