///
/// @file       impl_type_ADAS_strt_HPATrainingPathInfo.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_HPATRAININGPATHINFO_H_
#define IMPL_TYPE_ADAS_STRT_HPATRAININGPATHINFO_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_Gen_val_ID.h"
#include "impl_type_ADAS_string_PathName.h"
#include "impl_type_ADAS_val_ParkingDist.h"
#include "impl_type_ADAS_arr_PathPoint.h"
#include "impl_type_ADAS_arr_HPAPathslot.h"
#include "impl_type_ADAS_arr_HPASpeedBumpInfo.h"
#include "impl_type_ADAS_enum_PathLabel.h"



struct ADAS_strt_HPATrainingPathInfo {
  Gen_val_ID HpaTrainingPathID;
  ADAS_string_PathName TrainingPathName;
  ADAS_val_ParkingDist HPAtrainingPath<PERSON>urrentDist;
  ADAS_val_ParkingDist TrainingPathLength;
  ADAS_arr_PathPoint TrainingLinePoint;
  ADAS_arr_HPAPathslot HPATrainingPathSlotInfo;
  ADAS_arr_HPASpeedBumpInfo HPASpeedBumpInfo;
  ADAS_enum_PathLabel TrainingPathLabel;
};

#endif // IMPL_TYPE_ADAS_STRT_HPATRAININGPATHINFO_H_
