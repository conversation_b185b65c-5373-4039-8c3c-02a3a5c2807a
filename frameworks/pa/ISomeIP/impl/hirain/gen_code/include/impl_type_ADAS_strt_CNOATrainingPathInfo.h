///
/// @file       impl_type_ADAS_strt_CNOATrainingPathInfo.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_CNOATRAININGPATHINFO_H_
#define IMPL_TYPE_ADAS_STRT_CNOATRAININGPATHINFO_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_Gen_val_ID.h"
#include "impl_type_ADAS_string_PathName.h"
#include "impl_type_ADAS_val_CNOADist.h"
#include "impl_type_ADAS_arr_PathPoint.h"
#include "impl_type_ADAS_enum_CNOATrainReady.h"
#include "impl_type_ADAS_enum_PathLabel.h"



struct ADAS_strt_CNOATrainingPathInfo {
  Gen_val_ID CNOATrainingPathID;
  ADAS_string_PathName CNOATrainingPathName;
  ADAS_val_CNOADist CNOATrainingPathCurrentDist;
  ADAS_val_CNOADist CN<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>eng<PERSON>;
  ADAS_arr_PathPoint CNOATrainingLinePoint;
  ADAS_enum_CNOATrainReady CNOATrainingisReady;
  ADAS_enum_PathLabel CNOATrainingLabel;
};

#endif // IMPL_TYPE_ADAS_STRT_CNOATRAININGPATHINFO_H_
