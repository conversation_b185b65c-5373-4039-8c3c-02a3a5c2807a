///
/// @file       impl_type_ADAS_strt_CityNOAFcnInfo.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_CITYNOAFCNINFO_H_
#define IMPL_TYPE_ADAS_STRT_CITYNOAFCNINFO_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_enum_CityNOASts.h"
#include "impl_type_ADAS_strt_CityNOAFcnInfo_Reserved_Type.h"



struct ADAS_strt_CityNOAFcnInfo {
  ADAS_enum_CityNOASts CityNOASts;
  ADAS_strt_CityNOAFcnInfo_Reserved_Type ADAS_strt_CityNOAFcnInfo_Reserved;
};

#endif // IMPL_TYPE_ADAS_STRT_CITYNOAFCNINFO_H_
