///
/// @file       impl_type_ADAS_strt_DrivingSafetyFuncInfo.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_DRIVINGSAFETYFUNCINFO_H_
#define IMPL_TYPE_ADAS_STRT_DRIVINGSAFETYFUNCINFO_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_strt_ESSInfo.h"
#include "impl_type_ADAS_strt_DrivingSafetyFunctionInfo.h"
#include "impl_type_ADAS_strt_DrivingSafetyFuncInfo_Reserved_Type.h"



struct ADAS_strt_DrivingSafetyFuncInfo {
  ADAS_strt_ESSInfo NtfESSInfo_Drvg;
  ADAS_strt_DrivingSafetyFunctionInfo ActiveSafetyFcnInfo_Drvg;
  ADAS_strt_DrivingSafetyFuncInfo_Reserved_Type ADAS_strt_DrivingSafetyFuncInfo_Reserved;
};

#endif // IMPL_TYPE_ADAS_STRT_DRIVINGSAFETYFUNCINFO_H_
