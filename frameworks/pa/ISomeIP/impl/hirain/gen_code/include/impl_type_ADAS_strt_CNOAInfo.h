///
/// @file       impl_type_ADAS_strt_CNOAInfo.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_CNOAINFO_H_
#define IMPL_TYPE_ADAS_STRT_CNOAINFO_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_enum_CNOASts.h"
#include "impl_type_ADAS_enum_CNOAOperateReminder.h"
#include "impl_type_ADAS_enum_CNOATrainingReminder.h"
#include "impl_type_ADAS_enum_CNOARepalyReminder.h"



struct ADAS_strt_CNOAInfo {
  ADAS_enum_CNOASts CityNOASts;
  ADAS_enum_CNOAOperateReminder CNOAOperateReminder;
  ADAS_enum_CNOATrainingReminder CNOATrainingReminder;
  ADAS_enum_CNOARepalyReminder CNOARepalyReminder;
};

#endif // IMPL_TYPE_ADAS_STRT_CNOAINFO_H_
