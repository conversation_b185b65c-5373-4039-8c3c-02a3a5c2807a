///
/// @file       impl_type_ADAS_strt_HPAPathDetail_Ext.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_HPAPATHDETAIL_EXT_H_
#define IMPL_TYPE_ADAS_STRT_HPAPATHDETAIL_EXT_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_Gen_val_ID.h"
#include "impl_type_ADAS_enum_PathState.h"
#include "impl_type_ADAS_string_PathName.h"
#include "impl_type_ADAS_val_PathSaveProgress.h"
#include "impl_type_ADAS_val_ParkingDist.h"
#include "impl_type_ADAS_arr_PathPoint.h"
#include "impl_type_ADAS_enum_PathMatchSts.h"
#include "impl_type_ADAS_enum_PathLabel.h"



struct ADAS_strt_HPAPathDetail_Ext {
  Gen_val_ID HpaPathID;
  ADAS_enum_PathState PathState;
  ADAS_string_PathName PathName;
  ADAS_val_PathSaveProgress PathSaveProgress;
  ADAS_val_ParkingDist PathLength;
  ADAS_arr_PathPoint LinePoint;
  ADAS_enum_PathMatchSts PathMatchSts;
  ADAS_enum_PathLabel PathLabel;
  Gen_val_ID HPAPathRank;
};

#endif // IMPL_TYPE_ADAS_STRT_HPAPATHDETAIL_EXT_H_
