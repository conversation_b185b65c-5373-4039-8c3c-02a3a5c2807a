///
/// @file       impl_type_ADAS_strt_ACCInfo.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_ACCINFO_H_
#define IMPL_TYPE_ADAS_STRT_ACCINFO_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_enum_ACCSts.h"
#include "impl_type_ADAS_enum_AutoResumeNotifier.h"
#include "impl_type_ADAS_val_SetSpd.h"
#include "impl_type_ADAS_enum_SetSpdUnit.h"
#include "impl_type_ADAS_enum_TimeGapSet.h"
#include "impl_type_ADAS_enum_LongiTakeOverReq.h"
#include "impl_type_ADAS_enum_GoNotifier.h"
#include "impl_type_ADAS_enum_ACCTextMessage.h"
#include "impl_type_ADAS_enum_ACCQuitReason.h"
#include "impl_type_ADAS_enum_FSRACruiseSpdModFb.h"
#include "impl_type_ADAS_enum_FSRACruiseSpdOffsetModFb.h"
#include "impl_type_ADAS_val_FSRACruiseSpdOffsetSpdFb.h"
#include "impl_type_ADAS_val_FSRACruiseSpdOffsetPercentFb.h"
#include "impl_type_ADAS_enum_ACCholdSts.h"
#include "impl_type_ADAS_enum_FSRASpdSetMod.h"
#include "impl_type_ADAS_enum_FDMSt.h"
#include "impl_type_Gen_val_unit8.h"
#include "impl_type_ADAS_enum_CruiseAccelerateSts.h"



struct ADAS_strt_ACCInfo {
  ADAS_enum_ACCSts ACCSts;
  ADAS_enum_AutoResumeNotifier AutoResumeNotifier;
  ADAS_val_SetSpd SetSpeed;
  ADAS_enum_SetSpdUnit SetSpeedUnit;
  ADAS_enum_TimeGapSet TimeGapSet;
  ADAS_enum_LongiTakeOverReq LongitudinalTakeOverRequest;
  ADAS_enum_GoNotifier ACCGoNotifier;
  ADAS_enum_ACCTextMessage ACCTextMessage;
  ADAS_enum_ACCQuitReason ACCQuitReason;
  ADAS_enum_FSRACruiseSpdModFb FSRACruiseSpdModFb;
  ADAS_enum_FSRACruiseSpdOffsetModFb FSRACruiseSpdOffsetModFb;
  ADAS_val_FSRACruiseSpdOffsetSpdFb SetACCSetSpdOffset;
  ADAS_val_FSRACruiseSpdOffsetPercentFb SetACCSetSpdPercOffset;
  ADAS_enum_ACCholdSts ACCholdSts;
  ADAS_enum_FSRASpdSetMod FSRASpdSetMod;
  ADAS_enum_FDMSt FDMSt;
  Gen_val_unit8 FDMDist;
  ADAS_enum_CruiseAccelerateSts CruiseAccelerateSts;
};

#endif // IMPL_TYPE_ADAS_STRT_ACCINFO_H_
