///
/// @file       impl_type_ADAS_strt_ESSInfo.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_ESSINFO_H_
#define IMPL_TYPE_ADAS_STRT_ESSINFO_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_enum_ESSSts.h"
#include "impl_type_ADAS_enum_ESSDirection.h"
#include "impl_type_ADAS_enum_AESSts.h"
#include "impl_type_ADAS_enum_AESDirection.h"



struct ADAS_strt_ESSInfo {
  ADAS_enum_ESSSts ESSSts;
  ADAS_enum_ESSDirection ESSDirection;
  ADAS_enum_AESSts AESSts;
  ADAS_enum_AESDirection AESDirection;
};

#endif // IMPL_TYPE_ADAS_STRT_ESSINFO_H_
