///
/// @file       impl_type_ADAS_strt_FreeSpaceBin.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_FREESPACEBIN_H_
#define IMPL_TYPE_ADAS_STRT_FREESPACEBIN_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_arr_FreeSpaceBinID.h"
#include "impl_type_Gen_val_unit8.h"
#include "impl_type_ADAS_enum_FreeSpaceRoadFlag.h"



struct ADAS_strt_FreeSpaceBin {
  ADAS_arr_FreeSpaceBinID FreeSpaceBinID;
  Gen_val_unit8 FSresolutionX;
  Gen_val_unit8 FSresolutionY;
  Gen_val_unit8 FSxBevBoundMin;
  Gen_val_unit8 FSxBevBoundMax;
  Gen_val_unit8 FSyBevBoundMin;
  Gen_val_unit8 FSyBevBoundMax;
  Gen_val_unit8 FSmaskThreshold;
  ADAS_enum_FreeSpaceRoadFlag FreeSpaceRoadFlag;
};

#endif // IMPL_TYPE_ADAS_STRT_FREESPACEBIN_H_
