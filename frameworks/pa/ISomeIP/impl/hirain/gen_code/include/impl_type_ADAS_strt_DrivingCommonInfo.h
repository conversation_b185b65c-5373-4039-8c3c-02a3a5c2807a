///
/// @file       impl_type_ADAS_strt_DrivingCommonInfo.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_DRIVINGCOMMONINFO_H_
#define IMPL_TYPE_ADAS_STRT_DRIVINGCOMMONINFO_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_enum_DrivingCommonTOR.h"
#include "impl_type_ADAS_enum_DrivingCommonSnG.h"
#include "impl_type_ADAS_enum_VoiceRemindSettingModeFb.h"
#include "impl_type_ADAS_val_LineEquation_C0.h"
#include "impl_type_ADAS_enum_LKSWarnModFb.h"
#include "impl_type_ADAS_enum_FCTADirection.h"
#include "impl_type_ADAS_enum_RCTADirection.h"
#include "impl_type_Gen_val_unit8.h"
#include "impl_type_ADAS_enum_ODDDisReason.h"
#include "impl_type_ADAS_enum_PlanStoplineFlag.h"
#include "impl_type_Gen_val_float32.h"
#include "impl_type_ADAS_enum_SensorFailureSts.h"
#include "impl_type_ADAS_enum_VLMReminder.h"
#include "impl_type_ADAS_enum_DrivingReserved.h"



struct ADAS_strt_DrivingCommonInfo {
  ADAS_enum_DrivingCommonTOR DrivingCommonTOR;
  ADAS_enum_DrivingCommonSnG DrivingCommonSnG;
  ADAS_enum_VoiceRemindSettingModeFb VoiceRemindSettingModeFb;
  ADAS_val_LineEquation_C0 DrivingDistoEgoCenter;
  ADAS_enum_LKSWarnModFb LKSwarningModFb;
  ADAS_enum_FCTADirection FCTADirection;
  ADAS_enum_RCTADirection RCTADirection;
  Gen_val_unit8 DistoIntersection;
  ADAS_enum_ODDDisReason ODDDistanceQuitReason;
  ADAS_enum_PlanStoplineFlag DrivingPlanStopline;
  Gen_val_float32 DistoNavChangePoint;
  ADAS_enum_SensorFailureSts SensorFailureSts;
  ADAS_enum_VLMReminder VLMReminder;
  ADAS_enum_DrivingReserved DrivingReserved;
};

#endif // IMPL_TYPE_ADAS_STRT_DRIVINGCOMMONINFO_H_
