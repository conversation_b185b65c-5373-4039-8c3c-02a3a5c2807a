///
/// @file       impl_type_ADAS_strt_APARPAInfo.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_APARPAINFO_H_
#define IMPL_TYPE_ADAS_STRT_APARPAINFO_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_enum_ParkingOperationResp.h"
#include "impl_type_ADAS_enum_ApaRpaSts.h"
#include "impl_type_ADAS_enum_PicPocType.h"
#include "impl_type_ADAS_enum_ParkinDirectionSts.h"
#include "impl_type_Gen_enum_AvailableSts.h"
#include "impl_type_ADAS_enum_ParkOutDirectionSt.h"
#include "impl_type_Gen_enum_ValidityStatus.h"
#include "impl_type_ADAS_enum_APAActiveFuncInd.h"
#include "impl_type_ADAS_val_SlotID.h"
#include "impl_type_ADAS_val_ParkingDist.h"
#include "impl_type_ADAS_arr_TrajectoryPoint.h"
#include "impl_type_ADAS_strt_EgoPose.h"
#include "impl_type_ADAS_strt_ApaTargetSlotInfo.h"



struct ADAS_strt_APARPAInfo {
  ADAS_enum_ParkingOperationResp ApaOperationResp;
  ADAS_enum_ApaRpaSts ApaRpaSts;
  ADAS_enum_PicPocType ParkingTypeSelectFb;
  ADAS_enum_ParkinDirectionSts ParkinDirectionSts;
  Gen_enum_AvailableSts FreeParkSlotAvaSts;
  ADAS_enum_ParkOutDirectionSt ParkOutDirectionSt;
  Gen_enum_ValidityStatus POCFLParallelSts;
  Gen_enum_ValidityStatus POCFLCorssSts;
  Gen_enum_ValidityStatus POCFRParallelSts;
  Gen_enum_ValidityStatus POCFRCrossSts;
  Gen_enum_ValidityStatus POCFrontCrossSts;
  Gen_enum_ValidityStatus POCRearCrossSts;
  Gen_enum_ValidityStatus POCRLCrossSts;
  Gen_enum_ValidityStatus POCRRCrossSts;
  ADAS_enum_APAActiveFuncInd APAActiveFuncInd;
  ADAS_val_SlotID ApaSelectIDInd;
  ADAS_val_ParkingDist ParkingStopDist;
  ADAS_arr_TrajectoryPoint APARPATrajectory;
  Gen_enum_AvailableSts RpaAvaliableStatus;
  ADAS_strt_EgoPose ApaEgoVehPos;
  ADAS_strt_ApaTargetSlotInfo ApaTargetSlotInfo;
};

#endif // IMPL_TYPE_ADAS_STRT_APARPAINFO_H_
