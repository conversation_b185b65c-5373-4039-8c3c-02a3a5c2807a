///
/// @file       impl_type_ADAS_strt_APARPAInfo_Ext.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_APARPAINFO_EXT_H_
#define IMPL_TYPE_ADAS_STRT_APARPAINFO_EXT_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_enum_ApaRpaFreeParkButtonSts.h"
#include "impl_type_ADAS_enum_ApaSideAlignSts.h"
#include "impl_type_ADAS_val_ApaRpaMraProcessbar.h"
#include "impl_type_ADSA_val_ParkingPauseCountDown.h"
#include "impl_type_ADAS_enum_ApaSettingStsFb.h"
#include "impl_type_ADAS_enum_FreeParkSlotParkableSts.h"
#include "impl_type_ADAS_arr_FreeParkPlannedTrajectory.h"
#include "impl_type_ADAS_enum_TargetSlotAvailableParkInType.h"
#include "impl_type_ADAS_enum_ApaSideAlignCfgAvailability.h"
#include "impl_type_Gen_enum_AvailableSts.h"
#include "impl_type_ADAS_strt_APARPAInfo_Ext_Reserved_Type.h"



struct ADAS_strt_APARPAInfo_Ext {
  ADAS_enum_ApaRpaFreeParkButtonSts FreeParkButtonSts;
  ADAS_enum_ApaSideAlignSts ApaSideAlignCfgSts;
  ADAS_val_ApaRpaMraProcessbar ApaRpaProcessbar;
  ADSA_val_ParkingPauseCountDown ParkingPauseCountDown;
  ADAS_enum_ApaSettingStsFb ApaSettingStsFb;
  ADAS_enum_FreeParkSlotParkableSts FreeParkSlotParkableSts;
  ADAS_arr_FreeParkPlannedTrajectory FreeParkPlannedTrajectory;
  ADAS_enum_ApaRpaFreeParkButtonSts ApaButtonSts;
  ADAS_enum_ApaRpaFreeParkButtonSts RpaButtonSts;
  ADAS_enum_TargetSlotAvailableParkInType ApaSelectSlotDirectionAvaSts;
  ADAS_enum_ApaSettingStsFb HpaSettingStsFb;
  ADAS_enum_ApaSideAlignCfgAvailability ApaSideAlignCfgAvailability;
  Gen_enum_AvailableSts HPATrainingAvl;
  ADAS_strt_APARPAInfo_Ext_Reserved_Type ADAS_strt_APARPAInfo_Ext_Reserved;
};

#endif // IMPL_TYPE_ADAS_STRT_APARPAINFO_EXT_H_
