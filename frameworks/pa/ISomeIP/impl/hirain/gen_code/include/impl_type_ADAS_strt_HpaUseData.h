///
/// @file       impl_type_ADAS_strt_HpaUseData.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_HPAUSEDATA_H_
#define IMPL_TYPE_ADAS_STRT_HPAUSEDATA_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_val_ParkingDist.h"
#include "impl_type_ADAS_val_ParkingProcessTakeTime.h"
#include "impl_type_ADAS_val_HPAVPAGiveWayPedTimes.h"
#include "impl_type_ADAS_val_HPAVPAGiveWayVehTimes.h"
#include "impl_type_Gen_val_unit8.h"



struct ADAS_strt_HpaUseData {
  ADAS_val_ParkingDist HpaCruiseDist;
  ADAS_val_ParkingProcessTakeTime HpaCruiseTime;
  ADAS_val_HPAVPAGiveWayPedTimes HPAVPAGiveWayPedTimes;
  ADAS_val_HPAVPAGiveWayVehTimes HPAVPAGiveWayVehTimes;
  Gen_val_unit8 HPASpeedBumpNum;
};

#endif // IMPL_TYPE_ADAS_STRT_HPAUSEDATA_H_
