///
/// @file       impl_type_ADAS_strt_DrivingSafetyFuncInfo_Reserved_Type.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_DRIVINGSAFETYFUNCINFO_RESERVED_TYPE_H_
#define IMPL_TYPE_ADAS_STRT_DRIVINGSAFETYFUNCINFO_RESERVED_TYPE_H_

#include <cstddef>
#include <cstdint>

#include "ara/core/array.h"
#include "impl_type_T_Reserved11Uint8.h"


using ADAS_strt_DrivingSafetyFuncInfo_Reserved_Type = ara::core::Array<T_Reserved11Uint8,20>;

static constexpr std::size_t kADAS_strt_DrivingSafetyFuncInfo_Reserved_TypeMinSize = 20u;
static constexpr std::size_t kADAS_strt_DrivingSafetyFuncInfo_Reserved_TypeMaxSize = 20u;

#endif // IMPL_TYPE_ADAS_STRT_DRIVINGSAFETYFUNCINFO_RESERVED_TYPE_H_
