///
/// @file       impl_type_ADAS_strt_ICAInfo.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_ICAINFO_H_
#define IMPL_TYPE_ADAS_STRT_ICAINFO_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_enum_ICASts.h"
#include "impl_type_ADAS_enum_ICATextInfo.h"
#include "impl_type_ADAS_enum_ICAQuitReason.h"
#include "impl_type_ADAS_enum_ICASenarioReminder.h"



struct ADAS_strt_ICAInfo {
  ADAS_enum_ICASts ICASts;
  ADAS_enum_ICATextInfo ICATextInfo;
  ADAS_enum_ICAQuitReason ICAQuitReason;
  ADAS_enum_ICASenarioReminder ICASenarioReminder;
};

#endif // IMPL_TYPE_ADAS_STRT_ICAINFO_H_
