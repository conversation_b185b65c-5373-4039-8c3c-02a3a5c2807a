///
/// @file       impl_type_ADAS_strt_DrivingSafetyFunctionInfo_Reserved_Type.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_DRIVINGSAFETYFUNCTIONINFO_RESERVED_TYPE_H_
#define IMPL_TYPE_ADAS_STRT_DRIVINGSAFETYFUNCTIONINFO_RESERVED_TYPE_H_

#include <cstddef>
#include <cstdint>

#include "ara/core/array.h"
#include "impl_type_T_Reserved1Uint8.h"


using ADAS_strt_DrivingSafetyFunctionInfo_Reserved_Type = ara::core::Array<T_Reserved1Uint8,7>;

static constexpr std::size_t kADAS_strt_DrivingSafetyFunctionInfo_Reserved_TypeMinSize = 7u;
static constexpr std::size_t kADAS_strt_DrivingSafetyFunctionInfo_Reserved_TypeMaxSize = 7u;

#endif // IMPL_TYPE_ADAS_STRT_DRIVINGSAFETYFUNCTIONINFO_RESERVED_TYPE_H_
