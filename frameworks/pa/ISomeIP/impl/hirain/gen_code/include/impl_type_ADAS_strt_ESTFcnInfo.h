///
/// @file       impl_type_ADAS_strt_ESTFcnInfo.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_ESTFCNINFO_H_
#define IMPL_TYPE_ADAS_STRT_ESTFCNINFO_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_enum_ESTSts.h"
#include "impl_type_ADAS_enum_ESTTextInfo.h"
#include "impl_type_ADAS_enum_ESTWarning.h"
#include "impl_type_ADAS_strt_ESTFcnInfo_Reserved_Type.h"



struct ADAS_strt_ESTFcnInfo {
  ADAS_enum_ESTSts ESTSts;
  ADAS_enum_ESTTextInfo ESTTextInfo;
  ADAS_enum_ESTWarning ESTWarning;
  ADAS_strt_ESTFcnInfo_Reserved_Type ADAS_strt_ESTFcnInfo_Reserved;
};

#endif // IMPL_TYPE_ADAS_STRT_ESTFCNINFO_H_
