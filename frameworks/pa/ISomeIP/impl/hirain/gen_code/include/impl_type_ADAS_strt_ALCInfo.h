///
/// @file       impl_type_ADAS_strt_ALCInfo.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_ALCINFO_H_
#define IMPL_TYPE_ADAS_STRT_ALCINFO_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_enum_ALCSts.h"
#include "impl_type_ADAS_enum_ALCDirIndicator.h"
#include "impl_type_ADAS_enum_ALCAbortReason.h"
#include "impl_type_ADAS_enum_ALCSoundWarn.h"
#include "impl_type_ADAS_enum_ALCTextInfo.h"
#include "impl_type_ADAS_enum_ALCTakeOverRequest.h"
#include "impl_type_ADAS_enum_LCPermitSt.h"



struct ADAS_strt_ALCInfo {
  ADAS_enum_ALCSts ALCStatus;
  ADAS_enum_ALCDirIndicator ALCDirIndicator;
  ADAS_enum_ALCAbortReason ALCAbortReason;
  ADAS_enum_ALCSoundWarn ALCSoundWarn;
  ADAS_enum_ALCTextInfo ALCTextInfomation;
  ADAS_enum_ALCTakeOverRequest ALCTakeOverRequest;
  ADAS_enum_LCPermitSt ALCPermitSt;
};

#endif // IMPL_TYPE_ADAS_STRT_ALCINFO_H_
