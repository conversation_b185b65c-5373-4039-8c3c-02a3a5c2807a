///
/// @file       impl_type_ADAS_strt_HPASlotDetail.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_HPASLOTDETAIL_H_
#define IMPL_TYPE_ADAS_STRT_HPASLOTDETAIL_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_Gen_val_SlotID.h"
#include "impl_type_ADAS_string_SlotName.h"
#include "impl_type_ADAS_strt_HpaSlotPoint.h"
#include "impl_type_ADAS_enum_SlotLabel.h"
#include "impl_type_ADAS_enum_HPADefaultSlotFlag.h"
#include "impl_type_ADAS_strt_HpaSavedSlot.h"
#include "impl_type_Gen_Val_Floor.h"



struct ADAS_strt_HPASlotDetail {
  Gen_val_SlotID HPASlotId;
  ADAS_string_SlotName HPASlotName;
  ADAS_strt_HpaSlotPoint HpaSlotPoint;
  ADAS_enum_SlotLabel HPASlotLabel;
  ADAS_enum_HPADefaultSlotFlag HPADefaultSlotFlag;
  ADAS_strt_HpaSavedSlot HPAPathSavedSlotInfo;
  Gen_Val_Floor HPASlotFloorInfo;
};

#endif // IMPL_TYPE_ADAS_STRT_HPASLOTDETAIL_H_
