///
/// @file       impl_type_ADAS_strt_CNOAPathDetail.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_CNOAPATHDETAIL_H_
#define IMPL_TYPE_ADAS_STRT_CNOAPATHDETAIL_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_Gen_val_ID.h"
#include "impl_type_ADAS_string_PathName.h"
#include "impl_type_ADAS_val_CNOADist.h"
#include "impl_type_ADAS_arr_PathPoint.h"
#include "impl_type_ADAS_enum_PathLabel.h"
#include "impl_type_Gen_enum_AvailableSts.h"



struct ADAS_strt_CNOAPathDetail {
  Gen_val_ID CNOAPathID;
  ADAS_string_PathName CNOAPathName;
  ADAS_string_PathName CNOAPathStartName;
  ADAS_string_PathName CNOAPathEndName;
  ADAS_val_CNOADist C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;
  ADAS_arr_PathPoint CNOALinePoint;
  ADAS_enum_PathLabel CNOAPathLabel;
  Gen_val_ID CNOAPathRank;
  Gen_enum_AvailableSts CNOAPathAvailable;
};

#endif // IMPL_TYPE_ADAS_STRT_CNOAPATHDETAIL_H_
