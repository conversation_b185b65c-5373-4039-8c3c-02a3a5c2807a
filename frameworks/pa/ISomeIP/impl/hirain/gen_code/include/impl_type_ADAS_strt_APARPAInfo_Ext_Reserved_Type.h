///
/// @file       impl_type_ADAS_strt_APARPAInfo_Ext_Reserved_Type.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_APARPAINFO_EXT_RESERVED_TYPE_H_
#define IMPL_TYPE_ADAS_STRT_APARPAINFO_EXT_RESERVED_TYPE_H_

#include <cstddef>
#include <cstdint>

#include "ara/core/array.h"
#include "impl_type_T_Reserved7Uint8.h"


using ADAS_strt_APARPAInfo_Ext_Reserved_Type = ara::core::Array<T_Reserved7Uint8,18>;

static constexpr std::size_t kADAS_strt_APARPAInfo_Ext_Reserved_TypeMinSize = 18u;
static constexpr std::size_t kADAS_strt_APARPAInfo_Ext_Reserved_TypeMaxSize = 18u;

#endif // IMPL_TYPE_ADAS_STRT_APARPAINFO_EXT_RESERVED_TYPE_H_
