///
/// @file       impl_type_ADAS_strt_DrivingLvl2Plus.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_DRIVINGLVL2PLUS_H_
#define IMPL_TYPE_ADAS_STRT_DRIVINGLVL2PLUS_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_strt_CityNOAFcnInfo.h"
#include "impl_type_ADAS_strt_HodDmsInfo.h"
#include "impl_type_ADAS_arr_TrafficLight.h"
#include "impl_type_ADAS_enum_NOP2ICACommandFb.h"
#include "impl_type_ADAS_strt_CNOAInfo.h"
#include "impl_type_ADAS_strt_OffRoadInfo.h"
#include "impl_type_ADAS_strt_VehiclePose.h"
#include "impl_type_ADAS_strt_DrivingLvl12_Reserved_Type.h"



struct ADAS_strt_DrivingLvl2Plus {
  ADAS_strt_CityNOAFcnInfo CityNOAFcnInfo_Drvg;
  ADAS_strt_HodDmsInfo NtfHodDmsInfo_Drvg;
  ADAS_arr_TrafficLight TrafficLight;
  ADAS_enum_NOP2ICACommandFb NOP2ICACommandFb;
  ADAS_strt_CNOAInfo CNOAinfo;
  ADAS_strt_OffRoadInfo OffRoadinfo;
  ADAS_strt_VehiclePose VehiclePosition;
  ADAS_strt_DrivingLvl12_Reserved_Type ADAS_strt_DrivingLvl2Plus_Reserved;
};

#endif // IMPL_TYPE_ADAS_STRT_DRIVINGLVL2PLUS_H_
