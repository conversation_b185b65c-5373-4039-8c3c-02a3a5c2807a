///
/// @file       impl_type_ADAS_arr_TrajectoryPoint.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_ARR_TRAJECTORYPOINT_H_
#define IMPL_TYPE_ADAS_ARR_TRAJECTORYPOINT_H_

#include <cstddef>

#include "ara/core/array.h"
#include "impl_type_ADAS_strt_TrajectoryPoint.h"


using ADAS_arr_TrajectoryPoint = ara::core::Array<ADAS_strt_TrajectoryPoint,64>;

static constexpr std::size_t kADAS_arr_TrajectoryPointMinSize = 64u;
static constexpr std::size_t kADAS_arr_TrajectoryPointMaxSize = 64u;

#endif // IMPL_TYPE_ADAS_ARR_TRAJECTORYPOINT_H_
