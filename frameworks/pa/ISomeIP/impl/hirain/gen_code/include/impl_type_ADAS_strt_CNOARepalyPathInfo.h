///
/// @file       impl_type_ADAS_strt_CNOARepalyPathInfo.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_CNOAREPALYPATHINFO_H_
#define IMPL_TYPE_ADAS_STRT_CNOAREPALYPATHINFO_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_Gen_val_ID.h"
#include "impl_type_ADAS_string_PathName.h"
#include "impl_type_ADAS_val_CNOADist.h"
#include "impl_type_ADAS_enum_PathLabel.h"
#include "impl_type_ADAS_enum_RoadMarkerType.h"
#include "impl_type_ADAS_arr_TrajectoryPoint.h"
#include "impl_type_ADAS_enum_ReplayOffRoute.h"



struct ADAS_strt_CNOARepalyPathInfo {
  Gen_val_ID CNOAReplayPathID;
  ADAS_string_PathName CNOAReplayPathName;
  ADAS_string_PathName CNOAReplayPathEndName;
  ADAS_val_CNOADist CNOAReplayDrivingDist;
  ADAS_val_CNOADist CNOARepayRemainDist;
  ADAS_enum_PathLabel CNOAReplayPathLabel;
  ADAS_enum_RoadMarkerType CNOAReplayTurntype;
  ADAS_arr_TrajectoryPoint CNOATrajectory;
  ADAS_val_CNOADist CNOAReplayTurntypeDist;
  ADAS_enum_ReplayOffRoute CNOAReplayOffRoute;
  Gen_val_ID CNOAReplayPercent;
};

#endif // IMPL_TYPE_ADAS_STRT_CNOAREPALYPATHINFO_H_
