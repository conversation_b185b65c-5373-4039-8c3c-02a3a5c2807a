///
/// @file       impl_type_ADAS_strt_GlobalPathDetail.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_GLOBALPATHDETAIL_H_
#define IMPL_TYPE_ADAS_STRT_GLOBALPATHDETAIL_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_arr_PathPoint.h"
#include "impl_type_ADAS_arr_SlotDetail.h"
#include "impl_type_ADAS_arr_Floor.h"



struct ADAS_strt_GlobalPathDetail {
  ADAS_arr_PathPoint Hpa_3DPathLinePoint;
  ADAS_arr_SlotDetail Hpa_3DPathSlotInfo;
  ADAS_arr_Floor Hpa_3DFloorInfo;
};

#endif // IMPL_TYPE_ADAS_STRT_GLOBALPATHDETAIL_H_
