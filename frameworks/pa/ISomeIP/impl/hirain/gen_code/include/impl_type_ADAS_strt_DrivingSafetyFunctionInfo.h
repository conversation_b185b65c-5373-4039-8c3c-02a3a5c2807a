///
/// @file       impl_type_ADAS_strt_DrivingSafetyFunctionInfo.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_DRIVINGSAFETYFUNCTIONINFO_H_
#define IMPL_TYPE_ADAS_STRT_DRIVINGSAFETYFUNCTIONINFO_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_enum_FCWStatus.h"
#include "impl_type_ADAS_enum_AEBStatus.h"
#include "impl_type_ADAS_enum_FCTAStatus.h"
#include "impl_type_ADAS_enum_RCTAStatus.h"
#include "impl_type_ADAS_enum_FcwSettingStsFB.h"
#include "impl_type_ADAS_enum_LDWSensitivityFB.h"
#include "impl_type_ADAS_strt_VehicleEgoPose.h"
#include "impl_type_ADAS_enum_CurRoadSurfaceTexture.h"
#include "impl_type_ADAS_strt_DrivingSafetyFunctionInfo_Reserved_Type.h"



struct ADAS_strt_DrivingSafetyFunctionInfo {
  ADAS_enum_FCWStatus FcwStatus;
  ADAS_enum_AEBStatus AebStatus;
  ADAS_enum_FCTAStatus FctaSts;
  ADAS_enum_RCTAStatus RctaSts;
  ADAS_enum_FcwSettingStsFB FCWSettingStsFB;
  ADAS_enum_LDWSensitivityFB LDWSensitivityFB;
  ADAS_strt_VehicleEgoPose VehicleEgoPose;
  ADAS_enum_CurRoadSurfaceTexture CurRoadSurfaceTexture;
  ADAS_strt_DrivingSafetyFunctionInfo_Reserved_Type ADAS_strt_ActiveSafetyFcnInfo_Reserved;
};

#endif // IMPL_TYPE_ADAS_STRT_DRIVINGSAFETYFUNCTIONINFO_H_
