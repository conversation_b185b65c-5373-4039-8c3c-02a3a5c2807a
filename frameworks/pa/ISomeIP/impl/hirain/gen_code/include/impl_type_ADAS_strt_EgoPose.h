///
/// @file       impl_type_ADAS_strt_EgoPose.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_EGOPOSE_H_
#define IMPL_TYPE_ADAS_STRT_EGOPOSE_H_

#include <cstddef>

#include "impl_type_Gen_val_Coordinate_X.h"
#include "impl_type_Gen_val_Coordinate_Y.h"
#include "impl_type_Gen_val_Coordinate_Z.h"
#include "impl_type_Gen_val_Yaw.h"
#include "impl_type_Gen_val_Pitch.h"
#include "impl_type_Gen_val_Roll.h"



struct ADAS_strt_EgoPose {
  Gen_val_Coordinate_X EgoVehPoseX;
  Gen_val_Coordinate_Y EgoVehPoseY;
  Gen_val_Coordinate_Z EgoVehPoseZ;
  Gen_val_Yaw EgoVehYaw;
  Gen_val_Pitch EgoVehPitch;
  Gen_val_Roll EgoVehRoll;
};

#endif // IMPL_TYPE_ADAS_STRT_EGOPOSE_H_
