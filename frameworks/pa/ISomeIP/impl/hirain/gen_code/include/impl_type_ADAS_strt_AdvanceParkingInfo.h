///
/// @file       impl_type_ADAS_strt_AdvanceParkingInfo.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_ADVANCEPARKINGINFO_H_
#define IMPL_TYPE_ADAS_STRT_ADVANCEPARKINGINFO_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_enum_HPASts.h"
#include "impl_type_ADAS_val_SlotID.h"
#include "impl_type_ADAS_strt_HpaUseData.h"
#include "impl_type_ADAS_enum_VPAWarningInfo.h"
#include "impl_type_Gen_val_ID.h"
#include "impl_type_ADAS_enum_HPAVPARepalyRemind.h"
#include "impl_type_ADAS_arr_TrajectoryPoint.h"
#include "impl_type_ADAS_enum_ParkingOperationResp.h"
#include "impl_type_ADAS_strt_EgoPose.h"
#include "impl_type_Gen_enum_AvailableSts.h"
#include "impl_type_ADAS_val_ParkingProgressBarInd.h"
#include "impl_type_ADAS_enum_VPAPickUpPosSelectReq.h"
#include "impl_type_ADAS_enum_VPADropOffPosSelectReq.h"
#include "impl_type_ADAS_enum_VPASts.h"
#include "impl_type_ADAS_enum_VPAFunctionType.h"
#include "impl_type_ADAS_val_ParkingDist.h"
#include "impl_type_ADAS_enum_HPARequestAPAUI.h"
#include "impl_type_ADAS_enum_HpaActiveCheckSts.h"



struct ADAS_strt_AdvanceParkingInfo {
  ADAS_enum_HPASts HPASts;
  ADAS_val_SlotID HPASearchOnWaySelectSlotID;
  ADAS_strt_HpaUseData HPAUseData;
  ADAS_enum_VPAWarningInfo VPAWarningInfo;
  Gen_val_ID HPAPathSelectIDInd;
  ADAS_enum_HPAVPARepalyRemind HPAVPARepalyRemind;
  ADAS_arr_TrajectoryPoint HPAVPATrajectory;
  ADAS_enum_ParkingOperationResp HpaOperationResp;
  ADAS_strt_EgoPose HpaEgoVehPos;
  Gen_enum_AvailableSts VPADropOffVld;
  Gen_enum_AvailableSts VPAPickUpVld;
  Gen_enum_AvailableSts VPASelfMapBuildVld;
  ADAS_val_ParkingProgressBarInd VPAProcessBarInd;
  Gen_enum_AvailableSts VPAVld;
  ADAS_enum_VPAPickUpPosSelectReq VPAPickUpPosSelectReq;
  ADAS_enum_VPADropOffPosSelectReq VPADropOffPosSelectReq;
  ADAS_enum_VPASts VPASts;
  ADAS_enum_VPAFunctionType VPAFuncTypeFb;
  ADAS_val_ParkingDist VPARemainingDist;
  ADAS_enum_HPARequestAPAUI HPARequestAPAUI;
  ADAS_enum_HpaActiveCheckSts HpaActiveCheckSts;
  Gen_enum_AvailableSts HPAReplayRecAvl;
};

#endif // IMPL_TYPE_ADAS_STRT_ADVANCEPARKINGINFO_H_
