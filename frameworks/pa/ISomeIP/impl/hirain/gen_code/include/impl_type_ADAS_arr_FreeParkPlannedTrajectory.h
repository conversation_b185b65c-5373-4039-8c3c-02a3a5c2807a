///
/// @file       impl_type_ADAS_arr_FreeParkPlannedTrajectory.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_ARR_FREEPARKPLANNEDTRAJECTORY_H_
#define IMPL_TYPE_ADAS_ARR_FREEPARKPLANNEDTRAJECTORY_H_

#include <cstddef>

#include "ara/core/array.h"
#include "impl_type_ADAS_strt_FreeParkTrajectoryPoints.h"


using ADAS_arr_FreeParkPlannedTrajectory = ara::core::Array<ADAS_strt_FreeParkTrajectoryPoints,160>;

static constexpr std::size_t kADAS_arr_FreeParkPlannedTrajectoryMinSize = 160u;
static constexpr std::size_t kADAS_arr_FreeParkPlannedTrajectoryMaxSize = 160u;

#endif // IMPL_TYPE_ADAS_ARR_FREEPARKPLANNEDTRAJECTORY_H_
