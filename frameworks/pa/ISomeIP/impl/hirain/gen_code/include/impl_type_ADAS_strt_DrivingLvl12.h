///
/// @file       impl_type_ADAS_strt_DrivingLvl12.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_DRIVINGLVL12_H_
#define IMPL_TYPE_ADAS_STRT_DRIVINGLVL12_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_strt_TrafficSign.h"
#include "impl_type_ADAS_strt_ISLIInfo.h"
#include "impl_type_ADAS_strt_ACCInfo.h"
#include "impl_type_ADAS_strt_ALCInfo.h"
#include "impl_type_ADAS_strt_ICAInfo.h"
#include "impl_type_ADAS_strt_IntelligentEvasionInfo.h"
#include "impl_type_ADAS_strt_HNOPfunctionInfo.h"
#include "impl_type_ADAS_strt_ESTFcnInfo.h"
#include "impl_type_ADAS_strt_LSSInfo.h"
#include "impl_type_ADAS_strt_DAIInfo.h"
#include "impl_type_ADAS_strt_DrivingCommonInfo.h"
#include "impl_type_ADAS_strt_DrivingLvl12_Reserved_Type.h"



struct ADAS_strt_DrivingLvl12 {
  ADAS_strt_TrafficSign NtfTrafficSign_Drvg;
  ADAS_strt_ISLIInfo NtfISLIInfo_Drvg;
  ADAS_strt_ACCInfo NtfACCInfo_Drvg;
  ADAS_strt_ALCInfo NtfALCInfo_Drvg;
  ADAS_strt_ICAInfo NtfICAInfo_Drvg;
  ADAS_strt_IntelligentEvasionInfo NtfIntelligentEvasionInfo_Drvg;
  ADAS_strt_HNOPfunctionInfo NtfHNOPfunctionInfo_Drvg;
  ADAS_strt_ESTFcnInfo ESTFcnInfo_Drvg;
  ADAS_strt_LSSInfo NtfLSSInfo_Drvg;
  ADAS_strt_DAIInfo NtfDAIInfo_Drvg;
  ADAS_strt_DrivingCommonInfo ADAS_strt_DrivingCommon;
  ADAS_strt_DrivingLvl12_Reserved_Type ADAS_strt_DrivingLvl12_Reserved;
};

#endif // IMPL_TYPE_ADAS_STRT_DRIVINGLVL12_H_
