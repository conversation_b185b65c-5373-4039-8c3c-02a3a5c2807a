///
/// @file       impl_type_ADAS_strt_HodDmsInfo.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_HODDMSINFO_H_
#define IMPL_TYPE_ADAS_STRT_HODDMSINFO_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_enum_DriverHandsoffWarning.h"
#include "impl_type_ADAS_enum_DMSWarning.h"
#include "impl_type_ADAS_enum_HandsOnReq.h"
#include "impl_type_ADAS_enum_ICAplussetting.h"



struct ADAS_strt_HodDmsInfo {
  ADAS_enum_DriverHandsoffWarning DriverhandsoffWarning;
  ADAS_enum_DMSWarning DMSWarning;
  ADAS_enum_HandsOnReq HandsOnReq;
  ADAS_enum_ICAplussetting ICAplussetting;
};

#endif // IMPL_TYPE_ADAS_STRT_HODDMSINFO_H_
