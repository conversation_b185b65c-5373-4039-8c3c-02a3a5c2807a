///
/// @file       impl_type_ADAS_strt_HNOPfunctionInfo.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_HNOPFUNCTIONINFO_H_
#define IMPL_TYPE_ADAS_STRT_HNOPFUNCTIONINFO_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_enum_HNOPSts.h"
#include "impl_type_ADAS_enum_NOPLaneChangeMod.h"
#include "impl_type_ADAS_enum_NOPLaneChangeStyle.h"
#include "impl_type_ADAS_enum_NOPLaneChangeInfo.h"
#include "impl_type_ADAS_enum_SafeStopReminder.h"
#include "impl_type_ADAS_enum_NOPQuitReason.h"
#include "impl_type_ADAS_enum_NOPALCReqDriverConf.h"
#include "impl_type_ADAS_enum_DegradeReminder.h"
#include "impl_type_ADAS_enum_NOPStReminder.h"
#include "impl_type_ADAS_enum_NOPLaneChangeReason.h"
#include "impl_type_ADAS_enum_HNOPScenariosReminder.h"
#include "impl_type_ADAS_enum_Confidencelevel.h"
#include "impl_type_ADAS_arr_TrajectoryPoint.h"
#include "impl_type_ADAS_enum_LeaveFastLaneSts.h"
#include "impl_type_ADAS_enum_NOPLaneChangeWarnType.h"
#include "impl_type_ADAS_enum_LCPermitSt.h"



struct ADAS_strt_HNOPfunctionInfo {
  ADAS_enum_HNOPSts HNOPSts;
  ADAS_enum_NOPLaneChangeMod NOPLaneChangeMod;
  ADAS_enum_NOPLaneChangeStyle NOPLaneChangeStyle;
  ADAS_enum_NOPLaneChangeInfo NOPLaneChangeInfo;
  ADAS_enum_SafeStopReminder SafeStopReminder;
  ADAS_enum_NOPQuitReason NOPQuitReason;
  ADAS_enum_NOPALCReqDriverConf NOPALCReqDriverConf;
  ADAS_enum_DegradeReminder NOPDegradeReminder;
  ADAS_enum_NOPStReminder NOPStReminder;
  ADAS_enum_NOPLaneChangeReason NOPLaneChangeReason;
  ADAS_enum_HNOPScenariosReminder HNOPScenariosReminder;
  ADAS_enum_Confidencelevel Confidencelevel;
  ADAS_arr_TrajectoryPoint TrajectoryPoint;
  ADAS_enum_LeaveFastLaneSts LeaveFastLaneSts;
  ADAS_enum_NOPLaneChangeWarnType NOPLaneChangeWarnTypeFb;
  ADAS_enum_LCPermitSt NOPLCPermitSt;
};

#endif // IMPL_TYPE_ADAS_STRT_HNOPFUNCTIONINFO_H_
