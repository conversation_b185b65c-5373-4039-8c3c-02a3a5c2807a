///
/// @file       impl_type_ADAS_strt_ApaTargetSlotInfo.h
/// @brief
///
///
#ifndef IMPL_TYPE_ADAS_STRT_APATARGETSLOTINFO_H_
#define IMPL_TYPE_ADAS_STRT_APATARGETSLOTINFO_H_

#include <cstddef>
#include <cstdint>

#include "impl_type_ADAS_strt_SlotPoint.h"
#include "impl_type_ADAS_enum_SlotType.h"
#include "impl_type_ADAS_enum_TargetSlotAvailableParkInType.h"
#include "impl_type_ADAS_strt_SlotWheelStopperInfo.h"



struct ADAS_strt_ApaTargetSlotInfo {
  ADAS_strt_SlotPoint TargetSlotPoint0Top1;
  ADAS_strt_SlotPoint TargetSlotPoint1Top2;
  ADAS_strt_SlotPoint TargetSlotPoint2Bottom1;
  ADAS_strt_SlotPoint TargetSlotPoint3Bottom2;
  ADAS_enum_SlotType TargetSlotType;
  ADAS_enum_TargetSlotAvailableParkInType TargetSlotAvailableParkInType;
  ADAS_strt_SlotWheelStopperInfo SlotWheelStopperInfo;
};

#endif // IMPL_TYPE_ADAS_STRT_APATARGETSLOTINFO_H_
