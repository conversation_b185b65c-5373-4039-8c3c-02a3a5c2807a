///
/// @file       ADCC_SensorInfo_skeleton.cc
/// @brief
///
///
#include "ns/adcc_sensorinfo/ADCC_SensorInfo_skeleton.h"

namespace ns {
namespace adcc_sensorinfo {
namespace skeleton {

ADCC_SensorInfoSkeleton::ADCC_SensorInfoSkeleton(ConstructionToken &&token)
  : Base(std::move(token)),
    NtfSensorStAll(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_SensorInfoSkeleton::ADCC_SensorInfoSkeleton(
  ara::com::InstanceIdentifier instance, ara::com::MethodCallProcessingMode mode)
  : Base(std::move(instance), mode),
    NtfSensorStAll(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_SensorInfoSkeleton::ADCC_SensorInfoSkeleton(
  ara::core::InstanceSpecifier specifier, ara::com::MethodCallProcessingMode mode)
  : Base(std::move(specifier), mode),
    NtfSensorStAll(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_SensorInfoSkeleton::ADCC_SensorInfoSkeleton(
  ara::com::InstanceIdentifierContainer instances, ara::com::MethodCallProcessingMode mode)
  : Base(std::move(instances), mode),
    NtfSensorStAll(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_SensorInfoSkeleton::ADCC_SensorInfoSkeleton(ADCC_SensorInfoSkeleton &&that)
  : Base(std::move(that)),
    NtfSensorStAll(std::move(that.NtfSensorStAll)) {
 //
}

ADCC_SensorInfoSkeleton::~ADCC_SensorInfoSkeleton(){

}

void ADCC_SensorInfoSkeleton::SetOwners(){
  for (auto &base : backend_skeletons_){
  	auto skeleton = dynamic_cast<ADCC_SensorInfoSkeletonInterface*>(base.get());
  	assert(skeleton != nullptr);

  	skeleton->SetOwner(this);
  	NtfSensorStAll.AddOwner(skeleton->GetNtfSensorStAllEvent());
  }
}

} // namespace skeleton
} // namespace adcc_sensorinfo
} // namespace ns

