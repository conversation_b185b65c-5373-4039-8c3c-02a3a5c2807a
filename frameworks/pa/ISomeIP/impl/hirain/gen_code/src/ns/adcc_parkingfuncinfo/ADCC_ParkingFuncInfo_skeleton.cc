///
/// @file       ADCC_ParkingFuncInfo_skeleton.cc
/// @brief
///
///
#include "ns/adcc_parkingfuncinfo/ADCC_ParkingFuncInfo_skeleton.h"

namespace ns {
namespace adcc_parkingfuncinfo {
namespace skeleton {

ADCC_ParkingFuncInfoSkeleton::ADCC_ParkingFuncInfoSkeleton(ConstructionToken &&token)
  : Base(std::move(token)),
    NtfAdvanceParkingInfo(backend_skeletons_.size()),
    NtfHPAPathDetail(backend_skeletons_.size()),
    NtfHPATrainigPathInfo(backend_skeletons_.size()),
    NtfVPASelfBuildMapDate(backend_skeletons_.size()),
    NtfHPARepalyPathInfo(backend_skeletons_.size()),
    NtfParkingL2GeneralInfo(backend_skeletons_.size()),
    NtfAPARPAInfo_Ext(backend_skeletons_.size()),
    NtfSvsFuncStatus(backend_skeletons_.size()),
    NtfHPASelfBuiltMapInfo(backend_skeletons_.size()),
    NtfHPAPathDetail_Ext(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_ParkingFuncInfoSkeleton::ADCC_ParkingFuncInfoSkeleton(
  ara::com::InstanceIdentifier instance, ara::com::MethodCallProcessingMode mode)
  : Base(std::move(instance), mode),
    NtfAdvanceParkingInfo(backend_skeletons_.size()),
    NtfHPAPathDetail(backend_skeletons_.size()),
    NtfHPATrainigPathInfo(backend_skeletons_.size()),
    NtfVPASelfBuildMapDate(backend_skeletons_.size()),
    NtfHPARepalyPathInfo(backend_skeletons_.size()),
    NtfParkingL2GeneralInfo(backend_skeletons_.size()),
    NtfAPARPAInfo_Ext(backend_skeletons_.size()),
    NtfSvsFuncStatus(backend_skeletons_.size()),
    NtfHPASelfBuiltMapInfo(backend_skeletons_.size()),
    NtfHPAPathDetail_Ext(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_ParkingFuncInfoSkeleton::ADCC_ParkingFuncInfoSkeleton(
  ara::core::InstanceSpecifier specifier, ara::com::MethodCallProcessingMode mode)
  : Base(std::move(specifier), mode),
    NtfAdvanceParkingInfo(backend_skeletons_.size()),
    NtfHPAPathDetail(backend_skeletons_.size()),
    NtfHPATrainigPathInfo(backend_skeletons_.size()),
    NtfVPASelfBuildMapDate(backend_skeletons_.size()),
    NtfHPARepalyPathInfo(backend_skeletons_.size()),
    NtfParkingL2GeneralInfo(backend_skeletons_.size()),
    NtfAPARPAInfo_Ext(backend_skeletons_.size()),
    NtfSvsFuncStatus(backend_skeletons_.size()),
    NtfHPASelfBuiltMapInfo(backend_skeletons_.size()),
    NtfHPAPathDetail_Ext(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_ParkingFuncInfoSkeleton::ADCC_ParkingFuncInfoSkeleton(
  ara::com::InstanceIdentifierContainer instances, ara::com::MethodCallProcessingMode mode)
  : Base(std::move(instances), mode),
    NtfAdvanceParkingInfo(backend_skeletons_.size()),
    NtfHPAPathDetail(backend_skeletons_.size()),
    NtfHPATrainigPathInfo(backend_skeletons_.size()),
    NtfVPASelfBuildMapDate(backend_skeletons_.size()),
    NtfHPARepalyPathInfo(backend_skeletons_.size()),
    NtfParkingL2GeneralInfo(backend_skeletons_.size()),
    NtfAPARPAInfo_Ext(backend_skeletons_.size()),
    NtfSvsFuncStatus(backend_skeletons_.size()),
    NtfHPASelfBuiltMapInfo(backend_skeletons_.size()),
    NtfHPAPathDetail_Ext(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_ParkingFuncInfoSkeleton::ADCC_ParkingFuncInfoSkeleton(ADCC_ParkingFuncInfoSkeleton &&that)
  : Base(std::move(that)),
    NtfAdvanceParkingInfo(std::move(that.NtfAdvanceParkingInfo)),
    NtfHPAPathDetail(std::move(that.NtfHPAPathDetail)),
    NtfHPATrainigPathInfo(std::move(that.NtfHPATrainigPathInfo)),
    NtfVPASelfBuildMapDate(std::move(that.NtfVPASelfBuildMapDate)),
    NtfHPARepalyPathInfo(std::move(that.NtfHPARepalyPathInfo)),
    NtfParkingL2GeneralInfo(std::move(that.NtfParkingL2GeneralInfo)),
    NtfAPARPAInfo_Ext(std::move(that.NtfAPARPAInfo_Ext)),
    NtfSvsFuncStatus(std::move(that.NtfSvsFuncStatus)),
    NtfHPASelfBuiltMapInfo(std::move(that.NtfHPASelfBuiltMapInfo)),
    NtfHPAPathDetail_Ext(std::move(that.NtfHPAPathDetail_Ext)) {
 //
}

ADCC_ParkingFuncInfoSkeleton::~ADCC_ParkingFuncInfoSkeleton(){

}

void ADCC_ParkingFuncInfoSkeleton::SetOwners(){
  for (auto &base : backend_skeletons_){
  	auto skeleton = dynamic_cast<ADCC_ParkingFuncInfoSkeletonInterface*>(base.get());
  	assert(skeleton != nullptr);

  	skeleton->SetOwner(this);
  	NtfAdvanceParkingInfo.AddOwner(skeleton->GetNtfAdvanceParkingInfoEvent());
  	NtfHPAPathDetail.AddOwner(skeleton->GetNtfHPAPathDetailEvent());
  	NtfHPATrainigPathInfo.AddOwner(skeleton->GetNtfHPATrainigPathInfoEvent());
  	NtfVPASelfBuildMapDate.AddOwner(skeleton->GetNtfVPASelfBuildMapDateEvent());
  	NtfHPARepalyPathInfo.AddOwner(skeleton->GetNtfHPARepalyPathInfoEvent());
  	NtfParkingL2GeneralInfo.AddOwner(skeleton->GetNtfParkingL2GeneralInfoEvent());
  	NtfAPARPAInfo_Ext.AddOwner(skeleton->GetNtfAPARPAInfo_ExtEvent());
  	NtfSvsFuncStatus.AddOwner(skeleton->GetNtfSvsFuncStatusEvent());
  	NtfHPASelfBuiltMapInfo.AddOwner(skeleton->GetNtfHPASelfBuiltMapInfoEvent());
  	NtfHPAPathDetail_Ext.AddOwner(skeleton->GetNtfHPAPathDetail_ExtEvent());
  }
}

} // namespace skeleton
} // namespace adcc_parkingfuncinfo
} // namespace ns

