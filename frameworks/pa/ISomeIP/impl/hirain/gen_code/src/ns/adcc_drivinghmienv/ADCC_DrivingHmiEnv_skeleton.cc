///
/// @file       ADCC_DrivingHmiEnv_skeleton.cc
/// @brief
///
///
#include "ns/adcc_drivinghmienv/ADCC_DrivingHmiEnv_skeleton.h"

namespace ns {
namespace adcc_drivinghmienv {
namespace skeleton {

ADCC_DrivingHmiEnvSkeleton::ADCC_DrivingHmiEnvSkeleton(ConstructionToken &&token)
  : Base(std::move(token)),
    NtfDynamicObjectList_Drvg(backend_skeletons_.size()),
    NtfStaticObjectList_Drvg(backend_skeletons_.size()),
    NtfLaneLineListt_Drvg(backend_skeletons_.size()),
    NtfRoadMarkerList(backend_skeletons_.size()),
    NtfLaneLineList_Ext_Drvg(backend_skeletons_.size()),
    NtfRoadStruct_Drvg(backend_skeletons_.size()),
    NtfFreeSpace(backend_skeletons_.size()),
    NtfOCCList(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_DrivingHmiEnvSkeleton::ADCC_DrivingHmiEnvSkeleton(
  ara::com::InstanceIdentifier instance, ara::com::MethodCallProcessingMode mode)
  : Base(std::move(instance), mode),
    NtfDynamicObjectList_Drvg(backend_skeletons_.size()),
    NtfStaticObjectList_Drvg(backend_skeletons_.size()),
    NtfLaneLineListt_Drvg(backend_skeletons_.size()),
    NtfRoadMarkerList(backend_skeletons_.size()),
    NtfLaneLineList_Ext_Drvg(backend_skeletons_.size()),
    NtfRoadStruct_Drvg(backend_skeletons_.size()),
    NtfFreeSpace(backend_skeletons_.size()),
    NtfOCCList(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_DrivingHmiEnvSkeleton::ADCC_DrivingHmiEnvSkeleton(
  ara::core::InstanceSpecifier specifier, ara::com::MethodCallProcessingMode mode)
  : Base(std::move(specifier), mode),
    NtfDynamicObjectList_Drvg(backend_skeletons_.size()),
    NtfStaticObjectList_Drvg(backend_skeletons_.size()),
    NtfLaneLineListt_Drvg(backend_skeletons_.size()),
    NtfRoadMarkerList(backend_skeletons_.size()),
    NtfLaneLineList_Ext_Drvg(backend_skeletons_.size()),
    NtfRoadStruct_Drvg(backend_skeletons_.size()),
    NtfFreeSpace(backend_skeletons_.size()),
    NtfOCCList(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_DrivingHmiEnvSkeleton::ADCC_DrivingHmiEnvSkeleton(
  ara::com::InstanceIdentifierContainer instances, ara::com::MethodCallProcessingMode mode)
  : Base(std::move(instances), mode),
    NtfDynamicObjectList_Drvg(backend_skeletons_.size()),
    NtfStaticObjectList_Drvg(backend_skeletons_.size()),
    NtfLaneLineListt_Drvg(backend_skeletons_.size()),
    NtfRoadMarkerList(backend_skeletons_.size()),
    NtfLaneLineList_Ext_Drvg(backend_skeletons_.size()),
    NtfRoadStruct_Drvg(backend_skeletons_.size()),
    NtfFreeSpace(backend_skeletons_.size()),
    NtfOCCList(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_DrivingHmiEnvSkeleton::ADCC_DrivingHmiEnvSkeleton(ADCC_DrivingHmiEnvSkeleton &&that)
  : Base(std::move(that)),
    NtfDynamicObjectList_Drvg(std::move(that.NtfDynamicObjectList_Drvg)),
    NtfStaticObjectList_Drvg(std::move(that.NtfStaticObjectList_Drvg)),
    NtfLaneLineListt_Drvg(std::move(that.NtfLaneLineListt_Drvg)),
    NtfRoadMarkerList(std::move(that.NtfRoadMarkerList)),
    NtfLaneLineList_Ext_Drvg(std::move(that.NtfLaneLineList_Ext_Drvg)),
    NtfRoadStruct_Drvg(std::move(that.NtfRoadStruct_Drvg)),
    NtfFreeSpace(std::move(that.NtfFreeSpace)),
    NtfOCCList(std::move(that.NtfOCCList)) {
 //
}

ADCC_DrivingHmiEnvSkeleton::~ADCC_DrivingHmiEnvSkeleton(){

}

void ADCC_DrivingHmiEnvSkeleton::SetOwners(){
  for (auto &base : backend_skeletons_){
  	auto skeleton = dynamic_cast<ADCC_DrivingHmiEnvSkeletonInterface*>(base.get());
  	assert(skeleton != nullptr);

  	skeleton->SetOwner(this);
  	NtfDynamicObjectList_Drvg.AddOwner(skeleton->GetNtfDynamicObjectList_DrvgEvent());
  	NtfStaticObjectList_Drvg.AddOwner(skeleton->GetNtfStaticObjectList_DrvgEvent());
  	NtfLaneLineListt_Drvg.AddOwner(skeleton->GetNtfLaneLineListt_DrvgEvent());
  	NtfRoadMarkerList.AddOwner(skeleton->GetNtfRoadMarkerListEvent());
  	NtfLaneLineList_Ext_Drvg.AddOwner(skeleton->GetNtfLaneLineList_Ext_DrvgEvent());
  	NtfRoadStruct_Drvg.AddOwner(skeleton->GetNtfRoadStruct_DrvgEvent());
  	NtfFreeSpace.AddOwner(skeleton->GetNtfFreeSpaceEvent());
  	NtfOCCList.AddOwner(skeleton->GetNtfOCCListEvent());
  }
}

} // namespace skeleton
} // namespace adcc_drivinghmienv
} // namespace ns

