///
/// @file       ADCC_DrivingHmiEnv_proxy.cc
/// @brief
///
///
# include "ns/adcc_drivinghmienv/ADCC_DrivingHmiEnv_proxy.h"

namespace ns {
namespace adcc_drivinghmienv {
namespace proxy {

ADCC_DrivingHmiEnvProxy::ADCC_DrivingHmiEnvProxy(HandleType const &handle)
  : Base(handle),
    NtfDynamicObjectList_Drvg(backend_proxy_->GetNtfDynamicObjectList_DrvgEvent()),
    NtfStaticObjectList_Drvg(backend_proxy_->GetNtfStaticObjectList_DrvgEvent()),
    NtfLaneLineListt_Drvg(backend_proxy_->GetNtfLaneLineListt_DrvgEvent()),
    NtfRoadMarkerList(backend_proxy_->GetNtfRoadMarkerListEvent()),
    NtfLaneLineList_Ext_Drvg(backend_proxy_->GetNtfLaneLineList_Ext_DrvgEvent()),
    NtfRoadStruct_Drvg(backend_proxy_->GetNtfRoadStruct_DrvgEvent()),
    NtfFreeSpace(backend_proxy_->GetNtfFreeSpaceEvent()),
    NtfOCCList(backend_proxy_->GetNtfOCCListEvent()) {
  //
 }

ADCC_DrivingHmiEnvProxy::ADCC_DrivingHmiEnvProxy(ConstructionToken &&token)
  : Base(std::move(token)),
    NtfDynamicObjectList_Drvg(backend_proxy_->GetNtfDynamicObjectList_DrvgEvent()),
    NtfStaticObjectList_Drvg(backend_proxy_->GetNtfStaticObjectList_DrvgEvent()),
    NtfLaneLineListt_Drvg(backend_proxy_->GetNtfLaneLineListt_DrvgEvent()),
    NtfRoadMarkerList(backend_proxy_->GetNtfRoadMarkerListEvent()),
    NtfLaneLineList_Ext_Drvg(backend_proxy_->GetNtfLaneLineList_Ext_DrvgEvent()),
    NtfRoadStruct_Drvg(backend_proxy_->GetNtfRoadStruct_DrvgEvent()),
    NtfFreeSpace(backend_proxy_->GetNtfFreeSpaceEvent()),
    NtfOCCList(backend_proxy_->GetNtfOCCListEvent()) {
  //
 }

ADCC_DrivingHmiEnvProxy::~ADCC_DrivingHmiEnvProxy() {

}

} // namespace proxy
} // namespace adcc_drivinghmienv
} // namespace ns

