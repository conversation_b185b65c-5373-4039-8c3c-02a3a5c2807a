///
/// @file       ADCC_DrivingFuncInfo_proxy.cc
/// @brief
///
///
# include "ns/adcc_drivingfuncinfo/ADCC_DrivingFuncInfo_proxy.h"

namespace ns {
namespace adcc_drivingfuncinfo {
namespace proxy {

ADCC_DrivingFuncInfoProxy::ADCC_DrivingFuncInfoProxy(HandleType const &handle)
  : Base(handle),
    NtfDrivingLvl12_Drvg(backend_proxy_->GetNtfDrivingLvl12_DrvgEvent()),
    NtfDrivingSafetyFuncInfo_Drvg(backend_proxy_->GetNtfDrivingSafetyFuncInfo_DrvgEvent()),
    NtfDrivingLvl2Plus_Drvg(backend_proxy_->GetNtfDrivingLvl2Plus_DrvgEvent()),
    NtfCNOAPathDetail(backend_proxy_->GetNtfCNOAPathDetailEvent()),
    CNOATrainingPathInfo(backend_proxy_->GetCNOATrainingPathInfoEvent()),
    CNOARepalyPathInfo(backend_proxy_->GetCNOARepalyPathInfoEvent()),
    NtfOffRoadPathDetail(backend_proxy_->GetNtfOffRoadPathDetailEvent()),
    NtfOffRoadTrainingPathInfo(backend_proxy_->GetNtfOffRoadTrainingPathInfoEvent()),
    NtfOffRoadRepalyPathInfo(backend_proxy_->GetNtfOffRoadRepalyPathInfoEvent()) {
  //
 }

ADCC_DrivingFuncInfoProxy::ADCC_DrivingFuncInfoProxy(ConstructionToken &&token)
  : Base(std::move(token)),
    NtfDrivingLvl12_Drvg(backend_proxy_->GetNtfDrivingLvl12_DrvgEvent()),
    NtfDrivingSafetyFuncInfo_Drvg(backend_proxy_->GetNtfDrivingSafetyFuncInfo_DrvgEvent()),
    NtfDrivingLvl2Plus_Drvg(backend_proxy_->GetNtfDrivingLvl2Plus_DrvgEvent()),
    NtfCNOAPathDetail(backend_proxy_->GetNtfCNOAPathDetailEvent()),
    CNOATrainingPathInfo(backend_proxy_->GetCNOATrainingPathInfoEvent()),
    CNOARepalyPathInfo(backend_proxy_->GetCNOARepalyPathInfoEvent()),
    NtfOffRoadPathDetail(backend_proxy_->GetNtfOffRoadPathDetailEvent()),
    NtfOffRoadTrainingPathInfo(backend_proxy_->GetNtfOffRoadTrainingPathInfoEvent()),
    NtfOffRoadRepalyPathInfo(backend_proxy_->GetNtfOffRoadRepalyPathInfoEvent()) {
  //
 }

ADCC_DrivingFuncInfoProxy::~ADCC_DrivingFuncInfoProxy() {

}

} // namespace proxy
} // namespace adcc_drivingfuncinfo
} // namespace ns

