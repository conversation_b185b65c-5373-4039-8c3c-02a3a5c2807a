///
/// @file       ADCC_ParkingFuncInfo_proxy.cc
/// @brief
///
///
# include "ns/adcc_parkingfuncinfo/ADCC_ParkingFuncInfo_proxy.h"

namespace ns {
namespace adcc_parkingfuncinfo {
namespace proxy {

ADCC_ParkingFuncInfoProxy::ADCC_ParkingFuncInfoProxy(HandleType const &handle)
  : Base(handle),
    NtfAdvanceParkingInfo(backend_proxy_->GetNtfAdvanceParkingInfoEvent()),
    NtfHPAPathDetail(backend_proxy_->GetNtfHPAPathDetailEvent()),
    NtfHPATrainigPathInfo(backend_proxy_->GetNtfHPATrainigPathInfoEvent()),
    NtfVPASelfBuildMapDate(backend_proxy_->GetNtfVPASelfBuildMapDateEvent()),
    NtfHPARepalyPathInfo(backend_proxy_->GetNtfHPARepalyPathInfoEvent()),
    NtfParkingL2GeneralInfo(backend_proxy_->GetNtfParkingL2GeneralInfoEvent()),
    NtfAPARPAInfo_Ext(backend_proxy_->GetNtfAPARPAInfo_ExtEvent()),
    NtfSvsFuncStatus(backend_proxy_->GetNtfSvsFuncStatusEvent()),
    NtfHPASelfBuiltMapInfo(backend_proxy_->GetNtfHPASelfBuiltMapInfoEvent()),
    NtfHPAPathDetail_Ext(backend_proxy_->GetNtfHPAPathDetail_ExtEvent()) {
  //
 }

ADCC_ParkingFuncInfoProxy::ADCC_ParkingFuncInfoProxy(ConstructionToken &&token)
  : Base(std::move(token)),
    NtfAdvanceParkingInfo(backend_proxy_->GetNtfAdvanceParkingInfoEvent()),
    NtfHPAPathDetail(backend_proxy_->GetNtfHPAPathDetailEvent()),
    NtfHPATrainigPathInfo(backend_proxy_->GetNtfHPATrainigPathInfoEvent()),
    NtfVPASelfBuildMapDate(backend_proxy_->GetNtfVPASelfBuildMapDateEvent()),
    NtfHPARepalyPathInfo(backend_proxy_->GetNtfHPARepalyPathInfoEvent()),
    NtfParkingL2GeneralInfo(backend_proxy_->GetNtfParkingL2GeneralInfoEvent()),
    NtfAPARPAInfo_Ext(backend_proxy_->GetNtfAPARPAInfo_ExtEvent()),
    NtfSvsFuncStatus(backend_proxy_->GetNtfSvsFuncStatusEvent()),
    NtfHPASelfBuiltMapInfo(backend_proxy_->GetNtfHPASelfBuiltMapInfoEvent()),
    NtfHPAPathDetail_Ext(backend_proxy_->GetNtfHPAPathDetail_ExtEvent()) {
  //
 }

ADCC_ParkingFuncInfoProxy::~ADCC_ParkingFuncInfoProxy() {

}

} // namespace proxy
} // namespace adcc_parkingfuncinfo
} // namespace ns

