///
/// @file       ADCC_ParkingHmiEnv_proxy.cc
/// @brief
///
///
# include "ns/adcc_parkinghmienv/ADCC_ParkingHmiEnv_proxy.h"

namespace ns {
namespace adcc_parkinghmienv {
namespace proxy {

ADCC_ParkingHmiEnvProxy::ADCC_ParkingHmiEnvProxy(HandleType const &handle)
  : Base(handle),
    NtfDynamicObjectList_Prkg(backend_proxy_->GetNtfDynamicObjectList_PrkgEvent()),
    NtfStaticObjectList_Prkg(backend_proxy_->GetNtfStaticObjectList_PrkgEvent()),
    NtfParkingSlot_Prkg(backend_proxy_->GetNtfParkingSlot_PrkgEvent()) {
  //
 }

ADCC_ParkingHmiEnvProxy::ADCC_ParkingHmiEnvProxy(ConstructionToken &&token)
  : Base(std::move(token)),
    NtfDynamicObjectList_Prkg(backend_proxy_->GetNtfDynamicObjectList_PrkgEvent()),
    NtfStaticObjectList_Prkg(backend_proxy_->GetNtfStaticObjectList_PrkgEvent()),
    NtfParkingSlot_Prkg(backend_proxy_->GetNtfParkingSlot_PrkgEvent()) {
  //
 }

ADCC_ParkingHmiEnvProxy::~ADCC_ParkingHmiEnvProxy() {

}

} // namespace proxy
} // namespace adcc_parkinghmienv
} // namespace ns

