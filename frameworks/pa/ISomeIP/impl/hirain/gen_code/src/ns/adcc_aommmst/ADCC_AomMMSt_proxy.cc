///
/// @file       ADCC_AomMMSt_proxy.cc
/// @brief
///
///
# include "ns/adcc_aommmst/ADCC_AomMMSt_proxy.h"

namespace ns {
namespace adcc_aommmst {
namespace proxy {

ADCC_AomMMStProxy::ADCC_AomMMStProxy(HandleType const &handle)
  : Base(handle),
    notifyExtMMResWLnk(backend_proxy_->GetnotifyExtMMResWLnkEvent()) {
  //
 }

ADCC_AomMMStProxy::ADCC_AomMMStProxy(ConstructionToken &&token)
  : Base(std::move(token)),
    notifyExtMMResWLnk(backend_proxy_->GetnotifyExtMMResWLnkEvent()) {
  //
 }

ADCC_AomMMStProxy::~ADCC_AomMMStProxy() {

}

} // namespace proxy
} // namespace adcc_aommmst
} // namespace ns

