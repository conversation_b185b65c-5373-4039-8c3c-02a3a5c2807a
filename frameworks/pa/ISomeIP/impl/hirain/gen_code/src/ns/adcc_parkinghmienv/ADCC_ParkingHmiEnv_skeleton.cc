///
/// @file       ADCC_ParkingHmiEnv_skeleton.cc
/// @brief
///
///
#include "ns/adcc_parkinghmienv/ADCC_ParkingHmiEnv_skeleton.h"

namespace ns {
namespace adcc_parkinghmienv {
namespace skeleton {

ADCC_ParkingHmiEnvSkeleton::ADCC_ParkingHmiEnvSkeleton(ConstructionToken &&token)
  : Base(std::move(token)),
    NtfDynamicObjectList_Prkg(backend_skeletons_.size()),
    NtfStaticObjectList_Prkg(backend_skeletons_.size()),
    NtfParkingSlot_Prkg(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_ParkingHmiEnvSkeleton::ADCC_ParkingHmiEnvSkeleton(
  ara::com::InstanceIdentifier instance, ara::com::MethodCallProcessingMode mode)
  : Base(std::move(instance), mode),
    NtfDynamicObjectList_Prkg(backend_skeletons_.size()),
    NtfStaticObjectList_Prkg(backend_skeletons_.size()),
    NtfParkingSlot_Prkg(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_ParkingHmiEnvSkeleton::ADCC_ParkingHmiEnvSkeleton(
  ara::core::InstanceSpecifier specifier, ara::com::MethodCallProcessingMode mode)
  : Base(std::move(specifier), mode),
    NtfDynamicObjectList_Prkg(backend_skeletons_.size()),
    NtfStaticObjectList_Prkg(backend_skeletons_.size()),
    NtfParkingSlot_Prkg(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_ParkingHmiEnvSkeleton::ADCC_ParkingHmiEnvSkeleton(
  ara::com::InstanceIdentifierContainer instances, ara::com::MethodCallProcessingMode mode)
  : Base(std::move(instances), mode),
    NtfDynamicObjectList_Prkg(backend_skeletons_.size()),
    NtfStaticObjectList_Prkg(backend_skeletons_.size()),
    NtfParkingSlot_Prkg(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_ParkingHmiEnvSkeleton::ADCC_ParkingHmiEnvSkeleton(ADCC_ParkingHmiEnvSkeleton &&that)
  : Base(std::move(that)),
    NtfDynamicObjectList_Prkg(std::move(that.NtfDynamicObjectList_Prkg)),
    NtfStaticObjectList_Prkg(std::move(that.NtfStaticObjectList_Prkg)),
    NtfParkingSlot_Prkg(std::move(that.NtfParkingSlot_Prkg)) {
 //
}

ADCC_ParkingHmiEnvSkeleton::~ADCC_ParkingHmiEnvSkeleton(){

}

void ADCC_ParkingHmiEnvSkeleton::SetOwners(){
  for (auto &base : backend_skeletons_){
  	auto skeleton = dynamic_cast<ADCC_ParkingHmiEnvSkeletonInterface*>(base.get());
  	assert(skeleton != nullptr);

  	skeleton->SetOwner(this);
  	NtfDynamicObjectList_Prkg.AddOwner(skeleton->GetNtfDynamicObjectList_PrkgEvent());
  	NtfStaticObjectList_Prkg.AddOwner(skeleton->GetNtfStaticObjectList_PrkgEvent());
  	NtfParkingSlot_Prkg.AddOwner(skeleton->GetNtfParkingSlot_PrkgEvent());
  }
}

} // namespace skeleton
} // namespace adcc_parkinghmienv
} // namespace ns

