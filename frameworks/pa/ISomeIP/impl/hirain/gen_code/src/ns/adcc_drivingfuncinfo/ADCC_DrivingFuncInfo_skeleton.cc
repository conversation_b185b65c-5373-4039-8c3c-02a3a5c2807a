///
/// @file       ADCC_DrivingFuncInfo_skeleton.cc
/// @brief
///
///
#include "ns/adcc_drivingfuncinfo/ADCC_DrivingFuncInfo_skeleton.h"

namespace ns {
namespace adcc_drivingfuncinfo {
namespace skeleton {

ADCC_DrivingFuncInfoSkeleton::ADCC_DrivingFuncInfoSkeleton(ConstructionToken &&token)
  : Base(std::move(token)),
    NtfDrivingLvl12_Drvg(backend_skeletons_.size()),
    NtfDrivingSafetyFuncInfo_Drvg(backend_skeletons_.size()),
    NtfDrivingLvl2Plus_Drvg(backend_skeletons_.size()),
    NtfCNOAPathDetail(backend_skeletons_.size()),
    CNOATrainingPathInfo(backend_skeletons_.size()),
    CNOARepalyPathInfo(backend_skeletons_.size()),
    NtfOffRoadPathDetail(backend_skeletons_.size()),
    NtfOffRoadTrainingPathInfo(backend_skeletons_.size()),
    NtfOffRoadRepalyPathInfo(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_DrivingFuncInfoSkeleton::ADCC_DrivingFuncInfoSkeleton(
  ara::com::InstanceIdentifier instance, ara::com::MethodCallProcessingMode mode)
  : Base(std::move(instance), mode),
    NtfDrivingLvl12_Drvg(backend_skeletons_.size()),
    NtfDrivingSafetyFuncInfo_Drvg(backend_skeletons_.size()),
    NtfDrivingLvl2Plus_Drvg(backend_skeletons_.size()),
    NtfCNOAPathDetail(backend_skeletons_.size()),
    CNOATrainingPathInfo(backend_skeletons_.size()),
    CNOARepalyPathInfo(backend_skeletons_.size()),
    NtfOffRoadPathDetail(backend_skeletons_.size()),
    NtfOffRoadTrainingPathInfo(backend_skeletons_.size()),
    NtfOffRoadRepalyPathInfo(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_DrivingFuncInfoSkeleton::ADCC_DrivingFuncInfoSkeleton(
  ara::core::InstanceSpecifier specifier, ara::com::MethodCallProcessingMode mode)
  : Base(std::move(specifier), mode),
    NtfDrivingLvl12_Drvg(backend_skeletons_.size()),
    NtfDrivingSafetyFuncInfo_Drvg(backend_skeletons_.size()),
    NtfDrivingLvl2Plus_Drvg(backend_skeletons_.size()),
    NtfCNOAPathDetail(backend_skeletons_.size()),
    CNOATrainingPathInfo(backend_skeletons_.size()),
    CNOARepalyPathInfo(backend_skeletons_.size()),
    NtfOffRoadPathDetail(backend_skeletons_.size()),
    NtfOffRoadTrainingPathInfo(backend_skeletons_.size()),
    NtfOffRoadRepalyPathInfo(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_DrivingFuncInfoSkeleton::ADCC_DrivingFuncInfoSkeleton(
  ara::com::InstanceIdentifierContainer instances, ara::com::MethodCallProcessingMode mode)
  : Base(std::move(instances), mode),
    NtfDrivingLvl12_Drvg(backend_skeletons_.size()),
    NtfDrivingSafetyFuncInfo_Drvg(backend_skeletons_.size()),
    NtfDrivingLvl2Plus_Drvg(backend_skeletons_.size()),
    NtfCNOAPathDetail(backend_skeletons_.size()),
    CNOATrainingPathInfo(backend_skeletons_.size()),
    CNOARepalyPathInfo(backend_skeletons_.size()),
    NtfOffRoadPathDetail(backend_skeletons_.size()),
    NtfOffRoadTrainingPathInfo(backend_skeletons_.size()),
    NtfOffRoadRepalyPathInfo(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_DrivingFuncInfoSkeleton::ADCC_DrivingFuncInfoSkeleton(ADCC_DrivingFuncInfoSkeleton &&that)
  : Base(std::move(that)),
    NtfDrivingLvl12_Drvg(std::move(that.NtfDrivingLvl12_Drvg)),
    NtfDrivingSafetyFuncInfo_Drvg(std::move(that.NtfDrivingSafetyFuncInfo_Drvg)),
    NtfDrivingLvl2Plus_Drvg(std::move(that.NtfDrivingLvl2Plus_Drvg)),
    NtfCNOAPathDetail(std::move(that.NtfCNOAPathDetail)),
    CNOATrainingPathInfo(std::move(that.CNOATrainingPathInfo)),
    CNOARepalyPathInfo(std::move(that.CNOARepalyPathInfo)),
    NtfOffRoadPathDetail(std::move(that.NtfOffRoadPathDetail)),
    NtfOffRoadTrainingPathInfo(std::move(that.NtfOffRoadTrainingPathInfo)),
    NtfOffRoadRepalyPathInfo(std::move(that.NtfOffRoadRepalyPathInfo)) {
 //
}

ADCC_DrivingFuncInfoSkeleton::~ADCC_DrivingFuncInfoSkeleton(){

}

void ADCC_DrivingFuncInfoSkeleton::SetOwners(){
  for (auto &base : backend_skeletons_){
  	auto skeleton = dynamic_cast<ADCC_DrivingFuncInfoSkeletonInterface*>(base.get());
  	assert(skeleton != nullptr);

  	skeleton->SetOwner(this);
  	NtfDrivingLvl12_Drvg.AddOwner(skeleton->GetNtfDrivingLvl12_DrvgEvent());
  	NtfDrivingSafetyFuncInfo_Drvg.AddOwner(skeleton->GetNtfDrivingSafetyFuncInfo_DrvgEvent());
  	NtfDrivingLvl2Plus_Drvg.AddOwner(skeleton->GetNtfDrivingLvl2Plus_DrvgEvent());
  	NtfCNOAPathDetail.AddOwner(skeleton->GetNtfCNOAPathDetailEvent());
  	CNOATrainingPathInfo.AddOwner(skeleton->GetCNOATrainingPathInfoEvent());
  	CNOARepalyPathInfo.AddOwner(skeleton->GetCNOARepalyPathInfoEvent());
  	NtfOffRoadPathDetail.AddOwner(skeleton->GetNtfOffRoadPathDetailEvent());
  	NtfOffRoadTrainingPathInfo.AddOwner(skeleton->GetNtfOffRoadTrainingPathInfoEvent());
  	NtfOffRoadRepalyPathInfo.AddOwner(skeleton->GetNtfOffRoadRepalyPathInfoEvent());
  }
}

} // namespace skeleton
} // namespace adcc_drivingfuncinfo
} // namespace ns

