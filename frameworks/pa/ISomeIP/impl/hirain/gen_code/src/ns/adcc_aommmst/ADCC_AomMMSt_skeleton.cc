///
/// @file       ADCC_AomMMSt_skeleton.cc
/// @brief
///
///
#include "ns/adcc_aommmst/ADCC_AomMMSt_skeleton.h"

namespace ns {
namespace adcc_aommmst {
namespace skeleton {

ADCC_AomMMStSkeleton::ADCC_AomMMStSkeleton(ConstructionToken &&token)
  : Base(std::move(token)),
    notifyExtMMResWLnk(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_AomMMStSkeleton::ADCC_AomMMStSkeleton(
  ara::com::InstanceIdentifier instance, ara::com::MethodCallProcessingMode mode)
  : Base(std::move(instance), mode),
    notifyExtMMResWLnk(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_AomMMStSkeleton::ADCC_AomMMStSkeleton(
  ara::core::InstanceSpecifier specifier, ara::com::MethodCallProcessingMode mode)
  : Base(std::move(specifier), mode),
    notifyExtMMResWLnk(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_AomMMStSkeleton::ADCC_AomMMStSkeleton(
  ara::com::InstanceIdentifierContainer instances, ara::com::MethodCallProcessingMode mode)
  : Base(std::move(instances), mode),
    notifyExtMMResWLnk(backend_skeletons_.size()) {
  SetOwners();
}

ADCC_AomMMStSkeleton::ADCC_AomMMStSkeleton(ADCC_AomMMStSkeleton &&that)
  : Base(std::move(that)),
    notifyExtMMResWLnk(std::move(that.notifyExtMMResWLnk)) {
 //
}

ADCC_AomMMStSkeleton::~ADCC_AomMMStSkeleton(){

}

void ADCC_AomMMStSkeleton::SetOwners(){
  for (auto &base : backend_skeletons_){
  	auto skeleton = dynamic_cast<ADCC_AomMMStSkeletonInterface*>(base.get());
  	assert(skeleton != nullptr);

  	skeleton->SetOwner(this);
  	notifyExtMMResWLnk.AddOwner(skeleton->GetnotifyExtMMResWLnkEvent());
  }
}

} // namespace skeleton
} // namespace adcc_aommmst
} // namespace ns

