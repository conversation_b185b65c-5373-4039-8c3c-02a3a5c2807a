///
/// @file       ADCC_DrivingFuncInfo_skeleton_impl.cc
/// @brief
///
///
#include "ara/com/networkbinding/someip/ns/adcc_drivingfuncinfo/ADCC_DrivingFuncInfo_skeleton_impl.h"
#include "ara/com/networkbinding/someip/skeleton/skeleton_factory_impl.h"
#include "ara/com/service_identifier.h"

namespace ara {
namespace com {
namespace networkbinding {
namespace someip {
namespace ns {
namespace adcc_drivingfuncinfo {
namespace skeleton {

namespace {

someip::skeleton::SkeletonFactoryImpl<ADCC_DrivingFuncInfoSkeletonImpl> g_ADCC_DrivingFuncInfoSkeletonFactoryImpl;
} // namespace

ADCC_DrivingFuncInfoSkeletonImpl::ADCC_DrivingFuncInfoSkeletonImpl(com::InstanceIdentifier const &instance)
  : Base(MakeServiceIdentifier<ServiceAttrs>(), instance),
    NtfDrivingLvl12_Drvg_(this),
    NtfDrivingSafetyFuncInfo_Drvg_(this),
    NtfDrivingLvl2Plus_Drvg_(this),
    NtfCNOAPathDetail_(this),
    CNOATrainingPathInfo_(this),
    CNOARepalyPathInfo_(this),
    NtfOffRoadPathDetail_(this),
    NtfOffRoadTrainingPathInfo_(this),
    NtfOffRoadRepalyPathInfo_(this) {
 //
}

ADCC_DrivingFuncInfoSkeletonImpl::~ADCC_DrivingFuncInfoSkeletonImpl() {

}

void ADCC_DrivingFuncInfoSkeletonImpl::SetOwner(::ns::adcc_drivingfuncinfo::skeleton::ADCC_DrivingFuncInfoSkeleton *owner) {
  SetMethodOwner(owner);

}

} // namespace skeleton
} // namespace adcc_drivingfuncinfo
} // namespace ns
} // namespace someip
} // namespace networkbinding
} // namespace com
} // namespace ara
