///
/// @file       ADCC_SensorInfo_skeleton_impl.cc
/// @brief
///
///
#include "ara/com/networkbinding/someip/ns/adcc_sensorinfo/ADCC_SensorInfo_skeleton_impl.h"
#include "ara/com/networkbinding/someip/skeleton/skeleton_factory_impl.h"
#include "ara/com/service_identifier.h"

namespace ara {
namespace com {
namespace networkbinding {
namespace someip {
namespace ns {
namespace adcc_sensorinfo {
namespace skeleton {

namespace {

someip::skeleton::SkeletonFactoryImpl<ADCC_SensorInfoSkeletonImpl> g_ADCC_SensorInfoSkeletonFactoryImpl;
} // namespace

ADCC_SensorInfoSkeletonImpl::ADCC_SensorInfoSkeletonImpl(com::InstanceIdentifier const &instance)
  : Base(MakeServiceIdentifier<ServiceAttrs>(), instance),
    NtfSensorStAll_(this) {
 //
}

ADCC_SensorInfoSkeletonImpl::~ADCC_SensorInfoSkeletonImpl() {

}

void ADCC_SensorInfoSkeletonImpl::SetOwner(::ns::adcc_sensorinfo::skeleton::ADCC_SensorInfoSkeleton *owner) {
  SetMethodOwner(owner);

}

} // namespace skeleton
} // namespace adcc_sensorinfo
} // namespace ns
} // namespace someip
} // namespace networkbinding
} // namespace com
} // namespace ara
