///
/// @file       ADCC_AomMMSt_skeleton_impl.cc
/// @brief
///
///
#include "ara/com/networkbinding/someip/ns/adcc_aommmst/ADCC_AomMMSt_skeleton_impl.h"
#include "ara/com/networkbinding/someip/skeleton/skeleton_factory_impl.h"
#include "ara/com/service_identifier.h"

namespace ara {
namespace com {
namespace networkbinding {
namespace someip {
namespace ns {
namespace adcc_aommmst {
namespace skeleton {

namespace {

someip::skeleton::SkeletonFactoryImpl<ADCC_AomMMStSkeletonImpl> g_ADCC_AomMMStSkeletonFactoryImpl;
} // namespace

ADCC_AomMMStSkeletonImpl::ADCC_AomMMStSkeletonImpl(com::InstanceIdentifier const &instance)
  : Base(MakeServiceIdentifier<ServiceAttrs>(), instance),
    notifyExtMMResWLnk_(this) {
 //
}

ADCC_AomMMStSkeletonImpl::~ADCC_AomMMStSkeletonImpl() {

}

void ADCC_AomMMStSkeletonImpl::SetOwner(::ns::adcc_aommmst::skeleton::ADCC_AomMMStSkeleton *owner) {
  SetMethodOwner(owner);

}

} // namespace skeleton
} // namespace adcc_aommmst
} // namespace ns
} // namespace someip
} // namespace networkbinding
} // namespace com
} // namespace ara
