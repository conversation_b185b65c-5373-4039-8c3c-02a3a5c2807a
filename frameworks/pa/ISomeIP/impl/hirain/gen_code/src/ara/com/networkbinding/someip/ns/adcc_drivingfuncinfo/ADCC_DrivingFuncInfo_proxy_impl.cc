///
/// @file       ADCC_DrivingFuncInfo_proxy_impl.cc
/// @brief
///
///
#include "ara/com/networkbinding/someip/ns/adcc_drivingfuncinfo/ADCC_DrivingFuncInfo_proxy_impl.h"
#include "ara/com/networkbinding/someip/proxy/proxy_factory_impl.h"
#include "ara/com/service_identifier.h"

namespace ara {
namespace com {
namespace networkbinding {
namespace someip {
namespace ns {
namespace adcc_drivingfuncinfo {
namespace proxy {

namespace {

someip::proxy::ProxyFactoryImpl<ADCC_DrivingFuncInfoProxyImpl> g_ADCC_DrivingFuncInfoProxyFactoryImpl;

} // namespace

ADCC_DrivingFuncInfoProxyImpl::ADCC_DrivingFuncInfoProxyImpl(com::InstanceIdentifier const &instance)
  : Base(MakeServiceIdentifier<ServiceAttrs>(), instance),
    NtfDrivingLvl12_Drvg_(this),
    NtfDrivingSafetyFuncInfo_Drvg_(this),
    NtfDrivingLvl2Plus_Drvg_(this),
    NtfCNOAPathDetail_(this),
    CNOATrainingPathInfo_(this),
    CNOARepalyPathInfo_(this),
    NtfOffRoadPathDetail_(this),
    NtfOffRoadTrainingPathInfo_(this),
    NtfOffRoadRepalyPathInfo_(this) {
 //
}

ADCC_DrivingFuncInfoProxyImpl::~ADCC_DrivingFuncInfoProxyImpl() {
 //
}

} // namespace proxy
} // namespace adcc_drivingfuncinfo
} // namespace ns
} // namespace someip
} // namespace networkbinding
} // namespace com
} // namespace ara

