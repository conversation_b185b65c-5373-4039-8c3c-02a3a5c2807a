///
/// @file       ADCC_DrivingHmiEnv_skeleton_impl.cc
/// @brief
///
///
#include "ara/com/networkbinding/someip/ns/adcc_drivinghmienv/ADCC_DrivingHmiEnv_skeleton_impl.h"
#include "ara/com/networkbinding/someip/skeleton/skeleton_factory_impl.h"
#include "ara/com/service_identifier.h"

namespace ara {
namespace com {
namespace networkbinding {
namespace someip {
namespace ns {
namespace adcc_drivinghmienv {
namespace skeleton {

namespace {

someip::skeleton::SkeletonFactoryImpl<ADCC_DrivingHmiEnvSkeletonImpl> g_ADCC_DrivingHmiEnvSkeletonFactoryImpl;
} // namespace

ADCC_DrivingHmiEnvSkeletonImpl::ADCC_DrivingHmiEnvSkeletonImpl(com::InstanceIdentifier const &instance)
  : Base(MakeServiceIdentifier<ServiceAttrs>(), instance),
    NtfDynamicObjectList_Drvg_(this),
    NtfStaticObjectList_Drvg_(this),
    NtfLaneLineListt_Drvg_(this),
    NtfRoadMarkerList_(this),
    NtfLaneLineList_Ext_Drvg_(this),
    NtfRoadStruct_Drvg_(this),
    NtfFreeSpace_(this),
    NtfOCCList_(this) {
 //
}

ADCC_DrivingHmiEnvSkeletonImpl::~ADCC_DrivingHmiEnvSkeletonImpl() {

}

void ADCC_DrivingHmiEnvSkeletonImpl::SetOwner(::ns::adcc_drivinghmienv::skeleton::ADCC_DrivingHmiEnvSkeleton *owner) {
  SetMethodOwner(owner);

}

} // namespace skeleton
} // namespace adcc_drivinghmienv
} // namespace ns
} // namespace someip
} // namespace networkbinding
} // namespace com
} // namespace ara
