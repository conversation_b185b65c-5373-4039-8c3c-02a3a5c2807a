///
/// @file       ADCC_DrivingHmiEnv_proxy_impl.cc
/// @brief
///
///
#include "ara/com/networkbinding/someip/ns/adcc_drivinghmienv/ADCC_DrivingHmiEnv_proxy_impl.h"
#include "ara/com/networkbinding/someip/proxy/proxy_factory_impl.h"
#include "ara/com/service_identifier.h"

namespace ara {
namespace com {
namespace networkbinding {
namespace someip {
namespace ns {
namespace adcc_drivinghmienv {
namespace proxy {

namespace {

someip::proxy::ProxyFactoryImpl<ADCC_DrivingHmiEnvProxyImpl> g_ADCC_DrivingHmiEnvProxyFactoryImpl;

} // namespace

ADCC_DrivingHmiEnvProxyImpl::ADCC_DrivingHmiEnvProxyImpl(com::InstanceIdentifier const &instance)
  : Base(MakeServiceIdentifier<ServiceAttrs>(), instance),
    NtfDynamicObjectList_Drvg_(this),
    NtfStaticObjectList_Drvg_(this),
    NtfLaneLineListt_Drvg_(this),
    NtfRoadMarkerList_(this),
    NtfLaneLineList_Ext_Drvg_(this),
    NtfRoadStruct_Drvg_(this),
    NtfFreeSpace_(this),
    NtfOCCList_(this) {
 //
}

ADCC_DrivingHmiEnvProxyImpl::~ADCC_DrivingHmiEnvProxyImpl() {
 //
}

} // namespace proxy
} // namespace adcc_drivinghmienv
} // namespace ns
} // namespace someip
} // namespace networkbinding
} // namespace com
} // namespace ara

