///
/// @file       ADCC_AomMMSt_proxy_impl.cc
/// @brief
///
///
#include "ara/com/networkbinding/someip/ns/adcc_aommmst/ADCC_AomMMSt_proxy_impl.h"
#include "ara/com/networkbinding/someip/proxy/proxy_factory_impl.h"
#include "ara/com/service_identifier.h"

namespace ara {
namespace com {
namespace networkbinding {
namespace someip {
namespace ns {
namespace adcc_aommmst {
namespace proxy {

namespace {

someip::proxy::ProxyFactoryImpl<ADCC_AomMMStProxyImpl> g_ADCC_AomMMStProxyFactoryImpl;

} // namespace

ADCC_AomMMStProxyImpl::ADCC_AomMMStProxyImpl(com::InstanceIdentifier const &instance)
  : Base(MakeServiceIdentifier<ServiceAttrs>(), instance),
    notifyExtMMResWLnk_(this) {
 //
}

ADCC_AomMMStProxyImpl::~ADCC_AomMMStProxyImpl() {
 //
}

} // namespace proxy
} // namespace adcc_aommmst
} // namespace ns
} // namespace someip
} // namespace networkbinding
} // namespace com
} // namespace ara

