///
/// @file       ADCC_SensorInfo_proxy_impl.cc
/// @brief
///
///
#include "ara/com/networkbinding/someip/ns/adcc_sensorinfo/ADCC_SensorInfo_proxy_impl.h"
#include "ara/com/networkbinding/someip/proxy/proxy_factory_impl.h"
#include "ara/com/service_identifier.h"

namespace ara {
namespace com {
namespace networkbinding {
namespace someip {
namespace ns {
namespace adcc_sensorinfo {
namespace proxy {

namespace {

someip::proxy::ProxyFactoryImpl<ADCC_SensorInfoProxyImpl> g_ADCC_SensorInfoProxyFactoryImpl;

} // namespace

ADCC_SensorInfoProxyImpl::ADCC_SensorInfoProxyImpl(com::InstanceIdentifier const &instance)
  : Base(MakeServiceIdentifier<ServiceAttrs>(), instance),
    NtfSensorStAll_(this) {
 //
}

ADCC_SensorInfoProxyImpl::~ADCC_SensorInfoProxyImpl() {
 //
}

} // namespace proxy
} // namespace adcc_sensorinfo
} // namespace ns
} // namespace someip
} // namespace networkbinding
} // namespace com
} // namespace ara

