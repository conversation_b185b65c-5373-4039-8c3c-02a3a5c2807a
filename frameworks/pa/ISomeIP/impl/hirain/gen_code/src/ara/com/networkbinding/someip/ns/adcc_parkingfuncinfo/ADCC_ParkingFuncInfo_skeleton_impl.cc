///
/// @file       ADCC_ParkingFuncInfo_skeleton_impl.cc
/// @brief
///
///
#include "ara/com/networkbinding/someip/ns/adcc_parkingfuncinfo/ADCC_ParkingFuncInfo_skeleton_impl.h"
#include "ara/com/networkbinding/someip/skeleton/skeleton_factory_impl.h"
#include "ara/com/service_identifier.h"

namespace ara {
namespace com {
namespace networkbinding {
namespace someip {
namespace ns {
namespace adcc_parkingfuncinfo {
namespace skeleton {

namespace {

someip::skeleton::SkeletonFactoryImpl<ADCC_ParkingFuncInfoSkeletonImpl> g_ADCC_ParkingFuncInfoSkeletonFactoryImpl;
} // namespace

ADCC_ParkingFuncInfoSkeletonImpl::ADCC_ParkingFuncInfoSkeletonImpl(com::InstanceIdentifier const &instance)
  : Base(MakeServiceIdentifier<ServiceAttrs>(), instance),
    NtfAdvanceParkingInfo_(this),
    NtfHPAPathDetail_(this),
    NtfHPATrainigPathInfo_(this),
    NtfVPASelfBuildMapDate_(this),
    NtfHPARepalyPathInfo_(this),
    NtfParkingL2GeneralInfo_(this),
    NtfAPARPAInfo_Ext_(this),
    NtfSvsFuncStatus_(this),
    NtfHPASelfBuiltMapInfo_(this),
    NtfHPAPathDetail_Ext_(this) {
 //
}

ADCC_ParkingFuncInfoSkeletonImpl::~ADCC_ParkingFuncInfoSkeletonImpl() {

}

void ADCC_ParkingFuncInfoSkeletonImpl::SetOwner(::ns::adcc_parkingfuncinfo::skeleton::ADCC_ParkingFuncInfoSkeleton *owner) {
  SetMethodOwner(owner);

}

} // namespace skeleton
} // namespace adcc_parkingfuncinfo
} // namespace ns
} // namespace someip
} // namespace networkbinding
} // namespace com
} // namespace ara
