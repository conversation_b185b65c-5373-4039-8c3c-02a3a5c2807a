///
/// @file       ADCC_ParkingFuncInfo_proxy_impl.cc
/// @brief
///
///
#include "ara/com/networkbinding/someip/ns/adcc_parkingfuncinfo/ADCC_ParkingFuncInfo_proxy_impl.h"
#include "ara/com/networkbinding/someip/proxy/proxy_factory_impl.h"
#include "ara/com/service_identifier.h"

namespace ara {
namespace com {
namespace networkbinding {
namespace someip {
namespace ns {
namespace adcc_parkingfuncinfo {
namespace proxy {

namespace {

someip::proxy::ProxyFactoryImpl<ADCC_ParkingFuncInfoProxyImpl> g_ADCC_ParkingFuncInfoProxyFactoryImpl;

} // namespace

ADCC_ParkingFuncInfoProxyImpl::ADCC_ParkingFuncInfoProxyImpl(com::InstanceIdentifier const &instance)
  : Base(MakeServiceIdentifier<ServiceAttrs>(), instance),
    NtfAdvanceParkingInfo_(this),
    NtfHPAPathDetail_(this),
    NtfHPATrainigPathInfo_(this),
    NtfVPASelfBuildMapDate_(this),
    NtfHPARepalyPathInfo_(this),
    NtfParkingL2GeneralInfo_(this),
    NtfAPARPAInfo_Ext_(this),
    NtfSvsFuncStatus_(this),
    NtfHPASelfBuiltMapInfo_(this),
    NtfHPAPathDetail_Ext_(this) {
 //
}

ADCC_ParkingFuncInfoProxyImpl::~ADCC_ParkingFuncInfoProxyImpl() {
 //
}

} // namespace proxy
} // namespace adcc_parkingfuncinfo
} // namespace ns
} // namespace someip
} // namespace networkbinding
} // namespace com
} // namespace ara

