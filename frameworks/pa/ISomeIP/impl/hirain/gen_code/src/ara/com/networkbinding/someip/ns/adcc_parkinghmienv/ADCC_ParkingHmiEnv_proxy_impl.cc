///
/// @file       ADCC_ParkingHmiEnv_proxy_impl.cc
/// @brief
///
///
#include "ara/com/networkbinding/someip/ns/adcc_parkinghmienv/ADCC_ParkingHmiEnv_proxy_impl.h"
#include "ara/com/networkbinding/someip/proxy/proxy_factory_impl.h"
#include "ara/com/service_identifier.h"

namespace ara {
namespace com {
namespace networkbinding {
namespace someip {
namespace ns {
namespace adcc_parkinghmienv {
namespace proxy {

namespace {

someip::proxy::ProxyFactoryImpl<ADCC_ParkingHmiEnvProxyImpl> g_ADCC_ParkingHmiEnvProxyFactoryImpl;

} // namespace

ADCC_ParkingHmiEnvProxyImpl::ADCC_ParkingHmiEnvProxyImpl(com::InstanceIdentifier const &instance)
  : Base(MakeServiceIdentifier<ServiceAttrs>(), instance),
    NtfDynamicObjectList_Prkg_(this),
    NtfStaticObjectList_Prkg_(this),
    NtfParkingSlot_Prkg_(this) {
 //
}

ADCC_ParkingHmiEnvProxyImpl::~ADCC_ParkingHmiEnvProxyImpl() {
 //
}

} // namespace proxy
} // namespace adcc_parkinghmienv
} // namespace ns
} // namespace someip
} // namespace networkbinding
} // namespace com
} // namespace ara

