#ifndef HISOMEIP_H
#define HISOMEIP_H

#include "autolink/frameworks/pa/ISomeIP/impl/someip_interface.h"
#include <memory>

namespace AutoLink {
namespace Frameworks {
namespace PA {

class HirainSomeIP : public SomeIPInterface
{
public:
    HirainSomeIP();
    ~HirainSomeIP();

    static std::shared_ptr<HirainSomeIP> Get();

    void Init() override;
    bool IsInited();

    void Start() override;
    void Stop() override;
    void BlockSignal() override;
    void RestoreSignal() override;
    void OnRecvValue(const SomeipPropRxData &propData);
    void RegisterHandler(SomeipPropDataHandler handler);

private:
    HirainSomeIP(const HirainSomeIP &) = delete;
    HirainSomeIP& operator= (const HirainSomeIP &) = delete;

private:
    struct HirainSomeIPPrivate *p_;
};

} // namespace PA
} // namespace Frameworks
} // namespace AutoLink

#endif
