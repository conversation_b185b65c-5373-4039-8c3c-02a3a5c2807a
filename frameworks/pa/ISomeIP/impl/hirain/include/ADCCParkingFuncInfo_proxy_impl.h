///
/// @copyright  This software is the property of HiRain Technologies.
///             Any information contained in this doc should not be reproduced,
///             or used, or disclosed without the written authorization from
///             HiRain Technologies.
///
/// @file      ADCCParkingFuncInfo_proxy_impl.h.h
/// @brief
///
/// <AUTHOR> Hongyang <<EMAIL>>
///
///
#include "ns/adcc_parkingfuncinfo/ADCC_ParkingFuncInfo_proxy.h"

#include "ara/core/io/event_loop_manager.h"
#include "ara/core/io/timer.h"
#include "ara/log/logger.h"
#include "ara/log/logging.h"

#include "autolink/frameworks/pa/ISomeIP/isomeip.h"

namespace ns{
namespace adcc_parkingfuncinfo {

using namespace AutoLink::Frameworks::PA;

class ADCCParkingFuncInfo_proxy_impl {
 public:
  explicit ADCCParkingFuncInfo_proxy_impl(ara::core::io::EventLoop *loop,
                            ara::core::InstanceSpecifier is);
  ~ADCCParkingFuncInfo_proxy_impl();

  void RegisterValueHandel(SomeipPropDataHandler handler);
  void ServiceActive();

  void Start();
  void Stop();

 private:
  ara::log::Logger *logger_;
  ara::core::io::EventLoop *loop_;
  ara::core::InstanceSpecifier is_;

  ara::com::FindServiceHandle adccparkingfuncinfo_finder_;
  std::shared_ptr<proxy::ADCC_ParkingFuncInfoProxy> adccparkingfuncinfo_proxy_;
  SomeipPropDataHandler handler_;
};
} // namespace adcc_parkingfuncinfons
} // namespace ns