///
/// @copyright  This software is the property of HiRain Technologies.
///             Any information contained in this doc should not be reproduced,
///             or used, or disclosed without the written authorization from
///             HiRain Technologies.
///
/// @file      ADCCDrivingHmiEnv_proxy_impl.h.h
/// @brief
///
/// <AUTHOR> Hongyang <<EMAIL>>
///
///
#include "ns/adcc_drivinghmienv/ADCC_DrivingHmiEnv_proxy.h"

#include "ara/core/io/event_loop_manager.h"
#include "ara/core/io/timer.h"
#include "ara/log/logger.h"
#include "ara/log/logging.h"

#include "autolink/frameworks/pa/ISomeIP/isomeip.h"

namespace ns{
namespace adcc_drivinghmienv {

using namespace AutoLink::Frameworks::PA;

class ADCCDrivingHmiEnv_proxy_impl {
 public:
  explicit ADCCDrivingHmiEnv_proxy_impl(ara::core::io::EventLoop *loop,
                            ara::core::InstanceSpecifier is);
  ~ADCCDrivingHmiEnv_proxy_impl();

  void RegisterValueHandel(SomeipPropDataHandler handler);
  void ServiceActive();

 private:
  ara::log::Logger *logger_;
  ara::core::io::EventLoop *loop_;
  ara::core::InstanceSpecifier is_;

  ara::com::FindServiceHandle adccdrivinghmienv_finder_;
  std::shared_ptr<proxy::ADCC_DrivingHmiEnvProxy> adccdrivinghmienv_proxy_;
  SomeipPropDataHandler handler;

};
} // namespace adcc_drivinghmienvns
} //namespace ns