///
/// @copyright  This software is the property of HiRain Technologies.
///             Any information contained in this doc should not be reproduced,
///             or used, or disclosed without the written authorization from
///             HiRain Technologies.
///
/// @file      ADCCSensorInfo_proxy_impl.h
/// @brief
///
/// <AUTHOR> Hongyang <<EMAIL>>
///
///
#include "ns/adcc_sensorinfo/ADCC_SensorInfo_proxy.h"

#include "ara/core/io/event_loop_manager.h"
#include "ara/core/io/timer.h"
#include "ara/log/logger.h"
#include "ara/log/logging.h"

#include "autolink/frameworks/pa/ISomeIP/isomeip.h"
namespace ns{
namespace adcc_sensorinfo {
using namespace AutoLink::Frameworks::PA;

class ADCCSensorInfo_proxy_impl {
 public:
  explicit ADCCSensorInfo_proxy_impl(ara::core::io::EventLoop *loop,
                            ara::core::InstanceSpecifier is);
  ~ADCCSensorInfo_proxy_impl();

  void RegisterValueHandel(SomeipPropDataHandler handler);
  void ServiceActive();

  void Start();
  void Stop();

 private:
  ara::log::Logger *logger_;
  ara::core::io::EventLoop *loop_;
  ara::core::InstanceSpecifier is_;

  ara::com::FindServiceHandle adccsensorinfo_finder_;
  std::shared_ptr<proxy::ADCC_SensorInfoProxy> adccsensorinfo_proxy_;
  SomeipPropDataHandler handler_;
};
} // namespace adcc_sensorinfons
} // namespace ns