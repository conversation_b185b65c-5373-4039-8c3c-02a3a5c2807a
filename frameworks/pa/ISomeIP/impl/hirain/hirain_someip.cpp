#include "autolink/frameworks/core/timer.h"
#include "hirain_someip.h"
#include <atomic>
#include "ara/core/initialization.h"

#include "autolink/frameworks/pa/ISomeIP/impl/hirain/include/ADCCAomMMSt_proxy_impl.h"
#include "autolink/frameworks/pa/ISomeIP/impl/hirain/include/ADCCDrivingFuncInfo_proxy_impl.h"
#include "autolink/frameworks/pa/ISomeIP/impl/hirain/include/ADCCDrivingHmiEnv_proxy_impl.h"
#include "autolink/frameworks/pa/ISomeIP/impl/hirain/include/ADCCParkingFuncInfo_proxy_impl.h"
#include "autolink/frameworks/pa/ISomeIP/impl/hirain/include/ADCCParkingHmiEnv_proxy_impl.h"
#include "autolink/frameworks/pa/ISomeIP/impl/hirain/include/ADCCSensorInfo_proxy_impl.h"
#include "autolink/frameworks/pa/ISomeIP/impl/hirain/gen_code/include/instance_specifier.h"

#include "ara/core/initialization.h"
#include "ara/core/instance_specifier.h"
#include "ara/core/io/event_loop_manager.h"
#include "ara/core/io/timer.h"

#include "ara/log/logger.h"
#include "ara/log/logging.h"

namespace AutoLink {
namespace Frameworks {
namespace PA {

#define LOG_TAG "HirainSomeIP"
using namespace AutoLink::Frameworks::Core;

struct Data
{
    int cycle{0};
    std::vector<std::string> nameVec;
};

std::map<std::string, Data> propMap =
{
    {"AB02_8101",           {100,   {"ACCSts",    "ACCTextMessage", "DrivingCommonSnG"}}},
    {"AB02_8102",           {100,   {"FcwStatus", "AebStatus"}}},
    {"AB02_8103",           {100,   {"DriverhandsoffWarning"}}},
    {"AB01_8010",           {200,   {"ADASSysSts",    "FLCameraSysSts"}}},
    {"AB03_8103",           {200,   {"ViewIdMain",       "PAS_WorkMode",    "NtfMAAInfo"}}},
    {"TrafficLightMsg",     {100,   {"trafficLightType", }}},
};

struct SigTimeout
{
    std::string name{};
    std::shared_ptr<Timer> timer{nullptr};
    bool flag{false};
    int count{0};
    int cycle{0};
    int size{0};

    SigTimeout() {}

    SigTimeout(std::string name_, int cycle_, int _size)
    {
        name = name_;
        cycle = cycle_;
        size = _size;
    }
};

struct HirainSomeIPPrivate
{
    bool isInit{false};
    SomeipPropDataHandler handler{nullptr};

    // 每个serverId下的eventId 一个超时检测
    std::vector<SigTimeout> verSigTimeout;
    std::mutex mt;

    std::atomic<bool> isSTR{false};

    ara::core::io::EventLoop *loop{ara::core::io::GetEventLoopManager().GetDefaultLoop()};

    ara::core::InstanceSpecifier is2{ICC_QNX_INST_ADCC_DRIVINGFUNCINFO};
    ns::adcc_drivingfuncinfo::ADCCDrivingFuncInfo_proxy_impl adccdrivingfuncinfo_client{loop, is2};

    ara::core::InstanceSpecifier is4{ICC_QNX_INST_ADCC_PARKINGFUNCINFO};
    ns::adcc_parkingfuncinfo::ADCCParkingFuncInfo_proxy_impl adccparkingfuncinfo_client{loop, is4};

    ara::core::InstanceSpecifier is6{ICC_QNX_INST_ADCC_SENSORINFO};
    ns::adcc_sensorinfo::ADCCSensorInfo_proxy_impl adccsensorinfo_client{loop, is6};
};

HirainSomeIP::HirainSomeIP() : p_(new HirainSomeIPPrivate)
{
    ara::core::Initialize();
}

HirainSomeIP::~HirainSomeIP()
{

}

std::shared_ptr<HirainSomeIP> HirainSomeIP::Get()
{
    static std::shared_ptr<HirainSomeIP> instance = std::make_shared<HirainSomeIP>();
    if (!instance->IsInited())
        instance->Init();

    return instance;
}

const int LIMIT_100 = 100;
const int LIMIT_LSEQ_100 = 20;
const int LIMIT_OVER_100 = 10;

void HirainSomeIP::Init()
{
    for (auto it : propMap)
    {
        SigTimeout tmp(it.first, it.second.cycle, it.second.nameVec.size());
        p_->verSigTimeout.push_back(tmp);
    }

    // for (auto &it : p_->verSigTimeout)
    // {
    //     if ((0 != it.cycle) && (nullptr == it.timer))  // 100ms
    //     {
    //         it.timer = Timer::Create(it.name.c_str(),  it.cycle, [&]()
    //         {
    //             if (it.flag)
    //             {
    //                 if (it.count < ((it.cycle <= LIMIT_100) ? LIMIT_LSEQ_100 : LIMIT_OVER_100))
    //                 {
    //                     ++it.count;
    //                 }
    //                 else
    //                 {
    //                     LOG_TAG_WARNING("HIRAIN %s recv timeout! ", it.name.c_str());
    //                     if (p_->handler)
    //                     {
    //                         it.flag = false;
    //                         SomeipPropRxData data(it.name, false);
    //                         // p_->handler(data);
    //                         OnRecvValue(data);
    //                     }
    //                 }
    //             }
    //         });
    //     }
    //     if (!(it.timer->IsRunning()))
    //     {
    //         it.timer->Start();
    //     }
    // }

    p_->isInit = true;
}

bool HirainSomeIP::IsInited()
{
    return p_->isInit;
}

void HirainSomeIP::Start()
{
    LOG_TAG_WARNING("Start get_id:%d ", std::this_thread::get_id());

    std::thread th([&](){

    // ara::core::InstanceSpecifier is1(ICC_QNX_INST_ADCC_AOMMMST);
    // autolink::adcc_aommmst::ADCCAomMMSt_proxy_impl adccaommmst_client(loop, is1);
    // adccaommmst_client.ServiceActive();

    p_->adccdrivingfuncinfo_client.RegisterValueHandel(std::bind(&HirainSomeIP::OnRecvValue, this, std::placeholders::_1 ));
    p_->adccdrivingfuncinfo_client.ServiceActive();
    // ara::core::InstanceSpecifier is3(ICC_QNX_INST_ADCC_DRIVINGHMIENV);
    // autolink::adcc_drivinghmienv::ADCCDrivingHmiEnv_proxy_impl adccdrivinghmienv_client(loop, is3);
    // adccdrivinghmienv_client.ServiceActive();

    p_->adccparkingfuncinfo_client.RegisterValueHandel(std::bind(&HirainSomeIP::OnRecvValue, this, std::placeholders::_1 ));
    p_->adccparkingfuncinfo_client.ServiceActive();

    // ara::core::InstanceSpecifier is5(ICC_QNX_INST_ADCC_PARKINGHMIENV);
    // autolink::adcc_parkinghmienv::ADCCParkingHmiEnv_proxy_impl adccparkinghmienv_client(loop, is5);
    // adccparkinghmienv_client.ServiceActive();

    p_->adccsensorinfo_client.RegisterValueHandel(std::bind(&HirainSomeIP::OnRecvValue, this, std::placeholders::_1 ));
    p_->adccsensorinfo_client.ServiceActive();

    LOG_TAG_WARNING("LoopForever start id:%d ", std::this_thread::get_id());

    p_->loop->LoopForever();
    });

    th.detach();
    LOG_TAG_WARNING("Start done ! ");
}

void HirainSomeIP::Stop()
{

}

void HirainSomeIP::BlockSignal()
{
    p_->isSTR = true;
    p_->adccdrivingfuncinfo_client.Stop();
    p_->adccparkingfuncinfo_client.Stop();
    p_->adccsensorinfo_client.Stop();
}

void HirainSomeIP::RestoreSignal()
{
    p_->isSTR = false;
    p_->adccdrivingfuncinfo_client.Start();
    p_->adccparkingfuncinfo_client.Start();
    p_->adccsensorinfo_client.Start();
}

void HirainSomeIP::OnRecvValue(const SomeipPropRxData &propData)
{
    if(p_->isSTR)
    {
        return;
    }
    std::lock_guard<std::mutex> lck(p_->mt);
    for (auto &it : p_->verSigTimeout)
    {
        if (propData.isValid && (propData.prop == it.name))
        {
            it.count = 0;
            it.flag = true;
        }
    }

    if (p_->handler)
    {
        p_->handler(propData);
    }
}

void HirainSomeIP::RegisterHandler(SomeipPropDataHandler handler)
{
    LOG_TAG_WARNING("RegisterSomeIPHandler success! ");
    p_->handler = handler;
}

} // namespace PA
} // namespace Frameworks
} // namespace AutoLink
