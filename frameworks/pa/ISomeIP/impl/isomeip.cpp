#include "autolink/frameworks/pa/ISomeIP/isomeip.h"
#include "autolink/frameworks/pa/ISomeIP/impl/hirain/hirain_someip.h"
#include "autolink/frameworks/log/log.h"

#include <memory>

namespace AutoLink {
namespace Frameworks {
namespace PA {

#define LOG_TAG "ISomeIp"

struct ISomeIPPrivate
{
    std::shared_ptr<SomeIPInterface> sp;
};

ISomeIP::ISomeIP() : p_(new ISomeIPPrivate)
{
// #ifdef HIRAIN
    p_->sp = HirainSomeIP::Get();
// #endif
}

ISomeIP::~ISomeIP()
{
    p_->sp = nullptr;
}

void ISomeIP::BlockSignal()
{
// #ifdef HIRAIN
    HirainSomeIP::Get()->BlockSignal();
// #endif
}

void ISomeIP::RestoreSignal()
{

}

void ISomeIP::RegisterPropValueHandler(SomeipPropDataHandler handler)
{
    p_->sp-><PERSON><PERSON><PERSON><PERSON>(handler);
}

void ISomeIP::Start()
{
    p_->sp->Start();
}


} // namespace PA
} // namespace Frameworks
} // namespace AutoLink
