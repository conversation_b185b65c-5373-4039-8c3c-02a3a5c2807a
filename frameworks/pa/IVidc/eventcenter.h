#ifndef EVENT_CENTER_H
#define EVENT_CENTER_H

#include <functional>
#include <unordered_map>
#include <string>
#include <mutex>

namespace AutoLink {
namespace Frameworks {
namespace PA {

enum IVIDC_EVENT_TYPE {
    IVIDC_EVENT_FIRST_FRAME = 0,
    IVIDC_EVENT_LAST_FRAME = 1,
    IVIDC_EVENT_FRAME_CB = 2,
    IVIDC_EVENT_ERROR = 3
};

typedef std::function<void(IVIDC_EVENT_TYPE)> IVidcEventHandler;
class EventCenter;
class EventCenterManager {
public:
    EventCenterManager(const EventCenterManager &) = delete;
    EventCenterManager &operator=(const EventCenterManager &) = delete;

    EventCenterManager& Get()
    {
        static EventCenterManager manager;
        return manager;
    }
    static void ReleaseAllCenter();
    static void ReleaseCenter(const char* pName);
    static bool CreateCenter(const char *pName, IVidcEventHandler handler);
    static void InvokeEvent(const char *pName, IVIDC_EVENT_TYPE event);

private:
    EventCenterManager();
    ~EventCenterManager();
    static std::shared_ptr<EventCenter> GetCenter(const std::string& pName);

private:
    static std::mutex _mtx;
    static std::unordered_map<std::string, std::shared_ptr<EventCenter>> _centerGroup;
};

class EventCenterPrivate;
class EventCenter {
public:
    ~EventCenter();

    void RegisterEventHandle(IVidcEventHandler);

    void InvokeEvent(IVIDC_EVENT_TYPE);

    EventCenter(const char *pName);
private:
    EventCenterPrivate *_p;

friend EventCenterManager;
};


}
}
}

#endif
