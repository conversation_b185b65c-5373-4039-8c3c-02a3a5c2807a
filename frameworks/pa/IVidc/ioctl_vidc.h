#ifndef IOCTL_VIDC_H
#define IOCTL_VIDC_H

#include <functional>
#include "vidc_types.h"
#include "ioctlClient.h"
#include "vidc_ioctl.h"
#include "IF/ividc_component.h"

namespace AutoLink {
namespace Frameworks {
namespace PA {

struct IoctlVidcPrivate;
class IoctlVidc : public IVidcComponent{
public:
    IoctlVidc(const char *);
    virtual ~IoctlVidc();

    bool Init() override;
public:
    bool IoctlGetParameter(vidc_property_id_type propId, void *payloadPtr, uint32 payloadBytes);
    bool IoctlSetParameter(vidc_property_id_type propId, void *payloadPtr, uint32 payloadBytes);
    bool IoctlSetBuffer(const vidc_buffer_info_type &info);
    bool IoctlGetRequirment(vidc_buffer_reqmnts_type &reqmnts);
    bool IoctlSetRequirment(vidc_buffer_reqmnts_type &reqmnts);
    bool IoctlStateStop(vidc_buffer_type type, bool async = false);
    bool IoctlStateStart(bool async = false);
    bool IoctlStateStart(vidc_start_mode_type mode, bool async = false);
    bool IoctlStateLoadResource(bool async = false);
    bool IoctlStatePause(bool async = false);
    bool IoctlStateResume(bool async = false);
    bool IoctlStateRelease(bool async = false);
    bool IoctlFlushBuffer(vidc_buffer_type mode, bool async = false);
    bool IoctlFreeBuffer(vidc_buffer_type type);

    // bool ChangeStateToExecutingBlock();
    bool IoctlEmptyBuffer(vidc_frame_data_type frameData);
    bool IoctlFillBuffer(vidc_frame_data_type frameData);
    bool processReconfig(vidc_buffer_type mode);
    bool FillAllBuffer();
    bool IsInternalConfig();
    void EnableEventReady();
public:
    static int IoctlEventCallback(uint8 *msgPtr, uint32 length, void *clientPtr);
protected:
    virtual int HandleEvent(vidc_drv_msg_info_type &info, uint32 length);
    void WaitforOutputBufferOut();
    void WaitforStopStateIdel();

private:
    IoctlVidcPrivate *_p;
};

}
}
}

#endif
