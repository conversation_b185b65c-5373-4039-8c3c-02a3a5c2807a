#include <unistd.h>
#include <thread>
#include "../ividc_demux.h"
#include "../ividc_decoder.h"
#include "../allocator/allocator_qnx_window.h"
#include "../allocator/allocator_pmem.h"
#include "autolink/frameworks/pa/IScreen/IScreen.h"
#include "autolink/frameworks/pa/IVidc/eventcenter.h"

using namespace AutoLink::Frameworks::PA;

#define LOG_TAG "IVIDC-TEST"

void demux_decoder()
{
    printf("test1\n");
    IVidcDemux demux("test1");
    IVidcDecoder decoder("test1");
    IScreenWindow &window = *IScreen::Get().CreateWindow();

    EventCenterManager::CreateCenter("test1", [&](IVIDC_EVENT_TYPE event){
        printf("event[%d]\n", event);
        if(event == IVIDC_EVENT_FIRST_FRAME) {
            decoder.Executing();
            window.SetVisible(true);
        }
    });
    

    demux.RegisterCallback(
    [&](vidc_frame_data_type &frameData){
        LOG_TAG_INFO("demux no empty");
    },
    [&](vidc_frame_data_type &frameData){
        LOG_TAG_INFO("demux fill buffer done");
        if(decoder.GetState() != IVIDC_STATE_EXECUTING && !decoder.IsInternalConfig()) {
            LOG_TAG_INFO("decoder state[%d]", decoder.GetState());
            decoder.Executing();
            // std::this_thread::sleep_for(std::chrono);
        }
        // IVidcComponent::DumpVidcFrameData(frameData);
        decoder.EmptyBuffer(frameData);
    });

    decoder.RegisterCallback(
    [&](vidc_frame_data_type &frameData){
        LOG_TAG_INFO("decoder empty buffer done");
        // IVidcComponent::DumpVidcFrameData(frameData);
        demux.FillBuffer(frameData);  
    },
    [&](vidc_frame_data_type &frameData){
        IVidcComponent::DumpVidcFrameData(frameData, "decoder FillDone, ready to render");
        RenderBuffer *renderBuffer = (RenderBuffer *)decoder.GetRenderBufferByAddr(frameData.frame_addr);
        if(renderBuffer != nullptr) {
            std::this_thread::sleep_for(std::chrono::milliseconds(20));
            LOG_TAG_INFO("decoder post render before");
            window.PostRenderBuffer(renderBuffer);
            LOG_TAG_INFO("decoder post render end");
            // std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
        LOG_TAG_INFO("renderBuffer[%p]", renderBuffer);
        decoder.FillBuffer(frameData);
    });
    
    demux.SetFilePath(L"./ivi-1.mp4");
    demux.GetVideoTrackInfo();
    LOG_TAG_INFO("1");

    auto portInfo = demux.GetPortBaseInfo(VIDC_BUFFER_OUTPUT);
    LOG_TAG_INFO("portInfo, width[%d], height[%d], rate[%d], codec[%d]",
            portInfo.width, portInfo.height, portInfo.frameRate, portInfo.codecType);
    decoder.SetPortBaseInfo(VIDC_BUFFER_INPUT, portInfo);

    // decoder.SetPortBaseInfo(VIDC_BUFFER_OUTPUT, portInfo);
    decoder.ConfigCodec();
    LOG_TAG_INFO("2");

    AllocatorPmem pmemAllocator;
    demux.AllocateBuffer(VIDC_BUFFER_OUTPUT, pmemAllocator, decoder.GetBufferSize(VIDC_BUFFER_INPUT), decoder.GetBufferCount(VIDC_BUFFER_INPUT));
    decoder.UseBuffer(VIDC_BUFFER_INPUT, pmemAllocator);
    LOG_TAG_INFO("3");

    window.SetDisplayId(4);
    window.SetZorder(99);
    window.SetBufferSize(portInfo.width, portInfo.height);
    window.SetColorFormat(SCREEN_FORMAT_NV12);
    window.SetRectangle(1, 0, portInfo.width, portInfo.height);
    window.SetVisible(true);

    // PortBaseInfo renderInfo;
    // renderInfo.width = portInfo.width;
    // renderInfo.height = portInfo.height;
    // renderInfo.frameRate = portInfo.

    AllocatorQnxWindows qnxAllocator;
    LOG_TAG_INFO("5");

    qnxAllocator.SetScreenWindow(window);
    LOG_TAG_INFO("6");
    decoder.AllocateBuffer(VIDC_BUFFER_OUTPUT, qnxAllocator, decoder.GetBufferSize(VIDC_BUFFER_OUTPUT), decoder.GetBufferCount(VIDC_BUFFER_OUTPUT));
    LOG_TAG_INFO("7");
    demux.Executing();
    // decoder.Executing();
    LOG_TAG_INFO("8");

    usleep(1000000000);

}

void render()
{
    // IScreenWindow &window = *IScreen::Get().CreateWindow();
    // window.SetDisplayId(1);
    // window.SetZorder(1);
    // window.SetBufferSize(portInfo.width, portInfo.height);
    // window.SetColorFormat(SCREEN_FORMAT_NV12);
    // window.SetRectangle(0, 0, portInfo.width, portInfo.height);
    // window.SetVisible(true);
    // win
}


int main()
{
    demux_decoder();
}
