#include "autolink/frameworks/pa/IVidc/allocator/allocator_qnx_window.h"

#define LOG_TAG "IVIDC-ALLO-QNX-WINDOW"

namespace AutoLink {
namespace Frameworks {
namespace PA {

AllocatorQnxWindows::AllocatorQnxWindows()
: _isReleased{true}{
    _buffers.clear();
}

AllocatorQnxWindows::~AllocatorQnxWindows()
{
    LOG_TAG_INFO("~AllocatorQnxWindows before");
    Release();
    LOG_TAG_INFO("~AllocatorQnxWindows end");
}

bool AllocatorQnxWindows::Allocate(int width, int height, int count)
{
    if(_pWindow == nullptr) {
        return false;
    }

    _width = width;
    _height = height;
    _count = count;
    LOG_TAG_INFO("AllocatorQnxWindows::Allocate width[%d], heigth[%d], count[%d]", _width, _height, _count);
    RenderBuffer **buffers = _pWindow->CreateRenderBuffers(width, height, count);
    _renderBuffers = buffers;
    if(buffers == nullptr) {
        LOG_TAG_INFO("AllocatorQnxWindows::Allocate _pWindow->CreateRenderBuffers nullptr");
        return false;
    }

    // TODO 此处为二级数组，大小应判断
    if(buffers[0] != nullptr) {
        _bytes = buffers[0]->size;
    }

    for(int i = 0; i < _count; ++i)
    {
        BufferType buffer;
        buffer.bytes = _bytes;
        buffer.addr = buffers[i]->ptr;
    
        bool rc = screen_get_buffer_property_pv(buffers[i]->screenBuf, SCREEN_PROPERTY_EGL_HANDLE, (void** )&buffer.handle);
        if(rc != 0) {
            LOG_TAG_INFO("screen_get_buffer_property_pv SCREEN_PROPERTY_EGL_HANDLE error");
            return false;
        }
        LOG_TAG_INFO("AllocatorQnxWindows::Allocate egl handle[%p]", buffer.handle);
        _buffers.push_back(std::move(buffer));
    }
    _isReleased = false;
    return true;
}

bool AllocatorQnxWindows::Release()
{
    std::lock_guard<std::mutex> lock(_mtx);
    if(_isReleased == false) {
        _pWindow->DestroyRenderBuffers();
        _buffers.clear();
        _renderBuffers = nullptr;
        _width = 0;
        _height = 0;
        _bytes = 0;
        _count = 0;
        _isReleased = true;
    }
    
    return true;
}

void* AllocatorQnxWindows::GetAddr(int index)
{
    if(index < _buffers.size()) {
        return _buffers.at(index).addr;
    }
    return nullptr;
}

void* AllocatorQnxWindows::GetHandle(int index)
{
    if(index < _buffers.size()) {
        return _buffers.at(index).handle;
    }
    return nullptr;
}

void* AllocatorQnxWindows::GetRenderBufferByAddr(void *addr)
{
    int i = 0;
    for(; i < _buffers.size(); ++i)
    {
        if(_buffers[i].addr == addr) {
            break;
        }
    }
    if(i == _buffers.size()) {
        LOG_TAG_INFO("AllocatorQnxWindows::GetRenderBufferByAddr not find pmem addr[%p]", addr);
        return nullptr;
    }

    return _renderBuffers[i];
}

void AllocatorQnxWindows::SetScreenWindow(IScreenWindow &window)
{
    _pWindow = &window;
}

}
}
}
