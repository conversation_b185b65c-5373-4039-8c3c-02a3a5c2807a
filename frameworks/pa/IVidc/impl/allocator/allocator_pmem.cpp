#include "autolink/frameworks/pa/IVidc/allocator/allocator_pmem.h"
#include <stdio.h>

namespace AutoLink {
namespace Frameworks {
namespace PA {

#define LOG_TAG "IVIDC-ALLO_PMEM"

AllocatorPmem::AllocatorPmem()
{
    _buffers.clear();
    _pmemFlag |= PMEM_FLAGS_CACHE_NONE;
}

AllocatorPmem::~AllocatorPmem()
{
    LOG_TAG_INFO("~AllocatorPmem before");
    if(_isReleased == false) {
        Release();
    }
    LOG_TAG_INFO("~AllocatorPmem end");
}

bool AllocatorPmem::Allocate(int bytes, int count)
{
    LOG_TAG_INFO("AllocatorPmem::Allocate bytes[%d], count[%d]", bytes, count);
    _bytes = bytes;
    _count = count;

    for(int i = 0; i < _count; ++i)
    {
        BufferType buffer;
        buffer.bytes = _bytes;
        buffer.count = _count;
        buffer.addr = pmem_malloc_ext_v2(
            _bytes,
            PMEM_VIDEO_ID,
            _pmemFlag,
            PMEM_ALIGNMENT_4K,
            0,
            (pmem_handle_t *)&buffer.handle,
            nullptr
        );
        LOG_TAG_INFO("AllocatorPmem::Allocate pmem handle[%p]", buffer.handle);

        if(buffer.addr == nullptr) {
            LOG_TAG_INFO("AllocatorPmem Allocate error");
            Release();
            return false;
        }

        _buffers.push_back(std::move(buffer));
    }

    return true;
}

bool AllocatorPmem::Release()
{
    int rc = 0;
    for(auto &buffer : _buffers)
    {
        rc = pmem_free(buffer.addr);
        if(rc != 0) {
            LOG_TAG_INFO("pmem_free");
            return false;
        }
    }
    _buffers.clear();
    _isReleased = true;
    return true;
}

void* AllocatorPmem::GetAddr(int index)
{
    if(_buffers.size() > index) {
        return _buffers.at(index).addr;
    }
    return nullptr;
}

void* AllocatorPmem::GetHandle(int index)
{
    if(_buffers.size() > index) {
        return _buffers.at(index).handle;
    }
    return nullptr;
}

}
}
}
