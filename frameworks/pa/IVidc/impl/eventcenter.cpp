#include "autolink/frameworks/pa/IVidc/eventcenter.h"
#include "taskqueue/taskqueue.h"

namespace AutoLink {
namespace Frameworks {
namespace PA {

std::mutex EventCenterManager::_mtx;
std::unordered_map<std::string, std::shared_ptr<EventCenter>> EventCenterManager::_centerGroup;

struct EventCenterPrivate {
    TaskQueue<IVIDC_EVENT_TYPE> _taskQueue;
    IVidcEventHandler _handler;
};

EventCenterManager::EventCenterManager()
{

}

EventCenterManager::~EventCenterManager()
{

}

std::shared_ptr<EventCenter> EventCenterManager::GetCenter(const std::string& pName)
{
    std::lock_guard<std::mutex> lock(_mtx);
    auto it = _centerGroup.find(pName);
    return it != _centerGroup.end() ? it->second : nullptr;
}

bool EventCenterManager::CreateCenter(const char *pName, IVidcEventHandler handler)
{
    std::string name(pName);
    if (GetCenter(name) != nullptr) {
        LOG_TAG_WARNING("EventCenterManager::CreateCenter exist name:%s", pName);
        return false;
    }
    std::shared_ptr<EventCenter> center = std::make_shared<EventCenter>(pName);
    if (center == nullptr) {
        LOG_TAG_WARNING("EventCenterManager::CreateCenter center is nullptr name:%s", pName);
        return false;
    }
    center->RegisterEventHandle(handler);
    {
        std::lock_guard<std::mutex> lock(_mtx);
        _centerGroup[name] = center;
    }
    LOG_TAG_INFO("EventCenterManager::CreateCenter center is ok name:%s", pName);
    return true;
}

void EventCenterManager::InvokeEvent(const char *pName, IVIDC_EVENT_TYPE event)
{
    std::string name(pName);
    std::shared_ptr<EventCenter> center = GetCenter(name);
    if (center == nullptr) {
        return;
    }
    center->InvokeEvent(event);
}

void EventCenterManager::ReleaseAllCenter()
{
    std::unordered_map<std::string, std::shared_ptr<EventCenter>> emptyGroup;
    {
        std::lock_guard<std::mutex> lock(_mtx);
        emptyGroup.swap(_centerGroup);
    }
    emptyGroup.clear();
}

void EventCenterManager::ReleaseCenter(const char* pName)
{
    std::string name(pName);
    std::shared_ptr<EventCenter> center = nullptr;
    {
        std::lock_guard<std::mutex> lock(_mtx);
        auto it = _centerGroup.find(name);
        if(it != _centerGroup.end()) {
            center = it->second;
            _centerGroup.erase(it);
        }
    }
    if (center != nullptr) {
        center.reset();
    }
}

EventCenter::EventCenter(const char *pName)
: _p(new EventCenterPrivate())
{
    _p->_taskQueue.RegisterEventHandler([this](IVIDC_EVENT_TYPE event){
        _p->_handler(event);
    });
    _p->_taskQueue.Start("IVIDC_EVENT_TYPE");
}

EventCenter::~EventCenter()
{
    _p->_taskQueue.Stop();
    delete _p;
}

void EventCenter::RegisterEventHandle(IVidcEventHandler handler)
{
    _p->_handler = handler;
}

void EventCenter::InvokeEvent(IVIDC_EVENT_TYPE event)
{
    _p->_taskQueue.Push(event);
}


}
}
}
