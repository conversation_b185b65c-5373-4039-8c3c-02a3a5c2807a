#include <unistd.h>
#include <stdio.h>
#include "../taskqueue.h"

using namespace AutoLink::Frameworks::PA;

int main()
{
    TaskQueue<int> queue;
    queue.RegisterEventHandler([](int num){
        printf("num = %d", num);
    });

    printf("===========");

    queue.Start();
    printf("===========");

    queue.Push(1);

    printf("===========");
    queue.Push(2);

    printf("===========");

    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    queue.Push(4);

    queue.Stop();
    

    return 0;
}