#ifndef TASK_QUEUE_H
#define TASK_QUEUE_H

#include <functional>
#include <thread>
#include <condition_variable>
#include <atomic>
#include <list>
#include <stdint.h>
#include "autolink/frameworks/log/log.h"

#define THREAD_NAME "taskqueue"
#define LOG_TAG "taskqueue"

namespace AutoLink {
namespace Frameworks {
namespace PA {

template<class T>
class TaskQueue {
public:
    TaskQueue()
    {

    }

    ~TaskQueue()
    {
        Stop();
    }

    bool Start(const char* name = NULL)
    {
        if(_handler != nullptr && _isRunning == false) {
            _isRunning = true;
            _workerThread = std::thread([&](){
                pthread_setname_np(pthread_self(), name == NULL ? "taskqueue" : name);
                while(_isRunning) {

                    T task;
                    bool ret = Pop(task);
                    if(ret == false) {
                        continue;
                    }
                    _handler(task);
                }
            });
            return true;
        }
        return false;
    }
    bool Stop()
    {
        bool exp = true;
        if(!_isRunning.compare_exchange_strong(exp, false)) {
            return false;
        }
        if(std::this_thread::get_id() == _workerThread.get_id()) {
            LOG_TAG_WARNING("Worker thread join self");
            throw std::runtime_error("Worker thread join self");
            return false;
        }
        {
            std::unique_lock<std::mutex> lck(_mtx);
            _taskList.clear();
        }
        _cv.notify_all();

        if(_workerThread.joinable()) {
            _workerThread.join();
        } else {
            LOG_TAG_WARNING("Worker thread can not join");
        }

        return true;
    }
    bool Push(T task)
    {
        {
            if(_isRunning == false) {
                return false;
            }
            {
                std::unique_lock<std::mutex> lck(_mtx);
                _taskList.push_back(std::move(task));
            }
        }

        _cv.notify_one();
        return true;
    }
    typedef std::function<void(T)> TaskQueueHandler;
    bool RegisterEventHandler(TaskQueueHandler func)
    {
        if(_isRunning) {
            return false;
        }
        _handler = func;
        return true;
    }

    uint32_t Size()
    {
        return _taskList.size();
    }
private:
    bool Pop(T& task)
    {
        if(_isRunning) {
            std::unique_lock<std::mutex> lck(_mtx);
            _cv.wait(lck, [this]{ return !_taskList.empty() || !_isRunning; });
            if(_taskList.empty()) {
                return false;
            }
            task = _taskList.front();
            _taskList.pop_front();
            return true;
        } else {
            return false;
        }

    }

    std::thread _workerThread;
    std::mutex _mtx;
    std::condition_variable _cv;
    std::atomic<bool> _isRunning{false};
    std::list<T> _taskList;
    std::function<void(T)> _handler{nullptr};
};

}
}
}

#endif
