#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <mutex>
#include <thread>
#include <condition_variable>
#include "autolink/frameworks/pa/IVidc/ividc_decoder.h"
#include "autolink/frameworks/pa/IVidc/eventcenter.h"

#define LOG_TAG "IVIDC-DECODER"

namespace AutoLink {
namespace Frameworks {
namespace PA {

IVidcDecoder::IVidcDecoder(const char* pName)
: IoctlVidc(pName){
}

IVidcDecoder::~IVidcDecoder()
{
    LOG_TAG_INFO("~IVidcDecoder before");
    Release();
    LOG_TAG_INFO("~IVidcDecoder end");
}

bool IVidcDecoder::Init()
{
    if (!IoctlVidc::Init()) {
        return false;
    }
    InitPorts();
    return true;
}

bool IVidcDecoder::ConfigCodec()
{
    if(_ports.find(VIDC_BUFFER_INPUT) == _ports.end()) {
        LOG_TAG_INFO("CofingCodec error, not config input buffer port");
    }
    if(_ports.find(VIDC_BUFFER_OUTPUT) == _ports.end()) {
        LOG_TAG_INFO("CofingCodec error, not config output buffer port");
    }

    // session type
    vidc_session_codec_type session;
    memset(&session, 0, sizeof(vidc_session_codec_type));
    session.session = VIDC_SESSION_DECODE;
    session.codec = _ports[VIDC_BUFFER_INPUT].baseInfo.codecType;
    bool rc = IoctlSetParameter(VIDC_I_SESSION_CODEC, &session, sizeof(session));
    if(rc == false) {
        LOG_TAG_INFO("IVidcDecoder::ConfigCodec IoctlSetParameter VIDC_I_SESSION_CODEC error");
        return false;
    }

    // order type
    vidc_output_order_type order;
    memset(&order, 0, sizeof(order));
    order.output_order = VIDC_DEC_ORDER_DISPLAY;
    rc = IoctlSetParameter(VIDC_I_DEC_OUTPUT_ORDER, &order, sizeof(order));
    if(rc == false) {
        LOG_TAG_INFO("IVidcDecoder::ConfigCodec IoctlSetParameter VIDC_I_DEC_OUTPUT_ORDER error");
        return false;
    }

    // reconfig
    vidc_enable_type enable;
    memset(&enable, 0, sizeof(enable));
    enable.enable = true;
    rc = IoctlSetParameter(VIDC_I_DEC_CONT_ON_RECONFIG, &enable, sizeof(enable));
    if(rc == false) {
        LOG_TAG_INFO("IVidcDecoder::ConfigCodec IoctlSetParameter VIDC_I_DEC_CONT_ON_RECONFIG error");
        return false;
    }

    // frame size
    vidc_frame_size_type frameSize;
    memset(&frameSize, 0, sizeof(frameSize));
    frameSize.buf_type = VIDC_BUFFER_INPUT;
    auto &info = _ports[VIDC_BUFFER_INPUT].baseInfo;
    frameSize.width = info.width;
    frameSize.height = info.height;
    rc = IoctlSetParameter(VIDC_I_FRAME_SIZE, &frameSize, sizeof(frameSize));
    if(rc == false) {
        LOG_TAG_INFO("IVidcDecoder::ConfigCodec IoctlSetParameter VIDC_I_FRAME_SIZE error");
        return false;
    }


    // frame rate
    // input / output must be same frame rate
    vidc_frame_rate_type frameRate;
    frameRate.buf_type = VIDC_BUFFER_OUTPUT;
    frameRate.fps_numerator = _ports[VIDC_BUFFER_INPUT].baseInfo.frameRate * 0x10000;
    frameRate.fps_denominator = 0x10000;
    rc = IoctlSetParameter(VIDC_I_FRAME_RATE, &frameRate, sizeof(frameRate));
    if(rc == false) {
        LOG_TAG_INFO("IVidcDecoder::ConfigCodec IoctlSetParameter output VIDC_I_FRAME_RATE error");
        return false;
    }

    frameRate.buf_type = VIDC_BUFFER_INPUT;
    rc = IoctlSetParameter(VIDC_I_FRAME_RATE, &frameRate, sizeof(frameRate));
    if(rc == false) {
        LOG_TAG_INFO("IVidcDecoder::ConfigCodec IoctlSetParameter input VIDC_I_FRAME_RATE error");
        return false;
    }

    vidc_color_format_config_type colorFmt;
    colorFmt.buf_type = VIDC_BUFFER_OUTPUT;
    colorFmt.color_format = VIDC_COLOR_FORMAT_NV12_UBWC;   // TODO 这里强制用 NV12 ，后面根据 config 配置
    rc = IoctlSetParameter(VIDC_I_COLOR_FORMAT, &colorFmt, sizeof(colorFmt));
    if(rc == false) {
        LOG_TAG_INFO("IVidcDecoder::ConfigCodec IoctlSetParameter VIDC_I_COLOR_FORMAT error");
        return false;
    }

    // get requirment
    _ports[VIDC_BUFFER_INPUT].requirement.buf_type = VIDC_BUFFER_INPUT;
    rc = IoctlGetRequirment(_ports[VIDC_BUFFER_INPUT].requirement);
    if(rc == false) {
        LOG_TAG_INFO("IVidcDecoder::ConfigCodec IoctlGetRequirment input buffer error");
        return false;
    } else {
        LOG_TAG_INFO("IVidcDecoder::ConfigCodec INPUT requirement buffer size[%d], count[%d]",
            _ports[VIDC_BUFFER_INPUT].requirement.size, _ports[VIDC_BUFFER_INPUT].requirement.min_count, _ports[VIDC_BUFFER_INPUT].requirement.max_count);
    }

    _ports[VIDC_BUFFER_OUTPUT].requirement.buf_type = VIDC_BUFFER_OUTPUT;
    rc = IoctlGetRequirment(_ports[VIDC_BUFFER_OUTPUT].requirement);
    if(rc == false) {
        LOG_TAG_INFO("IVidcDecoder::ConfigCodec IoctlGetRequirment output buffer error");
        return false;
    } else {
        LOG_TAG_INFO("IVidcDecoder::ConfigCodec OUTPUT requirement buffer size[%d], count[%d]",
            _ports[VIDC_BUFFER_OUTPUT].requirement.size, _ports[VIDC_BUFFER_OUTPUT].requirement.min_count, _ports[VIDC_BUFFER_OUTPUT].requirement.max_count);
    }

    // copy input base info to output
    _ports[VIDC_BUFFER_OUTPUT].baseInfo.width = _ports[VIDC_BUFFER_INPUT].baseInfo.width;
    _ports[VIDC_BUFFER_OUTPUT].baseInfo.height = _ports[VIDC_BUFFER_INPUT].baseInfo.height;
    _ports[VIDC_BUFFER_OUTPUT].baseInfo.colorFmt = _ports[VIDC_BUFFER_INPUT].baseInfo.colorFmt;

    // TODO 此处可以获取 plane_def 信息


    return true;
}

bool IVidcDecoder::Executing()
{
    LOG_TAG_INFO("IVidcDecoder::Executing begin, _state[%d]", _state);

    if (IsInternalConfig() == true) {
        return false;
    }
    switch (_state) {
        case IVIDC_STATE_LOADED: {
            bool rc = IoctlStateLoadResource();
            if (rc == false) {
                LOG_TAG_INFO("IVidcDecoder::Executing IoctlStateLoadResource error");
                return false;
            }

            vidc_start_mode_type mode = VIDC_START_INPUT;
            rc = IoctlStateStart(mode);
            if (rc == false) {
                LOG_TAG_INFO("IVidcDecoder::Executing IoctlStateStart VIDC_START_INPUT error");
                return false;
            }

            mode = VIDC_START_OUTPUT;
            rc = IoctlStateStart(mode);
            if (rc == false) {
                LOG_TAG_INFO("IVidcDecoder::Executing IoctlStateStart VIDC_START_OUTPUT error");
                return false;
            }
        } break;

        case IVIDC_STATE_IDLE: {
            vidc_start_mode_type mode = VIDC_START_INPUT;
            bool rc = IoctlStateStart(mode);
            if (rc == false) {
                LOG_TAG_INFO("IVidcDecoder::Executing IoctlStateStart VIDC_START_INPUT error");
                return false;
            }

            mode = VIDC_START_OUTPUT;
            rc = IoctlStateStart(mode);
            if (rc == false) {
                LOG_TAG_INFO("IVidcDecoder::Executing IoctlStateStart VIDC_START_OUTPUT error");
                return false;
            }

        } break;

        case IVIDC_STATE_PAUSE: {
            // bool rc = IoctlStateResume();
            bool rc = true;
            if(rc == false) {
            LOG_TAG_INFO("IVidcDecoder::Executing IoctlStateResume error");
            return false;
            }
        } break;
        default:
            LOG_TAG_INFO("IVidcDecoder::Executing error action, state[%d]", _state);
            break;
    }

    {
        std::unique_lock<std::mutex> lck(_stateMtx);
        _state = IVIDC_STATE_EXECUTING;
    }
    _stateCv.notify_all();
    return true;
}

bool IVidcDecoder::Pause()
{
    LOG_TAG_INFO("IVidcDecoder::Pause");
    // bool rc = IoctlStatePause();
    bool rc = true;
    if(rc) {
        {
            std::unique_lock<std::mutex> lck(_stateMtx);
            _state = IVIDC_STATE_PAUSE;
        }
        _stateCv.notify_all();
    } else {
        LOG_TAG_INFO("IVidcDecoder::Pause error");
    }
    return rc;
}


bool IVidcDecoder::Release()
{
    LOG_TAG_INFO("IVidcDecoder::Release, state[%d]", _state);
    switch (_state)
    {
    case IVIDC_STATE_PAUSE :
    case IVIDC_STATE_EXECUTING :
    {
        {
            std::unique_lock<std::mutex> lck(_stateMtx);
            _state = IVIDC_STATE_SUSPEND;
        }
        bool rc = IoctlStateStop(VIDC_BUFFER_INPUT, true);
        if(rc == false) {
            LOG_TAG_INFO("IVidcDecoder::Release IoctlStateStop VIDC_BUFFER_INPUT error");
            return false;
        }

        rc = IoctlStateStop(VIDC_BUFFER_OUTPUT, true);
        if(rc == false) {
            LOG_TAG_INFO("IVidcDecoder::Release IoctlStateStop VIDC_BUFFER_OUTPUT error");
            return false;
        }

        rc = IoctlFreeBuffer(VIDC_BUFFER_INPUT);
        if(rc == false) {
            LOG_TAG_INFO("IVidcDecoder::Release IoctlFreeBuffer VIDC_BUFFER_INPUT error");
        }

        rc = IoctlFreeBuffer(VIDC_BUFFER_OUTPUT);
        if(rc == false) {
            LOG_TAG_INFO("IVidcDecoder::Release IoctlFreeBuffer VIDC_BUFFER_OUTPUT error");
        }

        WaitforStopStateIdel();

        rc = IoctlStateRelease(false);
        if(rc == false) {
            LOG_TAG_INFO("IVidcDecoder::Release IoctlStateRelease error");
            return false;
        }
        return true;
    } break;
    case IVIDC_STATE_IDLE : {
        bool rc = IoctlStateRelease(false);
        if(rc == false) {
            LOG_TAG_INFO("IVidcDecoder::Release IoctlStateRelease error");
        }
        return true;
    } break;
    
    default:
        LOG_TAG_INFO("IVidcDecoder::Release error action, state[%d]", _state);
        break;
    }

}

bool IVidcDecoder::UseBuffer(vidc_buffer_type type, AllocatorBase &allocator)
{
    if(_ports.find(type) == _ports.end()) {
        LOG_TAG_INFO("IVidcDecoder::UseBuffer port not init");
        return false;
    }

    vidc_buffer_info_type info;
    memset(&info, 0, sizeof(vidc_buffer_info_type));
    info.buf_type = type;
    info.pid = getpid();
    info.extradata_buf_addr = nullptr;
    info.extradata_buf_size = 0;

    int count = allocator.GetBufferCount();
    _ports[type].bufferCount = count;
    vidc_buffer_reqmnts_type requirmnt;
    memcpy(&requirmnt, &_ports[type].requirement, sizeof(requirmnt));
    requirmnt.actual_count = count;
    requirmnt.size = allocator.GetBufferSize();
    bool ret = IoctlSetRequirment(requirmnt);
    if(ret == false) {
        LOG_TAG_INFO("IVidcDecoder::UseBuffer IoctlSetRequirment error");
        return false;
    }
    _ports[type].buffers.clear();
    _ports[type].handle_buffers.clear();

    for(int i = 0; i < count; ++i)
    {
        info.buf_size = allocator.GetBufferSize();
        info.buf_addr = (uint8 *)allocator.GetAddr(i);
        info.buf_handle = (pmem_handle_t)allocator.GetHandle(i);
        bool rc = IoctlSetBuffer(info);
        if(rc == false) {
            LOG_TAG_INFO("IoctlVidc::UseBuffer IoctlSetBuffer error");
            return false;
        }

        vidc_frame_data_type frameData;
        memset(&frameData, 0, sizeof(vidc_frame_data_type));
        frameData.buf_type = type;
        frameData.alloc_len = allocator.GetBufferSize();
        frameData.frame_addr = (uint8 *)allocator.GetAddr(i);
        frameData.frame_handle = (pmem_handle_t)allocator.GetHandle(i);
        _ports[type].buffers.insert(std::make_pair(frameData.frame_addr, frameData));
        _ports[type].handle_buffers.insert(std::make_pair(frameData.frame_handle, frameData));
        // DumpVidcFrameData(frameData, "IVidcDecoder::UseBuffer");
    }
    LOG_TAG_INFO("IVidcDecoder::UseBuffer successfully");
    _ports[type].allocator = &allocator;
    return true;
}

bool IVidcDecoder::EmptyBuffer(vidc_frame_data_type &frameData)
{
    bool ret = IoctlEmptyBuffer(frameData);
    if(ret == false) {
        // std::this_thread::sleep_for(std::chrono::milliseconds(20));
        ret = IoctlEmptyBuffer(frameData);
    }
    return ret;
}

bool IVidcDecoder::FillBuffer(vidc_frame_data_type &frameData)
{
    if(IsInternalConfig()) {
        LOG_TAG_INFO("IVidcDecoder::FillBuffer IsInternalConfig");
        return false;
    }
    if(frameData.frame_addr != nullptr) {
        bool ret = GetBufferByAddr(VIDC_BUFFER_OUTPUT, frameData.frame_addr, frameData);
        if(ret == false) {
            LOG_TAG_INFO("IVidcDecoder::FillBuffer GetBufferByAddr error");
            return false;
        }
    } else if(frameData.frame_handle != nullptr) {
        bool ret = GetBufferByHandle(VIDC_BUFFER_OUTPUT, frameData.frame_handle, frameData);
        if(ret == false) {
            LOG_TAG_INFO("IVidcDecoder::FillBuffer GetBufferByHandle error");
            return false;
        }
    } else{
        LOG_TAG_INFO("IVidcDecoder::FillBuffer handle and addr is nullptr");
        return false;
    }
    // DumpVidcFrameData(frameData, "IoctlFillBuffer");
    return IoctlFillBuffer(frameData);
}

uint32 IVidcDecoder::GetBufferCount(vidc_buffer_type type)
{
    if(_ports.find(type) == _ports.end()) {
        LOG_TAG_INFO("IVidcDecoder::GetBufferCount buffer[%d] not init", type);
        return 0;
    }

    return _ports[type].requirement.min_count;
}

uint32 IVidcDecoder::GetBufferSize(vidc_buffer_type type)
{
    if(_ports.find(type) == _ports.end()) {
        LOG_TAG_INFO("IVidcDecoder::GetBufferCount buffer[%d] not init", type);
        return 0;
    }
    return _ports[type].requirement.size;
}



void* IVidcDecoder::GetRenderBufferByAddr(void *addr)
{
    if(_ports[VIDC_BUFFER_OUTPUT].allocator->isRenderBuffer() == false) {
        LOG_TAG_INFO("IVidcDecoder::GetRenderBufferByHandle not render buffer");
        return nullptr;
    }

    return _ports[VIDC_BUFFER_OUTPUT].allocator->GetRenderBufferByAddr(addr);
}

void IVidcDecoder::FillDone(vidc_frame_data_type &frameData)
{
    // LOG_TAG_INFO("IVidcDecoder::FillDone call filldone callback before");
    IVidcComponent::FillDone(frameData);
    // LOG_TAG_INFO("IVidcDecoder::FillDone call filldone callback end");

    if((_isFirstFrameDone == false) && (frameData.alloc_len != 0)) {
        _isFirstFrameDone = true;
        // EventCenter::Get().InvokeEvent(IVIDC_EVENT_FIRST_FRAME);
        Pause();
        EventCenterManager::InvokeEvent(_name.c_str(), IVIDC_EVENT_FIRST_FRAME);
    }

    if((_isLastFrameDone == false) && (frameData.flags & VIDC_FRAME_FLAG_CODECCONFIG)) {
        _isLastFrameDone = true;
        // EventCenter::Get().InvokeEvent(IVIDC_EVENT_LAST_FRAME);
        EventCenterManager::InvokeEvent(_name.c_str(), IVIDC_EVENT_LAST_FRAME);
        
    }
}


}
}
}