#include <string.h>
#include <stdio.h>
#include <sys/types.h>
#include <mutex>
#include <condition_variable>
#include <unistd.h>
#include <atomic>
#include "autolink/frameworks/pa/IVidc/ioctl_vidc.h"
#include "taskqueue/taskqueue.h"
#include "autolink/frameworks/pa/IVidc/eventcenter.h"

#define LOG_TAG "IVIDC-IOCTL"

namespace AutoLink {
namespace Frameworks {
namespace PA {

enum IVIDC_COMMAND {
    IVIDC_COMMAND_NONE = 0,
    IVIDC_COMMAND_LAOD_RESOURCE = 1,
    IVIDC_COMMAND_START = 2,
    IVIDC_COMMAND_START_INPUT = 3,
    IVIDC_COMMAND_START_OUTPUT = 4,
    IVIDC_COMMAND_PAUSE = 5,
    IVIDC_COMMAND_STOP_INPUT = 6,
    IVIDC_COMMAND_STOP_OUTPUT = 7,
    IVIDC_COMMAND_RESUME = 8,
    IVIDC_COMMAND_RELEASE_RESOURCE = 9,
    IVIDC_COMMAND_FLUSH_INPUT = 10,
    IVIDC_COMMAND_FLUSH_OUTPUT = 11
};

enum IVIDC_DECODER_COMMAND {
    IVIDC_DECODER_INTPUT_RECONFIG = 0,
    IVIDC_DECODER_OUTPUT_RECONFIG = 1
};

struct IoctlVidcPrivate {
    ioctl_session_t *_handle;
    IVIDC_COMMAND _command{IVIDC_COMMAND_NONE};
    std::mutex _mtx;
    std::condition_variable _cv;
    TaskQueue<IVIDC_DECODER_COMMAND> _taskQueue;
    bool isReconfigNow{false};
    bool hasReconfig{false};
    std::mutex _reconfigMtx;
    std::condition_variable _reconfigCv;
    std::atomic<bool> _eventReady{false};
    std::mutex _eventReadyMtx;
    std::condition_variable _eventReadyCv;
    std::atomic<int32_t> _inputBufferInUseCount;
    std::atomic<int32_t> _outputBufferInUseCount;
};

IoctlVidc::IoctlVidc(const char* name)
: _p(new IoctlVidcPrivate())
, IVidcComponent(name)
{
}

IoctlVidc::~IoctlVidc()
{
    LOG_TAG_INFO("~IoctlVidc before");
    if (_p != nullptr) {
        _p->_taskQueue.Stop();
        if(_p->_handle != nullptr) {
            int rc = device_close(_p->_handle);
            usleep(20000);
            if(rc != VIDC_ERR_NONE) {
                LOG_TAG_ERROR("IoctlVidc::~IoctlVidc device_close error, ret[%ld]", rc);
            } else {
                LOG_TAG_ERROR("IoctlVidc::~IoctlVidc device_close ok");
            }
            _p->_handle = nullptr;
        }
        delete _p;
        _p = nullptr;
    }
    LOG_TAG_INFO("~IoctlVidc end");
}

bool IoctlVidc::Init()
{
    if (_p == nullptr) {
        LOG_TAG_INFO("IoctlVidc Init ptr is nullptr");
        return false;
    }
    ioctl_callback_t callback = {};
    callback.handler = IoctlVidc::IoctlEventCallback;
    callback.data = static_cast<void*>(this);
    LOG_TAG_INFO("IoctlVidc Init");
    // TODO 添加 waitfor
    _p->_handle = device_open((char *)"VideoCore/vidc_drv", &callback);
    if(_p->_handle == nullptr) {
        _state = IVIDC_STATE_UNLOADED;
        LOG_TAG_INFO("IoctlVidc Init device_open error");
        return false;
    }
    LOG_TAG_INFO("IoctlVidc::Init device_open successfully");
    _state = IVIDC_STATE_LOADED;

    _p->_taskQueue.RegisterEventHandler([&](IVIDC_DECODER_COMMAND command){
        if(command == IVIDC_DECODER_INTPUT_RECONFIG) {
            processReconfig(VIDC_BUFFER_INPUT);
        } else if(command == IVIDC_DECODER_OUTPUT_RECONFIG) {
            processReconfig(VIDC_BUFFER_OUTPUT);
        }
    });
    _p->_taskQueue.Start("IVIDC_DECODER_COMMAND");
    return true;
}

bool IoctlVidc::IoctlGetParameter(vidc_property_id_type propId, void *payloadPtr, uint32 payloadBytes)
{
    uint8 buffer[256] = {0};
    uint32 bytes = 0;
    vidc_drv_property_type *propertyPtr = nullptr;

    memset(buffer, 0, sizeof(buffer));
    // propertyPtr payload 必须足够大
    propertyPtr = (vidc_drv_property_type *)buffer;
    bytes = sizeof(vidc_property_hdr_type) + payloadBytes;
    memcpy(propertyPtr->payload, payloadPtr, payloadBytes);
    propertyPtr->prop_hdr.size = payloadBytes;
    propertyPtr->prop_hdr.prop_id = propId;

    int rc = device_ioctl(_p->_handle, VIDC_IOCTL_GET_PROPERTY, (uint8 *)propertyPtr, bytes, (uint8 *)payloadPtr, payloadBytes);
    if(rc != VIDC_ERR_NONE) {
        LOG_TAG_INFO("IoctlVidc CommandGetParameter device_ioctl VIDC_IOCTL_GET_PROPERTY error, propId[%ld], rc[%ld]", propId, rc);
        return false;
    }
    return true;
}



bool IoctlVidc::IoctlSetParameter(vidc_property_id_type propId, void *payloadPtr, uint32 payloadBytes)
{
    int8 buffer[1024] = {0};
    uint32 bytes = 0;
    vidc_drv_property_type *propertyPtr = nullptr;

    memset(buffer, 0, sizeof(buffer));

    // propertyPtr payload 必须足够大
    propertyPtr = (vidc_drv_property_type *)buffer;
    bytes = sizeof(vidc_property_hdr_type) + payloadBytes;
    memcpy(propertyPtr->payload, payloadPtr, payloadBytes);
    propertyPtr->prop_hdr.size = payloadBytes;
    propertyPtr->prop_hdr.prop_id = propId;

    int rc = device_ioctl(_p->_handle, VIDC_IOCTL_SET_PROPERTY, (uint8 *)propertyPtr, bytes, nullptr, 0);
    if(rc != VIDC_ERR_NONE) {
        LOG_TAG_INFO("IoctlVidc CommandSetParameter device_ioctl VIDC_IOCTL_SET_PROPERTY error, propId[%ld], rc[%ld]", propId, rc);
        return false;
    }
    return true;
}

bool IoctlVidc::IoctlSetBuffer(const vidc_buffer_info_type &info)
{
    if(info.buf_size < _ports[info.buf_type].requirement.size) {
        LOG_TAG_INFO("IoctlVidc::IoctlSetBuffer buffer[%d] size is too small, actual[%d], requirment[%d]",
            info.buf_type, info.buf_size, _ports[info.buf_type].requirement.size);
        // return false;
    }

    // debug
    LOG_TAG_INFO("IoctlVidc::IoctlSetBuffer vidc_buffer_info_type type[%d], contig[%d], size[%ld], addr[%p], extradata_buf_size[%ld], extradata_buf_addr[%ld], pid[%d], buf_handle[%p], extradata_buf_handle[%p]",
        info.buf_type, info.contiguous, info.buf_size, info.buf_addr, info.extradata_buf_size, info.extradata_buf_addr, info.pid, info.buf_handle, info.extradata_buf_handle);

    int ret = device_ioctl(_p->_handle, VIDC_IOCTL_SET_BUFFER, (uint8 *)&info, sizeof(info), nullptr, 0);
    if(ret != VIDC_ERR_NONE) {
        LOG_TAG_INFO("IoctlVidc::IoctlSetBuffer device_ioctl error, ret[%ld]", ret);
        return false;
    }
    return true;
}

bool IoctlVidc::IoctlGetRequirment(vidc_buffer_reqmnts_type &reqmnts)
{
    bool ret = IoctlGetParameter(VIDC_I_BUFFER_REQUIREMENTS, &reqmnts, sizeof(vidc_buffer_reqmnts_type));
    if(ret == false) {
        LOG_TAG_INFO("IoctlVidc::IoctlGetRequirment error");
        return false;
    }
    return true;
}

bool IoctlVidc::IoctlSetRequirment(vidc_buffer_reqmnts_type &reqmnts)
{
    LOG_TAG_INFO("IoctlVidc::IoctlSetRequirment buffer size[%d], buffer count[%d]", reqmnts.size, reqmnts.actual_count);
    int ret = IoctlSetParameter(VIDC_I_BUFFER_REQUIREMENTS, &reqmnts, sizeof(vidc_buffer_reqmnts_type));
    if(ret == false) {
        LOG_TAG_INFO("IoctlVidc::IoctlSetRequirment error");
        return false;
    }
    return true;
}

bool IoctlVidc::IoctlStateStop(vidc_buffer_type type, bool async)
{
    LOG_TAG_INFO("IoctlVidc::IoctlStateStop[%d]", type);
    if(_state != IVIDC_STATE_EXECUTING && _state != IVIDC_STATE_PAUSE && _state != IVIDC_STATE_SUSPEND) {
        LOG_TAG_INFO("IoctlVidc::IoctlStateStop current state[%d] can't stop", _state);
        return false;
    }

    {
        std::unique_lock<std::mutex> lck(_stateMtx);
        _state = IVIDC_STATE_SUSPEND;
    }

    vidc_stop_mode_type stopMode = VIDC_STOP_UNUSED;
    if(type == VIDC_BUFFER_INPUT) {
        stopMode = VIDC_STOP_INPUT;
        _p->_command = IVIDC_COMMAND_STOP_INPUT;
    } else if(type == VIDC_BUFFER_OUTPUT) {
        stopMode = VIDC_STOP_OUTPUT;
        _p->_command = IVIDC_COMMAND_STOP_OUTPUT;
    }

    LOG_TAG_INFO("IoctlVidc::IoctlStateStop 1");

    if(async == true) {
        int ret = device_ioctl(_p->_handle, VIDC_IOCTL_STOP, (uint8 *)&stopMode, sizeof(vidc_stop_mode_type), nullptr, 0);
        if(ret != VIDC_ERR_NONE) {
            LOG_TAG_INFO("IoctlVidc::IoctlStateStop device_ioctl error, ret[%d]", ret);
            return false;
        }
    } else {
        std::unique_lock<std::mutex> lck(_p->_mtx);
        int ret = device_ioctl(_p->_handle, VIDC_IOCTL_STOP, (uint8 *)&stopMode, sizeof(vidc_stop_mode_type), nullptr, 0);
        if(ret != VIDC_ERR_NONE) {
            LOG_TAG_INFO("IoctlVidc::IoctlStateStop device_ioctl error, ret[%d]", ret);
            return false;
        }
        _p->_cv.wait(lck);
        _p->_command = IVIDC_COMMAND_NONE;
    }

    LOG_TAG_INFO("IoctlVidc::IoctlStateStop 2");

    // {
    //     std::unique_lock<std::mutex> lck(_stateMtx);
    //     _state = IVIDC_STATE_IDLE;
    // }
    // _stateCv.notify_all();
    return true;
}

bool IoctlVidc::IoctlStateStart(bool async)
{
    LOG_TAG_INFO("IoctlVidc::IoctlStateStart");
    if(_state != IVIDC_STATE_IDLE) {
        LOG_TAG_INFO("IoctlVidc::IoctlStateStart current state[%d] can't start", _state);
        return false;
    }

    if(async == true) {
        int ret = device_ioctl(_p->_handle, VIDC_IOCTL_START, nullptr, 0, nullptr, 0);
        if(ret != VIDC_ERR_NONE) {
            LOG_TAG_INFO("IoctlVidc::IoctlStateStart error, ret[%d]", ret);
            return false;
        }
        return true;
    } else {
        std::unique_lock<std::mutex> lck(_p->_mtx);
        _p->_command = IVIDC_COMMAND_START;
        int ret = device_ioctl(_p->_handle, VIDC_IOCTL_START, nullptr, 0, nullptr, 0);
        if(ret != VIDC_ERR_NONE) {
            LOG_TAG_INFO("IoctlVidc::IoctlStateStart error, ret[%d]", ret);
            return false;
        }
        _p->_cv.wait(lck);
        _p->_command = IVIDC_COMMAND_NONE;
        return true;
    }
}

bool IoctlVidc::IoctlStateStart(vidc_start_mode_type mode, bool async)
{
    LOG_TAG_INFO("IoctlVidc::IoctlStateStart");
    if(_state != IVIDC_STATE_IDLE) {
        LOG_TAG_INFO("IoctlVidc::IoctlStateStart current state[%d] can't start", _state);
        return false;
    }

    if(async == true) {
        int ret = device_ioctl(_p->_handle, VIDC_IOCTL_START, (uint8_t *)&mode, sizeof(vidc_start_mode_type), nullptr, 0);
        if(ret != VIDC_ERR_NONE) {
            LOG_TAG_INFO("IoctlVidc::IoctlStateStart error, ret[%d]", ret);
            return false;
        }
        return true;
    } else {
        std::unique_lock<std::mutex> lck(_p->_mtx);
        _p->_command = IVIDC_COMMAND_START;
        int ret = device_ioctl(_p->_handle, VIDC_IOCTL_START, (uint8_t *)&mode, sizeof(vidc_start_mode_type), nullptr, 0);
        if(ret != VIDC_ERR_NONE) {
            LOG_TAG_INFO("IoctlVidc::IoctlStateStart error, ret[%d]", ret);
            return false;
        }

        _p->_cv.wait_for(lck, std::chrono::milliseconds(200));
        if(_p->_command != IVIDC_COMMAND_NONE) {
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
            int ret = device_ioctl(_p->_handle, VIDC_IOCTL_START, (uint8_t *)&mode, sizeof(vidc_start_mode_type), nullptr, 0);
            if(ret != VIDC_ERR_NONE) {
                LOG_TAG_INFO("IoctlVidc::IoctlStateStart error, ret[%d]", ret);
                return false;
            }
            _p->_cv.wait_for(lck, std::chrono::milliseconds(200));
        } else {
            return true;
        }
        if(_p->_command != IVIDC_COMMAND_NONE) {
            return false;
        }
        return true;
    }

}

bool IoctlVidc::IoctlStateLoadResource(bool async)
{
    if(_state > IVIDC_STATE_LOADED) {
        LOG_TAG_INFO("IoctlVidc::IoctlStateLoadResource resource already load");
        return true;
    } else if(_state != IVIDC_STATE_LOADED) {
        LOG_TAG_INFO("IoctlVidc::IoctlStateLoadResource device open failed");
    } else {
        if(async == true) {
            int ret = device_ioctl(_p->_handle, VIDC_IOCTL_LOAD_RESOURCES, nullptr, 0, nullptr, 0);
            if(ret != VIDC_ERR_NONE) {
                LOG_TAG_INFO("IoctlVidc::IoctlStateLoadResource VIDC_IOCTL_LOAD_RESOURCES error, ret[%d]", ret);
                return false;
            }
            return true;
        } else {
            std::unique_lock<std::mutex> lck(_p->_mtx);
            _p->_command = IVIDC_COMMAND_LAOD_RESOURCE;
            int ret = device_ioctl(_p->_handle, VIDC_IOCTL_LOAD_RESOURCES, nullptr, 0, nullptr, 0);
            if(ret != VIDC_ERR_NONE) {
                LOG_TAG_INFO("IoctlVidc::IoctlStateLoadResource VIDC_IOCTL_LOAD_RESOURCES error, ret[%d]", ret);
                return false;
            }
            _p->_cv.wait(lck);
            _p->_command = IVIDC_COMMAND_NONE;
            return true;
        }
    }
}

bool IoctlVidc::IoctlStatePause(bool async)
{
    LOG_TAG_INFO("IoctlVidc::IoctlStatePause");
    if(_state != IVIDC_STATE_EXECUTING) {
        LOG_TAG_INFO("IoctlVidc::IoctlStatePause state[%d] can't pause", _state);
        return false;
    }

    if(async == true) {
        int ret = device_ioctl(_p->_handle, VIDC_IOCTL_PAUSE, nullptr, 0, nullptr, 0);
        if(ret != VIDC_ERR_NONE) {
            LOG_TAG_INFO("IoctlVidc::IoctlStatePause error, ret[%d]", ret);
            return false;
        }
        return true;
    } else {
        std::unique_lock<std::mutex> lck(_p->_mtx);
        _p->_command = IVIDC_COMMAND_PAUSE;
        int ret = device_ioctl(_p->_handle, VIDC_IOCTL_PAUSE, nullptr, 0, nullptr, 0);
        if(ret != VIDC_ERR_NONE) {
            LOG_TAG_INFO("IoctlVidc::IoctlStatePause error, ret[%d]", ret);
            return false;
        }
        _p->_cv.wait(lck);
        _p->_command = IVIDC_COMMAND_NONE;
        return true;
    }

}

bool IoctlVidc::IoctlStateResume(bool async)
{
    LOG_TAG_INFO("IoctlVidc::IoctlStateResume");
    if(_state != IVIDC_STATE_PAUSE) {
        LOG_TAG_INFO("IoctlVidc::IoctlStateResume state[%d] can't resume", _state);
        return false;
    }
    if(async == true) {
        int ret = device_ioctl(_p->_handle, VIDC_IOCTL_RESUME, nullptr, 0, nullptr, 0);
        if(ret != VIDC_ERR_NONE) {
            LOG_TAG_INFO("IoctlVidc::IoctlStateResume error, ret[%d]", ret);
            return false;
        }
        return true;
    } else {
        std::unique_lock<std::mutex> lck(_p->_mtx);
        _p->_command = IVIDC_COMMAND_RESUME;
        int ret = device_ioctl(_p->_handle, VIDC_IOCTL_RESUME, nullptr, 0, nullptr, 0);
        if(ret != VIDC_ERR_NONE) {
            LOG_TAG_INFO("IoctlVidc::IoctlStateResume error, ret[%d]", ret);
            return false;
        }
        _p->_cv.wait(lck);
        _p->_command = IVIDC_COMMAND_NONE;
        return true;
    }
}

bool IoctlVidc::IoctlStateRelease(bool async)
{
    LOG_TAG_INFO("IoctlVidc::IoctlStateRelease");
    if(async == true) {
        int ret = device_ioctl(_p->_handle, VIDC_IOCTL_RELEASE_RESOURCES, nullptr, 0, nullptr, 0);
        if(ret != VIDC_ERR_NONE) {
            LOG_TAG_INFO("IoctlVidc::IoctlStateRelease error, ret[%d]", ret);
            return false;
        }
        return true;
    } else {
        std::unique_lock<std::mutex> lck(_p->_mtx);
        _p->_command = IVIDC_COMMAND_RELEASE_RESOURCE;
        int ret = device_ioctl(_p->_handle, VIDC_IOCTL_RELEASE_RESOURCES, nullptr, 0, nullptr, 0);
        if(ret != VIDC_ERR_NONE) {
            LOG_TAG_INFO("IoctlVidc::IoctlStateRelease error, ret[%d]", ret);
            return false;
        }
        _p->_cv.wait(lck);
        _p->_command = IVIDC_COMMAND_NONE;
        return true;
    }
}

int IoctlVidc::IoctlEventCallback(uint8 *msgPtr, uint32 length, void *clientPtr)
{
    if (clientPtr == NULL || msgPtr == NULL)
    {
        LOG_TAG_INFO("VidcIoctl::eventCallback client or msg data is NULL");
        return -1;
    }

    IoctlVidc *thisPtr = static_cast<IoctlVidc *>(clientPtr);
    vidc_drv_msg_info_type *infoPtr = (vidc_drv_msg_info_type *)msgPtr;
    thisPtr->HandleEvent(*infoPtr, length);
}



bool IoctlVidc::IoctlEmptyBuffer(vidc_frame_data_type frameData)
{
    if(_ports.find(VIDC_BUFFER_INPUT) == _ports.end()) {
        LOG_TAG_INFO("IoctlVidc::IoctlEmptyBuffer has no VIDC_BUFFER_INPUT buffer");
        return false;
    }

    {
        std::unique_lock<std::mutex> lck(_p->_reconfigMtx);
        _p->_reconfigCv.wait(lck, [this]{ return _p->isReconfigNow == false; });
    }

    IVidcComponent::EmptyBuffer(frameData);

    frameData.buf_type = VIDC_BUFFER_INPUT;
    frameData.frame_addr = nullptr;
    frameData.alloc_metadata_len = 0;

    // DumpVidcFrameData(frameData, "IoctlEmptyBuffer");

    int ret = device_ioctl(
        _p->_handle,
        VIDC_IOCTL_EMPTY_INPUT_BUFFER,
        (uint8 *)&frameData,
        sizeof(vidc_frame_data_type),
        NULL,
        0);
    if(ret != VIDC_ERR_NONE) {
        LOG_TAG_INFO("IoctlVidc::CommandEmptyBuffer device_ioctl VIDC_IOCTL_EMPTY_INPUT_BUFFER error, ret[%ld]", ret);
        return false;
    }
    _p->_inputBufferInUseCount++;
    // LOG_TAG_INFO(" ============= IoctlVidc::IoctlEmptyBuffer end  ===============");
    return true;
}


bool IoctlVidc::IoctlFillBuffer(vidc_frame_data_type frameData)
{
    if(_ports.find(VIDC_BUFFER_OUTPUT) == _ports.end()) {
        LOG_TAG_INFO("IoctlVidc::IoctlFillBuffer has no VIDC_BUFFER_INPUT buffer");
        return false;
    }
    {
        std::unique_lock<std::mutex> lck(_p->_reconfigMtx);
        _p->_reconfigCv.wait(lck, [this]{ return _p->isReconfigNow == false; });
    }
    {
        std::unique_lock<std::mutex> lck(_stateMtx);
        if(_state == IVIDC_STATE_PAUSE) {
            LOG_TAG_ERROR("IoctlVidc::IoctlFillBuffer error, _state is pause, wait to executing");
            while(_state != IVIDC_STATE_EXECUTING && _state != IVIDC_STATE_SUSPEND) {
                LOG_TAG_INFO("IoctlVidc::IoctlFillBuffer wait state executing or suspend timeout, _state[%d]", _state);
                _stateCv.wait_for(lck, std::chrono::seconds(1));
            }
            return false;
        } else if(_state == IVIDC_STATE_SUSPEND) {
            LOG_TAG_ERROR("IoctlVidc::IoctlFillBuffer error, _state is suspend");
            return false;
        }
    }

    auto &buffers = _ports[VIDC_BUFFER_OUTPUT].handle_buffers;
    if(buffers.find(frameData.frame_handle) == buffers.end()) {
        LOG_TAG_INFO("IoctlVidc::IoctlFillBuffer invalid frameData");
        return false;
    }

    frameData.buf_type = VIDC_BUFFER_OUTPUT;
    frameData.frame_addr = nullptr;
    frameData.flags = 0;
    frameData.timestamp = 0;
    frameData.data_len = 0;
    int ret = device_ioctl(
        _p->_handle,
        VIDC_IOCTL_FILL_OUTPUT_BUFFER,
        (uint8 *)&frameData,
        sizeof(vidc_frame_data_type),
        nullptr,
        0);
    if(ret != VIDC_ERR_NONE) {
        LOG_TAG_INFO("IoctlVidc::IoctlFillBuffer device_ioctl VIDC_IOCTL_FILL_OUTPUT_BUFFER error, ret[%d]", ret);
        return false;
    }
    _p->_outputBufferInUseCount++;
    LOG_TAG_INFO("IoctlVidc::IoctlFillBuffer fillbuffer end outputBufferIsUseCount count[%d]", _p->_outputBufferInUseCount.load());
    return true;
}

bool IoctlVidc::IoctlFlushBuffer(vidc_buffer_type type, bool async)
{
    if(_ports.find(type) == _ports.end()) {
        LOG_TAG_INFO("IoctlVidc::IoctlFlushBuffer port not init");
        return false;
    }

    vidc_flush_mode_type flushType;

    if(type == VIDC_BUFFER_INPUT) {
        flushType = VIDC_FLUSH_INPUT;
        _p->_command = IVIDC_COMMAND_FLUSH_INPUT;
    } else if(type == VIDC_BUFFER_OUTPUT) {
        flushType = VIDC_FLUSH_OUTPUT;
        _p->_command = IVIDC_COMMAND_FLUSH_OUTPUT;
    }
    LOG_TAG_INFO("IoctlVidc::IoctlFlushBuffer before");
    if(async == true) {
        int ret = device_ioctl(
            _p->_handle,
            VIDC_IOCTL_FLUSH,
            (uint8 *)&flushType,
            sizeof(vidc_flush_mode_type),
            nullptr,
            0);
        if(ret == VIDC_ERR_NONE) {
            LOG_TAG_INFO("IoctlVidc::IoctlFlushBuffer VIDC_IOCTL_FLUSH error, ret[%ld]", ret);
            return false;
        }
    } else {
        std::unique_lock<std::mutex> lck(_p->_mtx);
        int ret = device_ioctl(
            _p->_handle,
            VIDC_IOCTL_FLUSH,
            (uint8 *)&flushType,
            sizeof(vidc_flush_mode_type),
            nullptr,
            0);
        if(ret == VIDC_ERR_NONE) {
            LOG_TAG_INFO("IoctlVidc::IoctlFlushBuffer VIDC_IOCTL_FLUSH error, ret[%ld]", ret);
            return false;
        }
        LOG_TAG_INFO("IoctlVidc::IoctlFlushBuffer wait");
        _p->_cv.wait(lck);
        _p->_command = IVIDC_COMMAND_NONE;
    }
    LOG_TAG_INFO("IoctlVidc::IoctlFlushBuffer end");
    return true;
}

bool IoctlVidc::IoctlFreeBuffer(vidc_buffer_type type)
{
    if(_ports.find(type) == _ports.end()) {
        LOG_TAG_INFO("IoctlVidc::IoctlFreeBuffer port not init");
        return false;
    }

    vidc_buffer_info_type info;
    memset(&info, 0, sizeof(vidc_buffer_info_type));
    LOG_TAG_INFO("IoctlVidc::IoctlFreeBuffer buffer size=%u", _ports[type].buffers.size());
    for(auto &buffer : _ports[type].buffers)
    {
        info.buf_type = type;
        info.buf_addr = nullptr;
        info.buf_handle = buffer.second.frame_handle;
        info.extradata_buf_handle = buffer.second.metadata_handle;
        if(info.extradata_buf_handle != nullptr) {
            info.contiguous = false;
        } else {
            info.contiguous = true;
        }

        int ret = device_ioctl(
            _p->_handle,
            VIDC_IOCTL_FREE_BUFFER,
            (uint8 *)&info,
            sizeof(vidc_buffer_info_type),
            nullptr,
            0);
        if(ret != VIDC_ERR_NONE) {
            LOG_TAG_ERROR("IoctlVidc::IoctlFreeBuffer error\n");
            return false;
        }
    }
    LOG_TAG_INFO("IoctlVidc::IoctlFreeBuffer end");
    return true;
}

bool IoctlVidc::IsInternalConfig()
{
    std::unique_lock<std::mutex> lck(_p->_reconfigMtx);
    return _p->isReconfigNow == true;
}

bool IoctlVidc::processReconfig(vidc_buffer_type type)
{
    LOG_TAG_INFO("IoctlVidc::processReconfig");
    if(_ports.find(type) == _ports.end()) {
        LOG_TAG_INFO("IoctlVidc::IoctlFlushBuffer port not init");
        return false;
    }

    {
        std::unique_lock<std::mutex> lck(_p->_reconfigMtx);
        _p->isReconfigNow = true;
    }

    LOG_TAG_INFO("processReconfig start reconfig");

    // 1. Ioctl stop
    LOG_TAG_INFO("processReconfig Ioctl stop");
    bool ret = false;
    // Ioctlstop don't need to wait when reconfig
    ret = IoctlStateStop(type, true);
    if(ret == false) {
        LOG_TAG_INFO("IoctlStateStop error");
    }

    // WaitforOutputBufferOut();

    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    LOG_TAG_INFO("processReconfig Ioctl free buffer");
    ret = IoctlFreeBuffer(type);
    if(ret == false) {
        LOG_TAG_INFO("IoctlFreeBuffer1 error");
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        ret = IoctlFreeBuffer(type);
        if(ret == false) {
            LOG_TAG_INFO("IoctlFreeBuffer2 error");
            return false;
        }
        LOG_TAG_INFO("IoctlFreeBuffer2 successfully");
    }
    // // 2. free buffer
    LOG_TAG_INFO("processReconfig free buffer");
    if(_ports[type].allocator) {
        _ports[type].allocator->Release();
    } else {
        LOG_TAG_INFO("IoctlVidc::processReconfig allocator release error");
        return false;
    }
    _ports[type].buffers.clear();
    _ports[type].handle_buffers.clear();
    memset(&_ports[type].baseInfo, 0, sizeof(PortBaseInfo));
    memset(&_ports[type].requirement, 0, sizeof(vidc_buffer_reqmnts_type));

    // set config
    vidc_output_order_type order;
    memset(&order, 0, sizeof(order));
    order.output_order = VIDC_DEC_ORDER_DISPLAY;
    ret = IoctlSetParameter(VIDC_I_DEC_OUTPUT_ORDER, &order, sizeof(order));
    if(ret == false) {
        LOG_TAG_INFO("IVidcDecoder::ConfigCodec IoctlSetParameter VIDC_I_DEC_OUTPUT_ORDER error");
        return false;
    }

    // 3. get config and print
    LOG_TAG_INFO("processReconfig get config and print");
    vidc_frame_rate_type frameRate;
    frameRate.buf_type = type;
    ret = IoctlGetParameter(VIDC_I_FRAME_RATE, &frameRate, sizeof(vidc_frame_rate_type));
    if(ret == false) {
        LOG_TAG_INFO("IoctlVidc::processReconfig error IoctlGetParameter VIDC_I_FRAME_RATE error");
    } else {
        LOG_TAG_INFO("IoctlVidc::processReconfigIoctlGetParameter VIDC_I_FRAME_RATE fps_denominator[%d], fps_numerator[%d]",
        frameRate.fps_denominator, frameRate.fps_numerator);
    }


    vidc_frame_size_type frameSize;
    frameSize.buf_type = type;
    ret = IoctlGetParameter(VIDC_I_FRAME_SIZE, &frameSize, sizeof(vidc_frame_size_type));
    if(ret == false) {
        LOG_TAG_INFO("IoctlVidc::processReconfig  IoctlGetParameter VIDC_I_FRAME_SIZE error");
    } else {
        LOG_TAG_INFO("IoctlVidc::processReconfig IoctlGetParameter VIDC_I_FRAME_SIZE framesize width[%d], height[%d]",
            frameSize.width, frameSize.height);
    }

    // fmt
    vidc_color_format_config_type colorFmt;
    colorFmt.buf_type = type;
    colorFmt.color_format = VIDC_COLOR_FORMAT_NV12_UBWC;   // TODO 这里强制用 NV12 ，后面根据 config 配置
    ret = IoctlSetParameter(VIDC_I_COLOR_FORMAT, &colorFmt, sizeof(colorFmt));
    if(ret == false) {
        LOG_TAG_INFO("IVidcDecoder::ConfigCodec IoctlSetParameter VIDC_I_COLOR_FORMAT error");
        return false;
    }

    // 4. get requirement
    LOG_TAG_INFO("processReconfig get requirement");
    _ports[type].requirement.buf_type = type;
    ret = IoctlGetRequirment(_ports[type].requirement);
    if(ret == false) {
        LOG_TAG_INFO("IVidcDecoder::processReconfig IoctlGetRequirment output buffer error");
        return false;
    } else {
        LOG_TAG_INFO("IVidcDecoder::processReconfig OUTPUT requirement buffer size[%d], count[%d]",
            _ports[type].requirement.size, _ports[type].requirement.min_count);
    }

    // 5. allocate buffer
    LOG_TAG_INFO("processReconfig allocate buffer");
    ret = _ports[type].allocator->Allocate(frameSize.width, frameSize.height, _ports[type].requirement.min_count);
    if(ret == false) {
        LOG_TAG_INFO("IoctlVidc::processReconfig Allocate Buffer error");
        return false;
    }


    // 6. set requirement
    LOG_TAG_INFO(" processReconfig set requirement");
    vidc_buffer_reqmnts_type reqmnt;
    memcpy(&reqmnt, &_ports[type].requirement, sizeof(vidc_buffer_reqmnts_type));
    reqmnt.actual_count = _ports[type].allocator->GetBufferCount();
    reqmnt.size = _ports[type].allocator->GetBufferSize();
    ret = IoctlSetRequirment(reqmnt);
    if(ret == false) {
        LOG_TAG_INFO("IVidcDecoder::processReconfig IoctlSetRequirment error");
        return false;
    }

    // 7. use buffer
    LOG_TAG_INFO("processReconfig use buffer");
    ret = UseBuffer(type, *_ports[type].allocator);
    if(ret == false) {
        LOG_TAG_INFO("IVidcDecoder::processReconfig UseBuffer error");
        return false;
    }

    // start output port
    ret = IoctlStateStart(VIDC_START_OUTPUT);
    if(ret == false) {
        LOG_TAG_INFO("IVidcDecoder::processReconfig IoctlStateStart VIDC_START_OUTPUT error");
        return false;
    }
    {
        std::unique_lock<std::mutex> lck(_stateMtx);
        _state = IVIDC_STATE_EXECUTING;
    }
    _stateCv.notify_all();

    {
        std::unique_lock<std::mutex> lck(_p->_reconfigMtx);
        _p->isReconfigNow = false;
    }
    _p->_reconfigCv.notify_all();


    // 8. fill all output buffer
    LOG_TAG_INFO("processReconfig fill all output buffer");
    FillAllBuffer();


    LOG_TAG_INFO("processReconfig end");
}

void IoctlVidc::WaitforOutputBufferOut()
{
    LOG_TAG_INFO("IoctlVidc::WaitforOutputBufferOut before");
    std::unique_lock<std::mutex> lck(_p->_mtx);
    _p->_cv.wait(lck, [this]{
        // LOG_TAG_INFO("WaitforOutputBufferOut, outputBufferInUseCount[%d]", _p->_inputBufferInUseCount);
        return _p->_inputBufferInUseCount == 0; });
    LOG_TAG_INFO("IoctlVidc::WaitforOutputBufferOut end");
}

void IoctlVidc::WaitforStopStateIdel()
{
    LOG_TAG_INFO("IoctlVidc::WaitforStopStateIdel in");
    std::unique_lock<std::mutex> lck(_stateMtx);
    _stateCv.wait(lck, [this] {
        return _state == IVIDC_STATE_IDLE;
    });
}

bool IoctlVidc::FillAllBuffer()
{
    auto &maps = _ports[VIDC_BUFFER_OUTPUT].buffers;
    LOG_TAG_INFO("IVidcDecoder::FillAllBuffer, size[%d]", maps.size());
    for(auto it = maps.begin(); it != maps.end(); ++it)
    {
        FillBuffer(it->second);
    }
    return true;
}

void IoctlVidc::EnableEventReady()
{
    {
        std::unique_lock<std::mutex> eventLock(_p->_eventReadyMtx);
        if (_p->_eventReady) {
            return;
        }
        _p->_eventReady = true;
    }
    LOG_TAG_INFO("IoctlVidc::EnableEventReady notify all");
    _p->_eventReadyCv.notify_all();
}

int IoctlVidc::HandleEvent(vidc_drv_msg_info_type &info, uint32 length)
{
    std::unique_lock<std::mutex> lck(_p->_mtx);
    if (!_p->_eventReady) {
        LOG_TAG_INFO("IoctlVidc::HandleEvent event is not ready");
        std::unique_lock<std::mutex> eventLock(_p->_eventReadyMtx);
        _p->_eventReadyCv.wait(eventLock, [this]()->bool{
            return this->_p->_eventReady;
        });
        LOG_TAG_INFO("IoctlVidc::HandleEvent event is ready");
    }
    LOG_TAG_INFO("IoctlVidc::HandleEvent callback event[%d]", info.event_type);
    switch (info.event_type)
    {
    case VIDC_EVT_INPUT_RECONFIG : {
        LOG_TAG_INFO("IoctlVidc::HandleEvent VIDC_EVT_INPUT_RECONFIG");
    } break;
    case VIDC_EVT_OUTPUT_RECONFIG : {
        LOG_TAG_INFO("IoctlVidc::HandleEvent VIDC_EVT_OUTPUT_RECONFIG");
        _p->_taskQueue.Push(IVIDC_DECODER_OUTPUT_RECONFIG);
    } break;
    case VIDC_EVT_LAST_FLAG : {
        LOG_TAG_INFO("IoctlVidc::HandleEvent VIDC_EVT_LAST_FLAG");
        EventCenterManager::InvokeEvent(_name.c_str(), IVIDC_EVENT_LAST_FRAME);
    } break;
    case VIDC_EVT_ERR_HWFATAL : {
        LOG_TAG_INFO("IoctlVidc::HandleEvent VIDC_EVT_ERR_HWFATAL");
    } break;
    case VIDC_EVT_RESP_INPUT_DONE : {
        LOG_TAG_INFO("IoctlVidc::HandleEvent VIDC_EVT_RESP_INPUT_DONE");
        _p->_inputBufferInUseCount--;
        EmptyDone(info.payload.frame_data);
    } break;
    case VIDC_EVT_RESP_OUTPUT_DONE : {
        LOG_TAG_INFO("IoctlVidc::HandleEvent VIDC_EVT_RESP_OUTPUT_DONE");
        _p->_outputBufferInUseCount--;
        FillDone(info.payload.frame_data);
        if(_p->_command == IVIDC_COMMAND_FLUSH_OUTPUT || info.payload.frame_data.flags & VIDC_FRAME_FLAG_LAST) {
            LOG_TAG_INFO("VIDC_EVT_RESP_OUTPUT_DONE reconfig");
            _p->_cv.notify_one();
        }
    } break;
    case VIDC_EVT_RESP_START_INPUT_DONE : {
        LOG_TAG_INFO("IoctlVidc::HandleEvent VIDC_EVT_RESP_START_INPUT_DONE");
        if(_p->_command == IVIDC_COMMAND_START) {
            _p->_command = IVIDC_COMMAND_NONE;
            _p->_cv.notify_one();
        }
    } break;
    case VIDC_EVT_RESP_START_OUTPUT_DONE : {
        LOG_TAG_INFO("IoctlVidc::HandleEvent VIDC_EVT_RESP_START_OUTPUT_DONE");
        if(_p->_command == IVIDC_COMMAND_START) {
            _p->_command = IVIDC_COMMAND_NONE;
            _p->_cv.notify_one();
        }
    } break;
    case VIDC_EVT_RESP_START : {
        LOG_TAG_INFO("IoctlVidc::HandleEvent VIDC_EVT_RESP_START");
    } break;
    case VIDC_EVT_RESP_PAUSE : {
        LOG_TAG_INFO("IoctlVidc::HandleEvent VIDC_EVT_RESP_PAUSE");
        if(_p->_command == IVIDC_COMMAND_PAUSE) {
            _p->_cv.notify_one();
        }
    } break;
    case VIDC_EVT_RESP_LOAD_RESOURCES : {
        LOG_TAG_INFO("IoctlVidc::HandleEvent VIDC_EVT_RESP_LOAD_RESOURCES");
        if(_p->_command == IVIDC_COMMAND_LAOD_RESOURCE) {
            _p->_cv.notify_one();
            {
                std::unique_lock<std::mutex> lck(_stateMtx);
                _state = IVIDC_STATE_IDLE;
            }
            _stateCv.notify_all();
        }
    } break;
    case VIDC_EVT_RESP_RELEASE_RESOURCES : {
        LOG_TAG_ERROR("IoctlVidc::HandleEvent VIDC_EVT_RESP_RELEASE_RESOURCES");
        if(_p->_command == IVIDC_COMMAND_RELEASE_RESOURCE) {
            _p->_cv.notify_one();
        }
    } break;
    case VIDC_EVT_RESP_FLUSH_INPUT_DONE : {
        LOG_TAG_INFO("IoctlVidc::HandleEvent VIDC_EVT_RESP_FLUSH_INPUT_DONE");
        if(_p->_command == IVIDC_COMMAND_FLUSH_INPUT) {
            _p->_cv.notify_one();
        }
    } break;
    case VIDC_EVT_RESP_FLUSH_OUTPUT_DONE : {
        LOG_TAG_INFO("IoctlVidc::HandleEvent VIDC_EVT_RESP_FLUSH_OUTPUT_DONE");
        if(_p->_command == IVIDC_COMMAND_FLUSH_OUTPUT) {
            _p->_cv.notify_one();
        }
    } break;
    case VIDC_EVT_RESP_STOP_INPUT_DONE :{
        LOG_TAG_ERROR("IoctlVidc::HandleEvent VIDC_EVT_RESP_STOP_INPUT_DONE");

    } break;
    case VIDC_EVT_RESP_STOP_OUTPUT_DONE :{
        LOG_TAG_ERROR("IoctlVidc::HandleEvent VIDC_EVT_RESP_STOP_OUTPUT_DONE");
        if(_state == IVIDC_STATE_SUSPEND) {
            {
                std::unique_lock<std::mutex> lck(_stateMtx);
                _state = IVIDC_STATE_IDLE;
            }
            _stateCv.notify_all();
        }
    } break;
    default:
        LOG_TAG_INFO("IoctlVidc::HandleEvent not catch case event_type[%d]", info.event_type);
    }
    return 0;
}

}
}
}
