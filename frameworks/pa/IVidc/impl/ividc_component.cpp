#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "autolink/frameworks/pa/IVidc/IF/ividc_component.h"

#define LOG_TAG "IVIDC-COMP"

namespace AutoLink {
namespace Frameworks {
namespace PA {

void IVidcComponent::InitPorts()
{
    if(isSink()) {
        PortData data;
        memset(&data.requirement, 0, sizeof(data.requirement));
        _ports.insert(std::make_pair(VIDC_BUFFER_INPUT, std::move(data)));
    }

    if(isSource()) {
        PortData data;
        memset(&data.requirement, 0, sizeof(data.requirement));
        _ports.insert(std::make_pair(VIDC_BUFFER_OUTPUT, std::move(data)));
    }
}

bool IVidcComponent::SetPortBaseInfo(vidc_buffer_type type, const PortBaseInfo &info)
{
    if(_ports.find(type) == _ports.end()) {
        PortData data;
        _ports.insert(std::make_pair(type, std::move(data)));
    }

    _ports[type].baseInfo = info;
    return true;
}


PortBaseInfo IVidcComponent::GetPortBaseInfo(vidc_buffer_type type)
{
    if(_ports.find(type) == _ports.end()) {
        LOG_TAG_INFO("IVidcComponent::GetPortBaseInfo port[%d] not init", type);
        return {};
    } else {
        return _ports[type].baseInfo;
    }
}

bool IVidcComponent::AllocateBuffer(vidc_buffer_type type, AllocatorBase &allocator, uint32_t bytes, uint32 count)
{
    LOG_TAG_INFO("IVidcComponent::AllocateBuffer type[%d], bytes[%d], count[%d]", type, bytes, count);
    // 需要判断是否是 render
    if(_ports.find(type) == _ports.end()) {
        
        return false;
    }

    int32_t actual_bytes = 0;
    int32_t actual_count = 0;

    actual_bytes = std::max(_ports[type].requirement.size, bytes);
    actual_count = std::max(_ports[type].requirement.min_count, count);

    LOG_TAG_INFO("actual_bytes[%ld], requirement size[%ld], bytes[%ld]", actual_bytes, _ports[type].requirement.size, bytes);
    LOG_TAG_INFO("actual_count[%ld], requirement count[%ld], count[%ld]", actual_count, _ports[type].requirement.min_count, count);


    // if(actual_count < 3) {
    //     actual_count = 3;
    // }
    _ports[type].requirement.actual_count = actual_count;
    if(allocator.isRenderBuffer() == true) {
        int32_t width = _ports[type].baseInfo.width;
        int32_t height = _ports[type].baseInfo.height;
        // TODO 此处默认指定 fmt 为 NV12，后面改成通过 config 配置
        int ret = allocator.Allocate(width, height, actual_count);
        if(ret == false) {
            LOG_TAG_INFO("IVidcComponent::AllocateBuffer allocator.Allocate render buffer error, type[%d]", type);
            return false;
        }
    } else {
        LOG_TAG_INFO("AllocateBuffer 2.3 actual_bytes[%d], actual_count[%d]", actual_bytes, actual_count);
        int ret = allocator.Allocate(actual_bytes, actual_count);
        if(ret == false) {
            LOG_TAG_INFO("IVidcComponent::AllocateBuffer allocator.Allocate error, type[%d]", type);
            return false;
        }
    }
    _ports[type].allocator = &allocator;
    int ret = UseBuffer(type, allocator);
    if(ret == false) {
        return false;
    }

    LOG_TAG_INFO("IVidcComponent::AllocateBuffer UseBuffer successfully");
    return true;
}

void IVidcComponent::RegisterCallback(CallbackType emptyDone, CallbackType fillDone)
{
    _emptyDoneCallback = emptyDone;
    _fillDoneCallback = fillDone;
}

bool IVidcComponent::ReleaseBuffer(vidc_buffer_type type)
{
    if(_ports.at(type).allocator == nullptr) {
        return true;
    }

    _ports.at(type).allocator->Release();
}

bool IVidcComponent::EmptyBuffer(vidc_frame_data_type &frameData)
{
    LOG_TAG_INFO("IVidcComponent::EmptyBuffer before state[%d]", _state);
    // std::unique_lock<std::mutex> lck(_stateMtx);
    // _stateCv.wait(lck, [this]{ return _state == IVIDC_STATE_EXECUTING; });
    frameData.buf_type = VIDC_BUFFER_INPUT;
    _ports[VIDC_BUFFER_INPUT].bufferInUseCount++;
    LOG_TAG_INFO("IVidcComponent::EmptyBuffer end state[%d]", _state);
}

bool IVidcComponent::FillBuffer(vidc_frame_data_type &frameData)
{
    LOG_TAG_INFO("IVidcComponent::FillBuffer before state[%d]", _state);
    // std::unique_lock<std::mutex> lck(_stateMtx);
    // _stateCv.wait(lck, [this]{ return _state == IVIDC_STATE_EXECUTING; });
    frameData.buf_type = VIDC_BUFFER_INPUT;
    _ports[VIDC_BUFFER_OUTPUT].bufferInUseCount++;
    LOG_TAG_INFO("IVidcComponent::FillBuffer end state[%d]", _state);
}

bool IVidcComponent::GetBufferByAddr(vidc_buffer_type type, void* addr, vidc_frame_data_type &frameData)
{
    if(_ports.find(type) == _ports.end()) {
        LOG_TAG_INFO("IVidcComponent::GetBufferByAddr port[%d] not init", type);
        return false;
    }

    auto &buffers = _ports[type].buffers;
    if(buffers.find(addr) == buffers.end()) {
        LOG_TAG_INFO("IVidcComponent::GetBufferByAddr not addr[%p] buffer", addr);
        return false;
    }

    frameData = buffers[addr];
    // DumpVidcFrameData(frameData, "frame_addrdcComponent::GetBufferByAddr");
    return true;
}


bool IVidcComponent::GetBufferByHandle(vidc_buffer_type type, pmem_handle_t handle, vidc_frame_data_type &frameData)
{
    if(_ports.find(type) == _ports.end()) {
        LOG_TAG_INFO("IVidcComponent::GetBufferByAddr port[%d] not init", type);
        return false;
    }

    auto &buffers = _ports[type].handle_buffers;
    if(buffers.find(handle) == buffers.end()) {
        LOG_TAG_INFO("IVidcComponent::GetBufferByAddr no addr[%p] buffer", handle);
        return false;
    }

    frameData = buffers[handle];
    // DumpVidcFrameData(frameData, "IVidcComponent::GetBufferByHandle");
    return true;
}

void IVidcComponent::DumpVidcFrameData(vidc_frame_data_type &frameData, const char *sumary)
{
    // LOG_TAG_INFO("===============Dump frameData before============");
    // LOG_TAG_INFO("frame_addr[%p]", frameData.frame_addr);
    // LOG_TAG_INFO("metadata_addr[%p]", frameData.metadata_addr);
    // LOG_TAG_INFO("alloc_len[%ld]", frameData.alloc_len);
    // LOG_TAG_INFO("data_len[%ld]", frameData.data_len);
    // LOG_TAG_INFO("offset[%ld]", frameData.offset);
    // LOG_TAG_INFO("timestamp[%ld]", frameData.timestamp);
    // LOG_TAG_INFO("flags[%x]", frameData.flags);
    // LOG_TAG_INFO("frm_clnt_data[%ld]", frameData.frm_clnt_data);
    // LOG_TAG_INFO("frame_type[%d]", frameData.frame_type);
    // LOG_TAG_INFO("frame_handle[%p]", frameData.frame_handle);
    // LOG_TAG_INFO("metadata_handle[%p]", frameData.metadata_handle);


    if(sumary == nullptr) {
        sumary = " ";
    }


    LOG_TAG_INFO("DumpVidcFrameData %s, frame_addr[%9p], metadata_addr[%7p], alloc_len[%7ld], data_len[%9ld], offset[%3ld], timestamp[%9ld], flags[%5x], frm_clnt_data[%4ld], frame_type[%2d], frame_handle[%9p], metadata_handle[%9p], alloc_metadata_len[%7ld]",
            sumary,
            frameData.frame_addr, frameData.metadata_addr,
            frameData.alloc_len, frameData.data_len,
            frameData.offset, frameData.timestamp,
            frameData.flags, frameData.frm_clnt_data,
            frameData.frame_type, frameData.frame_handle,
            frameData.metadata_handle, frameData.alloc_metadata_len);

    // LOG_TAG_INFO("===============Dump frameData end============");
}

void IVidcComponent::EmptyDone(vidc_frame_data_type &frameData)
{
    {
        std::unique_lock<std::mutex> lck(_stateMtx);
        LOG_TAG_INFO("IVidcComponent::FillDone before, state[%d]", _state);
        if(_state == IVIDC_STATE_SUSPEND) {
            return;
        }
        _stateCv.wait(lck, [this]{ return _state == IVIDC_STATE_EXECUTING; });
    }

    _ports[VIDC_BUFFER_INPUT].bufferInUseCount--;
    frameData.buf_type = VIDC_BUFFER_OUTPUT;
    _emptyDoneCallback(frameData);
    LOG_TAG_INFO("IVidcComponent::EmptyDone end");
}

void IVidcComponent::FillDone(vidc_frame_data_type &frameData)
{
    {
        std::unique_lock<std::mutex> lck(_stateMtx);
        LOG_TAG_INFO("IVidcComponent::FillDone before, state[%d]", _state);
        if(_state == IVIDC_STATE_SUSPEND) {
            return;
        }
        _stateCv.wait(lck, [this]{ return _state == IVIDC_STATE_EXECUTING; });
    }

    _ports[VIDC_BUFFER_OUTPUT].bufferInUseCount--;
    frameData.buf_type = VIDC_BUFFER_OUTPUT;
    // DumpVidcFrameData(frameData, "FillDone");
    _fillDoneCallback(frameData);
    LOG_TAG_INFO("IVidcComponent::FillDone end");
}

}
}
}