#include <stdio.h>
#include <string.h>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <algorithm>
#include <unistd.h>

#include "autolink/frameworks/pa/IVidc/ividc_demux.h"
#include "filesource.h"
#include "taskqueue/taskqueue.h"

#define LOG_TAG "IVIDC-DEMUX"

namespace AutoLink {
namespace Frameworks {
namespace PA {

enum IVIDC_DEMUX_COMMAND {
    IVIDC_DEMUX_OUTPUT = 0,
    IVIDC_DEMUX_GET_TRACK_INFO = 1
};

struct IVidcDemuxPrivate {
    FileSource *_fileSource{nullptr};
    uint32 _videoTrackId{0};
    std::mutex _frameDataListMtx;
    bool _needSendConfig{false};
    std::deque<vidc_frame_data_type> _fillBuffers;
    TaskQueue<IVIDC_DEMUX_COMMAND> _taskQueue;
    uint64_t _maxTimestamp{0};
    bool _isFoundKeyFrame{false};
    bool _cycle{false};
    bool _seekPos{0};
};

IVidcDemux::IVidcDemux(const char* pName)
: _p(new IVidcDemuxPrivate())
, IVidcComponent(pName)
{
}

IVidcDemux::~IVidcDemux()
{
    LOG_TAG_INFO("~IVidcDemux before");
    if (_p != nullptr) {
        _p->_taskQueue.Stop();
        if (_p->_fileSource != nullptr) {
            delete _p->_fileSource;
        }
        delete _p;
        _p = nullptr;
    }
    LOG_TAG_INFO("~IVidcDemux end");
}

bool IVidcDemux::Init()
{
    if (_p == nullptr) {
        return false;
    }
    _p->_fileSource = new FileSource(FileSourceCallback, this);
    if (_p->_fileSource == nullptr) {
        return false;
    }
    LOG_TAG_INFO("IVidcDemux::Init fileSource");
    InitPorts();

    _p->_taskQueue.RegisterEventHandler([&](IVIDC_DEMUX_COMMAND command){
        if(command == IVIDC_DEMUX_OUTPUT) {
            processOutput();
        } else if(command == IVIDC_DEMUX_GET_TRACK_INFO) {
            processGetTrackInfo();
        }
    });
    _p->_taskQueue.Start("IVIDC_DEMUX_COMMAND");
    return true;
}

bool IVidcDemux::Executing()
{
    LOG_TAG_INFO("IVidcDemux::Executing before");
    switch (_state)
    {
    case IVIDC_STATE_EXECUTING :{
        return true;
    }
    case IVIDC_STATE_UNLOADED :
    case IVIDC_STATE_LOADED : {
        return false;
    } break;
    case IVIDC_STATE_PAUSE: {
        _p->_needSendConfig = false;
    }
    case IVIDC_STATE_IDLE: {
        _p->_needSendConfig = true;
        {
            std::unique_lock<std::mutex> lck(_stateMtx);
            _state = IVIDC_STATE_EXECUTING;
        }
        _stateCv.notify_all();
        FillAllBuffer();
    } break;
    default:
        break;
    }
    LOG_TAG_INFO("IVidcDemux::Executing end");
}

bool IVidcDemux::Pause()
{
    switch (_state)
    {
    case IVIDC_STATE_PAUSE:
    case IVIDC_STATE_IDLE: {
        return true;
    } break;
    case IVIDC_STATE_UNLOADED :
    case IVIDC_STATE_LOADED : {
        return false;
    }
    case IVIDC_STATE_EXECUTING :{
        std::unique_lock<std::mutex> lck(_stateMtx);
        _state = IVIDC_STATE_PAUSE;
    }
    default:
        break;
    }
}

bool IVidcDemux::Release()
{
    {
        std::unique_lock<std::mutex> lck(_stateMtx);
        _state = IVIDC_STATE_SUSPEND;
    }
    _stateCv.notify_all();
    // 停止工作线程
    _p->_taskQueue.Stop();

    _p->_fileSource->CleanupResource();
    FileSourceStatus ret = _p->_fileSource->CloseFile();
    if(ret != FILE_SOURCE_SUCCESS) {
        LOG_TAG_ERROR("fileSource close file error");
        return false;
    }
    // 释放 buffer 放到外层 manager 做，直接释放 allocator 避免一个组件释放了另一个组件正在使用

    return true;
}

bool IVidcDemux::UseBuffer(vidc_buffer_type type, AllocatorBase &allocator)
{

    if(_ports.find(type) == _ports.end()) {
        LOG_TAG_INFO("IVidcDecoder::UseBuffer port not init");
        return false;
    }

    int count = allocator.GetBufferCount();
    _ports[type].requirement.actual_count = count;
    _ports[type].bufferCount = count;

    for(int i = 0; i < count; ++i)
    {
        vidc_frame_data_type frameData;
        memset(&frameData, 0, sizeof(vidc_frame_data_type));
        frameData.buf_type = type;
        frameData.alloc_len = allocator.GetBufferSize();
        frameData.frame_addr = (uint8 *)allocator.GetAddr(i);
        frameData.frame_handle = (pmem_handle_t)allocator.GetHandle(i);
        _ports[type].buffers.insert(std::make_pair(frameData.frame_addr, frameData));
        _ports[type].handle_buffers.insert(std::make_pair(frameData.frame_handle, frameData));
        // DumpVidcFrameData(frameData, "IVidcDemux::UseBuffer");
    }
    return true;
}

bool IVidcDemux::EmptyBuffer(vidc_frame_data_type &frameData)
{
    LOG_TAG_INFO("IVidcDemux::EmptyBuffer is a source component, can't empty");
    return false;
}

bool IVidcDemux::FillBuffer(vidc_frame_data_type &frameData)
{
    LOG_TAG_INFO("IVidcDemux::FillBuffer");
    if(frameData.frame_handle == nullptr) {
        bool ret = GetBufferByAddr(VIDC_BUFFER_OUTPUT, frameData.frame_addr, frameData);
        if(ret == false) {
            LOG_TAG_INFO("IVidcDemux::FillBuffer GetBufferByAddr error");
            return false;
        }
    }
    IVidcComponent::FillBuffer(frameData);
    {
        std::unique_lock<std::mutex> lck(_p->_frameDataListMtx);
        _p->_fillBuffers.push_back(frameData);
    }
    _p->_taskQueue.Push(IVIDC_DEMUX_OUTPUT);
    LOG_TAG_INFO("IVidcDemux::FillBuffer add demux task, taskQueueSize[%d]", _p->_taskQueue.Size());
    return true;
}

bool IVidcDemux::SetFilePath(wchar_t* path)
{
    LOG_TAG_INFO("IVidcDemux::SetFilePath path[%s]", path);
    std::unique_lock<std::mutex> lck(_stateMtx);
    FileSourceStatus status = _p->_fileSource->OpenFile(path, path, path, FILE_SOURCE_MPEG4, true);
    if(status != FILE_SOURCE_SUCCESS) {
        LOG_TAG_INFO("IVidcDemux::SetFilePath OpenFile failed");
        return false;
    }
    LOG_TAG_INFO("_fileSource OpenFile end");
    _state = IVIDC_STATE_LOADED;
    _stateCv.wait(lck);
    LOG_TAG_INFO("Demux wait end");
    return true;
}

bool IVidcDemux::SetCyclePlayInfo(bool cycle, uint32_t seekPos)
{
    _p->_cycle = cycle;
    _p->_seekPos = seekPos;
}

bool IVidcDemux::GetVideoTrackInfo()
{
    LOG_TAG_INFO("GetVideoTrackInfo");
    FileSource &fs = *_p->_fileSource;
    int32 trackCnt = fs.GetWholeTracksIDList(nullptr);
    if(trackCnt < 0) {
        LOG_TAG_INFO("IVidcDemux::GetVideoInfo GetWholeTracksIDList trackCnt < 0 error");
        return false;
    }
    LOG_TAG_INFO("trackCnt = %d", trackCnt);    //TODO debug
    FileSourceTrackIdInfoType *trackInfoPtr = (FileSourceTrackIdInfoType *)malloc(sizeof(FileSourceTrackIdInfoType) * trackCnt);
    fs.GetWholeTracksIDList(trackInfoPtr);
    for(int i = 0; i < trackCnt; ++i)
    {
        if(trackInfoPtr[i].majorType != FILE_SOURCE_MJ_TYPE_VIDEO) {
            continue;
        }
        _p->_videoTrackId = trackInfoPtr[i].id;
        LOG_TAG_INFO("video trackId = %d", _p->_videoTrackId);
        MediaTrackInfo info = {0};
        memset(&info, 0, sizeof(MediaTrackInfo));
        FileSourceStatus ret = fs.GetMediaTrackInfo(_p->_videoTrackId, &info);
        if(ret != FILE_SOURCE_SUCCESS) {
            LOG_TAG_INFO("IVidcDemux::GetVideoInfo GetMediaTrackInfo error, id[%d], ret[%d]", _p->_videoTrackId, ret);
            return false;
        }
        uint32 bytes = fs.GetTrackMaxFrameBufferSize(_p->_videoTrackId);
        LOG_TAG_INFO("IVidcDemux::GetVideoTrackInfo GetTrackMaxFrameBufferSize[%ld]", bytes);
        SetPortBaseInfo(info, bytes);
    }
    return true;
}

void IVidcDemux::SetPortBaseInfo(MediaTrackInfo &info, uint32 bytes)
{
    LOG_TAG_INFO("width[%d], heigth[%d], frameRate[%d], Codec[%ld], bytes[%d]",
        info.videoTrackInfo.frameWidth,
        info.videoTrackInfo.frameHeight,
        info.videoTrackInfo.frameRate,
        info.videoTrackInfo.videoCodec,
        bytes);

    if(_ports.find(VIDC_BUFFER_OUTPUT) == _ports.end()) {
        LOG_TAG_INFO("IVidcDemux::SetPortBaseInfo VIDC_BUFFER_OUTPUT port not init");
        return;
    }

    _ports[VIDC_BUFFER_OUTPUT].requirement.size = bytes;

    PortBaseInfo baseInfo;
    baseInfo.width = info.videoTrackInfo.frameWidth;
    baseInfo.height = info.videoTrackInfo.frameHeight;
    baseInfo.frameRate = info.videoTrackInfo.frameRate;
    baseInfo.codecType = FileSourceCodecTypeToVidcCodecType(info.videoTrackInfo.videoCodec);
    baseInfo.colorFmt = VIDC_COLOR_FORMAT_NV12; // TODO 固定使用 nv12 后面改成配置
    memcpy(&_ports[VIDC_BUFFER_OUTPUT].baseInfo, &baseInfo, sizeof(PortBaseInfo));
}

vidc_codec_type IVidcDemux::FileSourceCodecTypeToVidcCodecType(FileSourceMnMediaType fsCodecType)
{
    // only accept for h264 and hevc
    switch (fsCodecType)
    {
    case FILE_SOURCE_MN_TYPE_H264 : {
        return VIDC_CODEC_H264;
    } break;
    case FILE_SOURCE_MN_TYPE_HEVC : {
        return VIDC_CODEC_HEVC;
    } break;
    default:
        break;
    }
}

void IVidcDemux::FileSourceCallback(FileSourceCallBackStatus status, void *ptr)
{
    IVidcDemux &demux = *(IVidcDemux *)ptr;
    demux.EventCallback(status);
}

void IVidcDemux::EventCallback(FileSourceCallBackStatus status)
{
    switch (status) {
    case FILE_SOURCE_OPEN_COMPLETE: {
        LOG_TAG_INFO("VidcDemux::fileSourceCallback FILE_SOURCE_OPEN_COMPLETE");
        std::unique_lock<std::mutex> lck(_stateMtx);
        if(_state == IVIDC_STATE_LOADED) {
            _stateCv.notify_one();
        }
        _state = IVIDC_STATE_IDLE;
    } break;
    case FILE_SOURCE_OPEN_FAIL: {
        LOG_TAG_INFO("VidcDemux::fileSourceCallback FILE_SOURCE_OPEN_FAIL");
        std::unique_lock<std::mutex> lck(_stateMtx);
        if(_state == IVIDC_STATE_LOADED) {
            _stateCv.notify_one();
        }
    } break;
    case FILE_SOURCE_SEEK_COMPLETE: {
        LOG_TAG_INFO("VidcDemux::fileSourceCallback FILE_SOURCE_SEEK_COMPLETE");
    } break;
    case FILE_SOURCE_SEEK_FAIL: {
        LOG_TAG_INFO("VidcDemux::fileSourceCallback FILE_SOURCE_SEEK_FAIL");
    } break;
    case FILE_SOURCE_ERROR_ABORT: {
        LOG_TAG_INFO("VidcDemux::fileSourceCallback FILE_SOURCE_ERROR_ABORT");
    } break;
    default:
        LOG_TAG_INFO("VidcDemux::fileSourceCallback Error unknown status %d",
                   status);
        break;
    }
}

void IVidcDemux::processOutput()
{
    {
        std::unique_lock<std::mutex> lck(_stateMtx);
        if(_state == IVIDC_STATE_SUSPEND) {
            return;
        }
        // _stateCv.wait(lck, [this]{ return _state == IVIDC_STATE_EXECUTING ; });
        while(_state != IVIDC_STATE_EXECUTING && _state != IVIDC_STATE_SUSPEND)
        {
            _stateCv.wait_for(lck, std::chrono::seconds(2));
        }

        if(_state == IVIDC_STATE_SUSPEND) {
            return;
        }
    }
    // LOG_TAG_INFO("============== IVidcDemux Fill before ==============");
    vidc_frame_data_type buffer{0};
    {
        std::unique_lock<std::mutex> lck(_p->_frameDataListMtx);
        buffer = _p->_fillBuffers.front();
        _p->_fillBuffers.pop_front();
    }

    // LOG_TAG_INFO("IVidcDemux::processOutput dump data");
    // IVidcComponent::DumpVidcFrameData(buffer);

    if(_p->_needSendConfig) {
        buffer.flags = VIDC_FRAME_FLAG_CODECCONFIG;
        GetFormtBlock(buffer);
        // _fillDoneCallback(buffer);
        FillDone(buffer);
        _p->_needSendConfig = false;

    } else {
        GetNextFrameBlock(buffer);
        // _fillDoneCallback(buffer);
        FillDone(buffer);
    }
    // LOG_TAG_INFO("============== IVidcDemux Fill end ==============");
}

void IVidcDemux::processGetTrackInfo()
{
    // 暂时不用
}

bool IVidcDemux::GetFormtBlock(vidc_frame_data_type &frameData)
{
    uint32 bytes = 0;
    FileSourceStatus ret = _p->_fileSource->GetFormatBlock(
        _p->_videoTrackId,
        nullptr,
        &bytes);
    if(ret != FILE_SOURCE_SUCCESS) {
        LOG_TAG_INFO("IVidcDemux::GetFormtBlock GetFormatBlock1 error, ret[%d]", ret);
        return false;
    } else {
        frameData.data_len = bytes;
        ret = _p->_fileSource->GetFormatBlock(
            _p->_videoTrackId,
            frameData.frame_addr,
            &bytes);
        if(ret != FILE_SOURCE_SUCCESS) {
            LOG_TAG_INFO("IVidcDemux::GetFormtBlock GetFormatBlock2 error, ret[%d]", ret);
            return false;
        } else {
            frameData.flags = VIDC_FRAME_FLAG_CODECCONFIG;
            return true;
        }
    }
}

bool IVidcDemux::GetNextFrameBlock(vidc_frame_data_type &frameData)
{
    frameData.data_len = frameData.alloc_len;
    FileSourceSampleInfo sampleInfo;
    memset(&sampleInfo, 0, sizeof(FileSourceSampleInfo));

    FileSourceMediaStatus ret = _p->_fileSource->GetNextMediaSample(
        _p->_videoTrackId,
        frameData.frame_addr,
        &frameData.data_len,
        sampleInfo);
    if(ret == FILE_SOURCE_DATA_OK && frameData.data_len > 0) {
        // if(sampleInfo.bStartTsValid) {
        //     frameData.timestamp = sampleInfo.startTime;
        // }
        // LOG_TAG_INFO("IVidcDemux::GetNextFrameBlock GetNextMediaSample success, timestamp[%ld], data_len[%ld]",
        //     frameData.timestamp, frameData.data_len);
        // return true;
        if(_p->_isFoundKeyFrame == false && sampleInfo.sync == 1) {
            _p->_isFoundKeyFrame = true;
        }

        if(sampleInfo.bStartTsValid == false) {
            frameData.data_len = 0;
            return false;
        }

        if(_p->_isFoundKeyFrame == true){
            frameData.timestamp = sampleInfo.startTime;
            frameData.flags |= VIDC_FRAME_FLAG_ENDOFFRAME;
            return true;
        } else {
            frameData.data_len = 0;
            return false;
        }

    } else if(ret == FILE_SOURCE_DATA_END){
        if(_p->_cycle == true) {
            _p->_fileSource->SeekAbsolutePosition(_p->_videoTrackId, _p->_seekPos, true, -1);
            return GetNextFrameBlock(frameData);
        } else {
            frameData.flags |= VIDC_FRAME_FLAG_EOS;
            return true;
        }

    } else {
        frameData.data_len = 0;
        LOG_TAG_INFO("IVidcDemux::GetNextFrameBlock GetNextMediaSample failed, ret[%d]", ret);
        return false;
    }
}

void IVidcDemux::FillAllBuffer()
{
    auto &maps = _ports[VIDC_BUFFER_OUTPUT].buffers;
    LOG_TAG_INFO("IVidcDemux::FillAllBuffer, buffer size[%d]", maps.size());
    for(auto it = maps.begin(); it != maps.end(); ++it)
    {
        FillBuffer(it->second);
    }
}


uint32 IVidcDemux::GetBufferCount(vidc_buffer_type type)
{
    if(type == VIDC_BUFFER_INPUT) {
        return false;
    }

    if(_ports.find(type) == _ports.end()) {
        LOG_TAG_INFO("IVidcDemux::GetBufferCount buffer[%d] not init");
        return false;
    }

    return std::max((uint32)3, _ports[type].requirement.min_count);
}

uint32 IVidcDemux::GetBufferSize(vidc_buffer_type type)
{
    if(type == VIDC_BUFFER_INPUT) {
        return false;
    }

    if(_ports.find(type) == _ports.end()) {
        LOG_TAG_INFO("IVidcDemux::GetBufferCount buffer[%d] not init");
        return false;
    }

    return _ports[type].requirement.size;
}

void IVidcDemux::FillDone(vidc_frame_data_type &frameData)
{
    {
        std::unique_lock<std::mutex> lck(_stateMtx);
        if(_state == IVIDC_STATE_SUSPEND) {
            return;
        }
        // _stateCv.wait(lck, [this]{ return _state == IVIDC_STATE_EXECUTING ; });
        while(_state != IVIDC_STATE_EXECUTING && _state != IVIDC_STATE_SUSPEND)
        {
            _stateCv.wait_for(lck, std::chrono::seconds(2));
        }
        if(_state == IVIDC_STATE_SUSPEND) {
            return;
        }
    }

    if(frameData.data_len == 0) {
        FillBuffer(frameData);
        return;
    }
    frameData.buf_type = VIDC_BUFFER_OUTPUT;
    _fillDoneCallback(frameData);
}

}
}
}
