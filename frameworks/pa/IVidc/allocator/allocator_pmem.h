#ifndef ALLOCATOR_PMEM_H
#define ALLOCATOR_PMEM_H

#include <vector>
#include "autolink/frameworks/pa/IVidc/IF/allocator_base.h"

namespace AutoLink {
namespace Frameworks {
namespace PA {

class AllocatorPmem : public AllocatorBase{
public:
    AllocatorPmem();
    ~AllocatorPmem();
    bool Allocate(int bytes, int count) override ;
    bool Allocate(int width, int height, int count) override { return false; }
    bool Release() override ;

    void* GetAddr(int index) override ;
    void* GetHandle(int index) override ;
    int GetBufferSize() override { return _bytes; }
    int GetBufferCount() override {return _count; }
    bool isRenderBuffer() override { return false; }

private:
    std::vector<BufferType> _buffers;
    uint32_t _pmemFlag{PMEM_FLAGS_PHYS_NON_CONTIG};
    int _bytes{0};
    int _count{0};
    bool _isReleased{true};
};

}
}
}

#endif