#ifndef ALLOCATOR_QNX_WINDOW_H
#define ALLOCATOR_QNX_WINDOW_H

#include <vector>
#include <mutex>
#include "autolink/frameworks/pa/IVidc/IF/allocator_base.h"
#include "autolink/frameworks/pa/IScreen/IScreenWindow.h"

namespace AutoLink {
namespace Frameworks {
namespace PA {

class AllocatorQnxWindows : public AllocatorBase{
public:
    AllocatorQnxWindows();
    ~AllocatorQnxWindows();

    bool Allocate(int bytes, int count) override { return false; }
    bool Allocate(int width, int height, int count) override;
    bool Release() override;

    void* GetAddr(int index) override;
    void* GetHandle(int index) override;
    int GetBufferSize() override { return _bytes; }
    int GetBufferCount() override {return _count; }
    bool isRenderBuffer() override { return true; }
    void* GetRenderBufferByAddr(void *) override;
public:
    void SetScreenWindow(IScreenWindow &window);

private:
    std::vector<BufferType> _buffers;
    int _width{0};
    int _height{0};
    int _bytes{0};
    int _count{0};
    bool _isReleased{true};
    std::mutex _mtx;
    IScreenWindow *_pWindow{nullptr};
    RenderBuffer **_renderBuffers{nullptr};
};

}
}
}

#endif
