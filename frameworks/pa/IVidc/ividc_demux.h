#ifndef IVIDC_DEMUX_H
#define IVIDC_DEMUX_H

#include "IF/ividc_component.h"
#include "filesource.h"

namespace AutoLink {
namespace Frameworks {
namespace PA {

struct IVidcDemuxPrivate;
class IVidcDemux : public IVidcComponent {
public:
    IVidcDemux(const char*);
    ~IVidcDemux();

    bool Init() override;
    bool isSink() override { return false; }
    bool isSource() override { return true; }

    bool Executing() override;
    bool Pause() override;
    bool Release() override;

    uint32 GetBufferCount(vidc_buffer_type) override;
    uint32 GetBufferSize(vidc_buffer_type) override;

    // bool AllocateBuffer(vidc_buffer_type buffer_type, AllocatorBase &allocator, uint32_t bytes, uint32 count) override;
    bool UseBuffer(vidc_buffer_type buffer_type, AllocatorBase &allocator) override;

    bool EmptyBuffer(vidc_frame_data_type &frameData) override;
    bool FillBuffer(vidc_frame_data_type &frameData) override;
public:
    bool SetFilePath(wchar_t* path);
    bool SetCyclePlayInfo(bool cycle, uint32_t seekPos);
    bool GetVideoTrackInfo();
    static void FileSourceCallback(FileSourceCallBackStatus status, void *ptr);
    void EventCallback(FileSourceCallBackStatus status);
private:
    void SetPortBaseInfo(MediaTrackInfo &, uint32);
    static vidc_codec_type FileSourceCodecTypeToVidcCodecType(FileSourceMnMediaType);
    void processOutput();
    void processGetTrackInfo();
    bool GetFormtBlock(vidc_frame_data_type &frameData);
    bool GetNextFrameBlock(vidc_frame_data_type &frameData);
    void FillAllBuffer();
    void FillDone(vidc_frame_data_type &frameData) override;

    IVidcDemuxPrivate *_p;
};

}
}
}

#endif