#ifndef IVIDC_DECODER_H
#define IVIDC_DECODER_H

#include "ioctl_vidc.h"

namespace AutoLink {
namespace Frameworks {
namespace PA {

class IVidcDecoder : public IoctlVidc {
public:
    IVidcDecoder(const char*);
    ~IVidcDecoder();
    bool ConfigCodec();

    bool Init() override;
    bool Executing() override;
    bool Pause() override;
    bool Release() override;

    uint32 GetBufferCount(vidc_buffer_type) override;
    uint32 GetBufferSize(vidc_buffer_type) override;

    bool isSink() override { return true; }
    bool isSource() override { return true; }
    bool UseBuffer(vidc_buffer_type type, AllocatorBase &allocator) override;
    bool EmptyBuffer(vidc_frame_data_type &frameData) override;
    bool FillBuffer(vidc_frame_data_type &frameData) override;
    void* GetRenderBufferByAddr(void *);

    // void EmptyDone(vidc_frame_data_type &frameData) override;
    void FillDone(vidc_frame_data_type &frameData) override;
private:
    // bool FillAllBuffer();
};

}
}
}

#endif