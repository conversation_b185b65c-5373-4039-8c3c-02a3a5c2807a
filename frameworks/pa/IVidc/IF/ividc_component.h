#ifndef IVIDC_COMPONENT_H
#define IVIDC_COMPONENT_H

#include <map>
#include <deque>
#include <functional>
#include <mutex>
#include <condition_variable>
#include "vidc_types.h"
#include "allocator_base.h"
#include "autolink/frameworks/log/log.h"

namespace AutoLink {
namespace Frameworks {
namespace PA {

typedef enum
{
    IVIDC_STATE_INVALID = 0,
    IVIDC_STATE_UNLOADED = 1,
    IVIDC_STATE_LOADED = 2,
    IVIDC_STATE_IDLE = 3,
    IVIDC_STATE_EXECUTING = 4,
    IVIDC_STATE_SUSPEND = 5,
    IVIDC_STATE_PAUSE = 6,
    IVIDC_STATE_COUNT = 7,
} IVIDC_STATE_TYPE;

typedef struct {
    int width;
    int hight;
    int count;
    int frame_rate;
    vidc_color_format_type vidc_color_type;
    vidc_codec_type decodc_type;
} IVIDC_PORT_DATA;

typedef struct {
    int width;
    int height;
    int frameRate;
    vidc_codec_type codecType;
    vidc_color_format_type colorFmt;
} PortBaseInfo;

typedef struct {
    std::map<void*, vidc_frame_data_type> buffers; // void* -> addr
    std::map<pmem_handle_t, vidc_frame_data_type> handle_buffers;
    vidc_buffer_reqmnts_type requirement;
    AllocatorBase *allocator{nullptr};
    PortBaseInfo baseInfo{0};
    int32_t bufferInUseCount{0};
    int32_t bufferCount{0};
    // std::deque<vidc_frame_data_type> bufferInUse;
    // std::deque<vidc_frame_data_type> bufferNotUse;
} PortData;

typedef std::function<void(vidc_frame_data_type &)> CallbackType;

class IVidcComponent {
public:

    IVidcComponent(const char* pName)
    :_name(pName){}

    virtual ~IVidcComponent() {}

    virtual bool Init() { return true; }
    void InitPorts();

    virtual bool isSink() { return false; }
    virtual bool isSource() { return false; }

    virtual bool Executing() { return false; }
    virtual bool Pause() { return false; }
    virtual bool Release() { return false; }
    IVIDC_STATE_TYPE GetState() { return _state; };

    virtual uint32 GetBufferCount(vidc_buffer_type) = 0;
    virtual uint32 GetBufferSize(vidc_buffer_type) = 0;

    virtual bool AllocateBuffer(vidc_buffer_type buffer_type, AllocatorBase &allocator, uint32_t bytes, uint32 count);
    virtual bool UseBuffer(vidc_buffer_type buffer_type, AllocatorBase &allocator) = 0;
    bool ReleaseBuffer(vidc_buffer_type type);

    virtual bool EmptyBuffer(vidc_frame_data_type &frameData);
    virtual bool FillBuffer(vidc_frame_data_type &frameData);
    bool GetBufferByAddr(vidc_buffer_type , void*, vidc_frame_data_type&);
    bool GetBufferByHandle(vidc_buffer_type, pmem_handle_t, vidc_frame_data_type &);

    void RegisterCallback(CallbackType EemptyDone, CallbackType fillDone);

    virtual void EmptyDone(vidc_frame_data_type &frameData);
    virtual void FillDone(vidc_frame_data_type &frameData);
    
    static void DumpVidcFrameData(vidc_frame_data_type &frameData, const char* sumary = nullptr);
public:
    bool SetPortBaseInfo(vidc_buffer_type, const PortBaseInfo &);
    PortBaseInfo GetPortBaseInfo(vidc_buffer_type);
protected:

    IVIDC_STATE_TYPE _state;
    std::mutex _stateMtx;
    std::condition_variable _stateCv;
    std::map<vidc_buffer_type, PortData> _ports;
    CallbackType _emptyDoneCallback;
    CallbackType _fillDoneCallback;
    bool _isFirstFrameDone{false};
    bool _isLastFrameDone{false};
    std::string _name;
};

}
}
}


#endif