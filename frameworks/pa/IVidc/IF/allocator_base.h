#ifndef ALLOCATOR_BASE_H
#define ALLOCATOR_BASE_H

#include <stdint.h>
#include "pmem.h"
#include "autolink/frameworks/log/log.h"

namespace AutoLink {
namespace Frameworks {
namespace PA {


class AllocatorBase {
public:
    typedef struct {
        pmem_handle_t handle;
        void* addr;
        uint32_t bytes;
        uint32_t count;
    } BufferType;

    virtual ~AllocatorBase() {}
    virtual bool Allocate(int bytes, int count) = 0;
    virtual bool Allocate(int width, int height, int count) = 0;
    virtual bool Release() = 0;

    virtual void* GetAddr(int index) = 0;
    virtual void* GetHandle(int index) = 0;
    virtual int GetBufferSize() = 0;
    virtual int GetBufferCount() = 0;
    // virtual int GetBaseInfo() = 0;
    // virtual int GetBufferCount() = 0;
    virtual void* GetRenderBufferByAddr(void *) { return nullptr; }

    virtual bool isRenderBuffer() = 0;
};

}
}
}

#endif