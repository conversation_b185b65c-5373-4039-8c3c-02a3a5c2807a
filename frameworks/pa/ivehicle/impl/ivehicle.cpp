#include "autolink/frameworks/pa/ivehicle/ivehicle.h"

#include <string>
#include <unordered_map>
#include <unordered_set>
#include <mutex>

#include "autolink/frameworks/log/log.h"
#include "autolink/hal/vehicleservice/ViClient.h"
#include "com.autolink.vehicle.pb.h"

namespace AutoLink {
namespace Frameworks {
namespace PA {

#define LOG_TAG "ivehicle"

#define USER_BIT_POS 30
#define SIGNAL_DEFAULT_BIT (1 << 30)
#define SIGNAL_LOST_BIT (1 << 31)
#define SIGNAL_STATE_MASK (0xC0000000)

using namespace autolink::vehicle;

class VehicleHelper : public ViCallback {
public:
    VehicleHelper()
        : _connected(false),
          _client(nullptr) {
        _client = new ViClient(this);
    }
    virtual ~VehicleHelper() {
    }

    /* IF */
    void OnConnectService() {
        _client->connectService();
    }
    void OnDisConnectService() {
        _client->disConnectService();
    }

    void onConnect() override {
        _connected = true;

        int32_t *itemlist = nullptr;
        uint16_t itemCount = 0;

        itemlist = new int32_t[_mapProp2HandlerGroup.size()];
        if (nullptr == itemlist) {
            LOG_TAG_ERROR("no memory to subscribe prop");
            return;
        }

        for (auto it = _mapProp2HandlerGroup.begin(); it != _mapProp2HandlerGroup.end(); ++it)
        {
            itemlist[itemCount] = it->first;
            itemCount++;
        }

        _client->subscribe(itemlist, itemCount);

        delete[] itemlist;

        LOG_TAG_INFO("end subscribe props");
    }
    void onDisconnect() override {
        _connected = false;
    }
    void onBroadcast(int32_t code, ViClientPropValue &val) override
    {
        LOG_TAG_INFO("onRecvPropValue prop[%d][0x%04x]", code, code);
        {
            std::lock_guard<std::mutex> lk(_mapmtx);
            auto range = _mapProp2HandlerGroup.equal_range(code);
            if (range.first == range.second)
            {
                return;
            }
            for (auto it = range.first; it != range.second; ++it)
            {
                VehiclePropertyValue vehiclePropertyValue;
                if (LoadVehiclePropertyValue(val, vehiclePropertyValue))
                {
                    (it->second)(vehiclePropertyValue);
                }
            }
        }
    }

    void OnSubscribeProperty(int32_t propId, VehiclePropValueHandler handler) {
        std::lock_guard<std::mutex> lk(_mapmtx);
        _mapProp2HandlerGroup.insert(std::make_pair(propId, handler));
        _client->subscribe(&propId, 1);


        // auto ret = _mapProp2HandlerGroup.find(propId);
        // if (ret == _mapProp2HandlerGroup.end()) {
        //     std::unordered_set<VehiclePropValueHandler *> handlerList;
        //     handlerList.insert(&handler);
        //     _mapProp2HandlerGroup.insert(std::make_pair(propId, std::move(handlerList)));

        //     LOG_TAG_INFO("reg prop[%d]", propId);
        // } else {
        //     ret->second.insert(&handler);
        // }
    }

    void OnUnsubscribeProperty(int32_t propId) {
        LOG_TAG_INFO("UnsubscribeProperty prop[%d]", propId);
        _client->unsubscribe(&propId, 1);

    }

    bool OnSetPropValue(const VehiclePropertyValue &prop) {
        bool ret = false;

        for (int i = 0; i < prop.value.size(); ++i) {
            LOG_TAG_INFO("SetPropValue [%d][0x%04x] [%d]>", prop.gid, prop.gid, i);

            switch (prop.value.at(i).valueType) {
            case IVEHICLE_TYPE_INT32: {
                ret = _client->setInt32Prop(prop.gid, AreaType::GLOBAL, prop.value.at(i).value.i32);
            } break;
            case IVEHICLE_TYPE_INT64: {
                ret = _client->setInt64Prop(prop.gid, AreaType::GLOBAL, prop.value.at(i).value.i64);
            } break;
            case IVEHICLE_TYPE_FLOAT: {
                ret = _client->setFloatProp(prop.gid, AreaType::GLOBAL, prop.value.at(i).value.f);
            } break;
            case IVEHICLE_TYPE_STRING: {
                ret = _client->setStringProp(prop.gid, AreaType::GLOBAL, prop.value.at(i).stringValue.c_str());
            } break;
            case IVEHICLE_TYPE_BYTES: {
                std::string str(prop.value.at(i).bytes.begin(), prop.value.at(i).bytes.end());
                ret = _client->setBytesProp(prop.gid,
                                            AreaType::GLOBAL,
                                            reinterpret_cast<const uint8_t *>(str.c_str()),
                                            str.size());
            } break;
            default:
                // nothing to do
                break;
            }
        }

        // LOG_TAG_INFO("SetPropValue <");

        return ret;
    }

private:
    bool LoadVehiclePropertyValue(ViClientPropValue &fromProtoValue, VehiclePropertyValue &toLocalValue) {
        // LOG_TAG_INFO("convert proto to local >");

        if (!fromProtoValue.has_propid()) {
            LOG_TAG_ERROR("convert proto to local failed with no propid");
            return false;
        }

        toLocalValue.gid = fromProtoValue.propid();

        if (!fromProtoValue.has_propstatus()) {
            LOG_TAG_ERROR("convert proto to local failed with no propstatus");
            // return false;
        } else {
            switch (fromProtoValue.propstatus()) {
            case PROPSTATUS::AVAILABLE:
                toLocalValue.state = IVEHICLE_SIGNAL_STATE_AVAILABLE;
                break;
            case PROPSTATUS::ERROR:
                toLocalValue.state = IVEHICLE_SIGNAL_STATE_ERROR;
                break;
            case PROPSTATUS::NOT_AVAILABLE:
                toLocalValue.state = IVEHICLE_SIGNAL_STATE_DEFAULT;
                break;
            default:
                LOG_TAG_ERROR("convert proto to local failed with unsupported propstatus");
                return false;
            }
        }

        if (!fromProtoValue.has_valuetype()) {
            LOG_TAG_ERROR("convert proto to local failed with no valuetype");
            return false;
        }

        if (!fromProtoValue.has_data()) {
            LOG_TAG_ERROR("convert proto to local failed with no data");
            return false;
        }

        switch (fromProtoValue.valuetype()) {
        case ValueType::INT32:
            if (fromProtoValue.data().int32values_size() > 0) {
                VehiclePropertyRawValue rawValue;
                rawValue.sid = fromProtoValue.propid();
                rawValue.valueType = IVEHICLE_TYPE_INT32;
                rawValue.value.i32 = fromProtoValue.data().int32values(0);

                if (!fromProtoValue.has_propstatus()) {
                    if (((SIGNAL_STATE_MASK & rawValue.value.i32) >> USER_BIT_POS) != 0) {
                        LOG_TAG_ERROR("prop [0x%04x] not available", rawValue.sid);
                        toLocalValue.state = IVEHICLE_SIGNAL_STATE_ERROR;
                    } else {
                        toLocalValue.state = IVEHICLE_SIGNAL_STATE_AVAILABLE;
                    }

                    // return false;
                } else {
                    switch (fromProtoValue.propstatus()) {
                    case PROPSTATUS::AVAILABLE:
                        toLocalValue.state = IVEHICLE_SIGNAL_STATE_AVAILABLE;
                        break;
                    case PROPSTATUS::ERROR:
                        toLocalValue.state = IVEHICLE_SIGNAL_STATE_ERROR;
                        break;
                    case PROPSTATUS::NOT_AVAILABLE:
                        toLocalValue.state = IVEHICLE_SIGNAL_STATE_DEFAULT;
                        break;
                    default:
                        LOG_TAG_ERROR("convert proto to local failed with unsupported propstatus");
                        return false;
                    }
                }

                toLocalValue.value.push_back(std::move(rawValue));
            } else {
                LOG_TAG_ERROR("convert proto to local failed with no int32");
                return false;
            }
            break;
        case ValueType::INT32_VEC:
            LOG_TAG_DEBUG("convert proto to local start in INT32_VEC");
            if (fromProtoValue.data().int32values_size() > 0)
            {
                VehiclePropertyRawValue rawValue;
                rawValue.sid = fromProtoValue.propid();
                rawValue.valueType = IVEHICLE_TYPE_INT32;
                for (int i = 0; i < fromProtoValue.data().int32values_size(); ++i)
                {
                    rawValue.value.i32 = fromProtoValue.data().int32values(i);

                    if (!fromProtoValue.has_propstatus())
                    {
                        if (((SIGNAL_STATE_MASK & rawValue.value.i32) >> USER_BIT_POS) != 0)
                        {
                            LOG_TAG_ERROR("prop [0x%04x] not available in int32_vec", rawValue.sid);
                            toLocalValue.state = IVEHICLE_SIGNAL_STATE_ERROR;
                        }
                        else
                        {
                            toLocalValue.state = IVEHICLE_SIGNAL_STATE_AVAILABLE;
                        }
                    }
                    else
                    {
                        switch (fromProtoValue.propstatus())
                        {
                        case PROPSTATUS::AVAILABLE:
                            toLocalValue.state = IVEHICLE_SIGNAL_STATE_AVAILABLE;
                            break;
                        case PROPSTATUS::ERROR:
                            toLocalValue.state = IVEHICLE_SIGNAL_STATE_ERROR;
                            break;
                        case PROPSTATUS::NOT_AVAILABLE:
                            toLocalValue.state = IVEHICLE_SIGNAL_STATE_DEFAULT;
                            break;
                        default:
                            LOG_TAG_ERROR("convert proto to local failed with unsupported propstatus in int32_vec");
                            return false;
                        }
                    }
                    toLocalValue.value.push_back(std::move(rawValue));
                }
            }
            else
            {
                LOG_TAG_ERROR("convert proto to local failed with no int32_vec");
                return false;
            }
            break;
        case ValueType::STRING:
        case ValueType::BOOL:
        case ValueType::INT64:
        case ValueType::INT64_VEC:
        case ValueType::FLOAT:
        case ValueType::FLOAT_VEC:
        case ValueType::BYTES:
        default:
            LOG_TAG_ERROR("convert proto to local failed with unsupported valuetype %d", fromProtoValue.valuetype());
            return false;
        }

        if (fromProtoValue.has_timestamp()) {
            // handle timestamp
        }

        if (fromProtoValue.has_areaid()) {
            // handle areaid
        }

        // LOG_TAG_INFO("convert proto to local <");

        return true;
    }

private:
    /* data */
    bool _connected;
    ViClient *_client{nullptr};
    std::unordered_multimap<int32_t, VehiclePropValueHandler> _mapProp2HandlerGroup;
    std::mutex _mapmtx;
};

struct IVehiclePrivate {
    VehicleHelper *_impl{nullptr};
};

static VehicleHelper *gVehicleHelperPtr = nullptr;

IVehicle::IVehicle()
    : p_(new IVehiclePrivate()) {
    p_->_impl = new VehicleHelper();
    if (nullptr == p_->_impl) {
        throw std::runtime_error("IVehicle invalid");
    }

    gVehicleHelperPtr = p_->_impl;
}

IVehicle::~IVehicle() {
}

// static
void IVehicle::BlockSignal() {
    LOG_TAG_INFO("BlockSignal");
    gVehicleHelperPtr->OnDisConnectService();
}

// static
void IVehicle::RestoreSignal() {
    LOG_TAG_INFO("RestoreSignal");
    gVehicleHelperPtr->OnConnectService();
}

// static
int32_t IVehicle::Name2Id(const char *name) {
    int32_t propId = ViClient::SignalName2Id(name);
    LOG_TAG_INFO("Name2Id [%s]>[%d]:[0x%04x]", name, propId, propId);
    return propId;
}

void IVehicle::Start() {
    LOG_TAG_INFO("IVehicle Start");
    (p_->_impl)->OnConnectService();
}

void IVehicle::Stop() {
    LOG_TAG_INFO("IVehicle Stop");
    (p_->_impl)->OnDisConnectService();
}

void IVehicle::SubscribeProperty(int32_t propId, VehiclePropValueHandler handler) {
    // LOG_TAG_INFO("SubscribeProperty prop[%d]", propId);
    (p_->_impl)->OnSubscribeProperty(propId, handler);
}

void IVehicle::UnsubscribeProperty(int32_t propId) {
    LOG_TAG_WARNING("UnsubscribeProperty prop[%d]", propId);
    (p_->_impl)->OnUnsubscribeProperty(propId);
}

bool IVehicle::SetPropValue(const VehiclePropertyValue &prop) {
    LOG_TAG_INFO("SetPropValue");
    return (p_->_impl)->OnSetPropValue(prop);
}
}
}
}
