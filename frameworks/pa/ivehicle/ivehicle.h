#ifndef IVEHICLE_H
#define IVEH<PERSON><PERSON>_H

#include <string>
#include <vector>
#include <functional>

namespace AutoLink {
namespace Frameworks {
namespace PA {

enum IVEHICLE_VALUE_TYPE : int8_t {
    IVEHICLE_TYPE_INT32 = 0,
    IVEHICLE_TYPE_UINT32 = 1,
    IVEH<PERSON>LE_TYPE_INT64 = 2,
    IVEHICLE_TYPE_UINT64 = 3,
    IVEHICLE_TYPE_FLOAT = 4,
    IVEHICLE_TYPE_DOUBLE = 5,
    IVEHICLE_TYPE_STRING = 6,
    IVEHICLE_TYPE_BYTES = 7,
};

enum IVEHICLE_SIGNAL_STATE {
    IVEHICLE_SIGNAL_STATE_DEFAULT = 0,   // The property has never received the signal from GW
    IVEHICLE_SIGNAL_STATE_AVAILABLE = 1, // The property is available
    IVEHICLE_SIGNAL_STATE_ERROR = 2,     // The property is error, such as siganl missing or can node lost
};

/*
    // +---------------------------+------------+------------
    // |                           |     sid    |     gid   |
    // +---------------------------+------------+------------
    // |         bosch             |            |     *     |
    // +----------------------------------------+------------
    // |         autolink          |      *     |     *     |
    // +-----------------------------------------------------
*/

struct VehiclePropertyRawValue {
    int32_t sid;
    IVEHICLE_VALUE_TYPE valueType;
    union {
        int32_t i32;
        uint32_t ui32;
        int64_t i64;
        uint64_t ui64;
        float f;
        double d;
    } value;
    std::vector<uint8_t> bytes;
    std::string stringValue;
};

struct VehiclePropertyValue {
    int32_t gid;
    IVEHICLE_SIGNAL_STATE state;
    std::vector<VehiclePropertyRawValue> value;
};

typedef std::function<void(const VehiclePropertyValue &propValue)> VehiclePropValueHandler;

struct IVehiclePrivate;
class IVehicle
{
public:
    IVehicle();
    ~IVehicle();
    static void BlockSignal();
    static void RestoreSignal();
    static int32_t Name2Id(const char* name);

    void Start();
    void Stop();

public:
    void SubscribeProperty(int32_t propId, VehiclePropValueHandler handler);
    void UnsubscribeProperty(int32_t propId);
    bool SetPropValue(const VehiclePropertyValue &prop);

private:
    struct IVehiclePrivate *p_;
};

} // namespace PA

} // namespace Frameworks

} // namespace AutoLink

#endif