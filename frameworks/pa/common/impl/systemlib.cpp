#include "autolink/frameworks/pa/common/systemlib.h"
#include <dlfcn.h>
#include <stdio.h>

namespace Alsdk {
namespace Platform {

int SystemLibLoad(const char *libName, SystemLib *lib)
{
    void *handle = dlopen(libName, RTLD_NOW | RTLD_GLOBAL);
    if (!handle) {
        fprintf(stderr, "LoadSystemLib %s failed, %s\n", libName, dlerror());
        return -1;
    }

    *lib = handle;
    return 0;
}

int SystemLibLoadSymbols(SystemLib lib, std::vector<SystemLibSymbol> &symbols)
{
    if (!lib)
        return -1;

    for (int i = 0; i < symbols.size(); ++i) {
        auto &symbol = symbols[i];
        dlerror();
        *symbol.addr = dlsym(lib, symbol.name);
        char *err = dlerror();
        if (err) {
            fprintf(stderr, "LoadSystemLibSymbols %s failed, %s\n", symbol.name, err);
            return -1;
        }
    }
    return 0;
}

int SystemLibUnload(SystemLib lib)
{
    if (!lib)
        return -1;
    return dlclose(lib);
}

} // namespace Platform
} // namespace Alsdk