#include "autolink/frameworks/pa/common/fdb.h"

namespace AutoLink {
namespace Frameworks {
namespace PA {
namespace Common {

class Fdb {
public:
    static Fdb &Get()
    {
        static Fdb fdb;
        return fdb;
    }

    Fdb(const Fdb &copy) = delete;

    Fdb &operator=(const Fdb &other) = delete;

    ~Fdb()
    {
        mainWorker.exit();
        FDB_CONTEXT->exit();

        mainWorker.join();
        FDB_CONTEXT->join();
    }

    CBaseWorker mainWorker;

private:
    Fdb()
    {
        mainWorker.name("fdbWorker");
        FDB_CONTEXT->start();
        mainWorker.start();
    }
};

class FdbClientImpl : public CBaseClient {
public:
    FdbClientImpl(FdbClient *interf, const std::string &serverUrl)
        : CBaseClient((serverUrl + ".client@" + std::to_string(getpid())).c_str(), &Fdb::Get().mainWorker), interf_(interf), url_(serverUrl)
    {
        enableReconnect(true);
        enableUDP(true);
        enableTimeStamp(true);
        setNsName(serverUrl.c_str());
    }

private:
    void onOnline(const CFdbOnlineInfo &info) override
    {
        interf_->onOnline(info);
    }

    void onOffline(const CFdbOnlineInfo &info)
    {
        interf_->onOffline(info);
    }

    void onBroadcast(CBaseJob::Ptr &msgRef)
    {
        interf_->onBroadcast(msgRef);
    }

    void onReply(CBaseJob::Ptr &msgRef)
    {
        interf_->onReply(msgRef);
    }

private:
    FdbClient *interf_;
    std::string url_;
};

class FdbServerImpl : public CBaseServer {
public:
    FdbServerImpl(FdbServer *interf, const std::string &serverUrl, const std::string &workerName)
        : CBaseServer((serverUrl + ".server@" + std::to_string(getpid())).c_str(), &Fdb::Get().mainWorker), interf_(interf), url_(serverUrl), worker_(nullptr)
    {
        if (!workerName.empty())
        {
            worker_ = new CBaseWorker(workerName.c_str());
            if (worker_)
            {
                worker_->start();
                worker(worker_);
            }
        }
        enableWatchdog(true);
        enableUDP(true);
        setNsName(serverUrl.c_str());
    }
    ~FdbServerImpl()
    {
        if (worker_)
        {
            worker_->exit();
            worker_->join();
            delete worker_;
            worker_ = nullptr;
        }
    }

private:
    void onOnline(const CFdbOnlineInfo &info) override
    {
        interf_->onOnline(info);
    }

    void onOffline(const CFdbOnlineInfo &info) override
    {
        interf_->onOffline(info);
    }

    void onInvoke(CBaseJob::Ptr &msgRef) override
    {
        interf_->onInvoke(msgRef);
    }

    void onSubscribe(CBaseJob::Ptr &msgRef) override
    {
        interf_->onSubscribe(msgRef);
    }

private:
    std::string url_;
    FdbServer *interf_;
    CBaseWorker *worker_;
};

struct FdbClientPrivate {
    FdbClientImpl *impl;
};

FdbClient::FdbClient(const std::string &serverUrl) : p_(new FdbClientPrivate)
{
    p_->impl = new FdbClientImpl(this, serverUrl);
}

FdbClient::~FdbClient()
{
    delete p_->impl;
    delete p_;
}

FdbSessionId_t FdbClient::connect()
{
    return p_->impl->connect();
}

bool FdbClient::connected()
{
    return p_->impl->connected();
}

void FdbClient::disconnect()
{
    return p_->impl->disconnect();
}

void FdbClient::addNotifyItem(CFdbMsgSubscribeList &msg_list, FdbMsgCode_t msg_code, const char *filter)
{
    return p_->impl->addNotifyItem(msg_list, msg_code, filter);
}

bool FdbClient::subscribe(CFdbMsgSubscribeList &subscribeList)
{
    return p_->impl->subscribe(subscribeList);
}

bool FdbClient::invoke(FdbMsgCode_t code, IFdbMsgBuilder &data)
{
    return p_->impl->invoke(code, data);
}

bool FdbClient::invoke(FdbMsgCode_t code, const void *buffer, int32_t size)
{
    return p_->impl->invoke(code, buffer, size);
}

bool FdbClient::invoke(CBaseJob::Ptr &msgRef, IFdbMsgBuilder &data, int32_t timeout)
{
    return p_->impl->invoke(msgRef, data, timeout);
}

bool FdbClient::invoke(CBaseJob::Ptr &msgRef, const void *buffer, int32_t size, int32_t timeout)
{
    return p_->impl->invoke(msgRef, buffer, size, timeout);
}

bool FdbClient::publish(FdbMsgCode_t code, IFdbMsgBuilder &data, const char *topic, bool force_update)
{
    return p_->impl->publish(code, data, topic, force_update);
}

bool FdbClient::publish(FdbMsgCode_t code, const void *buffer, int32_t size, const char *topic, bool force_update)
{
    return p_->impl->publish(code, buffer, size, topic, force_update);
}

struct FdbServerPrivate {
    FdbServerImpl *impl;
};

FdbServer::FdbServer(const std::string &serverUrl, const std::string &workerName) : p_(new FdbServerPrivate)
{
    p_->impl = new FdbServerImpl(this, serverUrl, workerName);
}

FdbServer::~FdbServer()
{
    delete p_->impl;
    delete p_;
}

bool FdbServer::bind()
{
    return p_->impl->bind();
}

void FdbServer::unbind()
{
    return p_->impl->unbind();
}

bool FdbServer::broadcast(FdbMsgCode_t code, IFdbMsgBuilder &data, const char *filter)
{
    return p_->impl->broadcast(code, data, filter);
}

bool FdbServer::broadcast(FdbMsgCode_t code, const void *buffer, int32_t size, const char *filter)
{
    return p_->impl->broadcast(code, buffer, size, filter);
}

} // namespace Common
} // namespace PA
} // namespace Frameworks
} // namespace AutoLink