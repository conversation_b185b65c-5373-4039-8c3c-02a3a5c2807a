#ifndef AUTOLINK_FRAMEWORKS_PA_COMMON_FDB_H
#define AUTOLINK_FRAMEWORKS_PA_COMMON_FDB_H

#include "fdbus/fdbus.h"
#include "fdbus/CFdbProtoMsgBuilder.h"

namespace AutoLink {
namespace Frameworks {
namespace PA {
namespace Common {

using namespace ipc::fdbus;

class FdbClient {
    friend class FdbClientImpl;

public:
    FdbClient(const std::string &serverUrl);

    virtual ~FdbClient();

    FdbSessionId_t connect();

    bool connected();

    void disconnect();

    void addNotifyItem(CFdbMsgSubscribeList &msg_list, FdbMsgCode_t msg_code, const char *filter = (const char *)0);

    bool subscribe(CFdbMsgSubscribeList &subscribeList);

    bool invoke(FdbMsgCode_t code, IFdbMsgBuilder &data);

    bool invoke(FdbMsgCode_t code, const void *buffer = 0, int32_t size = 0);

    bool invoke(CBaseJob::Ptr &msg_ref, IFdbMsgBuilder &data, int32_t timeout = 0);

    bool invoke(CBaseJob::Ptr &msg_ref, const void *buffer = 0, int32_t size = 0, int32_t timeout = 0);

    bool publish(FdbMsgCode_t code, IFdbMsgBuilder &data, const char *topic = 0, bool force_update = false);

    bool publish(FdbMsgCode_t code, const void *buffer = 0, int32_t size = 0, const char *topic = 0, bool force_update = false);

protected:
    virtual void onOnline(const CFdbOnlineInfo &info) {}

    virtual void onOffline(const CFdbOnlineInfo &info) {}

    virtual void onBroadcast(CBaseJob::Ptr &msg_ref) {}

    virtual void onReply(CBaseJob::Ptr &msg_ref) {}

private:
    struct FdbClientPrivate *p_;
};

class FdbServer {
    friend class FdbServerImpl;

public:
    FdbServer(const std::string &serverUrl, const std::string &workerName = "");

    ~FdbServer();

    bool bind();

    void unbind();

    bool broadcast(FdbMsgCode_t code, IFdbMsgBuilder &data, const char *filter = 0);

    bool broadcast(FdbMsgCode_t code, const void *buffer = 0, int32_t size = 0, const char *filter = 0);

protected:
    virtual void onOnline(const CFdbOnlineInfo &info) {}

    virtual void onOffline(const CFdbOnlineInfo &info) {}

    virtual void onInvoke(CBaseJob::Ptr &msgRef) {}

    virtual void onSubscribe(CBaseJob::Ptr &msg_ref) {}

private:
    struct FdbServerPrivate *p_;
};
} // namespace Common
} // namespace PA
} // namespace Frameworks
} // namespace AutoLink
#endif
