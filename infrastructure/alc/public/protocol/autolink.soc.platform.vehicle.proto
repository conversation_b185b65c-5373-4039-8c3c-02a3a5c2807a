syntax = "proto2";

package autolink.soc.platform.vehicle.pb;

enum MsgIDSoc {
    MSG_ID_GET_GROUP_INFO   = 1;// use MsgGroupIdSoc to get MsgGroupSoc
    MSG_ID_SEND_GROUP_INFO  = 2;// use MsgGroupIdSoc to send MsgGroupSoc
    MSG_ID_GET_SIGNAL_INFO  = 3;// use MsgSignalIdSoc to get MsgSignalSoc
    MSG_ID_SEND_SIGNAL_INFO = 4;// use MsgSignalIdSoc to send MsgSignalSoc
}

enum StateSoc {
    DEFAULT     = 0;
    INACTIVE    = 1;
    ACTIVE      = 2;
}

enum ValueTypeSoc {
    TYPE_UINT32     = 3;
    TYPE_BOOLEAN    = 4;
    TYPE_SINT32     = 7;
    TYPE_UINT64     = 8;
    TYPE_SINT64     = 9;
    TYPE_BYTES      = 11;
}

enum InvalidIdSoc {
    ID_DUMMY = 2147483647;
}

// the following GID can be subscribed
enum GroupIdSoc {

    GID_DUMMY = 2147483647;
}

// the following SID can be subscribed
enum SignalIdSoc {
    DVR_1_DVRFailReasons_541 = *********;// UINT8
    DVR_1_RecordSts_541 = *********;// UINT8
    STAT_AMPNaviVolume_461 = *********;// UINT8
    STAT_AMPVRVolume_461 = 134316035;// UINT8
    STAT_AMPKeyTone_461 = 134348804;// UINT8
    STAT_AMPMediaVolume_461 = 134381573;// UINT8
    STAT_AMPPhoneVolume_461 = 134414342;// UINT8
    STAT_AMPSoundFocus_461 = 134447111;// UINT8
    STAT_AMPSoundEffect_461 = 134479880;// UINT8
    STAT_AMPSoundEffectBass_461 = 134512649;// UINT8
    STAT_AMPSoundEffectMidrange_461 = 134545418;// UINT8
    STAT_AMPSoundEffectTreble_461 = 134578187;// UINT8
    STAT_AMPSoundFieldBalance_461 = 134610956;// UINT8
    STAT_AMPSoundFieldFader_461 = 134643725;// UINT8
    STAT_REQ_AMPA2BMediaSound_461 = 134676494;// UINT8
    STAT_REQ_AMPA2BNaviSound_461 = 134709263;// UINT8
    STAT_REQ_AMPA2BVRSound_461 = 134742032;// UINT8
    STAT_REQ_AMPA2BTTSSound_461 = 134774801;// UINT8
    STAT_REQ_AMPA2BPhoneSound_461 = 134807570;// UINT8
    STAT_REQ_AMPAlarm_461 = 134840339;// UINT8
    STAT_AMPMediaDuck_474 = 134873108;// UINT8
    STAT_AMPSpeedVolume_474 = 134905877;// UINT8
    STAT_AMPMute_474 = 134938646;// UINT8
    STAT_AMPRestoreDefaults_474 = 134971415;// UINT8
    STAT_AMPAlarmVolume_474 = 135004184;// UINT8
    STAT_Headrest_Mode_474 = 135036953;// UINT8
    STAT_AMPSoundEffectMegaBass_474 = 135069722;// UINT8
    STAT_AMPSoundEffectMidBass_474 = 135102491;// UINT8
    STAT_AMPSoundEffectMidTreble_474 = 135135260;// UINT8
    STAT_VirtualSbwfrOnOff_474 = 135168029;// UINT8
    STAT_SurndFnOnOff_474 = 135200798;// UINT8
    STAT_AMPBackgroundVolume_474 = 135233567;// UINT8
    STAT_AMPSoundBypass_474 = 135266336;// UINT8
    STAT_AMPPwrRdySts_474 = *********;// UINT8
    STAT_AMPVersion_474 = *********;// UINT8
    EAMP_AMPKTVvoiceVolumeFed_4B6 = *********;// UINT8
    EAMP_AMPENCVolumeFed_4B6 = *********;// UINT8
    EAMP_AMPA2BKTVvoiceSoundFed_4B6 = *********;// UINT8
    STAT_AMPSoundFocus_2_4B6 = *********;// UINT8
    EAMP_AMPA2BENCSoundFed_4B6 = *********;// UINT8
    EAMP_AMPA2BChimeSoundFed_4B6 = *********;// UINT8
    EPC_LeftPedalSystem_Sts_551 = *********;// UINT8
    EPC_RightPedalSystem_Sts_551 = *********;// UINT8
    EPC_LeftPedalTimeTravel_551 = *********;// UINT8
    EPC_RightPedalTimeTravel_551 = *********;// UINT8
    EPC_LeftPedalRealTime_Sts_551 = *********;// UINT8
    EPC_LeftPedalBreakIce_Sts_551 = *********;// UINT8
    EPC_RightPedalRealTime_Sts_551 = *********;// UINT8
    EPC_RightPedalBreakIce_Sts_551 = *********;// UINT8
    EPC_Enable_CmdStatus_551 = *********;// UINT8
    EPC_LeftPedalCmdStatus_551 = *********;// UINT8
    EPC_RightPedalCmdStatus_551 = *********;// UINT8
    EPC_LeftPedal3D_Sts_551 = *********;// UINT8
    EPC_RightPedal3D_Sts_551 = *********;// UINT8
    SBOX_MSD_Res_545 = *********;// UINT8
    SBOX_Ecallphonenub_Res_545 = *********;// UINT8
    SBOX_GNSS_ANT_State_5AB = *********;// UINT8
    SBOX_SIM_State_5AB = *********;// UINT8
    SBOX_BT_State_5AB = *********;// UINT8
    SBOX_TT_State_5AB = *********;// UINT8
    SBOX_TT_ANT_State_5AB = *********;// UINT8
    SBOX_BatterState_5AB = *********;// UINT8
    SBOX_PowerState_5AB = *********;// UINT8
    SBOX_SimActivation_5AB = *********;// BOOL
    SBOX_TspActivation_5AB = 805339200;// BOOL
    SBOX_SatelliteStatus_5AB = 136282177;// UINT8
    SBOX_BtConState_5AB = 136314946;// UINT8
    SBOX_SelfCheck_5AB = 136347715;// UINT8
    SBOX_SatelliteSignalValue_5AB = 136380484;// UINT8
    SBOX_BatTemp_5AB = 136413253;// UINT8
    SBOX_TimeSpentOnline_5AB = 268435526;// UINT16
    SBOX_RunTime_5AB = 268468295;// UINT16
    SBOX_EcallState_5AB = 136446024;// UINT8
    SBOX_SetMsgTotal_547 = 268501065;// UINT16
    SBOX_SetMSDTotal_547 = 268533834;// UINT16
    SBOX_SetCallTimeThreshold_547 = 268566603;// UINT16
    SBOX_SetTodayCallTimeThreshold_547 = 268599372;// UINT16
    SBOX_SetCurretnCallTime_547 = 268632141;// UINT16
    SBOX_SetCurretnMsgThreshold_547 = 268664910;// UINT16
    SBOX_SetMsgTotalThreshold_547 = 268697679;// UINT16
    SBOX_SetMsdThreshold_547 = 268730448;// UINT16
    SBOX_SetCurrenMsdCnt_547 = 268763217;// UINT16
    SBOX_SetCustomMsgThreshold_547 = 268795986;// UINT16
    SBOX_SetCurrentCustomMsgCnt_547 = 268828755;// UINT16
    SBOX_ICC_MACReq_549 = 805371988;// BOOL
    SBOX_MAC_Req0_549 = 136478805;// UINT8
    SBOX_MAC_Req1_549 = 136511574;// UINT8
    SBOX_MAC_Req2_549 = 136544343;// UINT8
    SBOX_MAC_Req3_549 = 136577112;// UINT8
    SBOX_MAC_Req4_549 = 136609881;// UINT8
    SBOX_MAC_Req5_549 = 136642650;// UINT8
    SBOX_ActiveTsp_Res_54E = 805404763;// BOOL
    SBOX_ActiveSim_Res_54E = 805437532;// BOOL
    SBOX_Mac_Res0_54E = 136675421;// UINT8
    SBOX_Mac_Res1_54E = 136708190;// UINT8
    SBOX_Mac_Res2_54E = 136740959;// UINT8
    SBOX_Mac_Res3_54E = 136773728;// UINT8
    SBOX_Mac_Res4_54E = 136806497;// UINT8
    SBOX_Mac_Res5_54E = 136839266;// UINT8
    SBOX_GetPlatformNumberRes_54E = 805470307;// BOOL
    SBOX_ICCId_Res0_54E = 136872036;// UINT8
    SBOX_ICCId_Res1_54E = 136904805;// UINT8
    SBOX_ICCId_Res2_54E = 136937574;// UINT8
    SBOX_ICCId_Res3_54E = 136970343;// UINT8
    SBOX_ICCId_Res4_54E = 137003112;// UINT8
    SBOX_ICCId_Res5_54E = 137035881;// UINT8
    SBOX_ICCId_Res6_54E = 137068650;// UINT8
    SBOX_ICCId_Res7_54E = 137101419;// UINT8
    SBOX_ICCId_Res8_54E = 137134188;// UINT8
    SBOX_ICCId_Res9_54E = 137166957;// UINT8
    SBOX_ICCId_Res10_54E = 137199726;// UINT8
    SBOX_ICCId_Res11_54E = 137232495;// UINT8
    SBOX_ICCId_Res12_54E = 137265264;// UINT8
    SBOX_ICCId_Res13_54E = 137298033;// UINT8
    SBOX_ICCId_Res14_54E = 137330802;// UINT8
    SBOX_ICCId_Res15_54E = 137363571;// UINT8
    SBOX_ICCId_Res16_54E = 137396340;// UINT8
    SBOX_ICCId_Res17_54E = 137429109;// UINT8
    SBOX_ICCId_Res18_54E = 137461878;// UINT8
    SBOX_ICCId_Res19_54E = 137494647;// UINT8
    SBOX_Vin0_54E = 137527416;// UINT8
    SBOX_Vin1_54E = 137560185;// UINT8
    SBOX_Vin2_54E = 137592954;// UINT8
    SBOX_Vin3_54E = 137625723;// UINT8
    SBOX_Vin4_54E = 137658492;// UINT8
    SBOX_Vin5_54E = 137691261;// UINT8
    SBOX_Vin6_54E = 137724030;// UINT8
    SBOX_Vin7_54E = 137756799;// UINT8
    SBOX_Vin8_54E = 137789568;// UINT8
    SBOX_Vin9_54E = 137822337;// UINT8
    SBOX_Vin10_54E = 137855106;// UINT8
    SBOX_Vin11_54E = 137887875;// UINT8
    SBOX_Vin12_54E = 137920644;// UINT8
    SBOX_Vin13_54E = 137953413;// UINT8
    SBOX_Vin14_54E = 137986182;// UINT8
    SBOX_Vin15_54E = 138018951;// UINT8
    SBOX_Vin16_54E = 138051720;// UINT8
    PLG_LatchSts_436 = 138084489;// UINT8
    BBCM_RLSeatEasyEntryExitFb_51E = 138117258;// UINT8
    BBCM_RRSeatEasyEntryExitFb_51E = 138150027;// UINT8
    BBCM_RLSeatNeedMemory_51E = 138182796;// UINT8
    BBCM_RLMemoryFb_51E = 138215565;// UINT8
    BBCM_RLRecoverFb_51E = 138248334;// UINT8
    BBCM_RRSeatNeedMemory_51E = 138281103;// UINT8
    BBCM_RRMemoryFb_51E = 138313872;// UINT8
    BBCM_RRRecoverFb_51E = 138346641;// UINT8
    BBCM_RLSeatLvlFb2nd_51E = 138379410;// UINT8
    BBCM_RLSeatBackAgFb2nd_51E = 138412179;// UINT8
    BBCM_RLSeatLegSpprtFb2nd_51E = 138444948;// UINT8
    BBCM_RLSeatLegRestAngleFb2nd_51E = 138477717;// UINT8
    BBCM_RRSeatLvlFb2nd_51E = 138510486;// UINT8
    BBCM_RRSeatBackAgFb2nd_51E = 138543255;// UINT8
    BBCM_RRSeatLegSpprtFb2nd_51E = 138576024;// UINT8
    BBCM_RRSeatLegRestAngleFb2nd_51E = 138608793;// UINT8
    BBCM_RLSeatBackAgFb3rd_51E = 138641562;// UINT8
    BBCM_RRSeatBackAgFb3rd_51E = 138674331;// UINT8
    BBCM_RLSeatFrontFlipFb3rd_51E = 138707100;// UINT8
    BBCM_RRSeatFrontFlipFb3rd_51E = 138739869;// UINT8
    CMSM_DisplaySwitch_4B1 = 138772638;// UINT8
    CMSM_DisplayAdjustrang_4B1 = 138805407;// UINT8
    CMSM_DisplayAdjustmode_4B1 = 138838176;// UINT8
    CMSM_DisplayStandard_4B1 = 138870945;// UINT8
    TotalOdometer_km_OBD_4E4 = 402653346;// UINT32
    DisplayVehicleSpeed_4E4 = 268861603;// UINT16
    ICC_BackLightLevel_4E4 = 138903716;// UINT8
    TotalOdometer_km_OBDValidData_4E4 = 805503141;// BOOL
    ICC_TotalodometerbackupEnable_4E4 = 138936486;// UINT8
    STAT_AMPAlarm_52A = 138969255;// UINT8
    Fusa_498_6_498 = 139002024;// UINT8
    Fusa_498_5_498 = 139034793;// UINT8
    Fusa_498_4_498 = 139067562;// UINT8
    Fusa_498_3_498 = 139100331;// UINT8
    Fusa_498_2_498 = 139133100;// UINT8
    Fusa_498_1_498 = 139165869;// UINT8
    ICC_CH_3_CRC_498 = 139198638;// UINT8
    ICC_CH_3_RollgCntr_498 = 139231407;// UINT8
    ICC_ASUAssistPassSet_498 = 139264176;// UINT8
    IHU_AutHldSet_498 = 139296945;// UINT8
    ICC_AutoEasyEntrySet_498 = 139329714;// UINT8
    ICC_ManualEasyOutSw_498 = 139362483;// UINT8
    ICC_ASCMaintainModeReq_498 = 139395252;// UINT8
    ICC_PABSetCmd_498 = 139428021;// UINT8
    ICC_EPBSetCmd_498 = 139460790;// UINT8
    ICC_ASUMemoryPosition_498 = 139493559;// UINT8
    ICC_ASUEasyLoadingSet_498 = 139526328;// UINT8
    Set_CSTFunctionSts_498 = 139559097;// UINT8
    ICC_ASURearaxlewithTaildoor_498 = 139591866;// UINT8
    CST_SensitivityReq_498 = 139624635;// UINT8
    TIHU_SetHDCOnOff_498 = 139657404;// UINT8
    Set_ESPFunctionSts_498 = 139690173;// UINT8
    ICC_ASUPreviewCont_498 = 139722942;// UINT8
    ICC_ASUHighwayModAdjust_498 = 139755711;// UINT8
    ICC_ASUMapAdjust_498 = 139788480;// UINT8
    ICC_DisChg_4D7 = 139821249;// UINT8
    ICC_StpDisChgReq_4D7 = 805535938;// BOOL
    ICC_RegenLevel_4D7 = 139854019;// UINT8
    ICC_ELockReq_4D7 = 805568708;// BOOL
    ICC_DisChgPercent_4D7 = 139886789;// UINT8
    ICC_ChgSocSet_4D7 = 139919558;// UINT8
    ICC_CampMode_4D7 = 139952327;// UINT8
    ICC_SftWarning_4D7 = 139985096;// UINT8
    ICC_TowingMode_4D7 = 140017865;// UINT8
    ICC_StopMode_4D7 = 140050634;// UINT8
    ICC_EPedalMode_4D7 = 140083403;// UINT8
    ICC_WindowKeySts_4DE = 140116172;// UINT8
    ICC_FaceKeySts_4DE = 140148941;// UINT8
    ICC_FootKeySts_4DE = 140181710;// UINT8
    BltCallSts_4DE = 140214479;// UINT8
    ICC_DockWinspSts_4DE = 140247248;// UINT8
    ICC_DockTSts_4DE = 140280017;// UINT8
    ICC_ACFastCool_4DE = 140312786;// UINT8
    ICC_ACFastHeat_4DE = 140345555;// UINT8
    ICC_SetCLMOn_4DE = 140378324;// UINT8
    ICC_SetAutoKeySts_4DE = 140411093;// UINT8
    ICC_SetFrontDeforestSts_4DE = 140443862;// UINT8
    ICC_SetCirculationModeKeySts_4DE = 140476631;// UINT8
    ICC_ZoneSelectionKeySts_4DE = 140509400;// UINT8
    ICC_ResetFilter_4DE = 140542169;// UINT8
    ICC_AUTODefrostOnKeySts_4DE = 140574938;// UINT8
    ICC_AutoAirClean_4DE = 140607707;// UINT8
    ICC_KeepWarm_4DE = 140640476;// UINT8
    ICC_BlowSpeedLevelKeySts_4DE = 140673245;// UINT8
    ICC_SetACRequestKeySts_4DE = 140706014;// UINT8
    ICC_DSwingSet_4DE = 140738783;// UINT8
    ICC_PSwingSet_4DE = 140771552;// UINT8
    ICC_AUTODefLevelSet_4DE = 140804321;// UINT8
    ICC_SetTemperature_L_C_4DE = 140837090;// UINT8
    ICC_SetTemperature_R_C_4DE = 140869859;// UINT8
    ICC_DLSwingUpDwn_4DE = 140902628;// UINT8
    ICC_DLSwingLeRi_4DE = 140935397;// UINT8
    ICC_DRSwingUpDwn_4DE = 140968166;// UINT8
    ICC_DRSwingLeRi_4DE = 141000935;// UINT8
    ICC_PLSwingUpDwn_4DE = 141033704;// UINT8
    ICC_PLSwingLeRi_4DE = 141066473;// UINT8
    ICC_PRSwingUpDwn_4DE = 141099242;// UINT8
    ICC_PRSwingLeRi_4DE = 141132011;// UINT8
    ICC_DCSwingLeRi_4DE = 141164780;// UINT8
    ICC_NetWorkTemperature_4DE = 141197549;// UINT8
    ICC_NetWorkHumidity_4DE = 141230318;// UINT8
    ICC_DLOnOff_4DE = 141263087;// UINT8
    ICC_DROnOff_4DE = 141295856;// UINT8
    ICC_PLOnOff_4DE = 141328625;// UINT8
    ICC_PROnOff_4DE = 141361394;// UINT8
    ICC_DCOnOff_4DE = 141394163;// UINT8
    ICC_LoveRemind_4DE = 141426932;// UINT8
    ICC_InteCleanCar_4DE = 141459701;// UINT8
    ICC_KeepWarmMemory_4DE = 141492470;// UINT8
    ICC_AutoLightSW_4E2 = 805601527;// BOOL
    ICC_CleanMode_0x4e2_4E2 = 141525240;// UINT8
    ICC_1_OTASts_4E2 = 141558009;// UINT8
    ICC_1_OTAHV_Req_4E2 = 141590778;// UINT8
    ICC_1_OTAPwrMngt_4E2 = 141623547;// UINT8
    ICC_DriveModeSwitch_4E2 = 141656316;// UINT8
    ICC_PropulsionMode_4E2 = 141689085;// UINT8
    ICC_SteeringMode_4E2 = 141721854;// UINT8
    ICC_SuspensionHeight_4E2 = 141754623;// UINT8
    ICC_SuspensionDamping_4E2 = 141787392;// UINT8
    ICC_AirconditionMode_4E2 = 141820161;// UINT8
    ICC_BrakePedalFeelMode_4E2 = 141852930;// UINT8
    ICC_SentinelHV_Req_4E2 = 141885699;// UINT8
    ICC_PetModeHV_Req_4E2 = 141918468;// UINT8
    ICC_SelfClearing_4E2 = 141951237;// UINT8
    ICC_WelcomeOpenSet_4E6 = 141984006;// UINT8
    ICC_WALOpenSet_4E6 = 142016775;// UINT8
    ICC_EasytrunkSet_4E6 = 142049544;// UINT8
    ICC_UIROpenSet_4E6 = 142082313;// UINT8
    ICC_BeanIDReq_4E6 = 142115082;// UINT8
    ICC_CWCWorkingStsSet_4E6 = 142147851;// UINT8
    ICC_CWCPhoneforgottenFunStsSet_4E6 = 142180620;// UINT8
    ICC_RCWCWorkingStsSet_4E6 = 142213389;// UINT8
    ICC_RCWCPhoneforgottenFunStsSet_4E6 = 142246158;// UINT8
    CurrentTimeYear_0x510_510 = 142278927;// UINT8
    CurrentTimeMonth_0x510_510 = 142311696;// UINT8
    CurrentTimeDay_0x510_510 = 142344465;// UINT8
    CurrentTimeHour_0x510_510 = 142377234;// UINT8
    CurrentTimeMinute_0x510_510 = 142410003;// UINT8
    CurrentTimeSecond_0x510_510 = 142442772;// UINT8
    CurrentTimeYear_UTC_5A7 = 142475541;// UINT8
    CurrentTimeMonth_UTC_5A7 = 142508310;// UINT8
    CurrentTimeDay_UTC_5A7 = 142541079;// UINT8
    CurrentTimeHour_UTC_5A7 = 142573848;// UINT8
    CurrentTimeMinute_UTC_5A7 = 142606617;// UINT8
    CurrentTimeSecond_UTC_5A7 = 142639386;// UINT8
    Set_AMPNaviVolume_44C = 142672155;// UINT8
    Set_AMPVRVolume_44C = 142704924;// UINT8
    Set_AMPKeyTone_44C = 142737693;// UINT8
    Set_AMPMediaVolume_44C = 142770462;// UINT8
    Set_AMPPhoneVolume_44C = 142803231;// UINT8
    Set_AMPSoundFocus_44C = 142836000;// UINT8
    Set_AMPSoundEffect_44C = 142868769;// UINT8
    Set_AMPSoundEffectBass_44C = 142901538;// UINT8
    Set_AMPSoundEffectMidrange_44C = 142934307;// UINT8
    Set_AMPSoundEffectTreble_44C = 142967076;// UINT8
    Set_AMPSoundFieldBalance_44C = 142999845;// UINT8
    Set_AMPSoundFieldFader_44C = 143032614;// UINT8
    Set_Headrest_Mode_44C = 143065383;// UINT8
    Set_SurndFnOnOff_44C = 143098152;// UINT8
    Set_VirtualSbwfrOnOff_44C = 143130921;// UINT8
    Set_AMPMediaDuck_45F = 143163690;// UINT8
    Set_AMPSpeedVolume_45F = 143196459;// UINT8
    Set_AMPMute_45F = 143229228;// UINT8
    REQ_AMPA2BMediaSound_45F = 143261997;// UINT8
    REQ_AMPA2BNaviSound_45F = 143294766;// UINT8
    REQ_AMPA2BVRSound_45F = 143327535;// UINT8
    REQ_AMPA2BTTSSound_45F = 143360304;// UINT8
    REQ_AMPA2BKeyTone_45F = 143393073;// UINT8
    Set_AMPRestoreDefaults_45F = 143425842;// UINT8
    Set_AMPAlarmVolume_45F = 143458611;// UINT8
    REQ_AMPA2BPhoneSound_45F = 143491380;// UINT8
    Set_AMPSoundEffectMegaBass_45F = 143524149;// UINT8
    Set_AMPSoundEffectMidBass_45F = 143556918;// UINT8
    Set_AMPSoundEffectMidTreble_45F = 143589687;// UINT8
    Set_AMPBackgroundVolume_45F = 143622456;// UINT8
    Set_AMPSoundBypass_45F = 143655225;// UINT8
    Set_AMPA2BConfig_45F = 143687994;// UINT8
    Fusa_47E_3_47E = 805634363;// BOOL
    Fusa_47E_2_47E = 143720764;// UINT8
    Fusa_47E_1_47E = 143753533;// UINT8
    ICC_BD_11_CRC_47E = 143786302;// UINT8
    ICC_BD_11_RollgCntr_47E = 143819071;// UINT8
    ICC_CPDDelaySwitchSet_47E = 143851840;// UINT8
    ICC_PPMIDBSRBPASet_47E = 143884609;// UINT8
    ICC_PPMIDHwSet_47E = 143917378;// UINT8
    ICC_DriveDrosinessLevel_47E = 143950147;// UINT8
    ICC_FLDoorButton_47E = 143982916;// UINT8
    ICC_FRDoorButton_47E = 144015685;// UINT8
    ICC_RRDoorButton_47E = 144048454;// UINT8
    ICC_RLDoorButton_47E = 144081223;// UINT8
    ICC_CloseAllDoors_47E = 144113992;// UINT8
    ICC_DoorRemoteKeyControlEnable_47E = 144146761;// UINT8
    ICC_SetOpenAngle_47E = 144179530;// UINT8
    ICC_CPDSwitchSet_47E = 144212299;// UINT8
    ICC_FrontDoorPowerMode_47E = 144245068;// UINT8
    ICC_RearDoorPowerMode_47E = 144277837;// UINT8
    ICC_PedalCloseDriverDoorEnable_47E = 144310606;// UINT8
    RR_Door_RearScreen_Control_47E = 144343375;// UINT8
    ICC_RearDoorVoiceControlEnable_47E = 144376144;// UINT8
    ICC_FLVoiceControl_47E = 144408913;// UINT8
    ICC_FRVoiceControl_47E = 144441682;// UINT8
    ICC_RRVoiceControl_47E = 144474451;// UINT8
    ICC_RLVoiceControl_47E = 144507220;// UINT8
    RL_Door_RearScreen_Control_47E = 144539989;// UINT8
    ADBenable_395 = 144572758;// UINT8
    RandomMusicShowCMD_395 = 144605527;// UINT8
    DIYMusicShowmod_395 = 144638296;// UINT8
    LiShowMod_395 = 144671065;// UINT8
    MusicLoudness_55HZ_395 = 144703834;// UINT8
    MusicLoudness_123HZ_395 = 144736603;// UINT8
    MusicLoudness_262HZ_395 = 144769372;// UINT8
    MusicLoudness_440HZ_395 = 144802141;// UINT8
    MusicLoudness_587HZ_395 = 144834910;// UINT8
    MusicLoudness_784HZ_395 = 144867679;// UINT8
    MusicLoudness_1318HZ_395 = 144900448;// UINT8
    MusicLoudness_2794HZ_395 = 144933217;// UINT8
    MusicLoudness_6272HZ_395 = 144965986;// UINT8
    ISDShowCMD_395 = 805667171;// BOOL
    ISDShowMode_395 = 144998756;// UINT8
    FaceID_4F9 = 145031525;// UINT8
    Fusa_485_2_485 = 268894566;// UINT16
    Fusa_485_1_485 = 145064295;// UINT8
    ICC_DA_15_CRC_485 = 145097064;// UINT8
    ICC_DA_15_RollgCntr_485 = 145129833;// UINT8
    ICC_DA_15_Resd_485 = 145162602;// UINT8
    ICC_DA_15_BSDLCWSettingSt_485 = 145195371;// UINT8
    ICC_DA_15_DOWSettingSt_485 = 145228140;// UINT8
    ICC_DA_15_RCWSettingSt_485 = 145260909;// UINT8
    ICC_DA_15_RCTARCTBWarnType_485 = 145293678;// UINT8
    ICC_DA_15_FCTAFCTBWarnType_485 = 145326447;// UINT8
    ICC_DA_15_FrontPDCMuteSet_485 = 805699952;// BOOL
    ICC_DA_15_FKPMuteSet_485 = 805732721;// BOOL
    ICC_DA_15_ExtremeEnergySaveMode_485 = 805765490;// BOOL
    Driver_EyeOnRoad_485 = 145359219;// UINT8
    Dms_Status_485 = 145391988;// UINT8
    Driver_AbnormalBehavior_485 = 145424757;// UINT8
    Driver_GazeRegion_485 = 145457526;// UINT8
    Dms_DriverPresence_485 = 145490295;// UINT8
    ICC_IHCfunctionSw_485 = 145523064;// UINT8
    Weather_Type_4FF = 145555833;// UINT8
    ICC_SentinelModeSts_4FF = 145588602;// UINT8
    ICC_CameraErrorCode_4FF = 145621371;// UINT8
    ICC_SentinelRequest_4FF = 145654140;// UINT8
    ICC_StartDeInitCameraStream_4FF = 145686909;// UINT8
    ICC_CameraRecoveryRequest_4FF = 145719678;// UINT8
    ICC_CleanMode_0x4ff_4FF = 145752447;// UINT8
    ICM_5_FuelLevel_284 = 145785216;// UINT8
    FuelShortGround_284 = 805798273;// BOOL
    FuelShortPower_284 = 805831042;// BOOL
    FuelOutOfRange_284 = 805863811;// BOOL
    FuelValidData_284 = 805896580;// BOOL
    IHU_SetSocManage_496 = 145817989;// UINT8
    IHU_SetSOC_496 = 145850758;// UINT8
    CTP_PowerModeSet_496 = 145883527;// UINT8
    LidOpenReq_496 = 805929352;// BOOL
    ICC_ForcedEVMode_496 = 145916297;// UINT8
    ICC_EmissionMode_496 = 805962122;// BOOL
    ICC_ForcedEVChargeMode_496 = 145949067;// UINT8
    EVOdometer_km_496 = 402686348;// UINT32
    EVOdometer_km_ValidData_496 = 805994893;// BOOL
    ICC_SetChrgnFctMem_496 = 145981838;// UINT8
    ICC_DK_19_CRC1_4DA = 146014607;// UINT8
    ICC_DK_19_RollgCntr1_4DA = 146047376;// UINT8
    ICC_DK_19_Resd1_4DA = 146080145;// UINT8
    ICC_DK_19_CMD_4DA = 268927378;// UINT16
    ICC_DK_19_PDU1_4DA = 536871315;// UINT64
    ICC_DK_19_PDU2_4DA = 536904084;// UINT64
    ICC_DK_19_PDU3_4DA = 536936853;// UINT64
    ICC_DK_19_PDU4_4DA = 402719126;// UINT32
    PassSeatHeiAdjmt_Target_4FC = 146112919;// UINT8
    SeatBackAndFwdAdjmt_Target_4FC = 146145688;// UINT8
    SeatBackAgAdjmt_Target_4FC = 146178457;// UINT8
    Set_ProfileID_4FC = 146211226;// UINT8
    SeatCushAgAdjmt_Target_4FC = 146243995;// UINT8
    LgRstFwdAndBckAdjmt_Target_4FC = 146276764;// UINT8
    FootRestAdjmt_Target_4FC = 146309533;// UINT8
    REQ_SeatPosRecall_4FC = 146342302;// UINT8
    REQ_SeatPosStore_4FC = 146375071;// UINT8
    Set_ToDefault_4FC = 146407840;// UINT8
    Set_EasyEntryEnable_4FC = 146440609;// UINT8
    REQ_SeatHeatPass_4FC = 146473378;// UINT8
    REQ_SeatVentPass_4FC = 146506147;// UINT8
    PassSeatMsgStr_LvlCmd_0x4fc_4FC = 146538916;// UINT8
    PassSeatMsg_ModeCmd_0x4fc_4FC = 146571685;// UINT8
    ICC_FLSeatHeatLevelCmd_423 = 146604454;// UINT8
    ICC_FRSeatHeatLevelCmd_423 = 146637223;// UINT8
    ICC_TrunkSW_423 = 146669992;// UINT8
    ICC_Set_PLGOperateSts_423 = 146702761;// UINT8
    ICC_RLSeatHeatLevelCmd_423 = 146735530;// UINT8
    ICC_RRSeatHeatLevelCmd_423 = 146768299;// UINT8
    ICC_FLSeatVentLevelCmd_423 = 146801068;// UINT8
    ICC_FRSeatVentLevelCmd_423 = 146833837;// UINT8
    ICC_RLSeatVentLevelCmd_423 = 146866606;// UINT8
    ICC_RRSeatVentLevelCmd_423 = 146899375;// UINT8
    ICC_FrontWiperMaintenanceMode_423 = 146932144;// UINT8
    ICC_RearWiperMaintenanceMode_423 = 146964913;// UINT8
    ICC_MultiplexSignalStatusSet_451 = 146997682;// UINT8
    ICC_DSBH_HeatReq_420 = 147030451;// UINT8
    ICC_DSBH_SetTemp_420 = 147063220;// UINT8
    ICC_PSBH_HeatReq_420 = 147095989;// UINT8
    ICC_PSBH_SetTemp_420 = 147128758;// UINT8
    PassSeatMsgStr_LvlCmd_0x458_458 = 147161527;// UINT8
    PassSeatMsg_ModeCmd_0x458_458 = 147194296;// UINT8
    DriverSeatMsgStr_Lvlcmd_458 = 147227065;// UINT8
    DriverSeatMsg_ModeCmd_458 = 147259834;// UINT8
    RightSeatMsgStr_LvlCmd_458 = 147292603;// UINT8
    RightSeatMsg_ModeCmd_458 = 147325372;// UINT8
    LeftSeatMsgStr_LvlCmd_458 = 147358141;// UINT8
    LeftSeatMsg_ModeCmd_458 = 147390910;// UINT8
    ICC_PwrOff_4F1 = 806027711;// BOOL
    ICC_EtmEgySave_4F1 = 806060480;// BOOL
    ICC_HoodSW_4F1 = 147423681;// UINT8
    ICC_GloveboxSW_4F1 = 147456450;// UINT8
    ICC_ChrgPortCover_Switch_4F1 = 147489219;// UINT8
    ICC_ReWinDefrstSwt_4F1 = 147521988;// UINT8
    ICC_RearMirrorFoldCmd_4F1 = 147554757;// UINT8
    ICC_CenterLockSwt_4F1 = 147587526;// UINT8
    ICC_ReverseExtMirrorSts_4F1 = 147620295;// UINT8
    ICC_LightMainSwitchSts_4F1 = 147653064;// UINT8
    ICC_AutoFoldSts_4F1 = 147685833;// UINT8
    ICC_HeadlampHeightSts_4F1 = 147718602;// UINT8
    ICC_WiprSnvty_4F1 = 147751371;// UINT8
    ICC_WindowCmd_4F1 = 147784140;// UINT8
    ICC_LowBeamDelayOff_4F1 = 147816909;// UINT8
    ICC_FLWinCmd_4F1 = 147849678;// UINT8
    ICC_FRWinCmd_4F1 = 147882447;// UINT8
    ICC_RLWinCmd_4F1 = 147915216;// UINT8
    ICC_RRWinCmd_4F1 = 147947985;// UINT8
    ICC_FGHeatSts_4F1 = 147980754;// UINT8
    ICC_AutolockSts_4F1 = 148013523;// UINT8
    ICC_WiperID_4F1 = 148046292;// UINT8
    ICC_MaintenanceMode_4F1 = 148079061;// UINT8
    ICC_lockSetSwitchSts_4F1 = 148111830;// UINT8
    ICC_ChildLockSW_4F1 = 148144599;// UINT8
    ICC_ParkUnlockEnable_4F1 = 148177368;// UINT8
    ICC_BLEOpenDriverDoorEnable_4F1 = 148210137;// UINT8
    ICC_WinInhbSwt_4F1 = 148242906;// UINT8
    ICC_SteerWhlHeatgSwt_4F1 = 148275675;// UINT8
    ICC_AutoHeatingset_4F1 = 148308444;// UINT8
    ICC_FrontFogSw_4F1 = 148341213;// UINT8
    ICC_RearFogSw_4F1 = 148373982;// UINT8
    ICC_DoorControlSW_4F1 = 148406751;// UINT8
    ICC_LockCarWinCloseSw_4F1 = 148439520;// UINT8
    ICC_RainCarWinCloseSw_4F1 = 148472289;// UINT8
    ICC_EasyEntryExitSet_4F1 = 148505058;// UINT8
    ICC_NapTimeSet_4F1 = 148537827;// UINT8
    ICC_NapAreaSet_4F1 = 148570596;// UINT8
    ICC_NapStatusSet_4F1 = 148603365;// UINT8
    ICC_NapCFSStatusSet_4F1 = 148636134;// UINT8
    ICC_NapSetTemperature_4F1 = 148668903;// UINT8
    ICC_SpoilerWelcomeFunSet_4F1 = 148701672;// UINT8
    ICC_SpoilerCtrlCmd_4F1 = 148734441;// UINT8
    ICC_HazardLightReq_4F1 = 148767210;// UINT8
    ICC_MusicStatus_4F1 = 148799979;// UINT8
    ICC_CinemaStatus_4F1 = 148832748;// UINT8
    ICC_BookChrgSetReq_471 = 148865517;// UINT8
    ICC_BkChrgStartTimeYear_471 = 268960238;// UINT16
    ICC_BkChrgStartTimeDay_471 = 148898287;// UINT8
    ICC_BkChrgStartTimeHour_471 = 148931056;// UINT8
    ICC_BkChrgStartTimemMin_471 = 148963825;// UINT8
    ICC_BkChrgStartTimeMonth_471 = 148996594;// UINT8
    ICC_BkChrgDuration_471 = 268993011;// UINT16
    ICC_BkChrgCycleType_471 = 149029364;// UINT8
    ICC_StopChrgReq_471 = 149062133;// UINT8
    ICC_BkChrgCycleMon_471 = 149094902;// UINT8
    ICC_BkChrgCycleTues_471 = 149127671;// UINT8
    ICC_BkChrgCycleWen_471 = 149160440;// UINT8
    ICC_BkChrgCycleThur_471 = 149193209;// UINT8
    ICC_BkChrgCycleFri_471 = 149225978;// UINT8
    ICC_BkChrgCycleSat_471 = 149258747;// UINT8
    ICC_BkChrgCycleSun_471 = 149291516;// UINT8
    ICC_KeepWarmStrt_471 = 149324285;// UINT8
    ICC_KeepWarmStrtHour_471 = 149357054;// UINT8
    ICC_KeepWarmStrtMin_471 = 149389823;// UINT8
    ICC_PetmodeFb_471 = 149422592;// UINT8
    ICC_SentinelModeSwitchSts_471 = 149455361;// UINT8
    ICC_SentinelModeWorkingSts_471 = 149488130;// UINT8
    ICC_SentinelModeFaultSts_471 = 149520899;// UINT8
    ICC_SentinelModeAlarmSts_471 = 149553668;// UINT8
    ICC_SentinelModeExitReason_471 = 149586437;// UINT8
    ICC_PetModeWarn_471 = 149619206;// UINT8
    ICC_TBOXPetmodeReq_471 = 149651975;// UINT8
    ICC_PetModeWarn_2_471 = 149684744;// UINT8
    ICM_FuelLevel_471 = 149717513;// UINT8
    ICM_AverageVehicleSpeed_471 = 149750282;// UINT8
    ICM_DistenceToEmpty_Km_471 = 269025803;// UINT16
    ICM_FuelLevelFailSts_471 = 806093324;// BOOL
    ICM_Maintenance_tips_471 = 806126093;// BOOL
    ICM_SumTrip_471 = 269058574;// UINT16
    ICC_HvBattKeepWarmSet_478 = 149783055;// UINT8
    ICC_EnergyRegSet_478 = 149815824;// UINT8
    ICC_HvBattKeepWarmActiveReq_478 = 149848593;// UINT8
    ICC_FLSeatHeiCmd_4BA = 149881362;// UINT8
    ICC_FLSeatLvlCmd_4BA = 149914131;// UINT8
    ICC_FLSeatBackAgCmd_4BA = 149946900;// UINT8
    ICC_FLSeatCushCmd_4BA = 149979669;// UINT8
    ICC_FLSeatLegSpprtCmd_4BA = 150012438;// UINT8
    ICC_FRSeatHeiCmd_4BA = 150045207;// UINT8
    ICC_FRSeatLvlCmd_4BA = 150077976;// UINT8
    ICC_FRSeatBackAgCmd_4BA = 150110745;// UINT8
    ICC_FRSeatLegSpprtCmd_4BA = 150143514;// UINT8
    ICC_MemoryRecoveryCmd_4BA = 150176283;// UINT8
    ICC_DeleteFaceID_4BA = 150209052;// UINT8
    ICC_FLSeatHeitargetCmd_4BA = 150241821;// UINT8
    ICC_FLSeatLvltargetCmd_4BA = 150274590;// UINT8
    ICC_FLSeatBackAgtargetCmd_4BA = 150307359;// UINT8
    ICC_FLSeatCushtargetCmd_4BA = 150340128;// UINT8
    ICC_FLSeatLegSpprttargetCmd_4BA = 150372897;// UINT8
    ICC_FRSeatHeitargetCmd_4BA = 150405666;// UINT8
    ICC_FRSeatLvltargetCmd_4BA = 150438435;// UINT8
    ICC_FRSeatBackAgtargetCmd_4BA = 150471204;// UINT8
    ICC_FRSeatLegSpprttargetCmd_4BA = 150503973;// UINT8
    ICC_NapFLSeatBackAgtargetSet_4BA = 150536742;// UINT8
    ICC_NapFRSeatBackAgtargetSet_4BA = 150569511;// UINT8
    ICC_NapFLSeatLvltargetSet_4BA = 150602280;// UINT8
    ICC_NapFRSeatLvltargetSet_4BA = 150635049;// UINT8
    ICC_FRSeatLegRestAngleCmd_4BA = 150667818;// UINT8
    ICC_FRSeatLegRestAngletargetCmd_4BA = 150700587;// UINT8
    ICC_FRSeatCushCmd_4BA = 150733356;// UINT8
    ICC_FRSeatCushtargetCmd_4BA = 150766125;// UINT8
    ICC_FRZeroGravitySeatSw_4BA = 150798894;// UINT8
    ICC_HomeLinkArmHornSet_4BA = 150831663;// UINT8
    ICC_HomeLinkWelLightSet_4BA = 150864432;// UINT8
    ICC_HomeLinkPositionSts_4BA = 150897201;// UINT8
    ICC_WelcomeOpenSetCmd_4BA = 150929970;// UINT8
    ICC_WALOpenSetCmd_4BA = 150962739;// UINT8
    ICC_UIROpenSetCmd_4BA = 150995508;// UINT8
    ICC_EnjoyableSeatSwitch_4BA = 151028277;// UINT8
    ICC_BLTKeyPESet_4BA = 151061046;// UINT8
    ICC_FRMemoryDeleteCmd_4BA = 151093815;// UINT8
    ICC_FLSitPosnlocation_4BA = 151126584;// UINT8
    ICC_FRSitPosnlocation_4BA = 151159353;// UINT8
    ICC_FREasyEntryExitSet_4BA = 151192122;// UINT8
    ICC_FRMemoryRecoveryCmd_4BA = 151224891;// UINT8
    ICC_ConsoleAdjustSwitch_4BA = 151257660;// UINT8
    ICC_CLTC_RangeAval_577 = 269091389;// UINT16
    ICC_DynamicRangeAval_577 = 269124158;// UINT16
    ICC_WLTC_RangeAval_577 = 269156927;// UINT16
    ICC_CLTC_RangeAvalFuel_577 = 269189696;// UINT16
    ICC_WLTC_RangeAvalFuel_577 = 269222465;// UINT16
    ICC_DynamicRangeAvalFuel_577 = 269255234;// UINT16
    ICC_CLTC_RangeAvalComp_577 = 269288003;// UINT16
    ICC_WLTC_RangeAvalComp_577 = 269320772;// UINT16
    ICC_DynamicRangeAvalComp_577 = 269353541;// UINT16
    ICC_Conditiontype_577 = 151290438;// UINT8
    ICC_CFSSwitch_4D6 = 151323207;// UINT8
    ICC_CFSLevelSet_4D6 = 151355976;// UINT8
    ICC_CFSPosSet_4D6 = 151388745;// UINT8
    ICC_PM25Switch_4D6 = 151421514;// UINT8
    ICC_AQSSwitch_4D6 = 151454283;// UINT8
    ICC_SecSetTemp_C_4D6 = 151487052;// UINT8
    ICC_SecFaceKeySts_4D6 = 151519821;// UINT8
    ICC_SecFootKeySts_4D6 = 151552590;// UINT8
    ICC_SecBlowSpeedLevelKeySts_4D6 = 151585359;// UINT8
    ICC_SecAutoSwitch_4D6 = 151618128;// UINT8
    ICC_SecCLMSwitch_4D6 = 151650897;// UINT8
    ICC_ThrSetTemp_C_4D6 = 151683666;// UINT8
    ICC_ThrFaceKeySts_4D6 = 151716435;// UINT8
    ICC_ThrFootKeySts_4D6 = 151749204;// UINT8
    ICC_ThrBlowSpeedLevelKeySts_4D6 = 151781973;// UINT8
    ICC_ThrAutoSwitch_4D6 = 151814742;// UINT8
    ICC_ThrCLMSwitch_4D6 = 151847511;// UINT8
    ICC_ThrACRequestKeySts_4D6 = 151880280;// UINT8
    ICC_DFMSwitch_4D6 = 151913049;// UINT8
    ICC_TirePressureDisplayUnit_509 = 151945818;// UINT8
    ICC_TireTemperatureDisplayUnit_509 = 151978587;// UINT8
    ICC_BtConFlag_509 = 152011356;// UINT8
    ICC_ToggleUnits_509 = 152044125;// UINT8
    ICC_KeyDriveModememory_48F = 152076894;// UINT8
    ICC_DriveModeSet_48F = 152109663;// UINT8
    ICC_DRIFTModeSetReq_48F = 152142432;// UINT8
    ICC_EXPERTEIPBESPSet_48F = 152175201;// UINT8
    ICC_EXPERTEcoSet_48F = 152207970;// UINT8
    ICC_EXPERTNormSet_48F = 152240739;// UINT8
    ICC_EXPERTSportSet_48F = 152273508;// UINT8
    ICC_CrossaxisSet_48F = 152306277;// UINT8
    ICC_EXPERTRWDSet_48F = 152339046;// UINT8
    ICC_EXPERTAWDSet_48F = 152371815;// UINT8
    ICC_EXPERTAutoSet_48F = 152404584;// UINT8
    ICC_CLIMBSet_48F = 152437353;// UINT8
    ICC_DriveModeExitSet_48F = 152470122;// UINT8
    ICC_XDriveMode_48F = 152502891;// UINT8
    ICC_Confidencelevel_48F = 152535660;// UINT8
    ICC_V2L_IntlDisChg_48F = 152568429;// UINT8
    ICC_ExhibitionModeSwitch_48F = 152601198;// UINT8
    ICC_TrailerMode_48F = 152633967;// UINT8
    ICC_V2L_DisChgMem_48F = 152666736;// UINT8
    ICC_DrivePowerDispSet_48F = 152699505;// UINT8
    ICC_WLTC_to_CLTC_Mode_48F = 152732274;// UINT8
    ICC_StopChrgnSwitch_48F = 152765043;// UINT8
    ICC_StopChrgnMode_48F = 152797812;// UINT8
    ICC_RegenerateLevelCtrl_48F = 152830581;// UINT8
    ICC_SinglePedalMem_48F = 152863350;// UINT8
    ICC_FuelDetnSwt_48F = 152896119;// UINT8
    ICC_FuelDetnState_48F = 152928888;// UINT8
    ICC_FuelDetnOpDefeated_48F = 152961657;// UINT8
    ICC_HVDownRepairMode_48F = 152994426;// UINT8
    ICC_UTURNSnowSet_48F = 153027195;// UINT8
    ICC_UTURNSandSet_48F = 153059964;// UINT8
    ICC_UTURNMudSet_48F = 153092733;// UINT8
    ICC_UTURNGrassSet_48F = 153125502;// UINT8
    ICC_LTCDispSet2_48F = 153158271;// UINT8
    ICC_SetSocManage_48F = 153191040;// UINT8
    ICC_DWDOnOff_Req_52E = 153223809;// UINT8
    ICC_ContainerLightSet_52E = 153256578;// UINT8
    ICC_RLSeatEasyEntryExitSet_533 = 153289347;// UINT8
    ICC_RRSeatEasyEntryExitSet_533 = 153322116;// UINT8
    ICC_RLMemoryRecoveryCmd_533 = 153354885;// UINT8
    ICC_RRMemoryRecoveryCmd_533 = 153387654;// UINT8
    ICC_RLSitPosnlocation_533 = 153420423;// UINT8
    ICC_RRSitPosnlocation_533 = 153453192;// UINT8
    ICC_CMSMDisplaySwitch_533 = 153485961;// UINT8
    ICC_CMSMDisplayAdjustmode_533 = 153518730;// UINT8
    ICC_CMSMSetstandardSwitch_533 = 153551499;// UINT8
    ICC_LeftPedalControl_533 = 153584268;// UINT8
    ICC_RightPedalControl_533 = 153617037;// UINT8
    ICC_EPCEnable_533 = 153649806;// UINT8
    ICC_AMPKTVvoiceVolumeSet_533 = 153682575;// UINT8
    ICC_AMPA2BENCSoundReq_533 = 153715344;// UINT8
    ICC_AMPENCVolumeSet_533 = 153748113;// UINT8
    ICC_AMPA2BChimeSoundReq_533 = 153780882;// UINT8
    Set_AMPSoundFocus_2_533 = 153813651;// UINT8
    ICC_AMPA2BKTVvoiceSoundReq_533 = 153846420;// UINT8
    ICC_MedStatusSet_533 = 153879189;// UINT8
    ICC_MedAreaSet_533 = 153911958;// UINT8
    ICC_LeftElectricPedalsSet_533 = 153944727;// UINT8
    ICC_RightElectricPedalsSet_533 = 153977496;// UINT8
    ICC_NapAreaSet2_533 = 154010265;// UINT8
    ICC_DisplayAdjustrAngReq_533 = 154043034;// UINT8
    ICC_BedStatus_533 = 154075803;// UINT8
    ICC_SRFCmd_535 = 154108572;// UINT8
    ICC_SRFPercentCmd_535 = 154141341;// UINT8
    ICC_CSunshadeReq_535 = 154174110;// UINT8
    ICC_CSunshadePercentReq_535 = 154206879;// UINT8
    ICC_SunshadeCmd_535 = 154239648;// UINT8
    ICC_SunshadePercentCmd_535 = 154272417;// UINT8
    ICC_SET_EPS_SteerReturnRmdSts_537 = 154305186;// UINT8
    ICC_SET_MFSShake_537 = 154337955;// UINT8
    ICC_DMS_1_DistractionLevel_537 = 154370724;// UINT8
    ICC_OverLoadWarnShieldSet_537 = 154403493;// UINT8
    ICC_BSWReq_537 = 154436262;// UINT8
    ICC_horizontalReq_537 = 154469031;// UINT8
    ICC_SET_BT_ReduceWindSpeed_539 = 154501800;// UINT8
    ICC_SET_IPM_FirstBlowing_539 = 154534569;// UINT8
    ICC_SET_IPM_BlowerDelay_539 = 154567338;// UINT8
    ICC_SET_CirculationInTunnels_539 = 154600107;// UINT8
    ICC_UVCLuminanceReq_539 = 154632876;// UINT8
    ICC_UVCControlReq_539 = 154665645;// UINT8
    ICC_CrossCountryCoolingReq_539 = 154698414;// UINT8
    ICC_KeepWarmReq_539 = 154731183;// UINT8
    ICC_keepwarmSetTemperatureReq_539 = 154763952;// UINT8
    ICC_ParkingAirConditioning_539 = 154796721;// UINT8
    ICC_CoolantFill_Req_539 = 154829490;// UINT8
    ICC_FraganceLightoffReq_539 = 154862259;// UINT8
    ICC_FraganceMemoffReq_539 = 154895028;// UINT8
    ICC_AirPurgeReminderReq_539 = 154927797;// UINT8
    ICC_SET_TDL_Rhythm_Switch_526 = 154960566;// UINT8
    ICC_SET_TDL_Switch_526 = 154993335;// UINT8
    ICC_SET_TDL_ColourModeAdj_526 = 155026104;// UINT8
    ICC_VoiceWake_526 = 155058873;// UINT8
    ICC_TDL_RhythmReq_526 = 155091642;// UINT8
    ICC_SET_ApiluminanceAdj_526 = 155124411;// UINT8
    ICC_SET_MusicLoudness_120HZ_526 = 155157180;// UINT8
    ICC_SET_MusicLoudness_250HZ_526 = 155189949;// UINT8
    ICC_SET_MusicLoudness_500HZ_526 = 155222718;// UINT8
    ICC_SET_MusicLoudness_1000HZ_526 = 155255487;// UINT8
    ICC_SET_MusicLoudness_1500HZ_526 = 155288256;// UINT8
    ICC_SET_MusicLoudness_2000HZ_526 = 155321025;// UINT8
    ICC_SET_MusicLoudness_6000HZ_526 = 155353794;// UINT8
    ICC_TDL_FlowLightSw_526 = 155386563;// UINT8
    ICC_TDL_FlowLightModeAdj_526 = 155419332;// UINT8
    ICC_SET_256ColourAdj_526 = 269386437;// UINT16
    ICC_Chb_Req_528 = 155452102;// UINT8
    ICC_ChbMem_Req_528 = 155484871;// UINT8
    ICC_ChbDelay_Req_528 = 155517640;// UINT8
    ICC_ChbSterilization_Req_528 = 155550409;// UINT8
    ICC_ChbItemsLeft_Req_528 = 155583178;// UINT8
    ICC_ParkingRadarSwSet_528 = 155615947;// UINT8
    ICC_ChbTimeset_Req_528 = 155648716;// UINT8
    ICC_ChbCoolset_Req_528 = 155681485;// UINT8
    ICC_ChbCoolorheat_Req_528 = 155714254;// UINT8
    ICC_ChbHeatset_Req_528 = 155747023;// UINT8
    ICC_LISD_DisplaySwitch_550 = 155779792;// UINT8
    ICC_LISD_ParkingShowCMD_550 = 155812561;// UINT8
    ICC_RoofCampLampSetL_550 = 155845330;// UINT8
    ICC_RISD_DisplaySwitch_550 = 155878099;// UINT8
    ICC_RISD_ParkingShowCMD_550 = 155910868;// UINT8
    ICC_RoofCampLampSetR_550 = 155943637;// UINT8
    ICC_LogoParkingReq_550 = 155976406;// UINT8
    ICC_LogoChargingReq_550 = 156009175;// UINT8
    ICC_DRLSw_550 = 156041944;// UINT8
    ICC_TopLightSet_550 = 156074713;// UINT8
    ICC_APillarYellowAmbientSet_550 = 156107482;// UINT8
    ICC_APillarWhiteAmbientSet_550 = 156140251;// UINT8
    ICC_LISD_ParkingShowMod_550 = 156173020;// UINT8
    ICC_RISD_ParkingShowMod_550 = 156205789;// UINT8
    ICC_APillarSpotLampSet_550 = 156238558;// UINT8
    ICC_PenetrationLampSetF_550 = 156271327;// UINT8
    ICC_MusicLampShowSW_550 = 156304096;// UINT8
    ICC_LogoColorReq_550 = 269419233;// UINT16
    ICC_LISD_FrameCounter_552 = 156336866;// UINT8
    ICC_LISD_RollingCounter_552 = 156369635;// UINT8
    ICC_LISD_Pixel1_OnOff_552 = 806159076;// BOOL
    ICC_LISD_Pixel2_OnOff_552 = 806191845;// BOOL
    ICC_LISD_Pixel3_OnOff_552 = 806224614;// BOOL
    ICC_LISD_Pixel4_OnOff_552 = 806257383;// BOOL
    ICC_LISD_Pixel5_OnOff_552 = 806290152;// BOOL
    ICC_LISD_Pixel6_OnOff_552 = 806322921;// BOOL
    ICC_LISD_Pixel7_OnOff_552 = 806355690;// BOOL
    ICC_LISD_Pixel8_OnOff_552 = 806388459;// BOOL
    ICC_LISD_Pixel9_OnOff_552 = 806421228;// BOOL
    ICC_LISD_Pixel10_OnOff_552 = 806453997;// BOOL
    ICC_LISD_Pixel11_OnOff_552 = 806486766;// BOOL
    ICC_LISD_Pixel12_OnOff_552 = 806519535;// BOOL
    ICC_LISD_Pixel13_OnOff_552 = 806552304;// BOOL
    ICC_LISD_Pixel14_OnOff_552 = 806585073;// BOOL
    ICC_LISD_Pixel15_OnOff_552 = 806617842;// BOOL
    ICC_LISD_Pixel16_OnOff_552 = 806650611;// BOOL
    ICC_LISD_Pixel17_OnOff_552 = 806683380;// BOOL
    ICC_LISD_Pixel18_OnOff_552 = 806716149;// BOOL
    ICC_LISD_Pixel19_OnOff_552 = 806748918;// BOOL
    ICC_LISD_Pixel20_OnOff_552 = 806781687;// BOOL
    ICC_LISD_Pixel21_OnOff_552 = 806814456;// BOOL
    ICC_LISD_Pixel22_OnOff_552 = 806847225;// BOOL
    ICC_LISD_Pixel23_OnOff_552 = 806879994;// BOOL
    ICC_LISD_Pixel24_OnOff_552 = 806912763;// BOOL
    ICC_LISD_Pixel25_OnOff_552 = 806945532;// BOOL
    ICC_LISD_Pixel26_OnOff_552 = 806978301;// BOOL
    ICC_LISD_Pixel27_OnOff_552 = 807011070;// BOOL
    ICC_LISD_Pixel28_OnOff_552 = 807043839;// BOOL
    ICC_LISD_Pixel29_OnOff_552 = 807076608;// BOOL
    ICC_LISD_Pixel30_OnOff_552 = 807109377;// BOOL
    ICC_LISD_Pixel31_OnOff_552 = 807142146;// BOOL
    ICC_LISD_Pixel32_OnOff_552 = 807174915;// BOOL
    ICC_LISD_Pixel33_OnOff_552 = 807207684;// BOOL
    ICC_LISD_Pixel34_OnOff_552 = 807240453;// BOOL
    ICC_LISD_Pixel35_OnOff_552 = 807273222;// BOOL
    ICC_LISD_Pixel36_OnOff_552 = 807305991;// BOOL
    ICC_LISD_Pixel37_OnOff_552 = 807338760;// BOOL
    ICC_LISD_Pixel38_OnOff_552 = 807371529;// BOOL
    ICC_LISD_Pixel39_OnOff_552 = 807404298;// BOOL
    ICC_LISD_Pixel40_OnOff_552 = 807437067;// BOOL
    ICC_LISD_Pixel41_OnOff_552 = 807469836;// BOOL
    ICC_LISD_Pixel42_OnOff_552 = 807502605;// BOOL
    ICC_LISD_Pixel43_OnOff_552 = 807535374;// BOOL
    ICC_LISD_Pixel44_OnOff_552 = 807568143;// BOOL
    ICC_LISD_Pixel45_OnOff_552 = 807600912;// BOOL
    ICC_LISD_Pixel46_OnOff_552 = 807633681;// BOOL
    ICC_LISD_Pixel47_OnOff_552 = 807666450;// BOOL
    ICC_LISD_Pixel48_OnOff_552 = 807699219;// BOOL
    ICC_LISD_Pixel49_OnOff_552 = 807731988;// BOOL
    ICC_LISD_Pixel50_OnOff_552 = 807764757;// BOOL
    ICC_LISD_Pixel51_OnOff_552 = 807797526;// BOOL
    ICC_LISD_Pixel52_OnOff_552 = 807830295;// BOOL
    ICC_LISD_Pixel53_OnOff_552 = 807863064;// BOOL
    ICC_LISD_Pixel54_OnOff_552 = 807895833;// BOOL
    ICC_LISD_Pixel55_OnOff_552 = 807928602;// BOOL
    ICC_LISD_Pixel56_OnOff_552 = 807961371;// BOOL
    ICC_LISD_Pixel57_OnOff_552 = 807994140;// BOOL
    ICC_LISD_Pixel58_OnOff_552 = 808026909;// BOOL
    ICC_LISD_Pixel59_OnOff_552 = 808059678;// BOOL
    ICC_LISD_Pixel60_OnOff_552 = 808092447;// BOOL
    ICC_LISD_Pixel61_OnOff_552 = 808125216;// BOOL
    ICC_LISD_Pixel62_OnOff_552 = 808157985;// BOOL
    ICC_LISD_Pixel63_OnOff_552 = 808190754;// BOOL
    ICC_LISD_Pixel64_OnOff_552 = 808223523;// BOOL
    ICC_LISD_Pixel65_OnOff_552 = 808256292;// BOOL
    ICC_LISD_Pixel66_OnOff_552 = 808289061;// BOOL
    ICC_LISD_Pixel67_OnOff_552 = 808321830;// BOOL
    ICC_LISD_Pixel68_OnOff_552 = 808354599;// BOOL
    ICC_LISD_Pixel69_OnOff_552 = 808387368;// BOOL
    ICC_LISD_Pixel70_OnOff_552 = 808420137;// BOOL
    ICC_LISD_Pixel71_OnOff_552 = 808452906;// BOOL
    ICC_LISD_Pixel72_OnOff_552 = 808485675;// BOOL
    ICC_LISD_Pixel73_OnOff_552 = 808518444;// BOOL
    ICC_LISD_Pixel74_OnOff_552 = 808551213;// BOOL
    ICC_LISD_Pixel75_OnOff_552 = 808583982;// BOOL
    ICC_LISD_Pixel76_OnOff_552 = 808616751;// BOOL
    ICC_LISD_Pixel77_OnOff_552 = 808649520;// BOOL
    ICC_LISD_Pixel78_OnOff_552 = 808682289;// BOOL
    ICC_LISD_Pixel79_OnOff_552 = 808715058;// BOOL
    ICC_LISD_Pixel80_OnOff_552 = 808747827;// BOOL
    ICC_LISD_Pixel81_OnOff_552 = 808780596;// BOOL
    ICC_LISD_Pixel82_OnOff_552 = 808813365;// BOOL
    ICC_LISD_Pixel83_OnOff_552 = 808846134;// BOOL
    ICC_LISD_Pixel84_OnOff_552 = 808878903;// BOOL
    ICC_LISD_Pixel85_OnOff_552 = 808911672;// BOOL
    ICC_LISD_Pixel86_OnOff_552 = 808944441;// BOOL
    ICC_LISD_Pixel87_OnOff_552 = 808977210;// BOOL
    ICC_LISD_Pixel88_OnOff_552 = 809009979;// BOOL
    ICC_LISD_Pixel89_OnOff_552 = 809042748;// BOOL
    ICC_LISD_Pixel90_OnOff_552 = 809075517;// BOOL
    ICC_LISD_Pixel91_OnOff_552 = 809108286;// BOOL
    ICC_LISD_Pixel92_OnOff_552 = 809141055;// BOOL
    ICC_LISD_Pixel93_OnOff_552 = 809173824;// BOOL
    ICC_LISD_Pixel94_OnOff_552 = 809206593;// BOOL
    ICC_LISD_Pixel95_OnOff_552 = 809239362;// BOOL
    ICC_LISD_Pixel96_OnOff_552 = 809272131;// BOOL
    ICC_LISD_Pixel97_OnOff_552 = 809304900;// BOOL
    ICC_LISD_Pixel98_OnOff_552 = 809337669;// BOOL
    ICC_LISD_Pixel99_OnOff_552 = 809370438;// BOOL
    ICC_LISD_Pixel100_OnOff_552 = 809403207;// BOOL
    ICC_LISD_Pixel101_OnOff_552 = 809435976;// BOOL
    ICC_LISD_Pixel102_OnOff_552 = 809468745;// BOOL
    ICC_LISD_Pixel103_OnOff_552 = 809501514;// BOOL
    ICC_LISD_Pixel104_OnOff_552 = 809534283;// BOOL
    ICC_LISD_Pixel105_OnOff_552 = 809567052;// BOOL
    ICC_LISD_Pixel106_OnOff_552 = 809599821;// BOOL
    ICC_LISD_Pixel107_OnOff_552 = 809632590;// BOOL
    ICC_LISD_Pixel108_OnOff_552 = 809665359;// BOOL
    ICC_LISD_Pixel109_OnOff_552 = 809698128;// BOOL
    ICC_LISD_Pixel110_OnOff_552 = 809730897;// BOOL
    ICC_LISD_Pixel111_OnOff_552 = 809763666;// BOOL
    ICC_LISD_Pixel112_OnOff_552 = 809796435;// BOOL
    ICC_LISD_Pixel113_OnOff_552 = 809829204;// BOOL
    ICC_LISD_Pixel114_OnOff_552 = 809861973;// BOOL
    ICC_LISD_Pixel115_OnOff_552 = 809894742;// BOOL
    ICC_LISD_Pixel116_OnOff_552 = 809927511;// BOOL
    ICC_LISD_Pixel117_OnOff_552 = 809960280;// BOOL
    ICC_LISD_Pixel118_OnOff_552 = 809993049;// BOOL
    ICC_LISD_Pixel119_OnOff_552 = 810025818;// BOOL
    ICC_LISD_Pixel120_OnOff_552 = 810058587;// BOOL
    ICC_LISD_Pixel121_OnOff_552 = 810091356;// BOOL
    ICC_LISD_Pixel122_OnOff_552 = 810124125;// BOOL
    ICC_LISD_Pixel123_OnOff_552 = 810156894;// BOOL
    ICC_LISD_Pixel124_OnOff_552 = 810189663;// BOOL
    ICC_LISD_Pixel125_OnOff_552 = 810222432;// BOOL
    ICC_LISD_Pixel126_OnOff_552 = 810255201;// BOOL
    ICC_LISD_Pixel127_OnOff_552 = 810287970;// BOOL
    ICC_LISD_Pixel128_OnOff_552 = 810320739;// BOOL
    ICC_LISD_Pixel129_OnOff_552 = 810353508;// BOOL
    ICC_LISD_Pixel130_OnOff_552 = 810386277;// BOOL
    ICC_LISD_Pixel131_OnOff_552 = 810419046;// BOOL
    ICC_LISD_Pixel132_OnOff_552 = 810451815;// BOOL
    ICC_LISD_Pixel133_OnOff_552 = 810484584;// BOOL
    ICC_LISD_Pixel134_OnOff_552 = 810517353;// BOOL
    ICC_LISD_Pixel135_OnOff_552 = 810550122;// BOOL
    ICC_LISD_Pixel136_OnOff_552 = 810582891;// BOOL
    ICC_LISD_Pixel137_OnOff_552 = 810615660;// BOOL
    ICC_LISD_Pixel138_OnOff_552 = 810648429;// BOOL
    ICC_LISD_Pixel139_OnOff_552 = 810681198;// BOOL
    ICC_LISD_Pixel140_OnOff_552 = 810713967;// BOOL
    ICC_LISD_Pixel141_OnOff_552 = 810746736;// BOOL
    ICC_LISD_Pixel142_OnOff_552 = 810779505;// BOOL
    ICC_LISD_Pixel143_OnOff_552 = 810812274;// BOOL
    ICC_LISD_Pixel144_OnOff_552 = 810845043;// BOOL
    ICC_LISD_Pixel145_OnOff_552 = 810877812;// BOOL
    ICC_LISD_Pixel146_OnOff_552 = 810910581;// BOOL
    ICC_LISD_Pixel147_OnOff_552 = 810943350;// BOOL
    ICC_LISD_Pixel148_OnOff_552 = 810976119;// BOOL
    ICC_LISD_Pixel149_OnOff_552 = 811008888;// BOOL
    ICC_LISD_Pixel150_OnOff_552 = 811041657;// BOOL
    ICC_LISD_Pixel151_OnOff_552 = 811074426;// BOOL
    ICC_LISD_Pixel152_OnOff_552 = 811107195;// BOOL
    ICC_LISD_Pixel153_OnOff_552 = 811139964;// BOOL
    ICC_LISD_Pixel154_OnOff_552 = 811172733;// BOOL
    ICC_LISD_Pixel155_OnOff_552 = 811205502;// BOOL
    ICC_LISD_Pixel156_OnOff_552 = 811238271;// BOOL
    ICC_LISD_Pixel157_OnOff_552 = 811271040;// BOOL
    ICC_LISD_Pixel158_OnOff_552 = 811303809;// BOOL
    ICC_LISD_Pixel159_OnOff_552 = 811336578;// BOOL
    ICC_LISD_Pixel160_OnOff_552 = 811369347;// BOOL
    ICC_LISD_Pixel161_OnOff_552 = 811402116;// BOOL
    ICC_LISD_Pixel162_OnOff_552 = 811434885;// BOOL
    ICC_LISD_Pixel163_OnOff_552 = 811467654;// BOOL
    ICC_LISD_Pixel164_OnOff_552 = 811500423;// BOOL
    ICC_LISD_Pixel165_OnOff_552 = 811533192;// BOOL
    ICC_LISD_Pixel166_OnOff_552 = 811565961;// BOOL
    ICC_LISD_Pixel167_OnOff_552 = 811598730;// BOOL
    ICC_LISD_Pixel168_OnOff_552 = 811631499;// BOOL
    ICC_LISD_Pixel169_OnOff_552 = 811664268;// BOOL
    ICC_LISD_Pixel170_OnOff_552 = 811697037;// BOOL
    ICC_LISD_Pixel171_OnOff_552 = 811729806;// BOOL
    ICC_LISD_Pixel172_OnOff_552 = 811762575;// BOOL
    ICC_LISD_Pixel173_OnOff_552 = 811795344;// BOOL
    ICC_LISD_Pixel174_OnOff_552 = 811828113;// BOOL
    ICC_LISD_Pixel175_OnOff_552 = 811860882;// BOOL
    ICC_LISD_Pixel176_OnOff_552 = 811893651;// BOOL
    ICC_LISD_Pixel177_OnOff_552 = 811926420;// BOOL
    ICC_LISD_Pixel178_OnOff_552 = 811959189;// BOOL
    ICC_LISD_Pixel179_OnOff_552 = 811991958;// BOOL
    ICC_LISD_Pixel180_OnOff_552 = 812024727;// BOOL
    ICC_LISD_Pixel181_OnOff_552 = 812057496;// BOOL
    ICC_LISD_Pixel182_OnOff_552 = 812090265;// BOOL
    ICC_LISD_Pixel183_OnOff_552 = 812123034;// BOOL
    ICC_LISD_Pixel184_OnOff_552 = 812155803;// BOOL
    ICC_LISD_Pixel185_OnOff_552 = 812188572;// BOOL
    ICC_LISD_Pixel186_OnOff_552 = 812221341;// BOOL
    ICC_LISD_Pixel187_OnOff_552 = 812254110;// BOOL
    ICC_LISD_Pixel188_OnOff_552 = 812286879;// BOOL
    ICC_LISD_Pixel189_OnOff_552 = 812319648;// BOOL
    ICC_LISD_Pixel190_OnOff_552 = 812352417;// BOOL
    ICC_LISD_Pixel191_OnOff_552 = 812385186;// BOOL
    ICC_LISD_Pixel192_OnOff_552 = 812417955;// BOOL
    ICC_LISD_Pixel193_OnOff_552 = 812450724;// BOOL
    ICC_LISD_Pixel194_OnOff_552 = 812483493;// BOOL
    ICC_LISD_Pixel195_OnOff_552 = 812516262;// BOOL
    ICC_LISD_Pixel196_OnOff_552 = 812549031;// BOOL
    ICC_LISD_Pixel197_OnOff_552 = 812581800;// BOOL
    ICC_LISD_Pixel198_OnOff_552 = 812614569;// BOOL
    ICC_LISD_Pixel199_OnOff_552 = 812647338;// BOOL
    ICC_LISD_Pixel200_OnOff_552 = 812680107;// BOOL
    ICC_LISD_Pixel201_OnOff_552 = 812712876;// BOOL
    ICC_LISD_Pixel202_OnOff_552 = 812745645;// BOOL
    ICC_LISD_Pixel203_OnOff_552 = 812778414;// BOOL
    ICC_LISD_Pixel204_OnOff_552 = 812811183;// BOOL
    ICC_LISD_Pixel205_OnOff_552 = 812843952;// BOOL
    ICC_LISD_Pixel206_OnOff_552 = 812876721;// BOOL
    ICC_LISD_Pixel207_OnOff_552 = 812909490;// BOOL
    ICC_LISD_Pixel208_OnOff_552 = 812942259;// BOOL
    ICC_LISD_Pixel209_OnOff_552 = 812975028;// BOOL
    ICC_LISD_Pixel210_OnOff_552 = 813007797;// BOOL
    ICC_LISD_Pixel211_OnOff_552 = 813040566;// BOOL
    ICC_LISD_Pixel212_OnOff_552 = 813073335;// BOOL
    ICC_LISD_Pixel213_OnOff_552 = 813106104;// BOOL
    ICC_LISD_Pixel214_OnOff_552 = 813138873;// BOOL
    ICC_LISD_Pixel215_OnOff_552 = 813171642;// BOOL
    ICC_LISD_Pixel216_OnOff_552 = 813204411;// BOOL
    ICC_LISD_Pixel217_OnOff_552 = 813237180;// BOOL
    ICC_LISD_Pixel218_OnOff_552 = 813269949;// BOOL
    ICC_LISD_Pixel219_OnOff_552 = 813302718;// BOOL
    ICC_LISD_Pixel220_OnOff_552 = 813335487;// BOOL
    ICC_LISD_Pixel221_OnOff_552 = 813368256;// BOOL
    ICC_LISD_Pixel222_OnOff_552 = 813401025;// BOOL
    ICC_LISD_Pixel223_OnOff_552 = 813433794;// BOOL
    ICC_LISD_Pixel224_OnOff_552 = 813466563;// BOOL
    ICC_LISD_Pixel225_OnOff_552 = 813499332;// BOOL
    ICC_LISD_Pixel226_OnOff_552 = 813532101;// BOOL
    ICC_LISD_Pixel227_OnOff_552 = 813564870;// BOOL
    ICC_LISD_Pixel228_OnOff_552 = 813597639;// BOOL
    ICC_LISD_Pixel229_OnOff_552 = 813630408;// BOOL
    ICC_LISD_Pixel230_OnOff_552 = 813663177;// BOOL
    ICC_LISD_Pixel231_OnOff_552 = 813695946;// BOOL
    ICC_LISD_Pixel232_OnOff_552 = 813728715;// BOOL
    ICC_LISD_Pixel233_OnOff_552 = 813761484;// BOOL
    ICC_LISD_Pixel234_OnOff_552 = 813794253;// BOOL
    ICC_LISD_Pixel235_OnOff_552 = 813827022;// BOOL
    ICC_LISD_Pixel236_OnOff_552 = 813859791;// BOOL
    ICC_LISD_Pixel237_OnOff_552 = 813892560;// BOOL
    ICC_LISD_Pixel238_OnOff_552 = 813925329;// BOOL
    ICC_LISD_Pixel239_OnOff_552 = 813958098;// BOOL
    ICC_LISD_Pixel240_OnOff_552 = 813990867;// BOOL
    ICC_LISD_Pixel241_OnOff_552 = 814023636;// BOOL
    ICC_LISD_Pixel242_OnOff_552 = 814056405;// BOOL
    ICC_LISD_Pixel243_OnOff_552 = 814089174;// BOOL
    ICC_LISD_Pixel244_OnOff_552 = 814121943;// BOOL
    ICC_LISD_Pixel245_OnOff_552 = 814154712;// BOOL
    ICC_LISD_Pixel246_OnOff_552 = 814187481;// BOOL
    ICC_LISD_Pixel247_OnOff_552 = 814220250;// BOOL
    ICC_LISD_Pixel248_OnOff_552 = 814253019;// BOOL
    ICC_LISD_Pixel249_OnOff_552 = 814285788;// BOOL
    ICC_LISD_Pixel250_OnOff_552 = 814318557;// BOOL
    ICC_LISD_Pixel251_OnOff_552 = 814351326;// BOOL
    ICC_LISD_Pixel252_OnOff_552 = 814384095;// BOOL
    ICC_LISD_Pixel253_OnOff_552 = 814416864;// BOOL
    ICC_LISD_Pixel254_OnOff_552 = 814449633;// BOOL
    ICC_LISD_Pixel255_OnOff_552 = 814482402;// BOOL
    ICC_LISD_Pixel256_OnOff_552 = 814515171;// BOOL
    ICC_LISD_Pixel257_OnOff_552 = 814547940;// BOOL
    ICC_LISD_Pixel258_OnOff_552 = 814580709;// BOOL
    ICC_LISD_Pixel259_OnOff_552 = 814613478;// BOOL
    ICC_LISD_Pixel260_OnOff_552 = 814646247;// BOOL
    ICC_LISD_Pixel261_OnOff_552 = 814679016;// BOOL
    ICC_LISD_Pixel262_OnOff_552 = 814711785;// BOOL
    ICC_LISD_Pixel263_OnOff_552 = 814744554;// BOOL
    ICC_LISD_Pixel264_OnOff_552 = 814777323;// BOOL
    ICC_LISD_Pixel265_OnOff_552 = 814810092;// BOOL
    ICC_LISD_Pixel266_OnOff_552 = 814842861;// BOOL
    ICC_LISD_Pixel267_OnOff_552 = 814875630;// BOOL
    ICC_LISD_Pixel268_OnOff_552 = 814908399;// BOOL
    ICC_LISD_Pixel269_OnOff_552 = 814941168;// BOOL
    ICC_LISD_Pixel270_OnOff_552 = 814973937;// BOOL
    ICC_LISD_Pixel271_OnOff_552 = 815006706;// BOOL
    ICC_LISD_Pixel272_OnOff_552 = 815039475;// BOOL
    ICC_LISD_Pixel273_OnOff_552 = 815072244;// BOOL
    ICC_LISD_Pixel274_OnOff_552 = 815105013;// BOOL
    ICC_LISD_Pixel275_OnOff_552 = 815137782;// BOOL
    ICC_LISD_Pixel276_OnOff_552 = 815170551;// BOOL
    ICC_LISD_Pixel277_OnOff_552 = 815203320;// BOOL
    ICC_LISD_Pixel278_OnOff_552 = 815236089;// BOOL
    ICC_LISD_Pixel279_OnOff_552 = 815268858;// BOOL
    ICC_LISD_Pixel280_OnOff_552 = 815301627;// BOOL
    ICC_LISD_Pixel281_OnOff_552 = 815334396;// BOOL
    ICC_LISD_Pixel282_OnOff_552 = 815367165;// BOOL
    ICC_LISD_Pixel283_OnOff_552 = 815399934;// BOOL
    ICC_LISD_Pixel284_OnOff_552 = 815432703;// BOOL
    ICC_LISD_Pixel285_OnOff_552 = 815465472;// BOOL
    ICC_LISD_Pixel286_OnOff_552 = 815498241;// BOOL
    ICC_LISD_Pixel287_OnOff_552 = 815531010;// BOOL
    ICC_LISD_Pixel288_OnOff_552 = 815563779;// BOOL
    ICC_LISD_Pixel289_OnOff_552 = 815596548;// BOOL
    ICC_LISD_Pixel290_OnOff_552 = 815629317;// BOOL
    ICC_LISD_Pixel291_OnOff_552 = 815662086;// BOOL
    ICC_LISD_Pixel292_OnOff_552 = 815694855;// BOOL
    ICC_LISD_Pixel293_OnOff_552 = 815727624;// BOOL
    ICC_LISD_Pixel294_OnOff_552 = 815760393;// BOOL
    ICC_LISD_Pixel295_OnOff_552 = 815793162;// BOOL
    ICC_LISD_Pixel296_OnOff_552 = 815825931;// BOOL
    ICC_LISD_Pixel297_OnOff_552 = 815858700;// BOOL
    ICC_LISD_Pixel298_OnOff_552 = 815891469;// BOOL
    ICC_LISD_Pixel299_OnOff_552 = 815924238;// BOOL
    ICC_LISD_Pixel300_OnOff_552 = 815957007;// BOOL
    ICC_LISD_Pixel301_OnOff_552 = 815989776;// BOOL
    ICC_LISD_Pixel302_OnOff_552 = 816022545;// BOOL
    ICC_LISD_Pixel303_OnOff_552 = 816055314;// BOOL
    ICC_LISD_Pixel304_OnOff_552 = 816088083;// BOOL
    ICC_LISD_Pixel305_OnOff_552 = 816120852;// BOOL
    ICC_LISD_Pixel306_OnOff_552 = 816153621;// BOOL
    ICC_LISD_Pixel307_OnOff_552 = 816186390;// BOOL
    ICC_LISD_Pixel308_OnOff_552 = 816219159;// BOOL
    ICC_LISD_Pixel309_OnOff_552 = 816251928;// BOOL
    ICC_LISD_Pixel310_OnOff_552 = 816284697;// BOOL
    ICC_LISD_Pixel311_OnOff_552 = 816317466;// BOOL
    ICC_LISD_Pixel312_OnOff_552 = 816350235;// BOOL
    ICC_LISD_Pixel313_OnOff_552 = 816383004;// BOOL
    ICC_LISD_Pixel314_OnOff_552 = 816415773;// BOOL
    ICC_LISD_Pixel315_OnOff_552 = 816448542;// BOOL
    ICC_LISD_Pixel316_OnOff_552 = 816481311;// BOOL
    ICC_LISD_Pixel317_OnOff_552 = 816514080;// BOOL
    ICC_LISD_Pixel318_OnOff_552 = 816546849;// BOOL
    ICC_LISD_Pixel319_OnOff_552 = 816579618;// BOOL
    ICC_LISD_Pixel320_OnOff_552 = 816612387;// BOOL
    ICC_LISD_Pixel321_OnOff_552 = 816645156;// BOOL
    ICC_LISD_Pixel322_OnOff_552 = 816677925;// BOOL
    ICC_LISD_Pixel323_OnOff_552 = 816710694;// BOOL
    ICC_LISD_Pixel324_OnOff_552 = 816743463;// BOOL
    ICC_LISD_Pixel325_OnOff_552 = 816776232;// BOOL
    ICC_LISD_Pixel326_OnOff_552 = 816809001;// BOOL
    ICC_LISD_Pixel327_OnOff_552 = 816841770;// BOOL
    ICC_LISD_Pixel328_OnOff_552 = 816874539;// BOOL
    ICC_LISD_Pixel329_OnOff_552 = 816907308;// BOOL
    ICC_LISD_Pixel330_OnOff_552 = 816940077;// BOOL
    ICC_LISD_Pixel331_OnOff_552 = 816972846;// BOOL
    ICC_LISD_Pixel332_OnOff_552 = 817005615;// BOOL
    ICC_LISD_Pixel333_OnOff_552 = 817038384;// BOOL
    ICC_LISD_Pixel334_OnOff_552 = 817071153;// BOOL
    ICC_LISD_Pixel335_OnOff_552 = 817103922;// BOOL
    ICC_LISD_Pixel336_OnOff_552 = 817136691;// BOOL
    ICC_LISD_Pixel337_OnOff_552 = 817169460;// BOOL
    ICC_LISD_Pixel338_OnOff_552 = 817202229;// BOOL
    ICC_LISD_Pixel339_OnOff_552 = 817234998;// BOOL
    ICC_LISD_Pixel340_OnOff_552 = 817267767;// BOOL
    ICC_LISD_Pixel341_OnOff_552 = 817300536;// BOOL
    ICC_LISD_Pixel342_OnOff_552 = 817333305;// BOOL
    ICC_LISD_Pixel343_OnOff_552 = 817366074;// BOOL
    ICC_LISD_Pixel344_OnOff_552 = 817398843;// BOOL
    ICC_LISD_Pixel345_OnOff_552 = 817431612;// BOOL
    ICC_LISD_Pixel346_OnOff_552 = 817464381;// BOOL
    ICC_LISD_Pixel347_OnOff_552 = 817497150;// BOOL
    ICC_LISD_Pixel348_OnOff_552 = 817529919;// BOOL
    ICC_LISD_Pixel349_OnOff_552 = 817562688;// BOOL
    ICC_LISD_Pixel350_OnOff_552 = 817595457;// BOOL
    ICC_LISD_Pixel351_OnOff_552 = 817628226;// BOOL
    ICC_LISD_Pixel352_OnOff_552 = 817660995;// BOOL
    ICC_LISD_Pixel353_OnOff_552 = 817693764;// BOOL
    ICC_LISD_Pixel354_OnOff_552 = 817726533;// BOOL
    ICC_LISD_Pixel355_OnOff_552 = 817759302;// BOOL
    ICC_LISD_Pixel356_OnOff_552 = 817792071;// BOOL
    ICC_LISD_Pixel357_OnOff_552 = 817824840;// BOOL
    ICC_LISD_Pixel358_OnOff_552 = 817857609;// BOOL
    ICC_LISD_Pixel359_OnOff_552 = 817890378;// BOOL
    ICC_LISD_Pixel360_OnOff_552 = 817923147;// BOOL
    ICC_LISD_Pixel361_OnOff_552 = 817955916;// BOOL
    ICC_LISD_Pixel362_OnOff_552 = 817988685;// BOOL
    ICC_LISD_Pixel363_OnOff_552 = 818021454;// BOOL
    ICC_LISD_Pixel364_OnOff_552 = 818054223;// BOOL
    ICC_LISD_Pixel365_OnOff_552 = 818086992;// BOOL
    ICC_LISD_Pixel366_OnOff_552 = 818119761;// BOOL
    ICC_LISD_Pixel367_OnOff_552 = 818152530;// BOOL
    ICC_LISD_Pixel368_OnOff_552 = 818185299;// BOOL
    ICC_LISD_Pixel369_OnOff_552 = 818218068;// BOOL
    ICC_LISD_Pixel370_OnOff_552 = 818250837;// BOOL
    ICC_LISD_Pixel371_OnOff_552 = 818283606;// BOOL
    ICC_LISD_Pixel372_OnOff_552 = 818316375;// BOOL
    ICC_LISD_Pixel373_OnOff_552 = 818349144;// BOOL
    ICC_LISD_Pixel374_OnOff_552 = 818381913;// BOOL
    ICC_LISD_Pixel375_OnOff_552 = 818414682;// BOOL
    ICC_LISD_Pixel376_OnOff_552 = 818447451;// BOOL
    ICC_LISD_Pixel377_OnOff_552 = 818480220;// BOOL
    ICC_LISD_Pixel378_OnOff_552 = 818512989;// BOOL
    ICC_LISD_Pixel379_OnOff_552 = 818545758;// BOOL
    ICC_LISD_Pixel380_OnOff_552 = 818578527;// BOOL
    ICC_LISD_Pixel381_OnOff_552 = 818611296;// BOOL
    ICC_LISD_Pixel382_OnOff_552 = 818644065;// BOOL
    ICC_LISD_Pixel383_OnOff_552 = 818676834;// BOOL
    ICC_LISD_Pixel384_OnOff_552 = 818709603;// BOOL
    ICC_LISD_Pixel385_OnOff_552 = 818742372;// BOOL
    ICC_LISD_Pixel386_OnOff_552 = 818775141;// BOOL
    ICC_LISD_Pixel387_OnOff_552 = 818807910;// BOOL
    ICC_LISD_Pixel388_OnOff_552 = 818840679;// BOOL
    ICC_LISD_Pixel389_OnOff_552 = 818873448;// BOOL
    ICC_LISD_Pixel390_OnOff_552 = 818906217;// BOOL
    ICC_LISD_Pixel391_OnOff_552 = 818938986;// BOOL
    ICC_LISD_Pixel392_OnOff_552 = 818971755;// BOOL
    ICC_LISD_Pixel393_OnOff_552 = 819004524;// BOOL
    ICC_LISD_Pixel394_OnOff_552 = 819037293;// BOOL
    ICC_LISD_Pixel395_OnOff_552 = 819070062;// BOOL
    ICC_LISD_Pixel396_OnOff_552 = 819102831;// BOOL
    ICC_LISD_Pixel397_OnOff_552 = 819135600;// BOOL
    ICC_LISD_Pixel398_OnOff_552 = 819168369;// BOOL
    ICC_LISD_Pixel399_OnOff_552 = 819201138;// BOOL
    ICC_LISD_Pixel400_OnOff_552 = 819233907;// BOOL
    ICC_LISD_Pixel401_OnOff_552 = 819266676;// BOOL
    ICC_LISD_Pixel402_OnOff_552 = 819299445;// BOOL
    ICC_LISD_Pixel403_OnOff_552 = 819332214;// BOOL
    ICC_LISD_Pixel404_OnOff_552 = 819364983;// BOOL
    ICC_LISD_Pixel405_OnOff_552 = 819397752;// BOOL
    ICC_LISD_Pixel406_OnOff_552 = 819430521;// BOOL
    ICC_LISD_Pixel407_OnOff_552 = 819463290;// BOOL
    ICC_LISD_Pixel408_OnOff_552 = 819496059;// BOOL
    ICC_LISD_Pixel409_OnOff_552 = 819528828;// BOOL
    ICC_LISD_Pixel410_OnOff_552 = 819561597;// BOOL
    ICC_LISD_Pixel411_OnOff_552 = 819594366;// BOOL
    ICC_LISD_Pixel412_OnOff_552 = 819627135;// BOOL
    ICC_LISD_Pixel413_OnOff_552 = 819659904;// BOOL
    ICC_LISD_Pixel414_OnOff_552 = 819692673;// BOOL
    ICC_LISD_Pixel415_OnOff_552 = 819725442;// BOOL
    ICC_LISD_Pixel416_OnOff_552 = 819758211;// BOOL
    ICC_LISD_Pixel417_OnOff_552 = 819790980;// BOOL
    ICC_LISD_Pixel418_OnOff_552 = 819823749;// BOOL
    ICC_LISD_Pixel419_OnOff_552 = 819856518;// BOOL
    ICC_LISD_Pixel420_OnOff_552 = 819889287;// BOOL
    ICC_LISD_Pixel421_OnOff_552 = 819922056;// BOOL
    ICC_LISD_Pixel422_OnOff_552 = 819954825;// BOOL
    ICC_LISD_Pixel423_OnOff_552 = 819987594;// BOOL
    ICC_LISD_Pixel424_OnOff_552 = 820020363;// BOOL
    ICC_LISD_Pixel425_OnOff_552 = 820053132;// BOOL
    ICC_LISD_Pixel426_OnOff_552 = 820085901;// BOOL
    ICC_LISD_Pixel427_OnOff_552 = 820118670;// BOOL
    ICC_LISD_Pixel428_OnOff_552 = 820151439;// BOOL
    ICC_LISD_Pixel429_OnOff_552 = 820184208;// BOOL
    ICC_LISD_Pixel430_OnOff_552 = 820216977;// BOOL
    ICC_LISD_Pixel431_OnOff_552 = 820249746;// BOOL
    ICC_LISD_Pixel432_OnOff_552 = 820282515;// BOOL
    ICC_LISD_Pixel433_OnOff_552 = 820315284;// BOOL
    ICC_LISD_Pixel434_OnOff_552 = 820348053;// BOOL
    ICC_LISD_Pixel435_OnOff_552 = 820380822;// BOOL
    ICC_LISD_Pixel436_OnOff_552 = 820413591;// BOOL
    ICC_LISD_Pixel437_OnOff_552 = 820446360;// BOOL
    ICC_LISD_Pixel438_OnOff_552 = 820479129;// BOOL
    ICC_LISD_Pixel439_OnOff_552 = 820511898;// BOOL
    ICC_LISD_Pixel440_OnOff_552 = 820544667;// BOOL
    ICC_LISD_Pixel441_OnOff_552 = 820577436;// BOOL
    ICC_LISD_Pixel442_OnOff_552 = 820610205;// BOOL
    ICC_LISD_Pixel443_OnOff_552 = 820642974;// BOOL
    ICC_LISD_Pixel444_OnOff_552 = 820675743;// BOOL
    ICC_LISD_Pixel445_OnOff_552 = 820708512;// BOOL
    ICC_LISD_Pixel446_OnOff_552 = 820741281;// BOOL
    ICC_LISD_Pixel447_OnOff_552 = 820774050;// BOOL
    ICC_LISD_Pixel448_OnOff_552 = 820806819;// BOOL
    ICC_LISD_Pixel449_OnOff_552 = 820839588;// BOOL
    ICC_LISD_Pixel450_OnOff_552 = 820872357;// BOOL
    ICC_LISD_Pixel451_OnOff_552 = 820905126;// BOOL
    ICC_LISD_Pixel452_OnOff_552 = 820937895;// BOOL
    ICC_LISD_Pixel453_OnOff_552 = 820970664;// BOOL
    ICC_LISD_Pixel454_OnOff_552 = 821003433;// BOOL
    ICC_LISD_Pixel455_OnOff_552 = 821036202;// BOOL
    ICC_LISD_Pixel456_OnOff_552 = 821068971;// BOOL
    ICC_LISD_Pixel457_OnOff_552 = 821101740;// BOOL
    ICC_LISD_Pixel458_OnOff_552 = 821134509;// BOOL
    ICC_LISD_Pixel459_OnOff_552 = 821167278;// BOOL
    ICC_LISD_Pixel460_OnOff_552 = 821200047;// BOOL
    ICC_LISD_Pixel461_OnOff_552 = 821232816;// BOOL
    ICC_LISD_Pixel462_OnOff_552 = 821265585;// BOOL
    ICC_LISD_Pixel463_OnOff_552 = 821298354;// BOOL
    ICC_LISD_Pixel464_OnOff_552 = 821331123;// BOOL
    ICC_LISD_Pixel465_OnOff_552 = 821363892;// BOOL
    ICC_LISD_Pixel466_OnOff_552 = 821396661;// BOOL
    ICC_LISD_Pixel467_OnOff_552 = 821429430;// BOOL
    ICC_LISD_Pixel468_OnOff_552 = 821462199;// BOOL
    ICC_LISD_Pixel469_OnOff_552 = 821494968;// BOOL
    ICC_LISD_Pixel470_OnOff_552 = 821527737;// BOOL
    ICC_LISD_Pixel471_OnOff_552 = 821560506;// BOOL
    ICC_LISD_Pixel472_OnOff_552 = 821593275;// BOOL
    ICC_LISD_Pixel473_OnOff_552 = 821626044;// BOOL
    ICC_LISD_Pixel474_OnOff_552 = 821658813;// BOOL
    ICC_LISD_Pixel475_OnOff_552 = 821691582;// BOOL
    ICC_LISD_Pixel476_OnOff_552 = 821724351;// BOOL
    ICC_LISD_Pixel477_OnOff_552 = 821757120;// BOOL
    ICC_LISD_Pixel478_OnOff_552 = 821789889;// BOOL
    ICC_LISD_Pixel479_OnOff_552 = 821822658;// BOOL
    ICC_LISD_Pixel480_OnOff_552 = 821855427;// BOOL
    ICC_LISD_Pixel481_OnOff_552 = 821888196;// BOOL
    ICC_LISD_Pixel482_OnOff_552 = 821920965;// BOOL
    ICC_LISD_Pixel483_OnOff_552 = 821953734;// BOOL
    ICC_LISD_Pixel484_OnOff_552 = 821986503;// BOOL
    ICC_LISD_Pixel485_OnOff_552 = 822019272;// BOOL
    ICC_LISD_Pixel486_OnOff_552 = 822052041;// BOOL
    ICC_LISD_Pixel487_OnOff_552 = 822084810;// BOOL
    ICC_LISD_Pixel488_OnOff_552 = 822117579;// BOOL
    ICC_LISD_Pixel489_OnOff_552 = 822150348;// BOOL
    ICC_LISD_Pixel490_OnOff_552 = 822183117;// BOOL
    ICC_LISD_Pixel491_OnOff_552 = 822215886;// BOOL
    ICC_LISD_Pixel492_OnOff_552 = 822248655;// BOOL
    ICC_LISD_Pixel493_OnOff_552 = 822281424;// BOOL
    ICC_LISD_Pixel494_OnOff_552 = 822314193;// BOOL
    ICC_LISD_Pixel495_OnOff_552 = 822346962;// BOOL
    ICC_LISD_Pixel496_OnOff_552 = 822379731;// BOOL
    ICC_LISD_Pixel497_OnOff_552 = 822412500;// BOOL
    ICC_LISD_Pixel498_OnOff_552 = 822445269;// BOOL
    ICC_LISD_Pixel499_OnOff_552 = 822478038;// BOOL
    ICC_LISD_Pixel500_OnOff_552 = 822510807;// BOOL
    ICC_LISD_Pixel501_OnOff_552 = 822543576;// BOOL
    ICC_LISD_Pixel502_OnOff_552 = 822576345;// BOOL
    ICC_LISD_Pixel503_OnOff_552 = 822609114;// BOOL
    ICC_LISD_Pixel504_OnOff_552 = 822641883;// BOOL
    ICC_RISD_FrameCounter_554 = 156402908;// UINT8
    ICC_RISD_RollingCounter_554 = 156435677;// UINT8
    ICC_RISD_Pixel1_OnOff_554 = 822674654;// BOOL
    ICC_RISD_Pixel2_OnOff_554 = 822707423;// BOOL
    ICC_RISD_Pixel3_OnOff_554 = 822740192;// BOOL
    ICC_RISD_Pixel4_OnOff_554 = 822772961;// BOOL
    ICC_RISD_Pixel5_OnOff_554 = 822805730;// BOOL
    ICC_RISD_Pixel6_OnOff_554 = 822838499;// BOOL
    ICC_RISD_Pixel7_OnOff_554 = 822871268;// BOOL
    ICC_RISD_Pixel8_OnOff_554 = 822904037;// BOOL
    ICC_RISD_Pixel9_OnOff_554 = 822936806;// BOOL
    ICC_RISD_Pixel10_OnOff_554 = 822969575;// BOOL
    ICC_RISD_Pixel11_OnOff_554 = 823002344;// BOOL
    ICC_RISD_Pixel12_OnOff_554 = 823035113;// BOOL
    ICC_RISD_Pixel13_OnOff_554 = 823067882;// BOOL
    ICC_RISD_Pixel14_OnOff_554 = 823100651;// BOOL
    ICC_RISD_Pixel15_OnOff_554 = 823133420;// BOOL
    ICC_RISD_Pixel16_OnOff_554 = 823166189;// BOOL
    ICC_RISD_Pixel17_OnOff_554 = 823198958;// BOOL
    ICC_RISD_Pixel18_OnOff_554 = 823231727;// BOOL
    ICC_RISD_Pixel19_OnOff_554 = 823264496;// BOOL
    ICC_RISD_Pixel20_OnOff_554 = 823297265;// BOOL
    ICC_RISD_Pixel21_OnOff_554 = 823330034;// BOOL
    ICC_RISD_Pixel22_OnOff_554 = 823362803;// BOOL
    ICC_RISD_Pixel23_OnOff_554 = 823395572;// BOOL
    ICC_RISD_Pixel24_OnOff_554 = 823428341;// BOOL
    ICC_RISD_Pixel25_OnOff_554 = 823461110;// BOOL
    ICC_RISD_Pixel26_OnOff_554 = 823493879;// BOOL
    ICC_RISD_Pixel27_OnOff_554 = 823526648;// BOOL
    ICC_RISD_Pixel28_OnOff_554 = 823559417;// BOOL
    ICC_RISD_Pixel29_OnOff_554 = 823592186;// BOOL
    ICC_RISD_Pixel30_OnOff_554 = 823624955;// BOOL
    ICC_RISD_Pixel31_OnOff_554 = 823657724;// BOOL
    ICC_RISD_Pixel32_OnOff_554 = 823690493;// BOOL
    ICC_RISD_Pixel33_OnOff_554 = 823723262;// BOOL
    ICC_RISD_Pixel34_OnOff_554 = 823756031;// BOOL
    ICC_RISD_Pixel35_OnOff_554 = 823788800;// BOOL
    ICC_RISD_Pixel36_OnOff_554 = 823821569;// BOOL
    ICC_RISD_Pixel37_OnOff_554 = 823854338;// BOOL
    ICC_RISD_Pixel38_OnOff_554 = 823887107;// BOOL
    ICC_RISD_Pixel39_OnOff_554 = 823919876;// BOOL
    ICC_RISD_Pixel40_OnOff_554 = 823952645;// BOOL
    ICC_RISD_Pixel41_OnOff_554 = 823985414;// BOOL
    ICC_RISD_Pixel42_OnOff_554 = 824018183;// BOOL
    ICC_RISD_Pixel43_OnOff_554 = 824050952;// BOOL
    ICC_RISD_Pixel44_OnOff_554 = 824083721;// BOOL
    ICC_RISD_Pixel45_OnOff_554 = 824116490;// BOOL
    ICC_RISD_Pixel46_OnOff_554 = 824149259;// BOOL
    ICC_RISD_Pixel47_OnOff_554 = 824182028;// BOOL
    ICC_RISD_Pixel48_OnOff_554 = 824214797;// BOOL
    ICC_RISD_Pixel49_OnOff_554 = 824247566;// BOOL
    ICC_RISD_Pixel50_OnOff_554 = 824280335;// BOOL
    ICC_RISD_Pixel51_OnOff_554 = 824313104;// BOOL
    ICC_RISD_Pixel52_OnOff_554 = 824345873;// BOOL
    ICC_RISD_Pixel53_OnOff_554 = 824378642;// BOOL
    ICC_RISD_Pixel54_OnOff_554 = 824411411;// BOOL
    ICC_RISD_Pixel55_OnOff_554 = 824444180;// BOOL
    ICC_RISD_Pixel56_OnOff_554 = 824476949;// BOOL
    ICC_RISD_Pixel57_OnOff_554 = 824509718;// BOOL
    ICC_RISD_Pixel58_OnOff_554 = 824542487;// BOOL
    ICC_RISD_Pixel59_OnOff_554 = 824575256;// BOOL
    ICC_RISD_Pixel60_OnOff_554 = 824608025;// BOOL
    ICC_RISD_Pixel61_OnOff_554 = 824640794;// BOOL
    ICC_RISD_Pixel62_OnOff_554 = 824673563;// BOOL
    ICC_RISD_Pixel63_OnOff_554 = 824706332;// BOOL
    ICC_RISD_Pixel64_OnOff_554 = 824739101;// BOOL
    ICC_RISD_Pixel65_OnOff_554 = 824771870;// BOOL
    ICC_RISD_Pixel66_OnOff_554 = 824804639;// BOOL
    ICC_RISD_Pixel67_OnOff_554 = 824837408;// BOOL
    ICC_RISD_Pixel68_OnOff_554 = 824870177;// BOOL
    ICC_RISD_Pixel69_OnOff_554 = 824902946;// BOOL
    ICC_RISD_Pixel70_OnOff_554 = 824935715;// BOOL
    ICC_RISD_Pixel71_OnOff_554 = 824968484;// BOOL
    ICC_RISD_Pixel72_OnOff_554 = 825001253;// BOOL
    ICC_RISD_Pixel73_OnOff_554 = 825034022;// BOOL
    ICC_RISD_Pixel74_OnOff_554 = 825066791;// BOOL
    ICC_RISD_Pixel75_OnOff_554 = 825099560;// BOOL
    ICC_RISD_Pixel76_OnOff_554 = 825132329;// BOOL
    ICC_RISD_Pixel77_OnOff_554 = 825165098;// BOOL
    ICC_RISD_Pixel78_OnOff_554 = 825197867;// BOOL
    ICC_RISD_Pixel79_OnOff_554 = 825230636;// BOOL
    ICC_RISD_Pixel80_OnOff_554 = 825263405;// BOOL
    ICC_RISD_Pixel81_OnOff_554 = 825296174;// BOOL
    ICC_RISD_Pixel82_OnOff_554 = 825328943;// BOOL
    ICC_RISD_Pixel83_OnOff_554 = 825361712;// BOOL
    ICC_RISD_Pixel84_OnOff_554 = 825394481;// BOOL
    ICC_RISD_Pixel85_OnOff_554 = 825427250;// BOOL
    ICC_RISD_Pixel86_OnOff_554 = 825460019;// BOOL
    ICC_RISD_Pixel87_OnOff_554 = 825492788;// BOOL
    ICC_RISD_Pixel88_OnOff_554 = 825525557;// BOOL
    ICC_RISD_Pixel89_OnOff_554 = 825558326;// BOOL
    ICC_RISD_Pixel90_OnOff_554 = 825591095;// BOOL
    ICC_RISD_Pixel91_OnOff_554 = 825623864;// BOOL
    ICC_RISD_Pixel92_OnOff_554 = 825656633;// BOOL
    ICC_RISD_Pixel93_OnOff_554 = 825689402;// BOOL
    ICC_RISD_Pixel94_OnOff_554 = 825722171;// BOOL
    ICC_RISD_Pixel95_OnOff_554 = 825754940;// BOOL
    ICC_RISD_Pixel96_OnOff_554 = 825787709;// BOOL
    ICC_RISD_Pixel97_OnOff_554 = 825820478;// BOOL
    ICC_RISD_Pixel98_OnOff_554 = 825853247;// BOOL
    ICC_RISD_Pixel99_OnOff_554 = 825886016;// BOOL
    ICC_RISD_Pixel100_OnOff_554 = 825918785;// BOOL
    ICC_RISD_Pixel101_OnOff_554 = 825951554;// BOOL
    ICC_RISD_Pixel102_OnOff_554 = 825984323;// BOOL
    ICC_RISD_Pixel103_OnOff_554 = 826017092;// BOOL
    ICC_RISD_Pixel104_OnOff_554 = 826049861;// BOOL
    ICC_RISD_Pixel105_OnOff_554 = 826082630;// BOOL
    ICC_RISD_Pixel106_OnOff_554 = 826115399;// BOOL
    ICC_RISD_Pixel107_OnOff_554 = 826148168;// BOOL
    ICC_RISD_Pixel108_OnOff_554 = 826180937;// BOOL
    ICC_RISD_Pixel109_OnOff_554 = 826213706;// BOOL
    ICC_RISD_Pixel110_OnOff_554 = 826246475;// BOOL
    ICC_RISD_Pixel111_OnOff_554 = 826279244;// BOOL
    ICC_RISD_Pixel112_OnOff_554 = 826312013;// BOOL
    ICC_RISD_Pixel113_OnOff_554 = 826344782;// BOOL
    ICC_RISD_Pixel114_OnOff_554 = 826377551;// BOOL
    ICC_RISD_Pixel115_OnOff_554 = 826410320;// BOOL
    ICC_RISD_Pixel116_OnOff_554 = 826443089;// BOOL
    ICC_RISD_Pixel117_OnOff_554 = 826475858;// BOOL
    ICC_RISD_Pixel118_OnOff_554 = 826508627;// BOOL
    ICC_RISD_Pixel119_OnOff_554 = 826541396;// BOOL
    ICC_RISD_Pixel120_OnOff_554 = 826574165;// BOOL
    ICC_RISD_Pixel121_OnOff_554 = 826606934;// BOOL
    ICC_RISD_Pixel122_OnOff_554 = 826639703;// BOOL
    ICC_RISD_Pixel123_OnOff_554 = 826672472;// BOOL
    ICC_RISD_Pixel124_OnOff_554 = 826705241;// BOOL
    ICC_RISD_Pixel125_OnOff_554 = 826738010;// BOOL
    ICC_RISD_Pixel126_OnOff_554 = 826770779;// BOOL
    ICC_RISD_Pixel127_OnOff_554 = 826803548;// BOOL
    ICC_RISD_Pixel128_OnOff_554 = 826836317;// BOOL
    ICC_RISD_Pixel129_OnOff_554 = 826869086;// BOOL
    ICC_RISD_Pixel130_OnOff_554 = 826901855;// BOOL
    ICC_RISD_Pixel131_OnOff_554 = 826934624;// BOOL
    ICC_RISD_Pixel132_OnOff_554 = 826967393;// BOOL
    ICC_RISD_Pixel133_OnOff_554 = 827000162;// BOOL
    ICC_RISD_Pixel134_OnOff_554 = 827032931;// BOOL
    ICC_RISD_Pixel135_OnOff_554 = 827065700;// BOOL
    ICC_RISD_Pixel136_OnOff_554 = 827098469;// BOOL
    ICC_RISD_Pixel137_OnOff_554 = 827131238;// BOOL
    ICC_RISD_Pixel138_OnOff_554 = 827164007;// BOOL
    ICC_RISD_Pixel139_OnOff_554 = 827196776;// BOOL
    ICC_RISD_Pixel140_OnOff_554 = 827229545;// BOOL
    ICC_RISD_Pixel141_OnOff_554 = 827262314;// BOOL
    ICC_RISD_Pixel142_OnOff_554 = 827295083;// BOOL
    ICC_RISD_Pixel143_OnOff_554 = 827327852;// BOOL
    ICC_RISD_Pixel144_OnOff_554 = 827360621;// BOOL
    ICC_RISD_Pixel145_OnOff_554 = 827393390;// BOOL
    ICC_RISD_Pixel146_OnOff_554 = 827426159;// BOOL
    ICC_RISD_Pixel147_OnOff_554 = 827458928;// BOOL
    ICC_RISD_Pixel148_OnOff_554 = 827491697;// BOOL
    ICC_RISD_Pixel149_OnOff_554 = 827524466;// BOOL
    ICC_RISD_Pixel150_OnOff_554 = 827557235;// BOOL
    ICC_RISD_Pixel151_OnOff_554 = 827590004;// BOOL
    ICC_RISD_Pixel152_OnOff_554 = 827622773;// BOOL
    ICC_RISD_Pixel153_OnOff_554 = 827655542;// BOOL
    ICC_RISD_Pixel154_OnOff_554 = 827688311;// BOOL
    ICC_RISD_Pixel155_OnOff_554 = 827721080;// BOOL
    ICC_RISD_Pixel156_OnOff_554 = 827753849;// BOOL
    ICC_RISD_Pixel157_OnOff_554 = 827786618;// BOOL
    ICC_RISD_Pixel158_OnOff_554 = 827819387;// BOOL
    ICC_RISD_Pixel159_OnOff_554 = 827852156;// BOOL
    ICC_RISD_Pixel160_OnOff_554 = 827884925;// BOOL
    ICC_RISD_Pixel161_OnOff_554 = 827917694;// BOOL
    ICC_RISD_Pixel162_OnOff_554 = 827950463;// BOOL
    ICC_RISD_Pixel163_OnOff_554 = 827983232;// BOOL
    ICC_RISD_Pixel164_OnOff_554 = 828016001;// BOOL
    ICC_RISD_Pixel165_OnOff_554 = 828048770;// BOOL
    ICC_RISD_Pixel166_OnOff_554 = 828081539;// BOOL
    ICC_RISD_Pixel167_OnOff_554 = 828114308;// BOOL
    ICC_RISD_Pixel168_OnOff_554 = 828147077;// BOOL
    ICC_RISD_Pixel169_OnOff_554 = 828179846;// BOOL
    ICC_RISD_Pixel170_OnOff_554 = 828212615;// BOOL
    ICC_RISD_Pixel171_OnOff_554 = 828245384;// BOOL
    ICC_RISD_Pixel172_OnOff_554 = 828278153;// BOOL
    ICC_RISD_Pixel173_OnOff_554 = 828310922;// BOOL
    ICC_RISD_Pixel174_OnOff_554 = 828343691;// BOOL
    ICC_RISD_Pixel175_OnOff_554 = 828376460;// BOOL
    ICC_RISD_Pixel176_OnOff_554 = 828409229;// BOOL
    ICC_RISD_Pixel177_OnOff_554 = 828441998;// BOOL
    ICC_RISD_Pixel178_OnOff_554 = 828474767;// BOOL
    ICC_RISD_Pixel179_OnOff_554 = 828507536;// BOOL
    ICC_RISD_Pixel180_OnOff_554 = 828540305;// BOOL
    ICC_RISD_Pixel181_OnOff_554 = 828573074;// BOOL
    ICC_RISD_Pixel182_OnOff_554 = 828605843;// BOOL
    ICC_RISD_Pixel183_OnOff_554 = 828638612;// BOOL
    ICC_RISD_Pixel184_OnOff_554 = 828671381;// BOOL
    ICC_RISD_Pixel185_OnOff_554 = 828704150;// BOOL
    ICC_RISD_Pixel186_OnOff_554 = 828736919;// BOOL
    ICC_RISD_Pixel187_OnOff_554 = 828769688;// BOOL
    ICC_RISD_Pixel188_OnOff_554 = 828802457;// BOOL
    ICC_RISD_Pixel189_OnOff_554 = 828835226;// BOOL
    ICC_RISD_Pixel190_OnOff_554 = 828867995;// BOOL
    ICC_RISD_Pixel191_OnOff_554 = 828900764;// BOOL
    ICC_RISD_Pixel192_OnOff_554 = 828933533;// BOOL
    ICC_RISD_Pixel193_OnOff_554 = 828966302;// BOOL
    ICC_RISD_Pixel194_OnOff_554 = 828999071;// BOOL
    ICC_RISD_Pixel195_OnOff_554 = 829031840;// BOOL
    ICC_RISD_Pixel196_OnOff_554 = 829064609;// BOOL
    ICC_RISD_Pixel197_OnOff_554 = 829097378;// BOOL
    ICC_RISD_Pixel198_OnOff_554 = 829130147;// BOOL
    ICC_RISD_Pixel199_OnOff_554 = 829162916;// BOOL
    ICC_RISD_Pixel200_OnOff_554 = 829195685;// BOOL
    ICC_RISD_Pixel201_OnOff_554 = 829228454;// BOOL
    ICC_RISD_Pixel202_OnOff_554 = 829261223;// BOOL
    ICC_RISD_Pixel203_OnOff_554 = 829293992;// BOOL
    ICC_RISD_Pixel204_OnOff_554 = 829326761;// BOOL
    ICC_RISD_Pixel205_OnOff_554 = 829359530;// BOOL
    ICC_RISD_Pixel206_OnOff_554 = 829392299;// BOOL
    ICC_RISD_Pixel207_OnOff_554 = 829425068;// BOOL
    ICC_RISD_Pixel208_OnOff_554 = 829457837;// BOOL
    ICC_RISD_Pixel209_OnOff_554 = 829490606;// BOOL
    ICC_RISD_Pixel210_OnOff_554 = 829523375;// BOOL
    ICC_RISD_Pixel211_OnOff_554 = 829556144;// BOOL
    ICC_RISD_Pixel212_OnOff_554 = 829588913;// BOOL
    ICC_RISD_Pixel213_OnOff_554 = 829621682;// BOOL
    ICC_RISD_Pixel214_OnOff_554 = 829654451;// BOOL
    ICC_RISD_Pixel215_OnOff_554 = 829687220;// BOOL
    ICC_RISD_Pixel216_OnOff_554 = 829719989;// BOOL
    ICC_RISD_Pixel217_OnOff_554 = 829752758;// BOOL
    ICC_RISD_Pixel218_OnOff_554 = 829785527;// BOOL
    ICC_RISD_Pixel219_OnOff_554 = 829818296;// BOOL
    ICC_RISD_Pixel220_OnOff_554 = 829851065;// BOOL
    ICC_RISD_Pixel221_OnOff_554 = 829883834;// BOOL
    ICC_RISD_Pixel222_OnOff_554 = 829916603;// BOOL
    ICC_RISD_Pixel223_OnOff_554 = 829949372;// BOOL
    ICC_RISD_Pixel224_OnOff_554 = 829982141;// BOOL
    ICC_RISD_Pixel225_OnOff_554 = 830014910;// BOOL
    ICC_RISD_Pixel226_OnOff_554 = 830047679;// BOOL
    ICC_RISD_Pixel227_OnOff_554 = 830080448;// BOOL
    ICC_RISD_Pixel228_OnOff_554 = 830113217;// BOOL
    ICC_RISD_Pixel229_OnOff_554 = 830145986;// BOOL
    ICC_RISD_Pixel230_OnOff_554 = 830178755;// BOOL
    ICC_RISD_Pixel231_OnOff_554 = 830211524;// BOOL
    ICC_RISD_Pixel232_OnOff_554 = 830244293;// BOOL
    ICC_RISD_Pixel233_OnOff_554 = 830277062;// BOOL
    ICC_RISD_Pixel234_OnOff_554 = 830309831;// BOOL
    ICC_RISD_Pixel235_OnOff_554 = 830342600;// BOOL
    ICC_RISD_Pixel236_OnOff_554 = 830375369;// BOOL
    ICC_RISD_Pixel237_OnOff_554 = 830408138;// BOOL
    ICC_RISD_Pixel238_OnOff_554 = 830440907;// BOOL
    ICC_RISD_Pixel239_OnOff_554 = 830473676;// BOOL
    ICC_RISD_Pixel240_OnOff_554 = 830506445;// BOOL
    ICC_RISD_Pixel241_OnOff_554 = 830539214;// BOOL
    ICC_RISD_Pixel242_OnOff_554 = 830571983;// BOOL
    ICC_RISD_Pixel243_OnOff_554 = 830604752;// BOOL
    ICC_RISD_Pixel244_OnOff_554 = 830637521;// BOOL
    ICC_RISD_Pixel245_OnOff_554 = 830670290;// BOOL
    ICC_RISD_Pixel246_OnOff_554 = 830703059;// BOOL
    ICC_RISD_Pixel247_OnOff_554 = 830735828;// BOOL
    ICC_RISD_Pixel248_OnOff_554 = 830768597;// BOOL
    ICC_RISD_Pixel249_OnOff_554 = 830801366;// BOOL
    ICC_RISD_Pixel250_OnOff_554 = 830834135;// BOOL
    ICC_RISD_Pixel251_OnOff_554 = 830866904;// BOOL
    ICC_RISD_Pixel252_OnOff_554 = 830899673;// BOOL
    ICC_RISD_Pixel253_OnOff_554 = 830932442;// BOOL
    ICC_RISD_Pixel254_OnOff_554 = 830965211;// BOOL
    ICC_RISD_Pixel255_OnOff_554 = 830997980;// BOOL
    ICC_RISD_Pixel256_OnOff_554 = 831030749;// BOOL
    ICC_RISD_Pixel257_OnOff_554 = 831063518;// BOOL
    ICC_RISD_Pixel258_OnOff_554 = 831096287;// BOOL
    ICC_RISD_Pixel259_OnOff_554 = 831129056;// BOOL
    ICC_RISD_Pixel260_OnOff_554 = 831161825;// BOOL
    ICC_RISD_Pixel261_OnOff_554 = 831194594;// BOOL
    ICC_RISD_Pixel262_OnOff_554 = 831227363;// BOOL
    ICC_RISD_Pixel263_OnOff_554 = 831260132;// BOOL
    ICC_RISD_Pixel264_OnOff_554 = 831292901;// BOOL
    ICC_RISD_Pixel265_OnOff_554 = 831325670;// BOOL
    ICC_RISD_Pixel266_OnOff_554 = 831358439;// BOOL
    ICC_RISD_Pixel267_OnOff_554 = 831391208;// BOOL
    ICC_RISD_Pixel268_OnOff_554 = 831423977;// BOOL
    ICC_RISD_Pixel269_OnOff_554 = 831456746;// BOOL
    ICC_RISD_Pixel270_OnOff_554 = 831489515;// BOOL
    ICC_RISD_Pixel271_OnOff_554 = 831522284;// BOOL
    ICC_RISD_Pixel272_OnOff_554 = 831555053;// BOOL
    ICC_RISD_Pixel273_OnOff_554 = 831587822;// BOOL
    ICC_RISD_Pixel274_OnOff_554 = 831620591;// BOOL
    ICC_RISD_Pixel275_OnOff_554 = 831653360;// BOOL
    ICC_RISD_Pixel276_OnOff_554 = 831686129;// BOOL
    ICC_RISD_Pixel277_OnOff_554 = 831718898;// BOOL
    ICC_RISD_Pixel278_OnOff_554 = 831751667;// BOOL
    ICC_RISD_Pixel279_OnOff_554 = 831784436;// BOOL
    ICC_RISD_Pixel280_OnOff_554 = 831817205;// BOOL
    ICC_RISD_Pixel281_OnOff_554 = 831849974;// BOOL
    ICC_RISD_Pixel282_OnOff_554 = 831882743;// BOOL
    ICC_RISD_Pixel283_OnOff_554 = 831915512;// BOOL
    ICC_RISD_Pixel284_OnOff_554 = 831948281;// BOOL
    ICC_RISD_Pixel285_OnOff_554 = 831981050;// BOOL
    ICC_RISD_Pixel286_OnOff_554 = 832013819;// BOOL
    ICC_RISD_Pixel287_OnOff_554 = 832046588;// BOOL
    ICC_RISD_Pixel288_OnOff_554 = 832079357;// BOOL
    ICC_RISD_Pixel289_OnOff_554 = 832112126;// BOOL
    ICC_RISD_Pixel290_OnOff_554 = 832144895;// BOOL
    ICC_RISD_Pixel291_OnOff_554 = 832177664;// BOOL
    ICC_RISD_Pixel292_OnOff_554 = 832210433;// BOOL
    ICC_RISD_Pixel293_OnOff_554 = 832243202;// BOOL
    ICC_RISD_Pixel294_OnOff_554 = 832275971;// BOOL
    ICC_RISD_Pixel295_OnOff_554 = 832308740;// BOOL
    ICC_RISD_Pixel296_OnOff_554 = 832341509;// BOOL
    ICC_RISD_Pixel297_OnOff_554 = 832374278;// BOOL
    ICC_RISD_Pixel298_OnOff_554 = 832407047;// BOOL
    ICC_RISD_Pixel299_OnOff_554 = 832439816;// BOOL
    ICC_RISD_Pixel300_OnOff_554 = 832472585;// BOOL
    ICC_RISD_Pixel301_OnOff_554 = 832505354;// BOOL
    ICC_RISD_Pixel302_OnOff_554 = 832538123;// BOOL
    ICC_RISD_Pixel303_OnOff_554 = 832570892;// BOOL
    ICC_RISD_Pixel304_OnOff_554 = 832603661;// BOOL
    ICC_RISD_Pixel305_OnOff_554 = 832636430;// BOOL
    ICC_RISD_Pixel306_OnOff_554 = 832669199;// BOOL
    ICC_RISD_Pixel307_OnOff_554 = 832701968;// BOOL
    ICC_RISD_Pixel308_OnOff_554 = 832734737;// BOOL
    ICC_RISD_Pixel309_OnOff_554 = 832767506;// BOOL
    ICC_RISD_Pixel310_OnOff_554 = 832800275;// BOOL
    ICC_RISD_Pixel311_OnOff_554 = 832833044;// BOOL
    ICC_RISD_Pixel312_OnOff_554 = 832865813;// BOOL
    ICC_RISD_Pixel313_OnOff_554 = 832898582;// BOOL
    ICC_RISD_Pixel314_OnOff_554 = 832931351;// BOOL
    ICC_RISD_Pixel315_OnOff_554 = 832964120;// BOOL
    ICC_RISD_Pixel316_OnOff_554 = 832996889;// BOOL
    ICC_RISD_Pixel317_OnOff_554 = 833029658;// BOOL
    ICC_RISD_Pixel318_OnOff_554 = 833062427;// BOOL
    ICC_RISD_Pixel319_OnOff_554 = 833095196;// BOOL
    ICC_RISD_Pixel320_OnOff_554 = 833127965;// BOOL
    ICC_RISD_Pixel321_OnOff_554 = 833160734;// BOOL
    ICC_RISD_Pixel322_OnOff_554 = 833193503;// BOOL
    ICC_RISD_Pixel323_OnOff_554 = 833226272;// BOOL
    ICC_RISD_Pixel324_OnOff_554 = 833259041;// BOOL
    ICC_RISD_Pixel325_OnOff_554 = 833291810;// BOOL
    ICC_RISD_Pixel326_OnOff_554 = 833324579;// BOOL
    ICC_RISD_Pixel327_OnOff_554 = 833357348;// BOOL
    ICC_RISD_Pixel328_OnOff_554 = 833390117;// BOOL
    ICC_RISD_Pixel329_OnOff_554 = 833422886;// BOOL
    ICC_RISD_Pixel330_OnOff_554 = 833455655;// BOOL
    ICC_RISD_Pixel331_OnOff_554 = 833488424;// BOOL
    ICC_RISD_Pixel332_OnOff_554 = 833521193;// BOOL
    ICC_RISD_Pixel333_OnOff_554 = 833553962;// BOOL
    ICC_RISD_Pixel334_OnOff_554 = 833586731;// BOOL
    ICC_RISD_Pixel335_OnOff_554 = 833619500;// BOOL
    ICC_RISD_Pixel336_OnOff_554 = 833652269;// BOOL
    ICC_RISD_Pixel337_OnOff_554 = 833685038;// BOOL
    ICC_RISD_Pixel338_OnOff_554 = 833717807;// BOOL
    ICC_RISD_Pixel339_OnOff_554 = 833750576;// BOOL
    ICC_RISD_Pixel340_OnOff_554 = 833783345;// BOOL
    ICC_RISD_Pixel341_OnOff_554 = 833816114;// BOOL
    ICC_RISD_Pixel342_OnOff_554 = 833848883;// BOOL
    ICC_RISD_Pixel343_OnOff_554 = 833881652;// BOOL
    ICC_RISD_Pixel344_OnOff_554 = 833914421;// BOOL
    ICC_RISD_Pixel345_OnOff_554 = 833947190;// BOOL
    ICC_RISD_Pixel346_OnOff_554 = 833979959;// BOOL
    ICC_RISD_Pixel347_OnOff_554 = 834012728;// BOOL
    ICC_RISD_Pixel348_OnOff_554 = 834045497;// BOOL
    ICC_RISD_Pixel349_OnOff_554 = 834078266;// BOOL
    ICC_RISD_Pixel350_OnOff_554 = 834111035;// BOOL
    ICC_RISD_Pixel351_OnOff_554 = 834143804;// BOOL
    ICC_RISD_Pixel352_OnOff_554 = 834176573;// BOOL
    ICC_RISD_Pixel353_OnOff_554 = 834209342;// BOOL
    ICC_RISD_Pixel354_OnOff_554 = 834242111;// BOOL
    ICC_RISD_Pixel355_OnOff_554 = 834274880;// BOOL
    ICC_RISD_Pixel356_OnOff_554 = 834307649;// BOOL
    ICC_RISD_Pixel357_OnOff_554 = 834340418;// BOOL
    ICC_RISD_Pixel358_OnOff_554 = 834373187;// BOOL
    ICC_RISD_Pixel359_OnOff_554 = 834405956;// BOOL
    ICC_RISD_Pixel360_OnOff_554 = 834438725;// BOOL
    ICC_RISD_Pixel361_OnOff_554 = 834471494;// BOOL
    ICC_RISD_Pixel362_OnOff_554 = 834504263;// BOOL
    ICC_RISD_Pixel363_OnOff_554 = 834537032;// BOOL
    ICC_RISD_Pixel364_OnOff_554 = 834569801;// BOOL
    ICC_RISD_Pixel365_OnOff_554 = 834602570;// BOOL
    ICC_RISD_Pixel366_OnOff_554 = 834635339;// BOOL
    ICC_RISD_Pixel367_OnOff_554 = 834668108;// BOOL
    ICC_RISD_Pixel368_OnOff_554 = 834700877;// BOOL
    ICC_RISD_Pixel369_OnOff_554 = 834733646;// BOOL
    ICC_RISD_Pixel370_OnOff_554 = 834766415;// BOOL
    ICC_RISD_Pixel371_OnOff_554 = 834799184;// BOOL
    ICC_RISD_Pixel372_OnOff_554 = 834831953;// BOOL
    ICC_RISD_Pixel373_OnOff_554 = 834864722;// BOOL
    ICC_RISD_Pixel374_OnOff_554 = 834897491;// BOOL
    ICC_RISD_Pixel375_OnOff_554 = 834930260;// BOOL
    ICC_RISD_Pixel376_OnOff_554 = 834963029;// BOOL
    ICC_RISD_Pixel377_OnOff_554 = 834995798;// BOOL
    ICC_RISD_Pixel378_OnOff_554 = 835028567;// BOOL
    ICC_RISD_Pixel379_OnOff_554 = 835061336;// BOOL
    ICC_RISD_Pixel380_OnOff_554 = 835094105;// BOOL
    ICC_RISD_Pixel381_OnOff_554 = 835126874;// BOOL
    ICC_RISD_Pixel382_OnOff_554 = 835159643;// BOOL
    ICC_RISD_Pixel383_OnOff_554 = 835192412;// BOOL
    ICC_RISD_Pixel384_OnOff_554 = 835225181;// BOOL
    ICC_RISD_Pixel385_OnOff_554 = 835257950;// BOOL
    ICC_RISD_Pixel386_OnOff_554 = 835290719;// BOOL
    ICC_RISD_Pixel387_OnOff_554 = 835323488;// BOOL
    ICC_RISD_Pixel388_OnOff_554 = 835356257;// BOOL
    ICC_RISD_Pixel389_OnOff_554 = 835389026;// BOOL
    ICC_RISD_Pixel390_OnOff_554 = 835421795;// BOOL
    ICC_RISD_Pixel391_OnOff_554 = 835454564;// BOOL
    ICC_RISD_Pixel392_OnOff_554 = 835487333;// BOOL
    ICC_RISD_Pixel393_OnOff_554 = 835520102;// BOOL
    ICC_RISD_Pixel394_OnOff_554 = 835552871;// BOOL
    ICC_RISD_Pixel395_OnOff_554 = 835585640;// BOOL
    ICC_RISD_Pixel396_OnOff_554 = 835618409;// BOOL
    ICC_RISD_Pixel397_OnOff_554 = 835651178;// BOOL
    ICC_RISD_Pixel398_OnOff_554 = 835683947;// BOOL
    ICC_RISD_Pixel399_OnOff_554 = 835716716;// BOOL
    ICC_RISD_Pixel400_OnOff_554 = 835749485;// BOOL
    ICC_RISD_Pixel401_OnOff_554 = 835782254;// BOOL
    ICC_RISD_Pixel402_OnOff_554 = 835815023;// BOOL
    ICC_RISD_Pixel403_OnOff_554 = 835847792;// BOOL
    ICC_RISD_Pixel404_OnOff_554 = 835880561;// BOOL
    ICC_RISD_Pixel405_OnOff_554 = 835913330;// BOOL
    ICC_RISD_Pixel406_OnOff_554 = 835946099;// BOOL
    ICC_RISD_Pixel407_OnOff_554 = 835978868;// BOOL
    ICC_RISD_Pixel408_OnOff_554 = 836011637;// BOOL
    ICC_RISD_Pixel409_OnOff_554 = 836044406;// BOOL
    ICC_RISD_Pixel410_OnOff_554 = 836077175;// BOOL
    ICC_RISD_Pixel411_OnOff_554 = 836109944;// BOOL
    ICC_RISD_Pixel412_OnOff_554 = 836142713;// BOOL
    ICC_RISD_Pixel413_OnOff_554 = 836175482;// BOOL
    ICC_RISD_Pixel414_OnOff_554 = 836208251;// BOOL
    ICC_RISD_Pixel415_OnOff_554 = 836241020;// BOOL
    ICC_RISD_Pixel416_OnOff_554 = 836273789;// BOOL
    ICC_RISD_Pixel417_OnOff_554 = 836306558;// BOOL
    ICC_RISD_Pixel418_OnOff_554 = 836339327;// BOOL
    ICC_RISD_Pixel419_OnOff_554 = 836372096;// BOOL
    ICC_RISD_Pixel420_OnOff_554 = 836404865;// BOOL
    ICC_RISD_Pixel421_OnOff_554 = 836437634;// BOOL
    ICC_RISD_Pixel422_OnOff_554 = 836470403;// BOOL
    ICC_RISD_Pixel423_OnOff_554 = 836503172;// BOOL
    ICC_RISD_Pixel424_OnOff_554 = 836535941;// BOOL
    ICC_RISD_Pixel425_OnOff_554 = 836568710;// BOOL
    ICC_RISD_Pixel426_OnOff_554 = 836601479;// BOOL
    ICC_RISD_Pixel427_OnOff_554 = 836634248;// BOOL
    ICC_RISD_Pixel428_OnOff_554 = 836667017;// BOOL
    ICC_RISD_Pixel429_OnOff_554 = 836699786;// BOOL
    ICC_RISD_Pixel430_OnOff_554 = 836732555;// BOOL
    ICC_RISD_Pixel431_OnOff_554 = 836765324;// BOOL
    ICC_RISD_Pixel432_OnOff_554 = 836798093;// BOOL
    ICC_RISD_Pixel433_OnOff_554 = 836830862;// BOOL
    ICC_RISD_Pixel434_OnOff_554 = 836863631;// BOOL
    ICC_RISD_Pixel435_OnOff_554 = 836896400;// BOOL
    ICC_RISD_Pixel436_OnOff_554 = 836929169;// BOOL
    ICC_RISD_Pixel437_OnOff_554 = 836961938;// BOOL
    ICC_RISD_Pixel438_OnOff_554 = 836994707;// BOOL
    ICC_RISD_Pixel439_OnOff_554 = 837027476;// BOOL
    ICC_RISD_Pixel440_OnOff_554 = 837060245;// BOOL
    ICC_RISD_Pixel441_OnOff_554 = 837093014;// BOOL
    ICC_RISD_Pixel442_OnOff_554 = 837125783;// BOOL
    ICC_RISD_Pixel443_OnOff_554 = 837158552;// BOOL
    ICC_RISD_Pixel444_OnOff_554 = 837191321;// BOOL
    ICC_RISD_Pixel445_OnOff_554 = 837224090;// BOOL
    ICC_RISD_Pixel446_OnOff_554 = 837256859;// BOOL
    ICC_RISD_Pixel447_OnOff_554 = 837289628;// BOOL
    ICC_RISD_Pixel448_OnOff_554 = 837322397;// BOOL
    ICC_RISD_Pixel449_OnOff_554 = 837355166;// BOOL
    ICC_RISD_Pixel450_OnOff_554 = 837387935;// BOOL
    ICC_RISD_Pixel451_OnOff_554 = 837420704;// BOOL
    ICC_RISD_Pixel452_OnOff_554 = 837453473;// BOOL
    ICC_RISD_Pixel453_OnOff_554 = 837486242;// BOOL
    ICC_RISD_Pixel454_OnOff_554 = 837519011;// BOOL
    ICC_RISD_Pixel455_OnOff_554 = 837551780;// BOOL
    ICC_RISD_Pixel456_OnOff_554 = 837584549;// BOOL
    ICC_RISD_Pixel457_OnOff_554 = 837617318;// BOOL
    ICC_RISD_Pixel458_OnOff_554 = 837650087;// BOOL
    ICC_RISD_Pixel459_OnOff_554 = 837682856;// BOOL
    ICC_RISD_Pixel460_OnOff_554 = 837715625;// BOOL
    ICC_RISD_Pixel461_OnOff_554 = 837748394;// BOOL
    ICC_RISD_Pixel462_OnOff_554 = 837781163;// BOOL
    ICC_RISD_Pixel463_OnOff_554 = 837813932;// BOOL
    ICC_RISD_Pixel464_OnOff_554 = 837846701;// BOOL
    ICC_RISD_Pixel465_OnOff_554 = 837879470;// BOOL
    ICC_RISD_Pixel466_OnOff_554 = 837912239;// BOOL
    ICC_RISD_Pixel467_OnOff_554 = 837945008;// BOOL
    ICC_RISD_Pixel468_OnOff_554 = 837977777;// BOOL
    ICC_RISD_Pixel469_OnOff_554 = 838010546;// BOOL
    ICC_RISD_Pixel470_OnOff_554 = 838043315;// BOOL
    ICC_RISD_Pixel471_OnOff_554 = 838076084;// BOOL
    ICC_RISD_Pixel472_OnOff_554 = 838108853;// BOOL
    ICC_RISD_Pixel473_OnOff_554 = 838141622;// BOOL
    ICC_RISD_Pixel474_OnOff_554 = 838174391;// BOOL
    ICC_RISD_Pixel475_OnOff_554 = 838207160;// BOOL
    ICC_RISD_Pixel476_OnOff_554 = 838239929;// BOOL
    ICC_RISD_Pixel477_OnOff_554 = 838272698;// BOOL
    ICC_RISD_Pixel478_OnOff_554 = 838305467;// BOOL
    ICC_RISD_Pixel479_OnOff_554 = 838338236;// BOOL
    ICC_RISD_Pixel480_OnOff_554 = 838371005;// BOOL
    ICC_RISD_Pixel481_OnOff_554 = 838403774;// BOOL
    ICC_RISD_Pixel482_OnOff_554 = 838436543;// BOOL
    ICC_RISD_Pixel483_OnOff_554 = 838469312;// BOOL
    ICC_RISD_Pixel484_OnOff_554 = 838502081;// BOOL
    ICC_RISD_Pixel485_OnOff_554 = 838534850;// BOOL
    ICC_RISD_Pixel486_OnOff_554 = 838567619;// BOOL
    ICC_RISD_Pixel487_OnOff_554 = 838600388;// BOOL
    ICC_RISD_Pixel488_OnOff_554 = 838633157;// BOOL
    ICC_RISD_Pixel489_OnOff_554 = 838665926;// BOOL
    ICC_RISD_Pixel490_OnOff_554 = 838698695;// BOOL
    ICC_RISD_Pixel491_OnOff_554 = 838731464;// BOOL
    ICC_RISD_Pixel492_OnOff_554 = 838764233;// BOOL
    ICC_RISD_Pixel493_OnOff_554 = 838797002;// BOOL
    ICC_RISD_Pixel494_OnOff_554 = 838829771;// BOOL
    ICC_RISD_Pixel495_OnOff_554 = 838862540;// BOOL
    ICC_RISD_Pixel496_OnOff_554 = 838895309;// BOOL
    ICC_RISD_Pixel497_OnOff_554 = 838928078;// BOOL
    ICC_RISD_Pixel498_OnOff_554 = 838960847;// BOOL
    ICC_RISD_Pixel499_OnOff_554 = 838993616;// BOOL
    ICC_RISD_Pixel500_OnOff_554 = 839026385;// BOOL
    ICC_RISD_Pixel501_OnOff_554 = 839059154;// BOOL
    ICC_RISD_Pixel502_OnOff_554 = 839091923;// BOOL
    ICC_RISD_Pixel503_OnOff_554 = 839124692;// BOOL
    ICC_RISD_Pixel504_OnOff_554 = 839157461;// BOOL
    ICC_LKSSettingModSt_556 = 156468950;// UINT8
    ICC_LDWWarnType_556 = 156501719;// UINT8
    ICC_LDWLDPSnvtySet_556 = 156534488;// UINT8
    ICC_ELKSettingSt_556 = 156567257;// UINT8
    ICC_ICAEnableBtnSts_556 = 156600026;// UINT8
    ICC_FCWSettingSt_556 = 156632795;// UINT8
    ICC_AEBSettingSt_556 = 156665564;// UINT8
    ICC_ISLIWarnMod_556 = 156698333;// UINT8
    ICC_Customize_buttons_556 = 156731102;// UINT8
    ICC_AVM_TouchEvt_556 = 156763871;// UINT8
    ICC_AVM_BtnPressInputValueX_556 = 269453024;// UINT16
    ICC_AVM_BtnPressInputValueY_556 = 269485793;// UINT16
    ICC_SetMsgTotal_54A = 269518562;// UINT16
    ICC_SetMSDTotal_54A = 269551331;// UINT16
    ICC_SetCallTimeThreshold_54A = 269584100;// UINT16
    ICC_SetTodayCallTimeThreshold_54A = 269616869;// UINT16
    ICC_SetCurretnCallTime_54A = 269649638;// UINT16
    ICC_SetCurretnMsgThreshold_54A = 269682407;// UINT16
    ICC_SetMsgTotalThreshold_54A = 269715176;// UINT16
    ICC_SetMsdThreshold_54A = 269747945;// UINT16
    ICC_SetCurrenMsdCnt_54A = 269780714;// UINT16
    ICC_SetCustomMsgThreshold_54A = 269813483;// UINT16
    ICC_SetCurrentCustomMsgCnt_54A = 269846252;// UINT16
    ICC_flag_54C = 156796653;// UINT8
    ICC_ActiveTsp_Req_54C = 156829422;// UINT8
    ICC_ActiveSim_Req_54C = 156862191;// UINT8
    ICC_GetMac_req_54C = 839190256;// BOOL
    ICC_GetIccid_Vin_54C = 839223025;// BOOL
    ICC_GivePlatformNumber_Flag_54C = 839255794;// BOOL
    ICC_Platform_Phonenub1_54C = 156894963;// UINT8
    ICC_Platform_Phonenub2_54C = 156927732;// UINT8
    ICC_Platform_Phonenub3_54C = 156960501;// UINT8
    ICC_Platform_Phonenub4_54C = 156993270;// UINT8
    ICC_Platform_Phonenub5_54C = 157026039;// UINT8
    ICC_Platform_Phonenub6_54C = 157058808;// UINT8
    ICC_Platform_Phonenub7_54C = 157091577;// UINT8
    ICC_Platform_Phonenub8_54C = 157124346;// UINT8
    ICC_Platform_Phonenub9_54C = 157157115;// UINT8
    ICC_Platform_Phonenub10_54C = 157189884;// UINT8
    ICC_Platform_Phonenub11_54C = 157222653;// UINT8
    ICC_Platform_Phonenub12_54C = 157255422;// UINT8
    ICC_Platform_Phonenub13_54C = 157288191;// UINT8
    ICC_Platform_Phonenub14_54C = 157320960;// UINT8
    ICC_Platform_Phonenub15_54C = 157353729;// UINT8
    ICC_Platform_Phonenub16_54C = 157386498;// UINT8
    ICC_Platform_Phonenub17_54C = 157419267;// UINT8
    ICC_Platform_Phonenub18_54C = 157452036;// UINT8
    ICC_Platform_Phonenub19_54C = 157484805;// UINT8
    ICC_Platform_Phonenub20_54C = 157517574;// UINT8
    ICC_Mac_Flag_54C = 839288583;// BOOL
    ICC_Mac0_54C = 157550344;// UINT8
    ICC_Mac1_54C = 157583113;// UINT8
    ICC_Mac2_54C = 157615882;// UINT8
    ICC_Mac3_54C = 157648651;// UINT8
    ICC_Mac4_54C = 157681420;// UINT8
    ICC_Mac5_54C = 157714189;// UINT8
    ICC_MemAttributeFL_5AD = 157746958;// UINT8
    ICC_MemAttributeFR_5AD = 157779727;// UINT8
    ICC_MemAttributeRL_5AD = 157812496;// UINT8
    ICC_MemAttributeRR_5AD = 157845265;// UINT8
    ICC_FLSeatHeatLvlCmd2_564 = 157878034;// UINT8
    ICC_RLSeatHeatLvlCmd2_564 = 157910803;// UINT8
    ICC_FLSeatVentLvlCmd2_564 = 157943572;// UINT8
    ICC_RLSeatVentLvlCmd2_564 = 157976341;// UINT8
    ICC_FRSeatHeatLvlCmd2_564 = 158009110;// UINT8
    ICC_RRSeatHeatLvlCmd2_564 = 158041879;// UINT8
    ICC_FRSeatVentLvlCmd2_564 = 158074648;// UINT8
    ICC_RRSeatVentLvlCmd2_564 = 158107417;// UINT8
    ICC_VINcode_10_573 = 158140186;// UINT8
    ICC_VINcode_11_573 = 158172955;// UINT8
    ICC_VINcode_12_573 = 158205724;// UINT8
    ICC_VINcode_13_573 = 158238493;// UINT8
    ICC_VINcode_14_573 = 158271262;// UINT8
    ICC_VINcode_15_573 = 158304031;// UINT8
    ICC_VINcode_16_573 = 158336800;// UINT8
    ICC_VINcode_17_573 = 158369569;// UINT8
    ICC_Auth_IMMO_45C = 536971042;// UINT64
    VCC_Teach_IMMO_47F = 537003811;// UINT64
    ICC_TotalFaultNum_520 = 158402340;// UINT8
    ICC_FrameIndex_520 = 158435109;// UINT8
    ICC_SupplierNum_520 = 158467878;// UINT8
    ICC_FaultNum1_520 = 269879079;// UINT16
    ICC_FaultNum2_520 = 269911848;// UINT16
    ICC_FaultNum3_520 = 269944617;// UINT16
    ICC_FaultNum4_520 = 269977386;// UINT16
    ICC_FaultNum5_520 = 270010155;// UINT16
    ICC_FaultNum6_520 = 270042924;// UINT16
    ICC_FaultNum7_520 = 270075693;// UINT16
    ICC_FaultNum8_520 = 270108462;// UINT16
    ICC_FaultNum9_520 = 270141231;// UINT16
    ICC_FaultNum10_520 = 270174000;// UINT16
    ICC_FaultNum11_520 = 270206769;// UINT16
    ICC_FaultNum12_520 = 270239538;// UINT16
    ICC_FaultNum13_520 = 270272307;// UINT16
    ICC_FaultNum14_520 = 270305076;// UINT16
    ICC_FaultNum15_520 = 270337845;// UINT16
    ICC_FaultNum16_520 = 270370614;// UINT16
    ICC_FaultNum17_520 = 270403383;// UINT16
    ICC_FaultNum18_520 = 270436152;// UINT16
    ICC_FaultNum19_520 = 270468921;// UINT16
    ICC_FaultNum20_520 = 270501690;// UINT16
    Xcp_ICC_REQ_481 = 537036603;// UINT64
    Xcp_ICC_RES_482 = 537069372;// UINT64
    RearDefrostSts_35B = 839321405;// BOOL
    FLZCU_9_PowerMode_49D = 158500670;// UINT8
    FLZCU_9_ArmingSts_49D = 158533439;// UINT8
    RearViewFoldSts_406 = 158566208;// UINT8
    FLZCU_AlrmSts_406 = 158598977;// UINT8
    FLZCU_SteeringMode_406 = 158631746;// UINT8
    FLZCU_SuspensionDamping_406 = 158664515;// UINT8
    FLZCU_SuspensionHeight_406 = 158697284;// UINT8
    FLZCU_OBDDiagOccupy_406 = 158730053;// UINT8
    FLZCU_AutoFoldSts_42F = 839354182;// BOOL
    FLZCU_AutolockSts_42F = 839386951;// BOOL
    FLZCU_FGHeat_Req_CmdFeedback_42F = 839419720;// BOOL
    FLZCU_WindowInhibitSts_42F = 839452489;// BOOL
    FLZCU_AutoReadLightSts_42F = 839485258;// BOOL
    FLZCU_HeatingSW_42F = 839518027;// BOOL
    FLZCU_AutoHeatingFb_42F = 839550796;// BOOL
    FLZCU_SteerNeedMemory_42F = 839583565;// BOOL
    FLZCU_RearNeedMemory_42F = 839616334;// BOOL
    FLZCU_FLHandleFoldedStatus_42F = 158762831;// UINT8
    FLZCU_RLHandleFoldedStatus_42F = 158795600;// UINT8
    FLZCU_FRHandleFoldedStatus_42F = 158828369;// UINT8
    FLZCU_RRHandleFoldedStatus_42F = 158861138;// UINT8
    FLZCU_RvsExtMirrFbSts_42F = 158893907;// UINT8
    FLZCU_LightMainSwitchSts_42F = 158926676;// UINT8
    FLZCU_BeamDelaySts_42F = 158959445;// UINT8
    FLZCU_LowBeamHighSts_42F = 158992214;// UINT8
    FLZCU_WipeSensitivitySts_42F = 159024983;// UINT8
    FLZCU_WipeMaintenanceSWSts_42F = 159057752;// UINT8
    FLZCU_ImmoRequest_42F = 159090521;// UINT8
    FLZCU_AlarmWarnSetSW_42F = 159123290;// UINT8
    FLZCU_CleanModeStatus_42F = 159156059;// UINT8
    FLZCU_FLReleaseLockSts_42F = 159188828;// UINT8
    FLZCU_RearFogLightSts_42F = 839649117;// BOOL
    FLZCU_FRFoglightSts_42F = 839681886;// BOOL
    FLZCU_FLFoglightSts_42F = 839714655;// BOOL
    FLZCU_RearFogLightFaults_42F = 839747424;// BOOL
    FLZCU_HighBeamFaults_42F = 839780193;// BOOL
    FLZCU_HighBeamSts_42F = 839812962;// BOOL
    FLZCU_LowBeamSts_42F = 839845731;// BOOL
    FLZCU_ParkUnlockEnableFb_42F = 839878500;// BOOL
    FLZCU_RLReleaseLockSts_42F = 159221605;// UINT8
    FLZCU_EasyEntryExitFb_42F = 159254374;// UINT8
    FLZCU_HMASwSts_42F = 839911271;// BOOL
    FLZCU_DaytimeLight_42F = 159287144;// UINT8
    FLZCU_ParkLightFaults_42F = 839944041;// BOOL
    FLZCU_LbatipVehPwrMod_42F = 839976810;// BOOL
    FLZCU_LVPowICDsp_42F = 159319915;// UINT8
    FLZCU_LockCarWinCloseFb_42F = 159352684;// UINT8
    FLZCU_BrkLightSts_42F = 159385453;// UINT8
    FLZCU_ReverseLightSts_42F = 159418222;// UINT8
    FLZCU_LowWashLqdWarning_42F = 159450991;// UINT8
    FLZCU_SteerWheelHeatingFb_42F = 159483760;// UINT8
    FLZCU_FrontFogLightSts_42F = 840009585;// BOOL
    FLZCU_NapAreaFb_42F = 159516530;// UINT8
    FLZCU_NapTimeFb_42F = 159549299;// UINT8
    FLZCU_NapAlarmStatus_42F = 159582068;// UINT8
    FLZCU_NapFailCause_42F = 159614837;// UINT8
    FLZCU_NapStatusFb_42F = 159647606;// UINT8
    FLZCU_CleanModeFailCause_42F = 159680375;// UINT8
    FLZCU_NapClosePrompt_42F = 159713144;// UINT8
    FLZCU_AutoWipingInhibit_42F = 159745913;// UINT8
    FLZCU_RLWinSts_42F = 159778682;// UINT8
    FLZCU_FLWinSts_42F = 159811451;// UINT8
    FLZCU_DaytimeLightFaults_42F = 159844220;// UINT8
    FLZCU_FrontFogLightFaults_42F = 159876989;// UINT8
    FLZCU_HomeLinkWelLightSetSts_42F = 159909758;// UINT8
    FLZCU_HomeLinkArmHornSetSts_42F = 159942527;// UINT8
    FLZCU_WelcomeOpenStas_42F = 159975296;// UINT8
    FLZCU_WALOpenStas_42F = 160008065;// UINT8
    FLZCU_UIROpenStas_42F = 160040834;// UINT8
    FLZCU_ExtLiSwtClsSts_42F = 160073603;// UINT8
    FLZCU_Totalodometerbackup_581 = 402753412;// UINT32
    FLZCU_FLSeatHeiFb_4BB = 160106373;// UINT8
    FLZCU_FLSeatLvlFb_4BB = 160139142;// UINT8
    FLZCU_FLSeatBackAgFb_4BB = 160171911;// UINT8
    FLZCU_FLSeatCushFb_4BB = 160204680;// UINT8
    FLZCU_FLSeatLegSpprtFb_4BB = 160237449;// UINT8
    FLZCU_SeatNeedMemory_4BB = 160270218;// UINT8
    FLZCU_MemoryFb_4BB = 160302987;// UINT8
    FLZCU_RecoverFb_4BB = 160335756;// UINT8
    FLZCU_VINcode_10_595 = 160368525;// UINT8
    FLZCU_VINcode_11_595 = 160401294;// UINT8
    FLZCU_VINcode_12_595 = 160434063;// UINT8
    FLZCU_VINcode_13_595 = 160466832;// UINT8
    FLZCU_VINcode_14_595 = 160499601;// UINT8
    FLZCU_VINcode_15_595 = 160532370;// UINT8
    FLZCU_VINcode_16_595 = 160565139;// UINT8
    FLZCU_VINcode_17_595 = 160597908;// UINT8
    LHTurnlightSts_0x430_430 = 840042389;// BOOL
    RHTurnlightSts_0x430_430 = 840075158;// BOOL
    LHFdoorSts_0x430_430 = 160630679;// UINT8
    LHFSeatBeltSW_0x430_430 = 840107928;// BOOL
    TrunkSts_430 = 160663449;// UINT8
    RLWinPosn_430 = 160696218;// UINT8
    SeatHeatLevelsFL_430 = 160728987;// UINT8
    SeatHeatLevelsRL_430 = 160761756;// UINT8
    SeatVentLevelsFL_430 = 160794525;// UINT8
    SeatVentLevelsRL_430 = 160827294;// UINT8
    FLWinPosn_430 = 160860063;// UINT8
    FLZCU_FrontWiperMaintenanceSts_430 = 160892832;// UINT8
    FLZCU_RearWiperMaintenanceSts_430 = 160925601;// UINT8
    VCU_2_G_CRC_4B9 = 160958370;// UINT8
    VCU_2_G_RollgCntr_4B9 = 160991139;// UINT8
    VCU_2_G_Resd_4B9 = 161023908;// UINT8
    VCU_2_G_GasPedalPosInvalidData_4B9 = 840140709;// BOOL
    VCU_2_G_PowerLimit_4B9 = 840173478;// BOOL
    VCU_2_G_BrakeLightReq_4B9 = 840206247;// BOOL
    VCU_2_G_VehicleSystemFailure_4B9 = 840239016;// BOOL
    VCU_2_G_BootloadSts_4B9 = 840271785;// BOOL
    VCU_2_G_DischgSts_4B9 = 840304554;// BOOL
    VCU_2_G_BrakePedalStsValidData_4B9 = 840337323;// BOOL
    VCU_2_G_ExhibitionMod_4B9 = 840370092;// BOOL
    VCU_2_G_BrakePedalSts_4B9 = 840402861;// BOOL
    VCU_2_G_DCDCEnable_4B9 = 840435630;// BOOL
    VCU_2_G_HVReady_4B9 = 840468399;// BOOL
    Res_VCU_2_G_Brake_state_4B9 = 840501168;// BOOL
    VCU_2_G_VCUEnergyflow_4B9 = 161056689;// UINT8
    VCU_2_G_DrvGearShiftFailureIndcn_4B9 = 161089458;// UINT8
    VCU_2_G_GasPedalPosition_4B9 = 270534579;// UINT16
    VCU_CLTC_RangeAval_4CD = 270567348;// UINT16
    VCU_DynamicRangeAval_4CD = 270600117;// UINT16
    VCU_HighspdRangeAval_4CD = 270632886;// UINT16
    VCU_WLTC_RangeAval_4CD = 270665655;// UINT16
    VCU_TenECAver_4EF = 270698424;// UINT16
    VCU_twenty_fiveECAver_4EF = 270731193;// UINT16
    VCU_fiftyECAver_4EF = 270763962;// UINT16
    HCU_EmPwr_40A = 161122235;// UINT8
    HCU_PowerModeFed_40A = 161155004;// UINT8
    HCU_PowertrainFault_40A = 840533949;// BOOL
    HCU_PowerModeChangeFail_40A = 840566718;// BOOL
    HCU_ChargingSts_40A = 161187775;// UINT8
    HCU_EnergyFlow_414 = 161220544;// UINT8
    HCU_SetSOCFed_414 = 161253313;// UINT8
    HCU_2_G_LowBatteryInfo_414 = 161286082;// UINT8
    Fusa_4B4_5_4B4 = 161318851;// UINT8
    Fusa_4B4_4_4B4 = 270796740;// UINT16
    Fusa_4B4_3_4B4 = 161351621;// UINT8
    Fusa_4B4_2_4B4 = 270829510;// UINT16
    Fusa_4B4_1_4B4 = 161384391;// UINT8
    VCU_1_G_CRC_4B4 = 161417160;// UINT8
    VCU_1_G_RollgCntr_4B4 = 161449929;// UINT8
    VCU_1_G_Pwr_4B4 = 270862282;// UINT16
    VCU_1_G_PTReady_4B4 = 840599499;// BOOL
    VCU_1_G_DriveMode_4B4 = 161482700;// UINT8
    VCU_1_G_BookChrgSetResponse_4B4 = 161515469;// UINT8
    VCU_1_G_HvBattKeepWarmActiveReq_4B4 = 161548238;// UINT8
    VCU_1_G_PRNDGearAct_4B4 = 161581007;// UINT8
    VCU_TowingMode_421 = 161613776;// UINT8
    VCU_EngyConsuPer50Km_421 = 270895057;// UINT16
    VCU_BookChrgSetFailRes_421 = 161646546;// UINT8
    VCU_BookChrgFailRes_421 = 161679315;// UINT8
    VCU_BookChrgStsFb_421 = 161712084;// UINT8
    HCU_EngineSelfMaintain_421 = 161744853;// UINT8
    EBS_U_BATT_413 = 270927830;// UINT16
    FLZCU_CarMode_3FE = 161777623;// UINT8
    FLZCU_SteerWhlposSts_3FE = 161810392;// UINT8
    FLZCU_MedStatusFb_3FE = 161843161;// UINT8
    FLZCU_MedFailCause_3FE = 161875930;// UINT8
    FLZCU_MedAreaFb_3FE = 161908699;// UINT8
    FLZCU_MedClosePrompt_3FE = 161941468;// UINT8
    FLZCU_NapArea2Fb_3FE = 161974237;// UINT8
    FLZCU_2LSeatLvltargetCmd_3FE = 162007006;// UINT8
    FLZCU_2LSeatBackAgtargetCmd_3FE = 162039775;// UINT8
    FLZCU_2LSeatLegSpprttargetCmd_3FE = 162072544;// UINT8
    FLZCU_FLWinThermalStatus_3FE = 840632289;// BOOL
    FLZCU_RLWinThermalStatus_3FE = 840665058;// BOOL
    FLZCU_MonoAmbLigSts_3FE = 840697827;// BOOL
    FLZCU_LogoChargingSts_3FE = 162105316;// UINT8
    FLZCU_2RSeatLvltargetCmd_3FE = 162138085;// UINT8
    FLZCU_2RSeatBackAgtargetCmd_3FE = 162170854;// UINT8
    FLZCU_2RSeatLegSpprttargetCmd_3FE = 162203623;// UINT8
    FLZCU_SeatVentLvlFL2_3FE = 162236392;// UINT8
    FLZCU_SeatVentLvlRL2_3FE = 162269161;// UINT8
    FLZCU_SeatHeatLvlFL2_3FE = 162301930;// UINT8
    FLZCU_SeatHeatLvlRL2_3FE = 162334699;// UINT8
    FLZCU_FLSitPosnSts_3FE = 162367468;// UINT8
    FLZCU_SteerOverHeatWarn_3FE = 840730605;// BOOL
    SAM_1_SteeringAngleSpeed_3FC = 162400238;// UINT8
    SAM_1_SteeringAngle_3FC = 270960623;// UINT16
    BMSH_MaxChgPwrPeak_3FC = 270993392;// UINT16
    BMSH_MaxDchgPwrPeak_3FC = 271026161;// UINT16
    FMCU_8_ISGF_SpdAct_3FC = 271058930;// UINT16
    RMCU_1_Speed_3FC = 271091699;// UINT16
    SAM_1_SteeringAngleVD_3FC = 840763380;// BOOL
    SAM_1_SteeringAngleSpeedVD_3FC = 840796149;// BOOL
    CSA_2_AllWarningInfo_3FC = 162433014;// UINT8
    RELC_RoofCampLampConfigDisp_3FC = 840828919;// BOOL
    RELC_TopLightConfigDisp_3FC = 840861688;// BOOL
    EELC_APillarLightConfigDisp_3FC = 840894457;// BOOL
    RELC_RoofCampLampStsL_3FC = 162465786;// UINT8
    RELC_RoofCampLampStsR_3FC = 162498555;// UINT8
    RELC_TopLightSts_3FC = 162531324;// UINT8
    EELC_APillarYellowAmbientSts_3FC = 162564093;// UINT8
    EELC_APillarWhiteAmbientSts_3FC = 162596862;// UINT8
    EELC_APillarSpotLampSts_3FC = 162629631;// UINT8
    HCU_4_G_AvgFuCns_476 = 271124480;// UINT16
    HCU_4_G_AvgEnergyCns_476 = 271157249;// UINT16
    HCU_4_G_SumEgyCns_476 = 271190018;// UINT16
    HCU_V2lDisChgBck_522 = 162662403;// UINT8
    HCU_DisChgMemFed_522 = 162695172;// UINT8
    HCU_V2VDisChgBck_522 = 162727941;// UINT8
    HCU_V2l_IntlDisChgBck_522 = 162760710;// UINT8
    HCU_FuelDetnOpDefeated_522 = 162793479;// UINT8
    HCU_FuelDetnModSwtFed_522 = 162826248;// UINT8
    HCU_FuelDetnStateFed_522 = 162859017;// UINT8
    HCU_HVDownRepairModeFed_522 = 162891786;// UINT8
    HCU_TrailerMode_522 = 162924555;// UINT8
    HCU_SumFuCns_522 = 271222796;// UINT16
    HCU_SumElecCns_522 = 271255565;// UINT16
    HCU_DrivePowerDispSetFed_522 = 162957326;// UINT8
    HCU_KeyDriveModememory_522 = 162990095;// UINT8
    HCU_DRIFTModeSts_522 = 163022864;// UINT8
    HCU_EXPERTEcoFed_522 = 163055633;// UINT8
    HCU_4LModeFed_522 = 840927250;// BOOL
    HCU_EXPERTNormFed_522 = 163088403;// UINT8
    HCU_SingleRegnRmn_522 = 840960020;// BOOL
    HCU_EXPERTSportFed_522 = 163121173;// UINT8
    HCU_CrossaxisFed_522 = 163153942;// UINT8
    HCU_EXPERTRWDFed_522 = 163186711;// UINT8
    HCU_EXPERTAWDFed_522 = 163219480;// UINT8
    HCU_EXPERTAutoFed_522 = 163252249;// UINT8
    HCU_CLIMBFed_522 = 163285018;// UINT8
    HCU_StopChrgnFed_522 = 163317787;// UINT8
    HCU_StopChrgnModeFed_522 = 163350556;// UINT8
    HCU_ChrgnFctMemSt_522 = 163383325;// UINT8
    HCU_LVPowSupSYSAbn_lit_522 = 840992798;// BOOL
    HCU_IMMOReleaseSignal_522 = 841025567;// BOOL
    HCU_PowerCut_522 = 841058336;// BOOL
    HCU_ParkingChargeReminder_522 = 841091105;// BOOL
    HCU_HEV_FunctionLimit_522 = 841123874;// BOOL
    HCU_SetModeNotChange_522 = 841156643;// BOOL
    HCU_UTURNSnowFed_522 = 841189412;// BOOL
    HCU_DriveInhibit_GearP_522 = 841222181;// BOOL
    HCU_AccleAbility_Limit_522 = 841254950;// BOOL
    HCU_VehicleSpeedMax_Limit_522 = 841287719;// BOOL
    HCU_EngineRunClmReq_522 = 841320488;// BOOL
    HCU_DisconnectChagWarn_522 = 841353257;// BOOL
    HCU_PowerModeChangeInhibit_522 = 841386026;// BOOL
    HCU_DisChargeStopFault_522 = 163416107;// UINT8
    HCU_DedicatedModeInhibit_522 = 841418796;// BOOL
    HCU_LeaveVehicleWarning_522 = 841451565;// BOOL
    HCU_TowingModeEna_522 = 841484334;// BOOL
    HCU_PowerLevel_522 = 163448879;// UINT8
    HCU_FrntEDLSts_522 = 163481648;// UINT8
    HCU_RearEDLSts_522 = 163514417;// UINT8
    HCU_DriveMode_JT_522 = 163547186;// UINT8
    HCU_CruiseLimitSpeedValue_522 = 271288371;// UINT16
    HCU_CCOSwtFed_522 = 163579956;// UINT8
    HCU_SinglePedalMemFed_522 = 841517109;// BOOL
    HCU_Pwr_522 = 271321142;// UINT16
    HCU_SocManageFed_522 = 163612727;// UINT8
    HCU_EgyRegenLevel_522 = 163645496;// UINT8
    HCU_DrvGearShiftFailureIndcn_522 = 163678265;// UINT8
    HCU_EXPERTEESPSet_522 = 163711034;// UINT8
    HCU_UTURNSandFed_522 = 841549883;// BOOL
    HCU_CruiseControlStatus_522 = 163743804;// UINT8
    HCU_LTCDispFed_522 = 163776573;// UINT8
    HCU_UTURNMudFed_522 = 841582654;// BOOL
    HCU_UTURNGrassFed_522 = 841615423;// BOOL
    HCU_EngyFlowToDisp_522 = 163809344;// UINT8
    HCU_DriveModeChangeFail_522 = 841648193;// BOOL
    HCU_iCCO_Warn_522 = 163842114;// UINT8
    HCU_iTAS_Warn_522 = 163874883;// UINT8
    HCU_DraggingModeRemind_522 = 163907652;// UINT8
    HCU_25_EDLFed_522 = 163940421;// UINT8
    HCU_25_Crossaxis_Warn_522 = 163973190;// UINT8
    HCU_iTAS_Active_522 = 164005959;// UINT8
    HCU_ExterTempLowHint_522 = 164038728;// UINT8
    HCU_25_iTAS_SlopWarn_522 = 164071497;// UINT8
    HCU_iTASSceneReq_522 = 164104266;// UINT8
    HCU_DrivingModReq_522 = 164137035;// UINT8
    HCU_25_VehSpdOffroadRmn_522 = 841680972;// BOOL
    HCU_25_LaunchMod_Warn_522 = 164169805;// UINT8
    HCU_25_SetVehSpdOffroad_522 = 164202574;// UINT8
    HCU_25_iDRIFT_Warn_522 = 164235343;// UINT8
    VCC_2_ConfigurationMessageTrg_433 = 841713744;// BOOL
    VCC_3_CRC_470 = 164268113;// UINT8
    VCC_3_RollgCntr_470 = 164300882;// UINT8
    VCC_3_Resd1_470 = 164333651;// UINT8
    VCC_3_SN_470 = 164366420;// UINT8
    TDL_SwitchSts_4AC = 164399189;// UINT8
    TDL_ColourModeSts_4AC = 164431958;// UINT8
    TDL_RhythmSwSts_4AC = 164464727;// UINT8
    TDL_ApiluminanceSts_4AC = 164497496;// UINT8
    TDL_VioceFunctionDone_4AC = 164530265;// UINT8
    TDL_FlowLightSwSts_4AC = 164563034;// UINT8
    TDL_FlowLightModeSts_4AC = 164595803;// UINT8
    TDL_RhythmModeSts_4AC = 164628572;// UINT8
    TDL_256ColourSts_4AC = 271353949;// UINT16
    FuelTankLidSts_499 = 841746526;// BOOL
    FuelTankLidLockFailureSts_499 = 841779295;// BOOL
    FuelTankLidSensorFailureSts_499 = 841812064;// BOOL
    CWC_1_CRC1_455 = 164661345;// UINT8
    CWC_1_RollgCntr1_455 = 164694114;// UINT8
    CWC_1_Resd1_455 = 164726883;// UINT8
    CWC_ChargingSts_455 = 164759652;// UINT8
    CWC_workingSts_455 = 841844837;// BOOL
    CWC_Phoneforgotten_455 = 841877606;// BOOL
    CWC_Phoneforgotten_ON_OFF_Sts_455 = 841910375;// BOOL
    CWC_1_ChrgFlt_455 = 164792424;// UINT8
    CWC_ChargingSts_R_455 = 164825193;// UINT8
    CWC_workingSts_R_455 = 841943146;// BOOL
    CWC_Phoneforgotten_R_455 = 841975915;// BOOL
    CWC_Phoneforgotten_ON_OFF_Sts_R_455 = 842008684;// BOOL
    CWC_1_ChrgFlt_R_455 = 164857965;// UINT8
    TBOX_1_BookChrgActiveReq_4CF = 164890734;// UINT8
    TBOX_BookChrgSts_4CF = 164923503;// UINT8
    TBOX_HvBattKeepWarmActiveReq_4CF = 164956272;// UINT8
    TBOX_BookChrgSetReq_4CF = 164989041;// UINT8
    TBOX_StopChrgReq_4CF = 842041458;// BOOL
    TBOX_KeepWarmSet_4CF = 165021811;// UINT8
    TBOX_HV_Req_4CF = 165054580;// UINT8
    CurrentTimeYear_0x508_508 = 165087349;// UINT8
    CurrentTimeMonth_0x508_508 = 165120118;// UINT8
    CurrentTimeDay_0x508_508 = 165152887;// UINT8
    CurrentTimeHour_0x508_508 = 165185656;// UINT8
    CurrentTimeMinute_0x508_508 = 165218425;// UINT8
    CurrentTimeSecond_0x508_508 = 165251194;// UINT8
    TBOX_PetmodeSwitch_427 = 165283963;// UINT8
    TBOX_BkChrgStartTimeDay_47C = 165316732;// UINT8
    TBOX_BkChrgStartTimeeHour_47C = 165349501;// UINT8
    TBOX_BkChrgStartTimeMin_47C = 165382270;// UINT8
    TBOX_BkChrgStartTimeMonth_47C = 165415039;// UINT8
    TBOX_BkChrgStartTimeYear_47C = 271386752;// UINT16
    TBOX_BkChrgDuration_47C = 271419521;// UINT16
    TBOX_BkChrgCycleType_47C = 165447810;// UINT8
    TBOX_BkChrgCycleMon_47C = 165480579;// UINT8
    TBOX_BkChrgCycleTues_47C = 165513348;// UINT8
    TBOX_BkChrgCycleWen_47C = 165546117;// UINT8
    TBOX_BkChrgCycleThur_47C = 165578886;// UINT8
    TBOX_BkChrgCycleFri_47C = 165611655;// UINT8
    TBOX_BkChrgCycleSat_47C = 165644424;// UINT8
    TBOX_BkChrgCycleSun_47C = 165677193;// UINT8
    TBOX_KeepWarmStrt_47C = 165709962;// UINT8
    TBOX_KeepWarmStrtHour_47C = 165742731;// UINT8
    TBOX_KeepWarmStrtMin_47C = 165775500;// UINT8
    Fusa_48A_3_48A = 402786445;// UINT32
    Fusa_48A_2_48A = 842074254;// BOOL
    Fusa_48A_1_48A = 165808271;// UINT8
    ONEBOX_1_G_CRC_48A = 165841040;// UINT8
    ONEBOX_1_G_RollgCntr_48A = 165873809;// UINT8
    VehicleSpeedVSOSig_48A = 271452306;// UINT16
    VehicleSpeedVSOSigValidData_48A = 842107027;// BOOL
    ESPSwitchStatus_48A = 842139796;// BOOL
    ABSActive_48A = 842172565;// BOOL
    ABSFailSts_48A = 842205334;// BOOL
    EBDFailSts_48A = 842238103;// BOOL
    TCSFailSts_48A = 842270872;// BOOL
    VDCFailSts_48A = 842303641;// BOOL
    TCSActive_48A = 842336410;// BOOL
    VDCActive_48A = 842369179;// BOOL
    BLRequestController_48A = 842401948;// BOOL
    FuelRollingCounter_4B2 = 165906589;// UINT8
    MILSts_4B2 = 165939358;// UINT8
    GPFWarning_4B2 = 165972127;// UINT8
    EngineSpeed_4B2 = 271485088;// UINT16
    EngineSpeedValidData_4B2 = 842434721;// BOOL
    EPCSts_4B2 = 842467490;// BOOL
    EMS_1_G_EngineSts_4B2 = 842500259;// BOOL
    EMS_EngineSts_Actual_4B2 = 842533028;// BOOL
    OilPressureWarningLamp_4B2 = 842565797;// BOOL
    RHFDoorSts_2AB = 166004902;// UINT8
    FRZCU_1_ParkLightSts_2AB = 842598567;// BOOL
    FRWinPosn_2AB = 166037672;// UINT8
    RRWinPosn_2AB = 166070441;// UINT8
    RHRDoorSts_2AB = 166103210;// UINT8
    ACU_3_LongitudinalAccelerationN_21 = 271517867;// UINT16
    ACU_3_LongitudinalAccelerationVDD_21 = 166135980;// UINT8
    EngineCoolantTemperature_278 = 166168749;// UINT8
    ASU_1_ASUSysFailrSts_4C1 = 166201518;// UINT8
    ASU_1_SuspCurrentLvl_4C1 = 166234287;// UINT8
    ASU_1_ECASSysSts_4C1 = 166267056;// UINT8
    ASU_1_ECASErrSts_4C1 = 166299825;// UINT8
    ASU_1_SuspTarLvl_4C1 = 166332594;// UINT8
    ASU_1_EasyEntryEna_4C1 = 842631347;// BOOL
    ASU_1_CDCErrSts_4C1 = 842664116;// BOOL
    ASU_1_RearaxlewithTaildoorFb_4C1 = 842696885;// BOOL
    ASU_1_Overheat_warning_4C1 = 842729654;// BOOL
    ASU_1_MaintainMod_4C1 = 166365367;// UINT8
    ASU_1_drivemodeheightlevFb_4C1 = 166398136;// UINT8
    ASU_1_HMIFailFb_4C1 = 166430905;// UINT8
    ASU_1_AssistPass_4C1 = 842762426;// BOOL
    BMS_1_CRC1_102 = 166463675;// UINT8
    BMS_1_RollgCntr1_102 = 166496444;// UINT8
    BMS_1_Resd1_102 = 166529213;// UINT8
    BMS_PackVoltage_102 = 271550654;// UINT16
    BMS_PackCurrent_102 = 271583423;// UINT16
    BMS_HvBattSts_102 = 166561984;// UINT8
    BMS_SysSelfCheck_102 = 166594753;// UINT8
    BMS_HVBattThermRunaway_102 = 166627522;// UINT8
    BMS_1_CRC2_102 = 166660291;// UINT8
    BMS_1_RollgCntr2_102 = 166693060;// UINT8
    BMS_1_Resd2_102 = 166725829;// UINT8
    BMS_ChargeVoltageReq_102 = 271616198;// UINT16
    BMS_ChgCurrReq_102 = 271648967;// UINT16
    BMS_InfraDcUActDc_102 = 271681736;// UINT16
    BMS_1_CRC3_102 = 166758601;// UINT8
    BMS_1_RollgCntr3_102 = 166791370;// UINT8
    BMS_1_Resd3_102 = 166824139;// UINT8
    BMS_InfraDcIActDc_102 = 271714508;// UINT16
    BMS_HvBattDynChrgnILim_102 = 271747277;// UINT16
    BMS_HvBattUChrgnLim_102 = 271780046;// UINT16
    BMS_1_CRC4_102 = 166856911;// UINT8
    BMS_1_RollgCntr4_102 = 166889680;// UINT8
    BMS_1_Resd4_102 = 166922449;// UINT8
    BMS_DChrgAPlusSt_102 = 842795218;// BOOL
    BMS_ChrgDcIAvl_102 = 271812819;// UINT16
    BMS_Insulation_R_102 = 271845588;// UINT16
    BMS_ChrgDcCnctrDetd_102 = 166955221;// UINT8
    BMS_DCChgMode_102 = 166987990;// UINT8
    BMS_ChrgDcChrgnFltStopReas_102 = 167020759;// UINT8
    BMS_1_CRC5_102 = 167053528;// UINT8
    BMS_1_RollgCntr5_102 = 167086297;// UINT8
    BMS_1_Resd5_102 = 167119066;// UINT8
    BMS_ChargeRequest_Mode_102 = 167151835;// UINT8
    BMS_ChgRemTime_102 = 271878364;// UINT16
    BMS_1_StateOfChargeDis_102 = 167184605;// UINT8
    BMS_StateOfEnergy_102 = 271911134;// UINT16
    BMS_1_CRC6_102 = 167217375;// UINT8
    BMS_1_RollgCntr6_102 = 167250144;// UINT8
    BMS_1_Resd6_102 = 167282913;// UINT8
    BMS_StateOfHealth_102 = 167315682;// UINT8
    BMS_BlanceState_102 = 842828003;// BOOL
    BMS_IsolationFault_102 = 167348452;// UINT8
    BMS_HVILFault_DC_102 = 842860773;// BOOL
    BMS_HVILFault_FMCU_102 = 842893542;// BOOL
    BMS_HVILFault_RMCU_102 = 842926311;// BOOL
    BMS_SOCDis_102 = 271943912;// UINT16
    BMS_InfraDcIActDcMin_102 = 271976681;// UINT16
    BMS_8_CRC1_50A = 167381226;// UINT8
    BMS_8_RollgCntr1_50A = 167413995;// UINT8
    BMS_8_Resd1_50A = 167446764;// UINT8
    BMS_HvBattCellOverU_50A = 167479533;// UINT8
    BMS_HvBattCellUnderU_50A = 167512302;// UINT8
    BMS_HvBattUnderU_50A = 167545071;// UINT8
    BMS_HvBattDischargeOverCurrent_50A = 167577840;// UINT8
    BMS_HvBattChargeOverCurrent_50A = 167610609;// UINT8
    BMS_HvBattOverU_50A = 167643378;// UINT8
    BMS_HvBattMismatching_50A = 842959091;// BOOL
    BMS_HvBattSocJump_50A = 842991860;// BOOL
    BMS_HvBattCellUUnif_50A = 843024629;// BOOL
    BMS_HvBattLowSoc_50A = 843057398;// BOOL
    BMS_HvBattOverSoc_50A = 843090167;// BOOL
    BMS_HvBattTempUniformity_50A = 843122936;// BOOL
    BMS_BattInterCANErr_50A = 843155705;// BOOL
    BMS_HvBattOverCharging_50A = 843188474;// BOOL
    BMS_HvBattOverTemperature_50A = 167676155;// UINT8
    FMCU_SysTempOvrInd_48B = 843221244;// BOOL
    LHFTireTemperature_524 = 167708925;// UINT8
    RHFTireTemperature_524 = 167741694;// UINT8
    LHRTireTemperature_524 = 167774463;// UINT8
    LHFTirePressure_524 = 167807232;// UINT8
    RHFTirePressure_524 = 167840001;// UINT8
    LHRTirePressure_524 = 167872770;// UINT8
    RHRTirePressure_524 = 167905539;// UINT8
    RHRTireTemperature_524 = 167938308;// UINT8
    TirePositionWarning_LHFTire_52C = 167971077;// UINT8
    TirePositionWarning_RHFTire_52C = 168003846;// UINT8
    TirePositionWarning_LHRTire_52C = 168036615;// UINT8
    TirePositionWarning_RHRTire_52C = 168069384;// UINT8
    TirePressureWarningLampSts_52C = 168102153;// UINT8
    TirePressureSystemFailSts_52C = 843254026;// BOOL
    TMS_MotorLoopErrorLevel_3DF = 168134923;// UINT8
    TMS_BatteryLoopErrorLevel_3DF = 168167692;// UINT8
    FrontDeforestDisplaySts_4A4 = 843286797;// BOOL
    TMS_DisplayActive_4A4 = 843319566;// BOOL
    TMS_ZoneSelectionDisplaySts_4A4 = 843352335;// BOOL
    TMS_AutoDisplaySts_4A4 = 843385104;// BOOL
    TMS_FiltertchangeDisplaySts_4A4 = 843417873;// BOOL
    TMS_AUTODefrostOnSts_4A4 = 843450642;// BOOL
    TMS_AUTODefrostDisplaySts_4A4 = 843483411;// BOOL
    TMS_RefInSufDispSts_4A4 = 843516180;// BOOL
    TMS_CooltInSufDispSts_4A4 = 843548949;// BOOL
    TMS_DLOnOffSts_4A4 = 843581718;// BOOL
    TMS_DROnOffSts_4A4 = 843614487;// BOOL
    TMS_PLOnOffSts_4A4 = 843647256;// BOOL
    WorkingSts_4A4 = 843680025;// BOOL
    TMS_PROnOffSts_4A4 = 843712794;// BOOL
    TMS_DCOnOffSts_4A4 = 843745563;// BOOL
    TMS_ACFastCool_4A4 = 843778332;// BOOL
    TMS_ACFastHeat_4A4 = 843811101;// BOOL
    TMS_LoveRemind_4A4 = 843843870;// BOOL
    TMS_InteCleanCar_4A4 = 843876639;// BOOL
    TMS_ACVentilationSts_4A4 = 843909408;// BOOL
    TMS_AutoAirCleanSts_4A4 = 843942177;// BOOL
    PM25Sts_4A4 = 843974946;// BOOL
    TMS_ACModeCustomSts_4A4 = 168200483;// UINT8
    TMS_ACRequestDisplaySts_4A4 = 168233252;// UINT8
    TMS_AUTODefLevelSts_4A4 = 168266021;// UINT8
    TMS_KeepWarm_4A4 = 168298790;// UINT8
    TMS_DSwingSts_4A4 = 168331559;// UINT8
    TMS_PSwingSts_4A4 = 168364328;// UINT8
    TMS_TuisongDispSts_4A4 = 168397097;// UINT8
    TMS_SetTemperature_L_C_4A4 = 168429866;// UINT8
    TMS_SetTemperature_R_C_4A4 = 168462635;// UINT8
    TMS_ModeAdjustDisplaySts_4A4 = 168495404;// UINT8
    TMS_CirculationModeDisplaySts_4A4 = 168528173;// UINT8
    TMS_IncarTemp_4A4 = 168560942;// UINT8
    TMS_FliterLife_4A4 = 168593711;// UINT8
    TMS_DLSwingUpDwnWPosn_4A4 = 168626480;// UINT8
    TMS_DLSwingLeRiWPosn_4A4 = 168659249;// UINT8
    TMS_DRSwingUpDwnWPosn_4A4 = 168692018;// UINT8
    TMS_DRSwingLeRiWPosn_4A4 = 168724787;// UINT8
    TMS_PLSwingUpDwnWPosn_4A4 = 168757556;// UINT8
    TMS_PLSwingLeRiWPosn_4A4 = 168790325;// UINT8
    TMS_PRSwingUpDwnWPosn_4A4 = 168823094;// UINT8
    TMS_PRSwingLeRiWPosn_4A4 = 168855863;// UINT8
    TMS_DCSwingLeRiWPosn_4A4 = 168888632;// UINT8
    AirSwingSts_4A4 = 844007737;// BOOL
    TemperatureUnit_4A4 = 844040506;// BOOL
    BlowAdvanceOnSts_4A4 = 844073275;// BOOL
    BlowDelayOffSts_4A4 = 844106044;// BOOL
    TMS_PM25_ExtSts_4A4 = 844138813;// BOOL
    TMS_PM25_IncarSts_4A4 = 844171582;// BOOL
    TMS_AQS_Sts_4A4 = 844204351;// BOOL
    PM25overproofDisplaySts_4A4 = 844237120;// BOOL
    AQSDisplaySts_4A4 = 844269889;// BOOL
    PM25AutoRunSts_4A4 = 844302658;// BOOL
    PM25AutoRunSetSts_4A4 = 844335427;// BOOL
    FragranceWelcomeModeSts_4A4 = 844368196;// BOOL
    FragranceDisplaysts_4A4 = 844400965;// BOOL
    TMS_AQS_Level_4A4 = 168921414;// UINT8
    ACRequestCommand_4A4 = 168954183;// UINT8
    PM25_Outcar_Level_4A4 = 168986952;// UINT8
    PM25_Incar_Level_4A4 = 169019721;// UINT8
    SetTemperature_L_F_4A4 = 169052490;// UINT8
    SetTemperature_R_F_4A4 = 169085259;// UINT8
    TMS_PM25_incar_4A4 = 272009548;// UINT16
    TMS_PM25_ext_4A4 = 272042317;// UINT16
    TMS_KeepWarmMemoryFb_4A4 = 169118030;// UINT8
    Fusa_264_5_264 = 402819407;// UINT32
    Fusa_264_4_264 = 169150800;// UINT8
    Fusa_264_3_264 = 169183569;// UINT8
    Fusa_264_2_264 = 169216338;// UINT8
    Fusa_264_1_264 = 169249107;// UINT8
    EPB_R_1_CRC1_264 = 169281876;// UINT8
    EPB_R_1_RollgCntr1_264 = 169314645;// UINT8
    EPB_1_FltLamp_264 = 169347414;// UINT8
    EPB_1_ParkLamp_264 = 169380183;// UINT8
    EPB_1_ActrSt_264 = 169412952;// UINT8
    EPB_1_RWUSt_264 = 844433753;// BOOL
    EPB_1_TextDisp_264 = 169445722;// UINT8
    IPB_EPBErrorStatus_264 = 844466523;// BOOL
    AVHWarningMessage_4A9 = 844499292;// BOOL
    CDPActive_4A9 = 844532061;// BOOL
    HDCFailSts_4A9 = 844564830;// BOOL
    HDCCtrlSts_4A9 = 169478495;// UINT8
    AVHSts_4A9 = 169511264;// UINT8
    AVHFailSts_4A9 = 844597601;// BOOL
    FWAWarningSts_4A9 = 169544034;// UINT8
    BrakeFluidSts_4A9 = 169576803;// UINT8
    BrakesystemFailSts_4A9 = 844630372;// BOOL
    CST_Status_4AD = 169609573;// UINT8
    BrakepedalFeelSts_4AD = 169642342;// UINT8
    CST_SensitivitySts_4AD = 169675111;// UINT8
    EBS_SOC_4B0 = 169707880;// UINT8
    DriverSeatMsgStr_LvlSts_425 = 169740649;// UINT8
    DriverSeatMsg_ModeSts_425 = 169773418;// UINT8
    LeftSeatMsgStr_LvlSts_429 = 169806187;// UINT8
    LeftSeatMsg_ModeSts_429 = 169838956;// UINT8
    PassSeatMsgStr_LvlSts_42D = 169871725;// UINT8
    PassSeatMsg_ModeSts_42D = 169904494;// UINT8
    RightSeatMsgStr_LvlSts_43F = 169937263;// UINT8
    RightSeatMsg_ModeSts_43F = 169970032;// UINT8
    SRF_OperateSts_4A5 = 170002801;// UINT8
    SRF_PositionSts_4A5 = 170035570;// UINT8
    SRF_OverHeatProtect_4A5 = 844663155;// BOOL
    SRF_Sunshade_OperateSts_4A5 = 170068340;// UINT8
    Sunshade_OverHeatProtect_4A5 = 844695925;// BOOL
    SRF_Sunroof_ActualSts_4A8 = 170101110;// UINT8
    SRF_Sunshade_ActualSts_4A8 = 170133879;// UINT8
    SRF_Sunshade_PositionSts_4A8 = 170166648;// UINT8
    OBC_CC_ConnectSts_477 = 170199417;// UINT8
    CDU_MILSts_477 = 844728698;// BOOL
    BMS_BattFaultLampSts_50F = 844761467;// BOOL
    BMS_OverTemp_LightSts_50F = 844794236;// BOOL
    BMS_RemainChg_Time_50F = 272075133;// UINT16
    BMS_ThermalRunawayDis_50F = 844827006;// BOOL
    BMS_TBOXIsolationFault_50F = 170232191;// UINT8
    BMS_PreWarmDis_50F = 170264960;// UINT8
    BMS_HvBattSoeActDisChaCyc_5FB = 272107905;// UINT16
    BMS_HvBattSoeActReChaCyc_5FB = 272140674;// UINT16
    BMS_HvBattRatedEnergy_5FB = 272173443;// UINT16
    BMS_HvBattRatedU_5FB = 272206212;// UINT16
    BMS_HvBattTypeCode_5FB = 170297733;// UINT8
    BMS_HvBattManufcCode_5FB = 402852230;// UINT32
    BMS_HvBattTempSensorTotalNum_5FB = 272238983;// UINT16
    BMS_HvBattTempSensorTempMaxNum_5FB = 170330504;// UINT8
    BMS_HvBattTempSensorTempMinNum_5FB = 170363273;// UINT8
    BMS_HvBattTempMaxNum_5FB = 170396042;// UINT8
    BMS_HvBattTempMinNum_5FB = 170428811;// UINT8
    BMS_BattCellTotalNum_5FB = 272271756;// UINT16
    BMS_HvBattVoltMaxNum_5FB = 170461581;// UINT8
    BMS_HvBattVoltMinNum_5FB = 170494350;// UINT8
    BMS_ActualSOCMin_5FB = 272304527;// UINT16
    BMS_ActualSOCMax_5FB = 272337296;// UINT16
    BMS_HvBattCellVoltMax_Num_4C8 = 170527121;// UINT8
    BMS_HvBattCellVoltMax_4C8 = 272370066;// UINT16
    BMS_HvBattCellVoltMin_Num_4C8 = 170559891;// UINT8
    BMS_HvBattCellVoltMin_4C8 = 272402836;// UINT16
    BMS_Chg_Sts_0x4c8_4C8 = 170592661;// UINT8
    EPS_2_Steer_ReturnRmd_Sts_4D3 = 170625430;// UINT8
    EPS_2_Steer_ReturnRmd_4D3 = 170658199;// UINT8
    EPS_2_MFS_ShakeSts_4D3 = 170690968;// UINT8
    DWD_TextDisp_532 = 170723737;// UINT8
    DWD_SoundIndcn_532 = 170756506;// UINT8
    LSensorFailSts_532 = 844859803;// BOOL
    RSensorFailSts_532 = 844892572;// BOOL
    DWD_Distance_532 = 170789277;// UINT8
    DWD_Switch_532 = 170822046;// UINT8
    RADAR_1_CRC1_530 = 170854815;// UINT8
    RADAR_1_RollgCntr1_530 = 170887584;// UINT8
    RADAR_1_RadarDetectSts_530 = 170920353;// UINT8
    RADAR_1_RadarWorkSts_530 = 844925346;// BOOL
    RADAR_1_Resd1_530 = 844958115;// BOOL
    RADAR_1_LHRRadarSensorDistance_530 = 170953124;// UINT8
    RADAR_1_RHMRRadarSensorDistance_530 = 170985893;// UINT8
    RADAR_1_LHMRRadarSensorDistance_530 = 171018662;// UINT8
    RADAR_1_RHRRadarSensorDistance_530 = 171051431;// UINT8
    RADAR_1_LHFRadarSensorDistance_530 = 171084200;// UINT8
    RADAR_1_RHFRadarSensorDistance_530 = 171116969;// UINT8
    RADAR_1_ParkingRadarSwSts_530 = 844990890;// BOOL
    RADAR_1_RHMFRadarSensorDistance_530 = 171149739;// UINT8
    RADAR_1_LHMFRadarSensorDistance_530 = 171182508;// UINT8
    RADAR_1_AudibleBeepRate_530 = 171215277;// UINT8
    EMS_EngineSpeed_Actual_103 = 272435630;// UINT16
    FRZCU_FRSeatHeiFb_4D2 = 171248047;// UINT8
    FRZCU_FRSeatLvlFb_4D2 = 171280816;// UINT8
    FRZCU_FRSeatBackAgFb_4D2 = 171313585;// UINT8
    FRZCU_FRSeatLegSpprtFb_4D2 = 171346354;// UINT8
    FRZCU_FRWinSts_4D2 = 171379123;// UINT8
    FRZCU_RRWinSts_4D2 = 171411892;// UINT8
    FRZCU_FRSeatLegRestAngleFb_4D2 = 171444661;// UINT8
    FRZCU_FRSeatCushFb_4D2 = 171477430;// UINT8
    FRZCU_FRSeatNeedMemory_4D2 = 171510199;// UINT8
    FRZCU_FRMemoryFb_4D2 = 171542968;// UINT8
    FRZCU_FRRecoverFb_4D2 = 171575737;// UINT8
    FRZCU_EasyEntryExitFb_4D2 = 171608506;// UINT8
    FRZCU_EnjoyableSeatWarning_4D2 = 171641275;// UINT8
    FRZCU_EnjoyableSeatSts_4D2 = 171674044;// UINT8
    SeatHeatLevelsFR_4D2 = 171706813;// UINT8
    SeatVentLevelsFR_4D2 = 171739582;// UINT8
    SeatHeatLevelsRR_4D2 = 171772351;// UINT8
    SeatVentLevelsRR_4D2 = 171805120;// UINT8
    FRZCU_FRSeatFoldFb_4D2 = 171837889;// UINT8
    MFSR_UP_SW_205 = 171870658;// UINT8
    MFSR_DW_SW_205 = 171903427;// UINT8
    MFSR_Left_SW_205 = 171936196;// UINT8
    MFSR_Right_SW_205 = 171968965;// UINT8
    MFSR_OK_SW_205 = 172001734;// UINT8
    MFSR_Voice_SW_205 = 172034503;// UINT8
    MFSR_Customize_205 = 172067272;// UINT8
    TMS_AirPurgeReminderSts_448 = 172100041;// UINT8
    TMS_BT_Reduce_Wind_SpeedSts_448 = 172132810;// UINT8
    TMS_First_BlowingSts_448 = 172165579;// UINT8
    TMS_CirculationInTunnelsSts_448 = 172198348;// UINT8
    TMS_crosscountry_coolingSts_448 = 172231117;// UINT8
    TMS_ParkingAirConditioningStatus_448 = 172263886;// UINT8
    TMS_CoolantFillSts_448 = 172296655;// UINT8
    TMS_UVC_ControlSts_448 = 172329424;// UINT8
    TMS_UVC_LuminanceSts_448 = 172362193;// UINT8
    TMS_ICC_Lightoff_Sts_448 = 172394962;// UINT8
    TMS_ICC_Memoff_Sts_448 = 172427731;// UINT8
    TMS_keepwarmSet_TemperatureSts_448 = 172460500;// UINT8
    TMS_CFSSwitchFb_4D8 = 172493269;// UINT8
    TDU_CFSPosOneType_4D8 = 172526038;// UINT8
    TDU_CFSPosTwoType_4D8 = 172558807;// UINT8
    TDU_CFSPosThreeType_4D8 = 172591576;// UINT8
    TDU_CFSRunOut_4D8 = 172624345;// UINT8
    TMS_CFSShortWarn_4D8 = 172657114;// UINT8
    TMS_CFSLevelSetFb_4D8 = 172689883;// UINT8
    TMS_CFSPosSetFb_4D8 = 172722652;// UINT8
    TMS_CFSPosOneLife_4D8 = 172755421;// UINT8
    TMS_CFSPosTwoLife_4D8 = 172788190;// UINT8
    TMS_CFSPosThreeLife_4D8 = 172820959;// UINT8
    TMS_PM25SwitchFb_4D8 = 172853728;// UINT8
    TMS_ThrModeStsFb_4D8 = 172886497;// UINT8
    TMS_ThrSetTempFb_C_4D8 = 172919266;// UINT8
    TMS_ThrCLMSwitchFb_4D8 = 172952035;// UINT8
    TMS_ThrAutoSwitchFb_4D8 = 172984804;// UINT8
    TMS_ThrBlowSpeedLevelKeyStsFb_4D8 = 173017573;// UINT8
    FRZCU_HoodSts_27E = 173050342;// UINT8
    FRZCU_AuthenResult_27E = 845023719;// BOOL
    FRZCU_PowerMode_27E = 173083112;// UINT8
    FRZCU_TrunkSts_27E = 173115881;// UINT8
    FRZCU_HandleSwitchStsFL_27E = 173148650;// UINT8
    FRZCU_HandleSwitchStsFR_27E = 173181419;// UINT8
    FRZCU_HandleSwitchStsRR_27E = 173214188;// UINT8
    FRZCU_HandleSwitchStsRL_27E = 173246957;// UINT8
    SeatLength_target_Passenger_27E = 173279726;// UINT8
    SeatBackrest_target_Passenger_27E = 173312495;// UINT8
    FRZCU_TrunkLockSts_27E = 173345264;// UINT8
    FRZCU_HoodLockSts_27E = 173378033;// UINT8
    FrontBackSpeedAdj_Cmd_27E = 173410802;// UINT8
    FRZCU_GloveboxSW_27E = 845056499;// BOOL
    FRZCU_FRReleaseLockSts_27E = 173443572;// UINT8
    FRZCU_RRReleaseLockSts_27E = 173476341;// UINT8
    FRZCU_PELockInKeyWarning_27E = 845089270;// BOOL
    FRZCU_PowerOFFWarning_27E = 845122039;// BOOL
    FRZCU_KeylessWarning_27E = 845154808;// BOOL
    FRZCU_OTAPwrDwnReqFb_27E = 173509113;// UINT8
    FRZCU_OTAPwrOnReqFb_27E = 173541882;// UINT8
    PEPS_InformationSource_3B5 = 173574651;// UINT8
    PEPS_OrderInformation_3B5 = 173607420;// UINT8
    PEPS_IDInformation_3B5 = 173640189;// UINT8
    PEPS_KeySts_3B5 = 173672958;// UINT8
    PEPS_ETRUnLockReq_3B5 = 173705727;// UINT8
    PEPS_WelcomeON_404 = 173738496;// UINT8
    PEPS_VehicleSearchReq_404 = 173771265;// UINT8
    PEPS_SATOReminder_404 = 173804034;// UINT8
    PEPS_PLGUnlockReq_404 = 173836803;// UINT8
    PEPS_RKETrunkSts_404 = 173869572;// UINT8
    PEPS_SSBInhibitCWCSts_404 = 173902341;// UINT8
    PEPS_WirelessChargingCtrSts_404 = 173935110;// UINT8
    PEPS_EmgyShutdown_404 = 173967879;// UINT8
    PEPS_SSBInputFailure_404 = 174000648;// UINT8
    PEPS_SmartSystemWarning1_1_404 = 174033417;// UINT8
    PEPS_SmartSystemWarning2_2_404 = 174066186;// UINT8
    PEPS_SmartSystemWarning3_1_404 = 174098955;// UINT8
    PEPS_SmartSystemWarning3_2_404 = 174131724;// UINT8
    PEPS_SmartSystemWarning4_2_404 = 174164493;// UINT8
    PEPS_SmartSystemWarning4_3_404 = 174197262;// UINT8
    PEPS_SystemWarning_404 = 174230031;// UINT8
    PEPS_BLTKeyPESts_404 = 174262800;// UINT8
    BMS_2_G_CRC_50B = 174295569;// UINT8
    BMS_2_G_RollgCntr_50B = 174328338;// UINT8
    BMS_2_G_Resd_50B = 174361107;// UINT8
    BMS_TBOXInsulation_R_50B = 272468500;// UINT16
    BMS_TBOXErrorNumber_50B = 174393877;// UINT8
    BMS_TBOXHvBattMaxT_50B = 174426646;// UINT8
    BMS_TBOXHvBattMinT_50B = 174459415;// UINT8
    BMS_SOCLight_50B = 174492184;// UINT8
    RLCR_2_CRC1_450 = 174524953;// UINT8
    RLCR_2_RollgCntr1_450 = 174557722;// UINT8
    RLCR_2_Resd1_450 = 174590491;// UINT8
    RLCR_2_RCWWarn_450 = 174623260;// UINT8
    RLCR_2_RCTAWarn_450 = 845187613;// BOOL
    RLCR_2_BSDLCWONOFFSts_450 = 174656030;// UINT8
    RLCR_2_DOWONOFFSts_450 = 174688799;// UINT8
    RLCR_2_RCWONOFFSts_450 = 174721568;// UINT8
    RLCR_2_RCTARCTBONOFFSts_450 = 174754337;// UINT8
    RRCR_2_CRC1_4F7 = 174787106;// UINT8
    RRCR_2_RollgCntr1_4F7 = 174819875;// UINT8
    RRCR_2_Resd1_4F7 = 174852644;// UINT8
    RRCR_2_RCWWarn_4F7 = 174885413;// UINT8
    RRCR_2_RCTAWarn_4F7 = 845220390;// BOOL
    RRCR_2_BSDLCWONOFFSts_4F7 = 174918183;// UINT8
    RRCR_2_DOWONOFFSts_4F7 = 174950952;// UINT8
    RRCR_2_RCWONOFFSts_4F7 = 174983721;// UINT8
    RRCR_2_RCTARCTBONOFFSts_4F7 = 175016490;// UINT8
    ONEBOX_4_LHFWheelSpeedRPM_24B = 272501291;// UINT16
    ONEBOX_4_RHFWheelSpeedRPM_24B = 272534060;// UINT16
    LHRWheelSpeedRPM_24B = 272566829;// UINT16
    RHRWheelSpeedRPM_24B = 272599598;// UINT16
    LHRPulseCounterFailSts_24B = 845253167;// BOOL
    LHRPulseCounter_24B = 272632368;// UINT16
    RHRPulseCounterFailSts_24B = 845285937;// BOOL
    RHRPulseCounter_24B = 272665138;// UINT16
    RHFPulseCounterFailSts_24B = 845318707;// BOOL
    RHFPulseCounter_24B = 272697908;// UINT16
    LHFPulseCounterFailSts_24B = 845351477;// BOOL
    LHFPulseCounter_24B = 272730678;// UINT16
    LHFWheelDriveDirection_24B = 175049271;// UINT8
    RHFWheelDriveDirection_24B = 175082040;// UINT8
    LHRWheelDriveDirection_24B = 175114809;// UINT8
    RHRWheelDriveDirection_24B = 175147578;// UINT8
    FMCU_ErrLvlDis_53A = 175180347;// UINT8
    FMCU_SpdAct_EDR_53A = 272763452;// UINT16
    ISGF_ErrLvl_309 = 175213117;// UINT8
    RMCU_ErrLvlDis_57B = 175245886;// UINT8
    RMCU_Speed_EDR_57B = 272796223;// UINT16
    BMS_PackVoltageDis_513 = 272828992;// UINT16
    BMS_PackCurrentDis_513 = 272861761;// UINT16
    ISGF_SysTempOvrInd_44B = 845384258;// BOOL
    TCU_23_GBFaultstatus_46A = 175278659;// UINT8
    TCU_23_TeachInState_46A = 175311428;// UINT8
    ACU_1_AirBagFailSts_49C = 175344197;// UINT8
    ACU_1_ThdRowLBeltWarning_49C = 845417030;// BOOL
    ACU_1_ThdRowRBeltWarning_49C = 845449799;// BOOL
    ACU_1_PsngrSeatBeltWarning_49C = 175376968;// UINT8
    ACU_1_SecRowLBeltWarning_49C = 845482569;// BOOL
    ACU_1_SecRowMBeltWarning_49C = 845515338;// BOOL
    ACU_1_SecRowRBeltWarning_49C = 845548107;// BOOL
    ACU_1_PsngrSeatOccupiedSts_49C = 175409740;// UINT8
    BMS_44_PackPowerRealTime_53D = 272894541;// UINT16
    BMS_Chg_Sts_0x32b_32B = 175442510;// UINT8
    LISDShowCMDSts_4F5 = 845580879;// BOOL
    LISDShowModeSts_4F5 = 175475280;// UINT8
    LISD_TransmitStatus_4F5 = 175508049;// UINT8
    LISD_DisplayStatus_4F5 = 175540818;// UINT8
    LISD_ParkingShowModSts_4F5 = 175573587;// UINT8
    LISD_ParkingShowCMDSts_4F5 = 175606356;// UINT8
    RISDShowCMDSts_4D1 = 845613653;// BOOL
    RISDShowModeSts_4D1 = 175639126;// UINT8
    RISD_TransmitStatus_4D1 = 175671895;// UINT8
    RISD_DisplayStatus_4D1 = 175704664;// UINT8
    RISD_ParkingShowModSts_4D1 = 175737433;// UINT8
    RISD_ParkingShowCMDSts_4D1 = 175770202;// UINT8
    LC_DIYMusicShowModSts_491 = 175802971;// UINT8
    LC_RandomMusicShowSts_491 = 175835740;// UINT8
    LC_LiShowModSts_491 = 175868509;// UINT8
    ILCF_1_LogoParkingSts_593 = 175901278;// UINT8
    ILCF_1_LogoChargingSts_593 = 175934047;// UINT8
    ILCF_1_DIYMusicShowModSts_593 = 175966816;// UINT8
    ILCF_1_RandomMusicShowSts_593 = 175999585;// UINT8
    ILCF_1_LogoColorAdjSts_593 = 272927330;// UINT16
    ADS_5_CRC1_335 = 176032355;// UINT8
    ADS_5_RollgCntr1_335 = 176065124;// UINT8
    ADS_5_Resd1_335 = 176097893;// UINT8
    ADS_5_ILOASts_335 = 176130662;// UINT8
    ADS_5_ICAQuitReason_335 = 176163431;// UINT8
    ADS_5_ISLISt_335 = 176196200;// UINT8
    ADS_5_ILOATextInfo_335 = 176228969;// UINT8
    ADS_5_ILOAActdirection_335 = 176261738;// UINT8
    ADS_5_LKS_warning_335 = 845646443;// BOOL
    ADS_5_ISLIOverSpeedWarn_335 = 176294508;// UINT8
    ADS_5_ISLISpeedLimitSign_335 = 176327277;// UINT8
    ADS_5_EgoLeftLineHeatgAg_335 = 272960110;// UINT16
    ADS_5_EgoLeLineID_335 = 845679215;// BOOL
    ADS_5_EgoLeLineColor_335 = 176360048;// UINT8
    ADS_5_EgoLeLineTyp_335 = 176392817;// UINT8
    ADS_5_EgoLeLineHozlDst_335 = 272992882;// UINT16
    ADS_5_CRC2_335 = 176425587;// UINT8
    ADS_5_RollgCntr2_335 = 176458356;// UINT8
    ADS_5_Resd2_335 = 176491125;// UINT8
    ADS_5_NeborLeLineID_335 = 845711990;// BOOL
    ADS_5_NeborLeLineColor_335 = 176523895;// UINT8
    ADS_5_NeborLeLineTyp_335 = 176556664;// UINT8
    ADS_5_EgoLeLineCrvt_335 = 273025657;// UINT16
    ADS_5_NeborLeLineHozlDst_335 = 273058426;// UINT16
    ADS_5_NeborRiLineID_335 = 845744763;// BOOL
    ADS_5_NeborRiLineColor_335 = 176589436;// UINT8
    ADS_5_NeborRiLineTyp_335 = 176622205;// UINT8
    ADS_5_NeborLeLineCrvt_335 = 273091198;// UINT16
    ADS_5_NeborRiLineHozlDst_335 = 273123967;// UINT16
    ADS_5_CRC3_335 = 176654976;// UINT8
    ADS_5_RollgCntr3_335 = 176687745;// UINT8
    ADS_5_Resd3_335 = 176720514;// UINT8
    ADS_5_ELKSettingSt_335 = 176753283;// UINT8
    ADS_5_LDWLDPSnvtySet_335 = 176786052;// UINT8
    ADS_5_LDWWarnType_335 = 176818821;// UINT8
    ADS_5_NeborRiLineCrvt_335 = 273156742;// UINT16
    ADS_5_IESSwtSet_335 = 176851591;// UINT8
    ADS_5_IHCSettingSt_335 = 176884360;// UINT8
    ADS_5_ISLIWarningMod_335 = 176917129;// UINT8
    ADS_5_ICAEnableBtnSts_335 = 176949898;// UINT8
    ADS_5_FCM_Camera_textinfo_335 = 176982667;// UINT8
    ADS_5_EgoRiLineID_335 = 177015436;// UINT8
    ADS_5_EgoRiLineTyp_335 = 177048205;// UINT8
    ADS_5_EgoRiLineColor_335 = 177080974;// UINT8
    ADS_5_EgoRiLineHozlDst_335 = 273189519;// UINT16
    ADS_5_CRC4_335 = 177113744;// UINT8
    ADS_5_RollgCntr4_335 = 177146513;// UINT8
    ADS_5_Resd4_335 = 177179282;// UINT8
    ADS_5_EgoRightLineHeatgAg_335 = 273222291;// UINT16
    ADS_5_EgoRiLineCrvt_335 = 273255060;// UINT16
    ADS_5_NeborLeftLineHeatgAg_335 = 273287829;// UINT16
    ADS_5_NeborRightLineHeatgAg_335 = 273320598;// UINT16
    ADS_4_CRC1_333 = 177212055;// UINT8
    ADS_4_RollgCntr1_333 = 177244824;// UINT8
    ADS_4_Resd1_333 = 177277593;// UINT8
    ADS_4_AEBStatus_333 = 177310362;// UINT8
    ADS_4_ACCSts_333 = 177343131;// UINT8
    ADS_4_TimeGapSet_333 = 177375900;// UINT8
    ADS_4_FcwMode_333 = 177408669;// UINT8
    ADS_4_AEBReqType_333 = 177441438;// UINT8
    ADS_4_SetSpd_333 = 177474207;// UINT8
    ADS_4_DAIStatus_333 = 177506976;// UINT8
    ADS_4_ACCTextMessage_333 = 177539745;// UINT8
    ADS_4_LongiTakeOverReq_333 = 845777570;// BOOL
    ADS_4_SetSpdUnit_333 = 845810339;// BOOL
    ADS_4_GoNotifier_333 = 177572516;// UINT8
    ADS_4_DAISts_333 = 177605285;// UINT8
    ADS_4_Radar_textinfo_333 = 177638054;// UINT8
    ADS_4_SCF_PopoverReq_333 = 845843111;// BOOL
    ADS_4_DrvrCfmSCFDispFb_333 = 845875880;// BOOL
    ADS_4_SCF_SpdLimUnit_333 = 177670825;// UINT8
    ADS_4_SCF_SpdLimSts_333 = 177703594;// UINT8
    ADS_4_CRC2_333 = 177736363;// UINT8
    ADS_4_RollgCntr2_333 = 177769132;// UINT8
    ADS_4_Resd2_333 = 177801901;// UINT8
    ADS_4_ACCObjTyp_333 = 177834670;// UINT8
    ADS_4_FrntFarObjTyp_333 = 177867439;// UINT8
    ADS_4_ACCObjLgtDstX_333 = 177900208;// UINT8
    ADS_4_FrntFarObjID_333 = 845908657;// BOOL
    ADS_4_Resd_333 = 845941426;// BOOL
    ADS_4_ACCObjID_333 = 845974195;// BOOL
    ADS_4_ACCObjHozDstY_333 = 273353396;// UINT16
    ADS_4_FrntFarObjHozDstY_333 = 273386165;// UINT16
    ADS_4_FrntFarObjLgtDstX_333 = 177932982;// UINT8
    ADS_4_CRC3_333 = 177965751;// UINT8
    ADS_4_RollgCntr3_333 = 177998520;// UINT8
    ADS_4_Resd3_333 = 178031289;// UINT8
    ADS_4_Resd_1_333 = 846006970;// BOOL
    ADS_4_LeObjID_333 = 846039739;// BOOL
    ADS_4_LeObjTyp_333 = 178064060;// UINT8
    ADS_4_LeObjHozDstY_333 = 273418941;// UINT16
    ADS_4_LeObjLgtDstX_333 = 178096830;// UINT8
    ADS_4_RiObjID_333 = 846072511;// BOOL
    ADS_4_RiObjTyp_333 = 178129600;// UINT8
    ADS_4_RiObjHozDstY_333 = 273451713;// UINT16
    ADS_4_RiObjLgtDstX_333 = 178162370;// UINT8
    ADS_4_CRC4_333 = 178195139;// UINT8
    ADS_4_RollgCntr4_333 = 178227908;// UINT8
    ADS_4_Resd4_333 = 178260677;// UINT8
    ADS_4_SCFSwtSet_333 = 178293446;// UINT8
    ADS_4_AEBSettingSt_333 = 178326215;// UINT8
    ADS_4_FCWSettingSt_333 = 178358984;// UINT8
    ADS_4_DAISwtSet_333 = 178391753;// UINT8
    ADS_4_CruiseAccelerateSts_333 = 178424522;// UINT8
    ExternalTemperature_F_494 = 178457291;// UINT8
    ExternalTemperature_C_494 = 178490060;// UINT8
    BlowSpeedLevelDisplaySts_494 = 178522829;// UINT8
    CCP_1_FLTempSwitchReq_47B = 178555598;// UINT8
    CCP_1_FrontAutoACSwitchReq_47B = 846105295;// BOOL
    CCP_1_FrontOFFSwitchReq_47B = 846138064;// BOOL
    CCP_1_FBlowSpdCtrlSwitchReq_47B = 178588369;// UINT8
    FLCCP_MainSetHeatReq_47B = 178621138;// UINT8
    FLCCP_MainSetVentilationReq_47B = 178653907;// UINT8
    TMS_PM25_Detect_453 = 846170836;// BOOL
    TMS_CCP_FLTempSts_453 = 178686677;// UINT8
    TMS_CCP_FrontAutoACSts_453 = 846203606;// BOOL
    TMS_CCP_ACStatus_453 = 846236375;// BOOL
    TMS_CCP_FrontOFFSts_453 = 846269144;// BOOL
    TMS_CCP_FBlowSpdCtrlSts_453 = 178719449;// UINT8
    TMS_CCP_FRTempSts_453 = 178752218;// UINT8
    TMS_CCP_RecyMode_453 = 178784987;// UINT8
    TMS_CCP_FrontDefrostSts_453 = 846301916;// BOOL
    TMS_UVC_WorkingBfSts_453 = 178817757;// UINT8
    TMS_UVC_LuminanceBfSts_453 = 178850526;// UINT8
    TMS_CCP_ACFastCool_453 = 178883295;// UINT8
    TMS_CCP_ACFastHeat_453 = 178916064;// UINT8
    CCP_2_FRTempSwitchReq_497 = 178948833;// UINT8
    CCP_2_RecyModeReq_497 = 178981602;// UINT8
    CCP_2_DefrostModeSwitchReq_497 = 846334691;// BOOL
    CCP_2_RearDefrostSwitchReq_497 = 846367460;// BOOL
    CCP_2_FrontBlowSpdCtrlSwitchReq_497 = 179014373;// UINT8
    FRCCP_DeputySetHeatReq_497 = 179047142;// UINT8
    FRCCP_DeputySetVentilationReq_497 = 179079911;// UINT8
    CCP_2_ACSwitchReq_497 = 846400232;// BOOL
    CHB_App_Sts_50C = 179112681;// UINT8
    CHB_AppMem_Sts_50C = 179145450;// UINT8
    CHB_AppDelay_Sts_50C = 179178219;// UINT8
    CHB_AppTimeset_Sts_50C = 179210988;// UINT8
    CHB_AppCoolorheat_Sts_50C = 179243757;// UINT8
    CHB_AppCoolset_Sts_50C = 179276526;// UINT8
    CHB_AppHeatset_Sts_50C = 179309295;// UINT8
    CHB_AppSterilization_Sts_50C = 179342064;// UINT8
    CHB_AppItemsLeft_Sts_50C = 179374833;// UINT8
    CHB_AppItemsLeftset_Sts_50C = 179407602;// UINT8
    CHB_AppChbDoor_Sts_50C = 179440371;// UINT8
    FuelTankLidSystemFailureSts_4D9 = 846433012;// BOOL
    FuelTankPressureConditionReached_4D9 = 179473141;// UINT8
    ACU_2_LateralAccelerationN_31 = 273484534;// UINT16
    ACU_2_YawRateE_31 = 273517303;// UINT16
    ACU_2_YawrateSigValidDataA_31 = 179505912;// UINT8
    ACU_2_LateralAccelerationSigVDD_31 = 179538681;// UINT8
    CSunshadeSts_400 = 179571450;// UINT8
    CSunshadePercentSts_400 = 179604219;// UINT8
    FRZCU_SeatHeatLvlFR2_400 = 179636988;// UINT8
    FRZCU_SeatVentLvlFR2_400 = 179669757;// UINT8
    FRZCU_SeatVentLvlRR2_400 = 179702526;// UINT8
    FRZCU_SeatHeatLvlRR2_400 = 179735295;// UINT8
    FRZCU_ContainerlightSts_400 = 179768064;// UINT8
    FRZCU_FRWinThermalSts_400 = 846465793;// BOOL
    FRZCU_RRWinThermalSts_400 = 846498562;// BOOL
    FRZCU_CSunshadeThermalSts_400 = 846531331;// BOOL
    OBC_ElectronicLockSts_49E = 179800836;// UINT8
    OBC_WorkingMode_49E = 179833605;// UINT8
    OBC_OutDischgCurr_49E = 273550086;// UINT16
    OBC_OutDischgVolt_49E = 273582855;// UINT16
    EPS_1_CRC_122 = 179866376;// UINT8
    EPS_1_RollgCntr_122 = 179899145;// UINT8
    EPSFailSts_122 = 179931914;// UINT8
    EPSSteeringAngleCalibrationSts_122 = 846564107;// BOOL
    EPS_EOTLearning_Sts_122 = 846596876;// BOOL
    MFSL_UP_SW_201 = 179964685;// UINT8
    MFSL_DW_SW_201 = 179997454;// UINT8
    MFSL_Left_SW_201 = 180030223;// UINT8
    MFSL_Right_SW_201 = 180062992;// UINT8
    MFSL_OK_SW_201 = 180095761;// UINT8
    MFSL_AVM_SW_201 = 180128530;// UINT8
    MFSL_Customize_201 = 180161299;// UINT8
    TBOX_PetmodeSetTemperature_480 = 180194068;// UINT8
    PPMID_1_PPMICounter_4B8 = 846629653;// BOOL
    PPMID_1_PPMISt_4B8 = 180226838;// UINT8
    PPMID_1_HWSts_4B8 = 180259607;// UINT8
    PPMID_1_BSRBPASts_4B8 = 180292376;// UINT8
    ADS_3_ICAStatus_31A = 180325145;// UINT8
    ADS_3_ICATextinfo_31A = 180357914;// UINT8
    ADS_3_ACCSts_31A = 180390683;// UINT8
    ADS_3_DriverHandsoffWarning_31A = 180423452;// UINT8
    ADS_3_LKSMod_31A = 180456221;// UINT8
    ADS_3_LKSSts_31A = 180488990;// UINT8
    ADS_3_ELKSts_31A = 180521759;// UINT8
    ADS_3_LKSLeftTrackingSt_31A = 180554528;// UINT8
    ADS_3_LKSRightTrackingSt_31A = 180587297;// UINT8
    ADS_3_ELKLeftActiveSt_31A = 180620066;// UINT8
    ADS_3_ELKRightActiveSt_31A = 180652835;// UINT8
    ASU_3_horizontalSts_483 = 180685604;// UINT8
    ASU_3_horizontalmodeFb_483 = 180718373;// UINT8
    OBC_DischgSts_48C = 180751142;// UINT8
    ATCM_DriveModeSw_315 = 180783911;// UINT8
    ATCM_XModeSw_315 = 180816680;// UINT8
    ATCM_SwitchUTurnModeSet_315 = 846662441;// BOOL
    BNCM_16_CRC1_4BE = 180849450;// UINT8
    BNCM_16_RollgCntr1_4BE = 180882219;// UINT8
    BNCM_16_Resd1_4BE = 180914988;// UINT8
    BNCM_16_CMD_4BE = 273615661;// UINT16
    BNCM_16_PDU1_4BE = 537103150;// UINT64
    BNCM_16_PDU2_4BE = 537135919;// UINT64
    BNCM_16_PDU3_4BE = 537168688;// UINT64
    BNCM_16_PDU4_4BE = 402885425;// UINT32
    RLCR_1_CRC1_447 = 180947762;// UINT8
    RLCR_1_RollgCntr1_447 = 180980531;// UINT8
    RLCR_1_Resd1_447 = 181013300;// UINT8
    RLCR_1_SysSt_447 = 181046069;// UINT8
    RLCR_1_BlkSts_447 = 846695222;// BOOL
    RLCR_1_BSDWarn_447 = 181078839;// UINT8
    RLCR_1_DOWWarn_447 = 181111608;// UINT8
    RLCR_1_DowLock_447 = 181144377;// UINT8
    RLCR_1_ELK_Collision_flag_447 = 181177146;// UINT8
    RLCR_1_ELK_CollisionFlagValid_447 = 181209915;// UINT8
    RRCR_1_CRC1_4F3 = 181242684;// UINT8
    RRCR_1_RollgCntr1_4F3 = 181275453;// UINT8
    RRCR_1_Resd1_4F3 = 181308222;// UINT8
    RRCR_1_SysSt_4F3 = 181340991;// UINT8
    RRCR_1_BlkSts_4F3 = 846728000;// BOOL
    RRCR_1_BSDWarn_4F3 = 181373761;// UINT8
    RRCR_1_DOWWarn_4F3 = 181406530;// UINT8
    RRCR_1_DowLock_4F3 = 181439299;// UINT8
    RRCR_1_ELK_Collision_flag_4F3 = 181472068;// UINT8
    RRCR_1_ELK_CollisionFlagValid_4F3 = 181504837;// UINT8
    ADS_2_AEBStatus_314 = 181537606;// UINT8
    ADS_2_FCWStatus_314 = 181570375;// UINT8
    FCM_2_IHCfunctionSts_314 = 181603144;// UINT8
    LHTurnlightSts_0x23b_23B = 846760777;// BOOL
    RHTurnlightSts_0x23b_23B = 846793546;// BOOL
    LHFdoorSts_0x23b_23B = 181635915;// UINT8
    LHFDoorLockSts_23B = 181668684;// UINT8
    LHTurnSW_23B = 846826317;// BOOL
    RHTurnSW_23B = 846859086;// BOOL
    HazardLightSW_23B = 846891855;// BOOL
    LHFSeatBeltSW_0x23b_23B = 846924624;// BOOL
    DirectionIndLeft_23B = 846957393;// BOOL
    DirectionIndRight_23B = 846990162;// BOOL
    LHRdoorSts_2BB = 181701459;// UINT8
    ChrgHoodSts_313 = 847022932;// BOOL
    FRZCU_FRSitPosnSts_313 = 181734229;// UINT8
    ADS_6_CRC1_20C = 181766998;// UINT8
    ADS_6_RollgCntr1_20C = 181799767;// UINT8
    ADS_6_Resd1_20C = 181832536;// UINT8
    ADS_6_AVM_FuncSts_20C = 181865305;// UINT8
    ADS_6_AVM_ButtonClick_20C = 181898074;// UINT8
    ADS_6_Customize_buttonsFB_20C = 181930843;// UINT8
    ADS_6_PAS_AudioTone_20C = 181963612;// UINT8
    ONEBOX_7_BSW_Active_23C = 181996381;// UINT8
    ONEBOX_7_iCCO_TgtVel_23C = 182029150;// UINT8
    VCU_3_G_ActWheelTorqueFA_4BC = 273648479;// UINT16
    VCU_3_G_ActWheelTorqueRA_4BC = 273681248;// UINT16
    ADCC_SysReadySts_30F = 847055713;// BOOL
    IMMO_Chall_ICC_456 = 537201506;// UINT64
    IMMO_Teach_VCC_47D = 537234275;// UINT64
    ADS_RCTAWarn_Left_40D = 847088484;// BOOL
    ADS_RCTAWarn_Rigth_40D = 847121253;// BOOL
    ADS_FCTAWarn_Left_40D = 847154022;// BOOL
    ADS_FCTAWarn_Right_40D = 847186791;// BOOL
    CSA_3_CRC1_3AF = 182061928;// UINT8
    CSA_3_RollgCntr1_3AF = 182094697;// UINT8
    CSA_3_Resd1_3AF = 182127466;// UINT8
    FlwheelBearing_3AF = 273714027;// UINT16
    FrwheelBearing_3AF = 273746796;// UINT16
    BlwheelBearing_3AF = 273779565;// UINT16
    BrwheelBearing_3AF = 273812334;// UINT16
    CSA_3_CRC2_3AF = 182160239;// UINT8
    CSA_3_RollgCntr2_3AF = 182193008;// UINT8
    VehicleBearing_kg_3AF = 273845105;// UINT16
    VehicleBearing_lb_3AF = 273877874;// UINT16
    GoodsWeight_kg_3AF = 273910643;// UINT16
    Goods_Weight_Ratio_3AF = 273943412;// UINT16
    CSA_3_CRC3_3AF = 182225781;// UINT8
    CSA_3_RollgCntr3_3AF = 182258550;// UINT8
    CSA_3_Resd3_3AF = 182291319;// UINT8
    GoodsWeight_lb_3AF = 273976184;// UINT16
    OverWeightValue_kg_3AF = 274008953;// UINT16
    OverWeightValue_lb_3AF = 274041722;// UINT16
    OverWeightRatio_3AF = 182324091;// UINT8
    OverWeightWarning_3AF = 847219580;// BOOL
    OverLoadWarnShieldSetFed_3AF = 182356861;// UINT8
    RLHS_2_AmbBrightness_460 = 274074494;// UINT16
    RLHS_LiSwithReason_460 = 182389631;// UINT8
    ASU_6_HMIFailFb_JT_352 = 182422400;// UINT8
    TCM_1_TrailerSts_4AF = 847252353;// BOOL

    SID_CLUSTER = 671091586;
    SID_OTA = 671091587;
    SID_LOG = 671088649;
    SID_DIAG = 671088650;
    SID_DUMMY = 2147483647;
}

// the following GSID can not be subscribed
enum GroupedSignalIdSoc {

    GSID_DUMMY = 2147483647;
}

message MsgGroupIdSoc {
    required GroupIdSoc     group_id    = 1;
}

message MsgSignalIdSoc {
    required SignalIdSoc    signal_id   = 1;
}

message MsgSignalValueSoc {
    required ValueTypeSoc       value_type  = 1;

    optional bool               value_bool  = 2;
    optional uint32             value_u32   = 3;
    optional sint32             value_s32   = 4;
    optional uint64             value_u64   = 5;
    optional sint64             value_s64   = 6;
    optional bytes              value_bytes = 7;
};

message MsgGroupedSignalSoc {
    required GroupedSignalIdSoc     gs_id       = 1;
    required MsgSignalValueSoc      gs_value    = 2;
};

message MsgSignalSoc {
    required SignalIdSoc        s_id        = 1;
    required StateSoc           s_state     = 2;
    required MsgSignalValueSoc  s_value     = 3;
};

message MsgGroupSoc {
    required GroupIdSoc             g_id        = 1;
    required StateSoc               g_state     = 2;

    repeated MsgGroupedSignalSoc    g_signals   = 3;
};