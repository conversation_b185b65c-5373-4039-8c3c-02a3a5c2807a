// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: autolink.soc.platform.vehicle.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_autolink_2esoc_2eplatform_2evehicle_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_autolink_2esoc_2eplatform_2evehicle_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019005 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_autolink_2esoc_2eplatform_2evehicle_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_autolink_2esoc_2eplatform_2evehicle_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[6]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_autolink_2esoc_2eplatform_2evehicle_2eproto;
namespace autolink {
namespace soc {
namespace platform {
namespace vehicle {
namespace pb {
class MsgGroupIdSoc;
struct MsgGroupIdSocDefaultTypeInternal;
extern MsgGroupIdSocDefaultTypeInternal _MsgGroupIdSoc_default_instance_;
class MsgGroupSoc;
struct MsgGroupSocDefaultTypeInternal;
extern MsgGroupSocDefaultTypeInternal _MsgGroupSoc_default_instance_;
class MsgGroupedSignalSoc;
struct MsgGroupedSignalSocDefaultTypeInternal;
extern MsgGroupedSignalSocDefaultTypeInternal _MsgGroupedSignalSoc_default_instance_;
class MsgSignalIdSoc;
struct MsgSignalIdSocDefaultTypeInternal;
extern MsgSignalIdSocDefaultTypeInternal _MsgSignalIdSoc_default_instance_;
class MsgSignalSoc;
struct MsgSignalSocDefaultTypeInternal;
extern MsgSignalSocDefaultTypeInternal _MsgSignalSoc_default_instance_;
class MsgSignalValueSoc;
struct MsgSignalValueSocDefaultTypeInternal;
extern MsgSignalValueSocDefaultTypeInternal _MsgSignalValueSoc_default_instance_;
}  // namespace pb
}  // namespace vehicle
}  // namespace platform
}  // namespace soc
}  // namespace autolink
PROTOBUF_NAMESPACE_OPEN
template<> ::autolink::soc::platform::vehicle::pb::MsgGroupIdSoc* Arena::CreateMaybeMessage<::autolink::soc::platform::vehicle::pb::MsgGroupIdSoc>(Arena*);
template<> ::autolink::soc::platform::vehicle::pb::MsgGroupSoc* Arena::CreateMaybeMessage<::autolink::soc::platform::vehicle::pb::MsgGroupSoc>(Arena*);
template<> ::autolink::soc::platform::vehicle::pb::MsgGroupedSignalSoc* Arena::CreateMaybeMessage<::autolink::soc::platform::vehicle::pb::MsgGroupedSignalSoc>(Arena*);
template<> ::autolink::soc::platform::vehicle::pb::MsgSignalIdSoc* Arena::CreateMaybeMessage<::autolink::soc::platform::vehicle::pb::MsgSignalIdSoc>(Arena*);
template<> ::autolink::soc::platform::vehicle::pb::MsgSignalSoc* Arena::CreateMaybeMessage<::autolink::soc::platform::vehicle::pb::MsgSignalSoc>(Arena*);
template<> ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* Arena::CreateMaybeMessage<::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace autolink {
namespace soc {
namespace platform {
namespace vehicle {
namespace pb {

enum MsgIDSoc : int {
  MSG_ID_GET_GROUP_INFO = 1,
  MSG_ID_SEND_GROUP_INFO = 2,
  MSG_ID_GET_SIGNAL_INFO = 3,
  MSG_ID_SEND_SIGNAL_INFO = 4
};
bool MsgIDSoc_IsValid(int value);
constexpr MsgIDSoc MsgIDSoc_MIN = MSG_ID_GET_GROUP_INFO;
constexpr MsgIDSoc MsgIDSoc_MAX = MSG_ID_SEND_SIGNAL_INFO;
constexpr int MsgIDSoc_ARRAYSIZE = MsgIDSoc_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* MsgIDSoc_descriptor();
template<typename T>
inline const std::string& MsgIDSoc_Name(T enum_t_value) {
  static_assert(::std::is_same<T, MsgIDSoc>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function MsgIDSoc_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    MsgIDSoc_descriptor(), enum_t_value);
}
inline bool MsgIDSoc_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, MsgIDSoc* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<MsgIDSoc>(
    MsgIDSoc_descriptor(), name, value);
}
enum StateSoc : int {
  DEFAULT = 0,
  INACTIVE = 1,
  ACTIVE = 2
};
bool StateSoc_IsValid(int value);
constexpr StateSoc StateSoc_MIN = DEFAULT;
constexpr StateSoc StateSoc_MAX = ACTIVE;
constexpr int StateSoc_ARRAYSIZE = StateSoc_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* StateSoc_descriptor();
template<typename T>
inline const std::string& StateSoc_Name(T enum_t_value) {
  static_assert(::std::is_same<T, StateSoc>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function StateSoc_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    StateSoc_descriptor(), enum_t_value);
}
inline bool StateSoc_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, StateSoc* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<StateSoc>(
    StateSoc_descriptor(), name, value);
}
enum ValueTypeSoc : int {
  TYPE_UINT32 = 3,
  TYPE_BOOLEAN = 4,
  TYPE_SINT32 = 7,
  TYPE_UINT64 = 8,
  TYPE_SINT64 = 9,
  TYPE_BYTES = 11
};
bool ValueTypeSoc_IsValid(int value);
constexpr ValueTypeSoc ValueTypeSoc_MIN = TYPE_UINT32;
constexpr ValueTypeSoc ValueTypeSoc_MAX = TYPE_BYTES;
constexpr int ValueTypeSoc_ARRAYSIZE = ValueTypeSoc_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ValueTypeSoc_descriptor();
template<typename T>
inline const std::string& ValueTypeSoc_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ValueTypeSoc>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ValueTypeSoc_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ValueTypeSoc_descriptor(), enum_t_value);
}
inline bool ValueTypeSoc_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ValueTypeSoc* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ValueTypeSoc>(
    ValueTypeSoc_descriptor(), name, value);
}
enum InvalidIdSoc : int {
  ID_DUMMY = 2147483647
};
bool InvalidIdSoc_IsValid(int value);
constexpr InvalidIdSoc InvalidIdSoc_MIN = ID_DUMMY;
constexpr InvalidIdSoc InvalidIdSoc_MAX = ID_DUMMY;
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* InvalidIdSoc_descriptor();
template<typename T>
inline const std::string& InvalidIdSoc_Name(T enum_t_value) {
  static_assert(::std::is_same<T, InvalidIdSoc>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function InvalidIdSoc_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    InvalidIdSoc_descriptor(), enum_t_value);
}
inline bool InvalidIdSoc_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, InvalidIdSoc* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<InvalidIdSoc>(
    InvalidIdSoc_descriptor(), name, value);
}
enum GroupIdSoc : int {
  GID_DUMMY = 2147483647
};
bool GroupIdSoc_IsValid(int value);
constexpr GroupIdSoc GroupIdSoc_MIN = GID_DUMMY;
constexpr GroupIdSoc GroupIdSoc_MAX = GID_DUMMY;
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* GroupIdSoc_descriptor();
template<typename T>
inline const std::string& GroupIdSoc_Name(T enum_t_value) {
  static_assert(::std::is_same<T, GroupIdSoc>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function GroupIdSoc_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    GroupIdSoc_descriptor(), enum_t_value);
}
inline bool GroupIdSoc_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, GroupIdSoc* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<GroupIdSoc>(
    GroupIdSoc_descriptor(), name, value);
}
enum SignalIdSoc : int {
  DVR_1_DVRFailReasons_541 = 134217728,
  DVR_1_RecordSts_541 = 134250497,
  STAT_AMPNaviVolume_461 = 134283266,
  STAT_AMPVRVolume_461 = 134316035,
  STAT_AMPKeyTone_461 = 134348804,
  STAT_AMPMediaVolume_461 = 134381573,
  STAT_AMPPhoneVolume_461 = 134414342,
  STAT_AMPSoundFocus_461 = 134447111,
  STAT_AMPSoundEffect_461 = 134479880,
  STAT_AMPSoundEffectBass_461 = 134512649,
  STAT_AMPSoundEffectMidrange_461 = 134545418,
  STAT_AMPSoundEffectTreble_461 = 134578187,
  STAT_AMPSoundFieldBalance_461 = 134610956,
  STAT_AMPSoundFieldFader_461 = 134643725,
  STAT_REQ_AMPA2BMediaSound_461 = 134676494,
  STAT_REQ_AMPA2BNaviSound_461 = 134709263,
  STAT_REQ_AMPA2BVRSound_461 = 134742032,
  STAT_REQ_AMPA2BTTSSound_461 = 134774801,
  STAT_REQ_AMPA2BPhoneSound_461 = 134807570,
  STAT_REQ_AMPAlarm_461 = 134840339,
  STAT_AMPMediaDuck_474 = 134873108,
  STAT_AMPSpeedVolume_474 = 134905877,
  STAT_AMPMute_474 = 134938646,
  STAT_AMPRestoreDefaults_474 = 134971415,
  STAT_AMPAlarmVolume_474 = 135004184,
  STAT_Headrest_Mode_474 = 135036953,
  STAT_AMPSoundEffectMegaBass_474 = 135069722,
  STAT_AMPSoundEffectMidBass_474 = 135102491,
  STAT_AMPSoundEffectMidTreble_474 = 135135260,
  STAT_VirtualSbwfrOnOff_474 = 135168029,
  STAT_SurndFnOnOff_474 = 135200798,
  STAT_AMPBackgroundVolume_474 = *********,
  STAT_AMPSoundBypass_474 = *********,
  STAT_AMPPwrRdySts_474 = *********,
  STAT_AMPVersion_474 = *********,
  EAMP_AMPKTVvoiceVolumeFed_4B6 = *********,
  EAMP_AMPENCVolumeFed_4B6 = *********,
  EAMP_AMPA2BKTVvoiceSoundFed_4B6 = *********,
  STAT_AMPSoundFocus_2_4B6 = *********,
  EAMP_AMPA2BENCSoundFed_4B6 = *********,
  EAMP_AMPA2BChimeSoundFed_4B6 = *********,
  EPC_LeftPedalSystem_Sts_551 = *********,
  EPC_RightPedalSystem_Sts_551 = *********,
  EPC_LeftPedalTimeTravel_551 = *********,
  EPC_RightPedalTimeTravel_551 = *********,
  EPC_LeftPedalRealTime_Sts_551 = *********,
  EPC_LeftPedalBreakIce_Sts_551 = *********,
  EPC_RightPedalRealTime_Sts_551 = *********,
  EPC_RightPedalBreakIce_Sts_551 = *********,
  EPC_Enable_CmdStatus_551 = *********,
  EPC_LeftPedalCmdStatus_551 = *********,
  EPC_RightPedalCmdStatus_551 = *********,
  EPC_LeftPedal3D_Sts_551 = *********,
  EPC_RightPedal3D_Sts_551 = *********,
  SBOX_MSD_Res_545 = *********,
  SBOX_Ecallphonenub_Res_545 = *********,
  SBOX_GNSS_ANT_State_5AB = *********,
  SBOX_SIM_State_5AB = *********,
  SBOX_BT_State_5AB = *********,
  SBOX_TT_State_5AB = *********,
  SBOX_TT_ANT_State_5AB = *********,
  SBOX_BatterState_5AB = *********,
  SBOX_PowerState_5AB = *********,
  SBOX_SimActivation_5AB = *********,
  SBOX_TspActivation_5AB = *********,
  SBOX_SatelliteStatus_5AB = *********,
  SBOX_BtConState_5AB = *********,
  SBOX_SelfCheck_5AB = *********,
  SBOX_SatelliteSignalValue_5AB = *********,
  SBOX_BatTemp_5AB = *********,
  SBOX_TimeSpentOnline_5AB = 268435526,
  SBOX_RunTime_5AB = 268468295,
  SBOX_EcallState_5AB = 136446024,
  SBOX_SetMsgTotal_547 = 268501065,
  SBOX_SetMSDTotal_547 = 268533834,
  SBOX_SetCallTimeThreshold_547 = 268566603,
  SBOX_SetTodayCallTimeThreshold_547 = 268599372,
  SBOX_SetCurretnCallTime_547 = 268632141,
  SBOX_SetCurretnMsgThreshold_547 = 268664910,
  SBOX_SetMsgTotalThreshold_547 = 268697679,
  SBOX_SetMsdThreshold_547 = 268730448,
  SBOX_SetCurrenMsdCnt_547 = 268763217,
  SBOX_SetCustomMsgThreshold_547 = 268795986,
  SBOX_SetCurrentCustomMsgCnt_547 = 268828755,
  SBOX_ICC_MACReq_549 = 805371988,
  SBOX_MAC_Req0_549 = 136478805,
  SBOX_MAC_Req1_549 = 136511574,
  SBOX_MAC_Req2_549 = 136544343,
  SBOX_MAC_Req3_549 = 136577112,
  SBOX_MAC_Req4_549 = 136609881,
  SBOX_MAC_Req5_549 = 136642650,
  SBOX_ActiveTsp_Res_54E = 805404763,
  SBOX_ActiveSim_Res_54E = 805437532,
  SBOX_Mac_Res0_54E = 136675421,
  SBOX_Mac_Res1_54E = 136708190,
  SBOX_Mac_Res2_54E = 136740959,
  SBOX_Mac_Res3_54E = 136773728,
  SBOX_Mac_Res4_54E = 136806497,
  SBOX_Mac_Res5_54E = 136839266,
  SBOX_GetPlatformNumberRes_54E = 805470307,
  SBOX_ICCId_Res0_54E = 136872036,
  SBOX_ICCId_Res1_54E = 136904805,
  SBOX_ICCId_Res2_54E = 136937574,
  SBOX_ICCId_Res3_54E = 136970343,
  SBOX_ICCId_Res4_54E = 137003112,
  SBOX_ICCId_Res5_54E = 137035881,
  SBOX_ICCId_Res6_54E = 137068650,
  SBOX_ICCId_Res7_54E = 137101419,
  SBOX_ICCId_Res8_54E = 137134188,
  SBOX_ICCId_Res9_54E = 137166957,
  SBOX_ICCId_Res10_54E = 137199726,
  SBOX_ICCId_Res11_54E = 137232495,
  SBOX_ICCId_Res12_54E = 137265264,
  SBOX_ICCId_Res13_54E = 137298033,
  SBOX_ICCId_Res14_54E = 137330802,
  SBOX_ICCId_Res15_54E = 137363571,
  SBOX_ICCId_Res16_54E = 137396340,
  SBOX_ICCId_Res17_54E = 137429109,
  SBOX_ICCId_Res18_54E = 137461878,
  SBOX_ICCId_Res19_54E = 137494647,
  SBOX_Vin0_54E = 137527416,
  SBOX_Vin1_54E = 137560185,
  SBOX_Vin2_54E = 137592954,
  SBOX_Vin3_54E = 137625723,
  SBOX_Vin4_54E = 137658492,
  SBOX_Vin5_54E = 137691261,
  SBOX_Vin6_54E = 137724030,
  SBOX_Vin7_54E = 137756799,
  SBOX_Vin8_54E = 137789568,
  SBOX_Vin9_54E = 137822337,
  SBOX_Vin10_54E = 137855106,
  SBOX_Vin11_54E = 137887875,
  SBOX_Vin12_54E = 137920644,
  SBOX_Vin13_54E = 137953413,
  SBOX_Vin14_54E = 137986182,
  SBOX_Vin15_54E = 138018951,
  SBOX_Vin16_54E = 138051720,
  PLG_LatchSts_436 = 138084489,
  BBCM_RLSeatEasyEntryExitFb_51E = 138117258,
  BBCM_RRSeatEasyEntryExitFb_51E = 138150027,
  BBCM_RLSeatNeedMemory_51E = 138182796,
  BBCM_RLMemoryFb_51E = 138215565,
  BBCM_RLRecoverFb_51E = 138248334,
  BBCM_RRSeatNeedMemory_51E = 138281103,
  BBCM_RRMemoryFb_51E = 138313872,
  BBCM_RRRecoverFb_51E = 138346641,
  BBCM_RLSeatLvlFb2nd_51E = 138379410,
  BBCM_RLSeatBackAgFb2nd_51E = 138412179,
  BBCM_RLSeatLegSpprtFb2nd_51E = 138444948,
  BBCM_RLSeatLegRestAngleFb2nd_51E = 138477717,
  BBCM_RRSeatLvlFb2nd_51E = 138510486,
  BBCM_RRSeatBackAgFb2nd_51E = 138543255,
  BBCM_RRSeatLegSpprtFb2nd_51E = 138576024,
  BBCM_RRSeatLegRestAngleFb2nd_51E = 138608793,
  BBCM_RLSeatBackAgFb3rd_51E = 138641562,
  BBCM_RRSeatBackAgFb3rd_51E = 138674331,
  BBCM_RLSeatFrontFlipFb3rd_51E = 138707100,
  BBCM_RRSeatFrontFlipFb3rd_51E = 138739869,
  CMSM_DisplaySwitch_4B1 = 138772638,
  CMSM_DisplayAdjustrang_4B1 = 138805407,
  CMSM_DisplayAdjustmode_4B1 = 138838176,
  CMSM_DisplayStandard_4B1 = 138870945,
  TotalOdometer_km_OBD_4E4 = 402653346,
  DisplayVehicleSpeed_4E4 = 268861603,
  ICC_BackLightLevel_4E4 = 138903716,
  TotalOdometer_km_OBDValidData_4E4 = 805503141,
  ICC_TotalodometerbackupEnable_4E4 = 138936486,
  STAT_AMPAlarm_52A = 138969255,
  Fusa_498_6_498 = 139002024,
  Fusa_498_5_498 = 139034793,
  Fusa_498_4_498 = 139067562,
  Fusa_498_3_498 = 139100331,
  Fusa_498_2_498 = 139133100,
  Fusa_498_1_498 = 139165869,
  ICC_CH_3_CRC_498 = 139198638,
  ICC_CH_3_RollgCntr_498 = 139231407,
  ICC_ASUAssistPassSet_498 = 139264176,
  IHU_AutHldSet_498 = 139296945,
  ICC_AutoEasyEntrySet_498 = 139329714,
  ICC_ManualEasyOutSw_498 = 139362483,
  ICC_ASCMaintainModeReq_498 = 139395252,
  ICC_PABSetCmd_498 = 139428021,
  ICC_EPBSetCmd_498 = 139460790,
  ICC_ASUMemoryPosition_498 = 139493559,
  ICC_ASUEasyLoadingSet_498 = 139526328,
  Set_CSTFunctionSts_498 = 139559097,
  ICC_ASURearaxlewithTaildoor_498 = 139591866,
  CST_SensitivityReq_498 = 139624635,
  TIHU_SetHDCOnOff_498 = 139657404,
  Set_ESPFunctionSts_498 = 139690173,
  ICC_ASUPreviewCont_498 = 139722942,
  ICC_ASUHighwayModAdjust_498 = 139755711,
  ICC_ASUMapAdjust_498 = 139788480,
  ICC_DisChg_4D7 = 139821249,
  ICC_StpDisChgReq_4D7 = 805535938,
  ICC_RegenLevel_4D7 = 139854019,
  ICC_ELockReq_4D7 = 805568708,
  ICC_DisChgPercent_4D7 = 139886789,
  ICC_ChgSocSet_4D7 = 139919558,
  ICC_CampMode_4D7 = 139952327,
  ICC_SftWarning_4D7 = 139985096,
  ICC_TowingMode_4D7 = 140017865,
  ICC_StopMode_4D7 = 140050634,
  ICC_EPedalMode_4D7 = 140083403,
  ICC_WindowKeySts_4DE = 140116172,
  ICC_FaceKeySts_4DE = 140148941,
  ICC_FootKeySts_4DE = 140181710,
  BltCallSts_4DE = 140214479,
  ICC_DockWinspSts_4DE = 140247248,
  ICC_DockTSts_4DE = 140280017,
  ICC_ACFastCool_4DE = 140312786,
  ICC_ACFastHeat_4DE = 140345555,
  ICC_SetCLMOn_4DE = 140378324,
  ICC_SetAutoKeySts_4DE = 140411093,
  ICC_SetFrontDeforestSts_4DE = 140443862,
  ICC_SetCirculationModeKeySts_4DE = 140476631,
  ICC_ZoneSelectionKeySts_4DE = 140509400,
  ICC_ResetFilter_4DE = 140542169,
  ICC_AUTODefrostOnKeySts_4DE = 140574938,
  ICC_AutoAirClean_4DE = 140607707,
  ICC_KeepWarm_4DE = 140640476,
  ICC_BlowSpeedLevelKeySts_4DE = 140673245,
  ICC_SetACRequestKeySts_4DE = 140706014,
  ICC_DSwingSet_4DE = 140738783,
  ICC_PSwingSet_4DE = 140771552,
  ICC_AUTODefLevelSet_4DE = 140804321,
  ICC_SetTemperature_L_C_4DE = 140837090,
  ICC_SetTemperature_R_C_4DE = 140869859,
  ICC_DLSwingUpDwn_4DE = 140902628,
  ICC_DLSwingLeRi_4DE = 140935397,
  ICC_DRSwingUpDwn_4DE = 140968166,
  ICC_DRSwingLeRi_4DE = 141000935,
  ICC_PLSwingUpDwn_4DE = 141033704,
  ICC_PLSwingLeRi_4DE = 141066473,
  ICC_PRSwingUpDwn_4DE = 141099242,
  ICC_PRSwingLeRi_4DE = 141132011,
  ICC_DCSwingLeRi_4DE = 141164780,
  ICC_NetWorkTemperature_4DE = 141197549,
  ICC_NetWorkHumidity_4DE = 141230318,
  ICC_DLOnOff_4DE = 141263087,
  ICC_DROnOff_4DE = 141295856,
  ICC_PLOnOff_4DE = 141328625,
  ICC_PROnOff_4DE = 141361394,
  ICC_DCOnOff_4DE = 141394163,
  ICC_LoveRemind_4DE = 141426932,
  ICC_InteCleanCar_4DE = 141459701,
  ICC_KeepWarmMemory_4DE = 141492470,
  ICC_AutoLightSW_4E2 = 805601527,
  ICC_CleanMode_0x4e2_4E2 = 141525240,
  ICC_1_OTASts_4E2 = 141558009,
  ICC_1_OTAHV_Req_4E2 = 141590778,
  ICC_1_OTAPwrMngt_4E2 = 141623547,
  ICC_DriveModeSwitch_4E2 = 141656316,
  ICC_PropulsionMode_4E2 = 141689085,
  ICC_SteeringMode_4E2 = 141721854,
  ICC_SuspensionHeight_4E2 = 141754623,
  ICC_SuspensionDamping_4E2 = 141787392,
  ICC_AirconditionMode_4E2 = 141820161,
  ICC_BrakePedalFeelMode_4E2 = 141852930,
  ICC_SentinelHV_Req_4E2 = 141885699,
  ICC_PetModeHV_Req_4E2 = 141918468,
  ICC_SelfClearing_4E2 = 141951237,
  ICC_WelcomeOpenSet_4E6 = 141984006,
  ICC_WALOpenSet_4E6 = 142016775,
  ICC_EasytrunkSet_4E6 = 142049544,
  ICC_UIROpenSet_4E6 = 142082313,
  ICC_BeanIDReq_4E6 = 142115082,
  ICC_CWCWorkingStsSet_4E6 = 142147851,
  ICC_CWCPhoneforgottenFunStsSet_4E6 = 142180620,
  ICC_RCWCWorkingStsSet_4E6 = 142213389,
  ICC_RCWCPhoneforgottenFunStsSet_4E6 = 142246158,
  CurrentTimeYear_0x510_510 = 142278927,
  CurrentTimeMonth_0x510_510 = 142311696,
  CurrentTimeDay_0x510_510 = 142344465,
  CurrentTimeHour_0x510_510 = 142377234,
  CurrentTimeMinute_0x510_510 = 142410003,
  CurrentTimeSecond_0x510_510 = 142442772,
  CurrentTimeYear_UTC_5A7 = 142475541,
  CurrentTimeMonth_UTC_5A7 = 142508310,
  CurrentTimeDay_UTC_5A7 = 142541079,
  CurrentTimeHour_UTC_5A7 = 142573848,
  CurrentTimeMinute_UTC_5A7 = 142606617,
  CurrentTimeSecond_UTC_5A7 = 142639386,
  Set_AMPNaviVolume_44C = 142672155,
  Set_AMPVRVolume_44C = 142704924,
  Set_AMPKeyTone_44C = 142737693,
  Set_AMPMediaVolume_44C = 142770462,
  Set_AMPPhoneVolume_44C = 142803231,
  Set_AMPSoundFocus_44C = 142836000,
  Set_AMPSoundEffect_44C = 142868769,
  Set_AMPSoundEffectBass_44C = 142901538,
  Set_AMPSoundEffectMidrange_44C = 142934307,
  Set_AMPSoundEffectTreble_44C = 142967076,
  Set_AMPSoundFieldBalance_44C = 142999845,
  Set_AMPSoundFieldFader_44C = 143032614,
  Set_Headrest_Mode_44C = 143065383,
  Set_SurndFnOnOff_44C = 143098152,
  Set_VirtualSbwfrOnOff_44C = 143130921,
  Set_AMPMediaDuck_45F = 143163690,
  Set_AMPSpeedVolume_45F = 143196459,
  Set_AMPMute_45F = 143229228,
  REQ_AMPA2BMediaSound_45F = 143261997,
  REQ_AMPA2BNaviSound_45F = 143294766,
  REQ_AMPA2BVRSound_45F = 143327535,
  REQ_AMPA2BTTSSound_45F = 143360304,
  REQ_AMPA2BKeyTone_45F = 143393073,
  Set_AMPRestoreDefaults_45F = 143425842,
  Set_AMPAlarmVolume_45F = 143458611,
  REQ_AMPA2BPhoneSound_45F = 143491380,
  Set_AMPSoundEffectMegaBass_45F = 143524149,
  Set_AMPSoundEffectMidBass_45F = 143556918,
  Set_AMPSoundEffectMidTreble_45F = 143589687,
  Set_AMPBackgroundVolume_45F = 143622456,
  Set_AMPSoundBypass_45F = 143655225,
  Set_AMPA2BConfig_45F = 143687994,
  Fusa_47E_3_47E = 805634363,
  Fusa_47E_2_47E = 143720764,
  Fusa_47E_1_47E = 143753533,
  ICC_BD_11_CRC_47E = 143786302,
  ICC_BD_11_RollgCntr_47E = 143819071,
  ICC_CPDDelaySwitchSet_47E = 143851840,
  ICC_PPMIDBSRBPASet_47E = 143884609,
  ICC_PPMIDHwSet_47E = 143917378,
  ICC_DriveDrosinessLevel_47E = 143950147,
  ICC_FLDoorButton_47E = 143982916,
  ICC_FRDoorButton_47E = 144015685,
  ICC_RRDoorButton_47E = 144048454,
  ICC_RLDoorButton_47E = 144081223,
  ICC_CloseAllDoors_47E = 144113992,
  ICC_DoorRemoteKeyControlEnable_47E = 144146761,
  ICC_SetOpenAngle_47E = 144179530,
  ICC_CPDSwitchSet_47E = 144212299,
  ICC_FrontDoorPowerMode_47E = 144245068,
  ICC_RearDoorPowerMode_47E = 144277837,
  ICC_PedalCloseDriverDoorEnable_47E = 144310606,
  RR_Door_RearScreen_Control_47E = 144343375,
  ICC_RearDoorVoiceControlEnable_47E = 144376144,
  ICC_FLVoiceControl_47E = 144408913,
  ICC_FRVoiceControl_47E = 144441682,
  ICC_RRVoiceControl_47E = 144474451,
  ICC_RLVoiceControl_47E = 144507220,
  RL_Door_RearScreen_Control_47E = 144539989,
  ADBenable_395 = 144572758,
  RandomMusicShowCMD_395 = 144605527,
  DIYMusicShowmod_395 = 144638296,
  LiShowMod_395 = 144671065,
  MusicLoudness_55HZ_395 = 144703834,
  MusicLoudness_123HZ_395 = 144736603,
  MusicLoudness_262HZ_395 = 144769372,
  MusicLoudness_440HZ_395 = 144802141,
  MusicLoudness_587HZ_395 = 144834910,
  MusicLoudness_784HZ_395 = 144867679,
  MusicLoudness_1318HZ_395 = 144900448,
  MusicLoudness_2794HZ_395 = 144933217,
  MusicLoudness_6272HZ_395 = 144965986,
  ISDShowCMD_395 = 805667171,
  ISDShowMode_395 = 144998756,
  FaceID_4F9 = 145031525,
  Fusa_485_2_485 = 268894566,
  Fusa_485_1_485 = 145064295,
  ICC_DA_15_CRC_485 = 145097064,
  ICC_DA_15_RollgCntr_485 = 145129833,
  ICC_DA_15_Resd_485 = 145162602,
  ICC_DA_15_BSDLCWSettingSt_485 = 145195371,
  ICC_DA_15_DOWSettingSt_485 = 145228140,
  ICC_DA_15_RCWSettingSt_485 = 145260909,
  ICC_DA_15_RCTARCTBWarnType_485 = 145293678,
  ICC_DA_15_FCTAFCTBWarnType_485 = 145326447,
  ICC_DA_15_FrontPDCMuteSet_485 = 805699952,
  ICC_DA_15_FKPMuteSet_485 = 805732721,
  ICC_DA_15_ExtremeEnergySaveMode_485 = 805765490,
  Driver_EyeOnRoad_485 = 145359219,
  Dms_Status_485 = 145391988,
  Driver_AbnormalBehavior_485 = 145424757,
  Driver_GazeRegion_485 = 145457526,
  Dms_DriverPresence_485 = 145490295,
  ICC_IHCfunctionSw_485 = 145523064,
  Weather_Type_4FF = 145555833,
  ICC_SentinelModeSts_4FF = 145588602,
  ICC_CameraErrorCode_4FF = 145621371,
  ICC_SentinelRequest_4FF = 145654140,
  ICC_StartDeInitCameraStream_4FF = 145686909,
  ICC_CameraRecoveryRequest_4FF = 145719678,
  ICC_CleanMode_0x4ff_4FF = 145752447,
  ICM_5_FuelLevel_284 = 145785216,
  FuelShortGround_284 = 805798273,
  FuelShortPower_284 = 805831042,
  FuelOutOfRange_284 = 805863811,
  FuelValidData_284 = 805896580,
  IHU_SetSocManage_496 = 145817989,
  IHU_SetSOC_496 = 145850758,
  CTP_PowerModeSet_496 = 145883527,
  LidOpenReq_496 = 805929352,
  ICC_ForcedEVMode_496 = 145916297,
  ICC_EmissionMode_496 = 805962122,
  ICC_ForcedEVChargeMode_496 = 145949067,
  EVOdometer_km_496 = 402686348,
  EVOdometer_km_ValidData_496 = 805994893,
  ICC_SetChrgnFctMem_496 = 145981838,
  ICC_DK_19_CRC1_4DA = 146014607,
  ICC_DK_19_RollgCntr1_4DA = 146047376,
  ICC_DK_19_Resd1_4DA = 146080145,
  ICC_DK_19_CMD_4DA = 268927378,
  ICC_DK_19_PDU1_4DA = 536871315,
  ICC_DK_19_PDU2_4DA = 536904084,
  ICC_DK_19_PDU3_4DA = 536936853,
  ICC_DK_19_PDU4_4DA = 402719126,
  PassSeatHeiAdjmt_Target_4FC = 146112919,
  SeatBackAndFwdAdjmt_Target_4FC = 146145688,
  SeatBackAgAdjmt_Target_4FC = 146178457,
  Set_ProfileID_4FC = 146211226,
  SeatCushAgAdjmt_Target_4FC = 146243995,
  LgRstFwdAndBckAdjmt_Target_4FC = 146276764,
  FootRestAdjmt_Target_4FC = 146309533,
  REQ_SeatPosRecall_4FC = 146342302,
  REQ_SeatPosStore_4FC = 146375071,
  Set_ToDefault_4FC = 146407840,
  Set_EasyEntryEnable_4FC = 146440609,
  REQ_SeatHeatPass_4FC = 146473378,
  REQ_SeatVentPass_4FC = 146506147,
  PassSeatMsgStr_LvlCmd_0x4fc_4FC = 146538916,
  PassSeatMsg_ModeCmd_0x4fc_4FC = 146571685,
  ICC_FLSeatHeatLevelCmd_423 = 146604454,
  ICC_FRSeatHeatLevelCmd_423 = 146637223,
  ICC_TrunkSW_423 = 146669992,
  ICC_Set_PLGOperateSts_423 = 146702761,
  ICC_RLSeatHeatLevelCmd_423 = 146735530,
  ICC_RRSeatHeatLevelCmd_423 = 146768299,
  ICC_FLSeatVentLevelCmd_423 = 146801068,
  ICC_FRSeatVentLevelCmd_423 = 146833837,
  ICC_RLSeatVentLevelCmd_423 = 146866606,
  ICC_RRSeatVentLevelCmd_423 = 146899375,
  ICC_FrontWiperMaintenanceMode_423 = 146932144,
  ICC_RearWiperMaintenanceMode_423 = 146964913,
  ICC_MultiplexSignalStatusSet_451 = 146997682,
  ICC_DSBH_HeatReq_420 = 147030451,
  ICC_DSBH_SetTemp_420 = 147063220,
  ICC_PSBH_HeatReq_420 = 147095989,
  ICC_PSBH_SetTemp_420 = 147128758,
  PassSeatMsgStr_LvlCmd_0x458_458 = 147161527,
  PassSeatMsg_ModeCmd_0x458_458 = 147194296,
  DriverSeatMsgStr_Lvlcmd_458 = 147227065,
  DriverSeatMsg_ModeCmd_458 = 147259834,
  RightSeatMsgStr_LvlCmd_458 = 147292603,
  RightSeatMsg_ModeCmd_458 = 147325372,
  LeftSeatMsgStr_LvlCmd_458 = 147358141,
  LeftSeatMsg_ModeCmd_458 = 147390910,
  ICC_PwrOff_4F1 = 806027711,
  ICC_EtmEgySave_4F1 = 806060480,
  ICC_HoodSW_4F1 = 147423681,
  ICC_GloveboxSW_4F1 = 147456450,
  ICC_ChrgPortCover_Switch_4F1 = 147489219,
  ICC_ReWinDefrstSwt_4F1 = 147521988,
  ICC_RearMirrorFoldCmd_4F1 = 147554757,
  ICC_CenterLockSwt_4F1 = 147587526,
  ICC_ReverseExtMirrorSts_4F1 = 147620295,
  ICC_LightMainSwitchSts_4F1 = 147653064,
  ICC_AutoFoldSts_4F1 = 147685833,
  ICC_HeadlampHeightSts_4F1 = 147718602,
  ICC_WiprSnvty_4F1 = 147751371,
  ICC_WindowCmd_4F1 = 147784140,
  ICC_LowBeamDelayOff_4F1 = 147816909,
  ICC_FLWinCmd_4F1 = 147849678,
  ICC_FRWinCmd_4F1 = 147882447,
  ICC_RLWinCmd_4F1 = 147915216,
  ICC_RRWinCmd_4F1 = 147947985,
  ICC_FGHeatSts_4F1 = 147980754,
  ICC_AutolockSts_4F1 = 148013523,
  ICC_WiperID_4F1 = 148046292,
  ICC_MaintenanceMode_4F1 = 148079061,
  ICC_lockSetSwitchSts_4F1 = 148111830,
  ICC_ChildLockSW_4F1 = 148144599,
  ICC_ParkUnlockEnable_4F1 = 148177368,
  ICC_BLEOpenDriverDoorEnable_4F1 = 148210137,
  ICC_WinInhbSwt_4F1 = 148242906,
  ICC_SteerWhlHeatgSwt_4F1 = 148275675,
  ICC_AutoHeatingset_4F1 = 148308444,
  ICC_FrontFogSw_4F1 = 148341213,
  ICC_RearFogSw_4F1 = 148373982,
  ICC_DoorControlSW_4F1 = 148406751,
  ICC_LockCarWinCloseSw_4F1 = 148439520,
  ICC_RainCarWinCloseSw_4F1 = 148472289,
  ICC_EasyEntryExitSet_4F1 = 148505058,
  ICC_NapTimeSet_4F1 = 148537827,
  ICC_NapAreaSet_4F1 = 148570596,
  ICC_NapStatusSet_4F1 = 148603365,
  ICC_NapCFSStatusSet_4F1 = 148636134,
  ICC_NapSetTemperature_4F1 = 148668903,
  ICC_SpoilerWelcomeFunSet_4F1 = 148701672,
  ICC_SpoilerCtrlCmd_4F1 = 148734441,
  ICC_HazardLightReq_4F1 = 148767210,
  ICC_MusicStatus_4F1 = 148799979,
  ICC_CinemaStatus_4F1 = 148832748,
  ICC_BookChrgSetReq_471 = 148865517,
  ICC_BkChrgStartTimeYear_471 = 268960238,
  ICC_BkChrgStartTimeDay_471 = 148898287,
  ICC_BkChrgStartTimeHour_471 = 148931056,
  ICC_BkChrgStartTimemMin_471 = 148963825,
  ICC_BkChrgStartTimeMonth_471 = 148996594,
  ICC_BkChrgDuration_471 = 268993011,
  ICC_BkChrgCycleType_471 = 149029364,
  ICC_StopChrgReq_471 = 149062133,
  ICC_BkChrgCycleMon_471 = 149094902,
  ICC_BkChrgCycleTues_471 = 149127671,
  ICC_BkChrgCycleWen_471 = 149160440,
  ICC_BkChrgCycleThur_471 = 149193209,
  ICC_BkChrgCycleFri_471 = 149225978,
  ICC_BkChrgCycleSat_471 = 149258747,
  ICC_BkChrgCycleSun_471 = 149291516,
  ICC_KeepWarmStrt_471 = 149324285,
  ICC_KeepWarmStrtHour_471 = 149357054,
  ICC_KeepWarmStrtMin_471 = 149389823,
  ICC_PetmodeFb_471 = 149422592,
  ICC_SentinelModeSwitchSts_471 = 149455361,
  ICC_SentinelModeWorkingSts_471 = 149488130,
  ICC_SentinelModeFaultSts_471 = 149520899,
  ICC_SentinelModeAlarmSts_471 = 149553668,
  ICC_SentinelModeExitReason_471 = 149586437,
  ICC_PetModeWarn_471 = 149619206,
  ICC_TBOXPetmodeReq_471 = 149651975,
  ICC_PetModeWarn_2_471 = 149684744,
  ICM_FuelLevel_471 = 149717513,
  ICM_AverageVehicleSpeed_471 = 149750282,
  ICM_DistenceToEmpty_Km_471 = 269025803,
  ICM_FuelLevelFailSts_471 = 806093324,
  ICM_Maintenance_tips_471 = 806126093,
  ICM_SumTrip_471 = 269058574,
  ICC_HvBattKeepWarmSet_478 = 149783055,
  ICC_EnergyRegSet_478 = 149815824,
  ICC_HvBattKeepWarmActiveReq_478 = 149848593,
  ICC_FLSeatHeiCmd_4BA = 149881362,
  ICC_FLSeatLvlCmd_4BA = 149914131,
  ICC_FLSeatBackAgCmd_4BA = 149946900,
  ICC_FLSeatCushCmd_4BA = 149979669,
  ICC_FLSeatLegSpprtCmd_4BA = 150012438,
  ICC_FRSeatHeiCmd_4BA = 150045207,
  ICC_FRSeatLvlCmd_4BA = 150077976,
  ICC_FRSeatBackAgCmd_4BA = 150110745,
  ICC_FRSeatLegSpprtCmd_4BA = 150143514,
  ICC_MemoryRecoveryCmd_4BA = 150176283,
  ICC_DeleteFaceID_4BA = 150209052,
  ICC_FLSeatHeitargetCmd_4BA = 150241821,
  ICC_FLSeatLvltargetCmd_4BA = 150274590,
  ICC_FLSeatBackAgtargetCmd_4BA = 150307359,
  ICC_FLSeatCushtargetCmd_4BA = 150340128,
  ICC_FLSeatLegSpprttargetCmd_4BA = 150372897,
  ICC_FRSeatHeitargetCmd_4BA = 150405666,
  ICC_FRSeatLvltargetCmd_4BA = 150438435,
  ICC_FRSeatBackAgtargetCmd_4BA = 150471204,
  ICC_FRSeatLegSpprttargetCmd_4BA = 150503973,
  ICC_NapFLSeatBackAgtargetSet_4BA = 150536742,
  ICC_NapFRSeatBackAgtargetSet_4BA = 150569511,
  ICC_NapFLSeatLvltargetSet_4BA = 150602280,
  ICC_NapFRSeatLvltargetSet_4BA = 150635049,
  ICC_FRSeatLegRestAngleCmd_4BA = 150667818,
  ICC_FRSeatLegRestAngletargetCmd_4BA = 150700587,
  ICC_FRSeatCushCmd_4BA = 150733356,
  ICC_FRSeatCushtargetCmd_4BA = 150766125,
  ICC_FRZeroGravitySeatSw_4BA = 150798894,
  ICC_HomeLinkArmHornSet_4BA = 150831663,
  ICC_HomeLinkWelLightSet_4BA = 150864432,
  ICC_HomeLinkPositionSts_4BA = 150897201,
  ICC_WelcomeOpenSetCmd_4BA = 150929970,
  ICC_WALOpenSetCmd_4BA = 150962739,
  ICC_UIROpenSetCmd_4BA = 150995508,
  ICC_EnjoyableSeatSwitch_4BA = 151028277,
  ICC_BLTKeyPESet_4BA = 151061046,
  ICC_FRMemoryDeleteCmd_4BA = 151093815,
  ICC_FLSitPosnlocation_4BA = 151126584,
  ICC_FRSitPosnlocation_4BA = 151159353,
  ICC_FREasyEntryExitSet_4BA = 151192122,
  ICC_FRMemoryRecoveryCmd_4BA = 151224891,
  ICC_ConsoleAdjustSwitch_4BA = 151257660,
  ICC_CLTC_RangeAval_577 = 269091389,
  ICC_DynamicRangeAval_577 = 269124158,
  ICC_WLTC_RangeAval_577 = 269156927,
  ICC_CLTC_RangeAvalFuel_577 = 269189696,
  ICC_WLTC_RangeAvalFuel_577 = 269222465,
  ICC_DynamicRangeAvalFuel_577 = 269255234,
  ICC_CLTC_RangeAvalComp_577 = 269288003,
  ICC_WLTC_RangeAvalComp_577 = 269320772,
  ICC_DynamicRangeAvalComp_577 = 269353541,
  ICC_Conditiontype_577 = 151290438,
  ICC_CFSSwitch_4D6 = 151323207,
  ICC_CFSLevelSet_4D6 = 151355976,
  ICC_CFSPosSet_4D6 = 151388745,
  ICC_PM25Switch_4D6 = 151421514,
  ICC_AQSSwitch_4D6 = 151454283,
  ICC_SecSetTemp_C_4D6 = 151487052,
  ICC_SecFaceKeySts_4D6 = 151519821,
  ICC_SecFootKeySts_4D6 = 151552590,
  ICC_SecBlowSpeedLevelKeySts_4D6 = 151585359,
  ICC_SecAutoSwitch_4D6 = 151618128,
  ICC_SecCLMSwitch_4D6 = 151650897,
  ICC_ThrSetTemp_C_4D6 = 151683666,
  ICC_ThrFaceKeySts_4D6 = 151716435,
  ICC_ThrFootKeySts_4D6 = 151749204,
  ICC_ThrBlowSpeedLevelKeySts_4D6 = 151781973,
  ICC_ThrAutoSwitch_4D6 = 151814742,
  ICC_ThrCLMSwitch_4D6 = 151847511,
  ICC_ThrACRequestKeySts_4D6 = 151880280,
  ICC_DFMSwitch_4D6 = 151913049,
  ICC_TirePressureDisplayUnit_509 = 151945818,
  ICC_TireTemperatureDisplayUnit_509 = 151978587,
  ICC_BtConFlag_509 = 152011356,
  ICC_ToggleUnits_509 = 152044125,
  ICC_KeyDriveModememory_48F = 152076894,
  ICC_DriveModeSet_48F = 152109663,
  ICC_DRIFTModeSetReq_48F = 152142432,
  ICC_EXPERTEIPBESPSet_48F = 152175201,
  ICC_EXPERTEcoSet_48F = 152207970,
  ICC_EXPERTNormSet_48F = 152240739,
  ICC_EXPERTSportSet_48F = 152273508,
  ICC_CrossaxisSet_48F = 152306277,
  ICC_EXPERTRWDSet_48F = 152339046,
  ICC_EXPERTAWDSet_48F = 152371815,
  ICC_EXPERTAutoSet_48F = 152404584,
  ICC_CLIMBSet_48F = 152437353,
  ICC_DriveModeExitSet_48F = 152470122,
  ICC_XDriveMode_48F = 152502891,
  ICC_Confidencelevel_48F = 152535660,
  ICC_V2L_IntlDisChg_48F = 152568429,
  ICC_ExhibitionModeSwitch_48F = 152601198,
  ICC_TrailerMode_48F = 152633967,
  ICC_V2L_DisChgMem_48F = 152666736,
  ICC_DrivePowerDispSet_48F = 152699505,
  ICC_WLTC_to_CLTC_Mode_48F = 152732274,
  ICC_StopChrgnSwitch_48F = 152765043,
  ICC_StopChrgnMode_48F = 152797812,
  ICC_RegenerateLevelCtrl_48F = 152830581,
  ICC_SinglePedalMem_48F = 152863350,
  ICC_FuelDetnSwt_48F = 152896119,
  ICC_FuelDetnState_48F = 152928888,
  ICC_FuelDetnOpDefeated_48F = 152961657,
  ICC_HVDownRepairMode_48F = 152994426,
  ICC_UTURNSnowSet_48F = 153027195,
  ICC_UTURNSandSet_48F = 153059964,
  ICC_UTURNMudSet_48F = 153092733,
  ICC_UTURNGrassSet_48F = 153125502,
  ICC_LTCDispSet2_48F = 153158271,
  ICC_SetSocManage_48F = 153191040,
  ICC_DWDOnOff_Req_52E = 153223809,
  ICC_ContainerLightSet_52E = 153256578,
  ICC_RLSeatEasyEntryExitSet_533 = 153289347,
  ICC_RRSeatEasyEntryExitSet_533 = 153322116,
  ICC_RLMemoryRecoveryCmd_533 = 153354885,
  ICC_RRMemoryRecoveryCmd_533 = 153387654,
  ICC_RLSitPosnlocation_533 = 153420423,
  ICC_RRSitPosnlocation_533 = 153453192,
  ICC_CMSMDisplaySwitch_533 = 153485961,
  ICC_CMSMDisplayAdjustmode_533 = 153518730,
  ICC_CMSMSetstandardSwitch_533 = 153551499,
  ICC_LeftPedalControl_533 = 153584268,
  ICC_RightPedalControl_533 = 153617037,
  ICC_EPCEnable_533 = 153649806,
  ICC_AMPKTVvoiceVolumeSet_533 = 153682575,
  ICC_AMPA2BENCSoundReq_533 = 153715344,
  ICC_AMPENCVolumeSet_533 = 153748113,
  ICC_AMPA2BChimeSoundReq_533 = 153780882,
  Set_AMPSoundFocus_2_533 = 153813651,
  ICC_AMPA2BKTVvoiceSoundReq_533 = 153846420,
  ICC_MedStatusSet_533 = 153879189,
  ICC_MedAreaSet_533 = 153911958,
  ICC_LeftElectricPedalsSet_533 = 153944727,
  ICC_RightElectricPedalsSet_533 = 153977496,
  ICC_NapAreaSet2_533 = 154010265,
  ICC_DisplayAdjustrAngReq_533 = 154043034,
  ICC_BedStatus_533 = 154075803,
  ICC_SRFCmd_535 = 154108572,
  ICC_SRFPercentCmd_535 = 154141341,
  ICC_CSunshadeReq_535 = 154174110,
  ICC_CSunshadePercentReq_535 = 154206879,
  ICC_SunshadeCmd_535 = 154239648,
  ICC_SunshadePercentCmd_535 = 154272417,
  ICC_SET_EPS_SteerReturnRmdSts_537 = 154305186,
  ICC_SET_MFSShake_537 = 154337955,
  ICC_DMS_1_DistractionLevel_537 = 154370724,
  ICC_OverLoadWarnShieldSet_537 = 154403493,
  ICC_BSWReq_537 = 154436262,
  ICC_horizontalReq_537 = 154469031,
  ICC_SET_BT_ReduceWindSpeed_539 = 154501800,
  ICC_SET_IPM_FirstBlowing_539 = 154534569,
  ICC_SET_IPM_BlowerDelay_539 = 154567338,
  ICC_SET_CirculationInTunnels_539 = 154600107,
  ICC_UVCLuminanceReq_539 = 154632876,
  ICC_UVCControlReq_539 = 154665645,
  ICC_CrossCountryCoolingReq_539 = 154698414,
  ICC_KeepWarmReq_539 = 154731183,
  ICC_keepwarmSetTemperatureReq_539 = 154763952,
  ICC_ParkingAirConditioning_539 = 154796721,
  ICC_CoolantFill_Req_539 = 154829490,
  ICC_FraganceLightoffReq_539 = 154862259,
  ICC_FraganceMemoffReq_539 = 154895028,
  ICC_AirPurgeReminderReq_539 = 154927797,
  ICC_SET_TDL_Rhythm_Switch_526 = 154960566,
  ICC_SET_TDL_Switch_526 = 154993335,
  ICC_SET_TDL_ColourModeAdj_526 = 155026104,
  ICC_VoiceWake_526 = 155058873,
  ICC_TDL_RhythmReq_526 = 155091642,
  ICC_SET_ApiluminanceAdj_526 = 155124411,
  ICC_SET_MusicLoudness_120HZ_526 = 155157180,
  ICC_SET_MusicLoudness_250HZ_526 = 155189949,
  ICC_SET_MusicLoudness_500HZ_526 = 155222718,
  ICC_SET_MusicLoudness_1000HZ_526 = 155255487,
  ICC_SET_MusicLoudness_1500HZ_526 = 155288256,
  ICC_SET_MusicLoudness_2000HZ_526 = 155321025,
  ICC_SET_MusicLoudness_6000HZ_526 = 155353794,
  ICC_TDL_FlowLightSw_526 = 155386563,
  ICC_TDL_FlowLightModeAdj_526 = 155419332,
  ICC_SET_256ColourAdj_526 = 269386437,
  ICC_Chb_Req_528 = 155452102,
  ICC_ChbMem_Req_528 = 155484871,
  ICC_ChbDelay_Req_528 = 155517640,
  ICC_ChbSterilization_Req_528 = 155550409,
  ICC_ChbItemsLeft_Req_528 = 155583178,
  ICC_ParkingRadarSwSet_528 = 155615947,
  ICC_ChbTimeset_Req_528 = 155648716,
  ICC_ChbCoolset_Req_528 = 155681485,
  ICC_ChbCoolorheat_Req_528 = 155714254,
  ICC_ChbHeatset_Req_528 = 155747023,
  ICC_LISD_DisplaySwitch_550 = 155779792,
  ICC_LISD_ParkingShowCMD_550 = 155812561,
  ICC_RoofCampLampSetL_550 = 155845330,
  ICC_RISD_DisplaySwitch_550 = 155878099,
  ICC_RISD_ParkingShowCMD_550 = 155910868,
  ICC_RoofCampLampSetR_550 = 155943637,
  ICC_LogoParkingReq_550 = 155976406,
  ICC_LogoChargingReq_550 = 156009175,
  ICC_DRLSw_550 = 156041944,
  ICC_TopLightSet_550 = 156074713,
  ICC_APillarYellowAmbientSet_550 = 156107482,
  ICC_APillarWhiteAmbientSet_550 = 156140251,
  ICC_LISD_ParkingShowMod_550 = 156173020,
  ICC_RISD_ParkingShowMod_550 = 156205789,
  ICC_APillarSpotLampSet_550 = 156238558,
  ICC_PenetrationLampSetF_550 = 156271327,
  ICC_MusicLampShowSW_550 = 156304096,
  ICC_LogoColorReq_550 = 269419233,
  ICC_LISD_FrameCounter_552 = 156336866,
  ICC_LISD_RollingCounter_552 = 156369635,
  ICC_LISD_Pixel1_OnOff_552 = 806159076,
  ICC_LISD_Pixel2_OnOff_552 = 806191845,
  ICC_LISD_Pixel3_OnOff_552 = 806224614,
  ICC_LISD_Pixel4_OnOff_552 = 806257383,
  ICC_LISD_Pixel5_OnOff_552 = 806290152,
  ICC_LISD_Pixel6_OnOff_552 = 806322921,
  ICC_LISD_Pixel7_OnOff_552 = 806355690,
  ICC_LISD_Pixel8_OnOff_552 = 806388459,
  ICC_LISD_Pixel9_OnOff_552 = 806421228,
  ICC_LISD_Pixel10_OnOff_552 = 806453997,
  ICC_LISD_Pixel11_OnOff_552 = 806486766,
  ICC_LISD_Pixel12_OnOff_552 = 806519535,
  ICC_LISD_Pixel13_OnOff_552 = 806552304,
  ICC_LISD_Pixel14_OnOff_552 = 806585073,
  ICC_LISD_Pixel15_OnOff_552 = 806617842,
  ICC_LISD_Pixel16_OnOff_552 = 806650611,
  ICC_LISD_Pixel17_OnOff_552 = 806683380,
  ICC_LISD_Pixel18_OnOff_552 = 806716149,
  ICC_LISD_Pixel19_OnOff_552 = 806748918,
  ICC_LISD_Pixel20_OnOff_552 = 806781687,
  ICC_LISD_Pixel21_OnOff_552 = 806814456,
  ICC_LISD_Pixel22_OnOff_552 = 806847225,
  ICC_LISD_Pixel23_OnOff_552 = 806879994,
  ICC_LISD_Pixel24_OnOff_552 = 806912763,
  ICC_LISD_Pixel25_OnOff_552 = 806945532,
  ICC_LISD_Pixel26_OnOff_552 = 806978301,
  ICC_LISD_Pixel27_OnOff_552 = 807011070,
  ICC_LISD_Pixel28_OnOff_552 = 807043839,
  ICC_LISD_Pixel29_OnOff_552 = 807076608,
  ICC_LISD_Pixel30_OnOff_552 = 807109377,
  ICC_LISD_Pixel31_OnOff_552 = 807142146,
  ICC_LISD_Pixel32_OnOff_552 = 807174915,
  ICC_LISD_Pixel33_OnOff_552 = 807207684,
  ICC_LISD_Pixel34_OnOff_552 = 807240453,
  ICC_LISD_Pixel35_OnOff_552 = 807273222,
  ICC_LISD_Pixel36_OnOff_552 = 807305991,
  ICC_LISD_Pixel37_OnOff_552 = 807338760,
  ICC_LISD_Pixel38_OnOff_552 = 807371529,
  ICC_LISD_Pixel39_OnOff_552 = 807404298,
  ICC_LISD_Pixel40_OnOff_552 = 807437067,
  ICC_LISD_Pixel41_OnOff_552 = 807469836,
  ICC_LISD_Pixel42_OnOff_552 = 807502605,
  ICC_LISD_Pixel43_OnOff_552 = 807535374,
  ICC_LISD_Pixel44_OnOff_552 = 807568143,
  ICC_LISD_Pixel45_OnOff_552 = 807600912,
  ICC_LISD_Pixel46_OnOff_552 = 807633681,
  ICC_LISD_Pixel47_OnOff_552 = 807666450,
  ICC_LISD_Pixel48_OnOff_552 = 807699219,
  ICC_LISD_Pixel49_OnOff_552 = 807731988,
  ICC_LISD_Pixel50_OnOff_552 = 807764757,
  ICC_LISD_Pixel51_OnOff_552 = 807797526,
  ICC_LISD_Pixel52_OnOff_552 = 807830295,
  ICC_LISD_Pixel53_OnOff_552 = 807863064,
  ICC_LISD_Pixel54_OnOff_552 = 807895833,
  ICC_LISD_Pixel55_OnOff_552 = 807928602,
  ICC_LISD_Pixel56_OnOff_552 = 807961371,
  ICC_LISD_Pixel57_OnOff_552 = 807994140,
  ICC_LISD_Pixel58_OnOff_552 = 808026909,
  ICC_LISD_Pixel59_OnOff_552 = 808059678,
  ICC_LISD_Pixel60_OnOff_552 = 808092447,
  ICC_LISD_Pixel61_OnOff_552 = 808125216,
  ICC_LISD_Pixel62_OnOff_552 = 808157985,
  ICC_LISD_Pixel63_OnOff_552 = 808190754,
  ICC_LISD_Pixel64_OnOff_552 = 808223523,
  ICC_LISD_Pixel65_OnOff_552 = 808256292,
  ICC_LISD_Pixel66_OnOff_552 = 808289061,
  ICC_LISD_Pixel67_OnOff_552 = 808321830,
  ICC_LISD_Pixel68_OnOff_552 = 808354599,
  ICC_LISD_Pixel69_OnOff_552 = 808387368,
  ICC_LISD_Pixel70_OnOff_552 = 808420137,
  ICC_LISD_Pixel71_OnOff_552 = 808452906,
  ICC_LISD_Pixel72_OnOff_552 = 808485675,
  ICC_LISD_Pixel73_OnOff_552 = 808518444,
  ICC_LISD_Pixel74_OnOff_552 = 808551213,
  ICC_LISD_Pixel75_OnOff_552 = 808583982,
  ICC_LISD_Pixel76_OnOff_552 = 808616751,
  ICC_LISD_Pixel77_OnOff_552 = 808649520,
  ICC_LISD_Pixel78_OnOff_552 = 808682289,
  ICC_LISD_Pixel79_OnOff_552 = 808715058,
  ICC_LISD_Pixel80_OnOff_552 = 808747827,
  ICC_LISD_Pixel81_OnOff_552 = 808780596,
  ICC_LISD_Pixel82_OnOff_552 = 808813365,
  ICC_LISD_Pixel83_OnOff_552 = 808846134,
  ICC_LISD_Pixel84_OnOff_552 = 808878903,
  ICC_LISD_Pixel85_OnOff_552 = 808911672,
  ICC_LISD_Pixel86_OnOff_552 = 808944441,
  ICC_LISD_Pixel87_OnOff_552 = 808977210,
  ICC_LISD_Pixel88_OnOff_552 = 809009979,
  ICC_LISD_Pixel89_OnOff_552 = 809042748,
  ICC_LISD_Pixel90_OnOff_552 = 809075517,
  ICC_LISD_Pixel91_OnOff_552 = 809108286,
  ICC_LISD_Pixel92_OnOff_552 = 809141055,
  ICC_LISD_Pixel93_OnOff_552 = 809173824,
  ICC_LISD_Pixel94_OnOff_552 = 809206593,
  ICC_LISD_Pixel95_OnOff_552 = 809239362,
  ICC_LISD_Pixel96_OnOff_552 = 809272131,
  ICC_LISD_Pixel97_OnOff_552 = 809304900,
  ICC_LISD_Pixel98_OnOff_552 = 809337669,
  ICC_LISD_Pixel99_OnOff_552 = 809370438,
  ICC_LISD_Pixel100_OnOff_552 = 809403207,
  ICC_LISD_Pixel101_OnOff_552 = 809435976,
  ICC_LISD_Pixel102_OnOff_552 = 809468745,
  ICC_LISD_Pixel103_OnOff_552 = 809501514,
  ICC_LISD_Pixel104_OnOff_552 = 809534283,
  ICC_LISD_Pixel105_OnOff_552 = 809567052,
  ICC_LISD_Pixel106_OnOff_552 = 809599821,
  ICC_LISD_Pixel107_OnOff_552 = 809632590,
  ICC_LISD_Pixel108_OnOff_552 = 809665359,
  ICC_LISD_Pixel109_OnOff_552 = 809698128,
  ICC_LISD_Pixel110_OnOff_552 = 809730897,
  ICC_LISD_Pixel111_OnOff_552 = 809763666,
  ICC_LISD_Pixel112_OnOff_552 = 809796435,
  ICC_LISD_Pixel113_OnOff_552 = 809829204,
  ICC_LISD_Pixel114_OnOff_552 = 809861973,
  ICC_LISD_Pixel115_OnOff_552 = 809894742,
  ICC_LISD_Pixel116_OnOff_552 = 809927511,
  ICC_LISD_Pixel117_OnOff_552 = 809960280,
  ICC_LISD_Pixel118_OnOff_552 = 809993049,
  ICC_LISD_Pixel119_OnOff_552 = 810025818,
  ICC_LISD_Pixel120_OnOff_552 = 810058587,
  ICC_LISD_Pixel121_OnOff_552 = 810091356,
  ICC_LISD_Pixel122_OnOff_552 = 810124125,
  ICC_LISD_Pixel123_OnOff_552 = 810156894,
  ICC_LISD_Pixel124_OnOff_552 = 810189663,
  ICC_LISD_Pixel125_OnOff_552 = 810222432,
  ICC_LISD_Pixel126_OnOff_552 = 810255201,
  ICC_LISD_Pixel127_OnOff_552 = 810287970,
  ICC_LISD_Pixel128_OnOff_552 = 810320739,
  ICC_LISD_Pixel129_OnOff_552 = 810353508,
  ICC_LISD_Pixel130_OnOff_552 = 810386277,
  ICC_LISD_Pixel131_OnOff_552 = 810419046,
  ICC_LISD_Pixel132_OnOff_552 = 810451815,
  ICC_LISD_Pixel133_OnOff_552 = 810484584,
  ICC_LISD_Pixel134_OnOff_552 = 810517353,
  ICC_LISD_Pixel135_OnOff_552 = 810550122,
  ICC_LISD_Pixel136_OnOff_552 = 810582891,
  ICC_LISD_Pixel137_OnOff_552 = 810615660,
  ICC_LISD_Pixel138_OnOff_552 = 810648429,
  ICC_LISD_Pixel139_OnOff_552 = 810681198,
  ICC_LISD_Pixel140_OnOff_552 = 810713967,
  ICC_LISD_Pixel141_OnOff_552 = 810746736,
  ICC_LISD_Pixel142_OnOff_552 = 810779505,
  ICC_LISD_Pixel143_OnOff_552 = 810812274,
  ICC_LISD_Pixel144_OnOff_552 = 810845043,
  ICC_LISD_Pixel145_OnOff_552 = 810877812,
  ICC_LISD_Pixel146_OnOff_552 = 810910581,
  ICC_LISD_Pixel147_OnOff_552 = 810943350,
  ICC_LISD_Pixel148_OnOff_552 = 810976119,
  ICC_LISD_Pixel149_OnOff_552 = 811008888,
  ICC_LISD_Pixel150_OnOff_552 = 811041657,
  ICC_LISD_Pixel151_OnOff_552 = 811074426,
  ICC_LISD_Pixel152_OnOff_552 = 811107195,
  ICC_LISD_Pixel153_OnOff_552 = 811139964,
  ICC_LISD_Pixel154_OnOff_552 = 811172733,
  ICC_LISD_Pixel155_OnOff_552 = 811205502,
  ICC_LISD_Pixel156_OnOff_552 = 811238271,
  ICC_LISD_Pixel157_OnOff_552 = 811271040,
  ICC_LISD_Pixel158_OnOff_552 = 811303809,
  ICC_LISD_Pixel159_OnOff_552 = 811336578,
  ICC_LISD_Pixel160_OnOff_552 = 811369347,
  ICC_LISD_Pixel161_OnOff_552 = 811402116,
  ICC_LISD_Pixel162_OnOff_552 = 811434885,
  ICC_LISD_Pixel163_OnOff_552 = 811467654,
  ICC_LISD_Pixel164_OnOff_552 = 811500423,
  ICC_LISD_Pixel165_OnOff_552 = 811533192,
  ICC_LISD_Pixel166_OnOff_552 = 811565961,
  ICC_LISD_Pixel167_OnOff_552 = 811598730,
  ICC_LISD_Pixel168_OnOff_552 = 811631499,
  ICC_LISD_Pixel169_OnOff_552 = 811664268,
  ICC_LISD_Pixel170_OnOff_552 = 811697037,
  ICC_LISD_Pixel171_OnOff_552 = 811729806,
  ICC_LISD_Pixel172_OnOff_552 = 811762575,
  ICC_LISD_Pixel173_OnOff_552 = 811795344,
  ICC_LISD_Pixel174_OnOff_552 = 811828113,
  ICC_LISD_Pixel175_OnOff_552 = 811860882,
  ICC_LISD_Pixel176_OnOff_552 = 811893651,
  ICC_LISD_Pixel177_OnOff_552 = 811926420,
  ICC_LISD_Pixel178_OnOff_552 = 811959189,
  ICC_LISD_Pixel179_OnOff_552 = 811991958,
  ICC_LISD_Pixel180_OnOff_552 = 812024727,
  ICC_LISD_Pixel181_OnOff_552 = 812057496,
  ICC_LISD_Pixel182_OnOff_552 = 812090265,
  ICC_LISD_Pixel183_OnOff_552 = 812123034,
  ICC_LISD_Pixel184_OnOff_552 = 812155803,
  ICC_LISD_Pixel185_OnOff_552 = 812188572,
  ICC_LISD_Pixel186_OnOff_552 = 812221341,
  ICC_LISD_Pixel187_OnOff_552 = 812254110,
  ICC_LISD_Pixel188_OnOff_552 = 812286879,
  ICC_LISD_Pixel189_OnOff_552 = 812319648,
  ICC_LISD_Pixel190_OnOff_552 = 812352417,
  ICC_LISD_Pixel191_OnOff_552 = 812385186,
  ICC_LISD_Pixel192_OnOff_552 = 812417955,
  ICC_LISD_Pixel193_OnOff_552 = 812450724,
  ICC_LISD_Pixel194_OnOff_552 = 812483493,
  ICC_LISD_Pixel195_OnOff_552 = 812516262,
  ICC_LISD_Pixel196_OnOff_552 = 812549031,
  ICC_LISD_Pixel197_OnOff_552 = 812581800,
  ICC_LISD_Pixel198_OnOff_552 = 812614569,
  ICC_LISD_Pixel199_OnOff_552 = 812647338,
  ICC_LISD_Pixel200_OnOff_552 = 812680107,
  ICC_LISD_Pixel201_OnOff_552 = 812712876,
  ICC_LISD_Pixel202_OnOff_552 = 812745645,
  ICC_LISD_Pixel203_OnOff_552 = 812778414,
  ICC_LISD_Pixel204_OnOff_552 = 812811183,
  ICC_LISD_Pixel205_OnOff_552 = 812843952,
  ICC_LISD_Pixel206_OnOff_552 = 812876721,
  ICC_LISD_Pixel207_OnOff_552 = 812909490,
  ICC_LISD_Pixel208_OnOff_552 = 812942259,
  ICC_LISD_Pixel209_OnOff_552 = 812975028,
  ICC_LISD_Pixel210_OnOff_552 = 813007797,
  ICC_LISD_Pixel211_OnOff_552 = 813040566,
  ICC_LISD_Pixel212_OnOff_552 = 813073335,
  ICC_LISD_Pixel213_OnOff_552 = 813106104,
  ICC_LISD_Pixel214_OnOff_552 = 813138873,
  ICC_LISD_Pixel215_OnOff_552 = 813171642,
  ICC_LISD_Pixel216_OnOff_552 = 813204411,
  ICC_LISD_Pixel217_OnOff_552 = 813237180,
  ICC_LISD_Pixel218_OnOff_552 = 813269949,
  ICC_LISD_Pixel219_OnOff_552 = 813302718,
  ICC_LISD_Pixel220_OnOff_552 = 813335487,
  ICC_LISD_Pixel221_OnOff_552 = 813368256,
  ICC_LISD_Pixel222_OnOff_552 = 813401025,
  ICC_LISD_Pixel223_OnOff_552 = 813433794,
  ICC_LISD_Pixel224_OnOff_552 = 813466563,
  ICC_LISD_Pixel225_OnOff_552 = 813499332,
  ICC_LISD_Pixel226_OnOff_552 = 813532101,
  ICC_LISD_Pixel227_OnOff_552 = 813564870,
  ICC_LISD_Pixel228_OnOff_552 = 813597639,
  ICC_LISD_Pixel229_OnOff_552 = 813630408,
  ICC_LISD_Pixel230_OnOff_552 = 813663177,
  ICC_LISD_Pixel231_OnOff_552 = 813695946,
  ICC_LISD_Pixel232_OnOff_552 = 813728715,
  ICC_LISD_Pixel233_OnOff_552 = 813761484,
  ICC_LISD_Pixel234_OnOff_552 = 813794253,
  ICC_LISD_Pixel235_OnOff_552 = 813827022,
  ICC_LISD_Pixel236_OnOff_552 = 813859791,
  ICC_LISD_Pixel237_OnOff_552 = 813892560,
  ICC_LISD_Pixel238_OnOff_552 = 813925329,
  ICC_LISD_Pixel239_OnOff_552 = 813958098,
  ICC_LISD_Pixel240_OnOff_552 = 813990867,
  ICC_LISD_Pixel241_OnOff_552 = 814023636,
  ICC_LISD_Pixel242_OnOff_552 = 814056405,
  ICC_LISD_Pixel243_OnOff_552 = 814089174,
  ICC_LISD_Pixel244_OnOff_552 = 814121943,
  ICC_LISD_Pixel245_OnOff_552 = 814154712,
  ICC_LISD_Pixel246_OnOff_552 = 814187481,
  ICC_LISD_Pixel247_OnOff_552 = 814220250,
  ICC_LISD_Pixel248_OnOff_552 = 814253019,
  ICC_LISD_Pixel249_OnOff_552 = 814285788,
  ICC_LISD_Pixel250_OnOff_552 = 814318557,
  ICC_LISD_Pixel251_OnOff_552 = 814351326,
  ICC_LISD_Pixel252_OnOff_552 = 814384095,
  ICC_LISD_Pixel253_OnOff_552 = 814416864,
  ICC_LISD_Pixel254_OnOff_552 = 814449633,
  ICC_LISD_Pixel255_OnOff_552 = 814482402,
  ICC_LISD_Pixel256_OnOff_552 = 814515171,
  ICC_LISD_Pixel257_OnOff_552 = 814547940,
  ICC_LISD_Pixel258_OnOff_552 = 814580709,
  ICC_LISD_Pixel259_OnOff_552 = 814613478,
  ICC_LISD_Pixel260_OnOff_552 = 814646247,
  ICC_LISD_Pixel261_OnOff_552 = 814679016,
  ICC_LISD_Pixel262_OnOff_552 = 814711785,
  ICC_LISD_Pixel263_OnOff_552 = 814744554,
  ICC_LISD_Pixel264_OnOff_552 = 814777323,
  ICC_LISD_Pixel265_OnOff_552 = 814810092,
  ICC_LISD_Pixel266_OnOff_552 = 814842861,
  ICC_LISD_Pixel267_OnOff_552 = 814875630,
  ICC_LISD_Pixel268_OnOff_552 = 814908399,
  ICC_LISD_Pixel269_OnOff_552 = 814941168,
  ICC_LISD_Pixel270_OnOff_552 = 814973937,
  ICC_LISD_Pixel271_OnOff_552 = 815006706,
  ICC_LISD_Pixel272_OnOff_552 = 815039475,
  ICC_LISD_Pixel273_OnOff_552 = 815072244,
  ICC_LISD_Pixel274_OnOff_552 = 815105013,
  ICC_LISD_Pixel275_OnOff_552 = 815137782,
  ICC_LISD_Pixel276_OnOff_552 = 815170551,
  ICC_LISD_Pixel277_OnOff_552 = 815203320,
  ICC_LISD_Pixel278_OnOff_552 = 815236089,
  ICC_LISD_Pixel279_OnOff_552 = 815268858,
  ICC_LISD_Pixel280_OnOff_552 = 815301627,
  ICC_LISD_Pixel281_OnOff_552 = 815334396,
  ICC_LISD_Pixel282_OnOff_552 = 815367165,
  ICC_LISD_Pixel283_OnOff_552 = 815399934,
  ICC_LISD_Pixel284_OnOff_552 = 815432703,
  ICC_LISD_Pixel285_OnOff_552 = 815465472,
  ICC_LISD_Pixel286_OnOff_552 = 815498241,
  ICC_LISD_Pixel287_OnOff_552 = 815531010,
  ICC_LISD_Pixel288_OnOff_552 = 815563779,
  ICC_LISD_Pixel289_OnOff_552 = 815596548,
  ICC_LISD_Pixel290_OnOff_552 = 815629317,
  ICC_LISD_Pixel291_OnOff_552 = 815662086,
  ICC_LISD_Pixel292_OnOff_552 = 815694855,
  ICC_LISD_Pixel293_OnOff_552 = 815727624,
  ICC_LISD_Pixel294_OnOff_552 = 815760393,
  ICC_LISD_Pixel295_OnOff_552 = 815793162,
  ICC_LISD_Pixel296_OnOff_552 = 815825931,
  ICC_LISD_Pixel297_OnOff_552 = 815858700,
  ICC_LISD_Pixel298_OnOff_552 = 815891469,
  ICC_LISD_Pixel299_OnOff_552 = 815924238,
  ICC_LISD_Pixel300_OnOff_552 = 815957007,
  ICC_LISD_Pixel301_OnOff_552 = 815989776,
  ICC_LISD_Pixel302_OnOff_552 = 816022545,
  ICC_LISD_Pixel303_OnOff_552 = 816055314,
  ICC_LISD_Pixel304_OnOff_552 = 816088083,
  ICC_LISD_Pixel305_OnOff_552 = 816120852,
  ICC_LISD_Pixel306_OnOff_552 = 816153621,
  ICC_LISD_Pixel307_OnOff_552 = 816186390,
  ICC_LISD_Pixel308_OnOff_552 = 816219159,
  ICC_LISD_Pixel309_OnOff_552 = 816251928,
  ICC_LISD_Pixel310_OnOff_552 = 816284697,
  ICC_LISD_Pixel311_OnOff_552 = 816317466,
  ICC_LISD_Pixel312_OnOff_552 = 816350235,
  ICC_LISD_Pixel313_OnOff_552 = 816383004,
  ICC_LISD_Pixel314_OnOff_552 = 816415773,
  ICC_LISD_Pixel315_OnOff_552 = 816448542,
  ICC_LISD_Pixel316_OnOff_552 = 816481311,
  ICC_LISD_Pixel317_OnOff_552 = 816514080,
  ICC_LISD_Pixel318_OnOff_552 = 816546849,
  ICC_LISD_Pixel319_OnOff_552 = 816579618,
  ICC_LISD_Pixel320_OnOff_552 = 816612387,
  ICC_LISD_Pixel321_OnOff_552 = 816645156,
  ICC_LISD_Pixel322_OnOff_552 = 816677925,
  ICC_LISD_Pixel323_OnOff_552 = 816710694,
  ICC_LISD_Pixel324_OnOff_552 = 816743463,
  ICC_LISD_Pixel325_OnOff_552 = 816776232,
  ICC_LISD_Pixel326_OnOff_552 = 816809001,
  ICC_LISD_Pixel327_OnOff_552 = 816841770,
  ICC_LISD_Pixel328_OnOff_552 = 816874539,
  ICC_LISD_Pixel329_OnOff_552 = 816907308,
  ICC_LISD_Pixel330_OnOff_552 = 816940077,
  ICC_LISD_Pixel331_OnOff_552 = 816972846,
  ICC_LISD_Pixel332_OnOff_552 = 817005615,
  ICC_LISD_Pixel333_OnOff_552 = 817038384,
  ICC_LISD_Pixel334_OnOff_552 = 817071153,
  ICC_LISD_Pixel335_OnOff_552 = 817103922,
  ICC_LISD_Pixel336_OnOff_552 = 817136691,
  ICC_LISD_Pixel337_OnOff_552 = 817169460,
  ICC_LISD_Pixel338_OnOff_552 = 817202229,
  ICC_LISD_Pixel339_OnOff_552 = 817234998,
  ICC_LISD_Pixel340_OnOff_552 = 817267767,
  ICC_LISD_Pixel341_OnOff_552 = 817300536,
  ICC_LISD_Pixel342_OnOff_552 = 817333305,
  ICC_LISD_Pixel343_OnOff_552 = 817366074,
  ICC_LISD_Pixel344_OnOff_552 = 817398843,
  ICC_LISD_Pixel345_OnOff_552 = 817431612,
  ICC_LISD_Pixel346_OnOff_552 = 817464381,
  ICC_LISD_Pixel347_OnOff_552 = 817497150,
  ICC_LISD_Pixel348_OnOff_552 = 817529919,
  ICC_LISD_Pixel349_OnOff_552 = 817562688,
  ICC_LISD_Pixel350_OnOff_552 = 817595457,
  ICC_LISD_Pixel351_OnOff_552 = 817628226,
  ICC_LISD_Pixel352_OnOff_552 = 817660995,
  ICC_LISD_Pixel353_OnOff_552 = 817693764,
  ICC_LISD_Pixel354_OnOff_552 = 817726533,
  ICC_LISD_Pixel355_OnOff_552 = 817759302,
  ICC_LISD_Pixel356_OnOff_552 = 817792071,
  ICC_LISD_Pixel357_OnOff_552 = 817824840,
  ICC_LISD_Pixel358_OnOff_552 = 817857609,
  ICC_LISD_Pixel359_OnOff_552 = 817890378,
  ICC_LISD_Pixel360_OnOff_552 = 817923147,
  ICC_LISD_Pixel361_OnOff_552 = 817955916,
  ICC_LISD_Pixel362_OnOff_552 = 817988685,
  ICC_LISD_Pixel363_OnOff_552 = 818021454,
  ICC_LISD_Pixel364_OnOff_552 = 818054223,
  ICC_LISD_Pixel365_OnOff_552 = 818086992,
  ICC_LISD_Pixel366_OnOff_552 = 818119761,
  ICC_LISD_Pixel367_OnOff_552 = 818152530,
  ICC_LISD_Pixel368_OnOff_552 = 818185299,
  ICC_LISD_Pixel369_OnOff_552 = 818218068,
  ICC_LISD_Pixel370_OnOff_552 = 818250837,
  ICC_LISD_Pixel371_OnOff_552 = 818283606,
  ICC_LISD_Pixel372_OnOff_552 = 818316375,
  ICC_LISD_Pixel373_OnOff_552 = 818349144,
  ICC_LISD_Pixel374_OnOff_552 = 818381913,
  ICC_LISD_Pixel375_OnOff_552 = 818414682,
  ICC_LISD_Pixel376_OnOff_552 = 818447451,
  ICC_LISD_Pixel377_OnOff_552 = 818480220,
  ICC_LISD_Pixel378_OnOff_552 = 818512989,
  ICC_LISD_Pixel379_OnOff_552 = 818545758,
  ICC_LISD_Pixel380_OnOff_552 = 818578527,
  ICC_LISD_Pixel381_OnOff_552 = 818611296,
  ICC_LISD_Pixel382_OnOff_552 = 818644065,
  ICC_LISD_Pixel383_OnOff_552 = 818676834,
  ICC_LISD_Pixel384_OnOff_552 = 818709603,
  ICC_LISD_Pixel385_OnOff_552 = 818742372,
  ICC_LISD_Pixel386_OnOff_552 = 818775141,
  ICC_LISD_Pixel387_OnOff_552 = 818807910,
  ICC_LISD_Pixel388_OnOff_552 = 818840679,
  ICC_LISD_Pixel389_OnOff_552 = 818873448,
  ICC_LISD_Pixel390_OnOff_552 = 818906217,
  ICC_LISD_Pixel391_OnOff_552 = 818938986,
  ICC_LISD_Pixel392_OnOff_552 = 818971755,
  ICC_LISD_Pixel393_OnOff_552 = 819004524,
  ICC_LISD_Pixel394_OnOff_552 = 819037293,
  ICC_LISD_Pixel395_OnOff_552 = 819070062,
  ICC_LISD_Pixel396_OnOff_552 = 819102831,
  ICC_LISD_Pixel397_OnOff_552 = 819135600,
  ICC_LISD_Pixel398_OnOff_552 = 819168369,
  ICC_LISD_Pixel399_OnOff_552 = 819201138,
  ICC_LISD_Pixel400_OnOff_552 = 819233907,
  ICC_LISD_Pixel401_OnOff_552 = 819266676,
  ICC_LISD_Pixel402_OnOff_552 = 819299445,
  ICC_LISD_Pixel403_OnOff_552 = 819332214,
  ICC_LISD_Pixel404_OnOff_552 = 819364983,
  ICC_LISD_Pixel405_OnOff_552 = 819397752,
  ICC_LISD_Pixel406_OnOff_552 = 819430521,
  ICC_LISD_Pixel407_OnOff_552 = 819463290,
  ICC_LISD_Pixel408_OnOff_552 = 819496059,
  ICC_LISD_Pixel409_OnOff_552 = 819528828,
  ICC_LISD_Pixel410_OnOff_552 = 819561597,
  ICC_LISD_Pixel411_OnOff_552 = 819594366,
  ICC_LISD_Pixel412_OnOff_552 = 819627135,
  ICC_LISD_Pixel413_OnOff_552 = 819659904,
  ICC_LISD_Pixel414_OnOff_552 = 819692673,
  ICC_LISD_Pixel415_OnOff_552 = 819725442,
  ICC_LISD_Pixel416_OnOff_552 = 819758211,
  ICC_LISD_Pixel417_OnOff_552 = 819790980,
  ICC_LISD_Pixel418_OnOff_552 = 819823749,
  ICC_LISD_Pixel419_OnOff_552 = 819856518,
  ICC_LISD_Pixel420_OnOff_552 = 819889287,
  ICC_LISD_Pixel421_OnOff_552 = 819922056,
  ICC_LISD_Pixel422_OnOff_552 = 819954825,
  ICC_LISD_Pixel423_OnOff_552 = 819987594,
  ICC_LISD_Pixel424_OnOff_552 = 820020363,
  ICC_LISD_Pixel425_OnOff_552 = 820053132,
  ICC_LISD_Pixel426_OnOff_552 = 820085901,
  ICC_LISD_Pixel427_OnOff_552 = 820118670,
  ICC_LISD_Pixel428_OnOff_552 = 820151439,
  ICC_LISD_Pixel429_OnOff_552 = 820184208,
  ICC_LISD_Pixel430_OnOff_552 = 820216977,
  ICC_LISD_Pixel431_OnOff_552 = 820249746,
  ICC_LISD_Pixel432_OnOff_552 = 820282515,
  ICC_LISD_Pixel433_OnOff_552 = 820315284,
  ICC_LISD_Pixel434_OnOff_552 = 820348053,
  ICC_LISD_Pixel435_OnOff_552 = 820380822,
  ICC_LISD_Pixel436_OnOff_552 = 820413591,
  ICC_LISD_Pixel437_OnOff_552 = 820446360,
  ICC_LISD_Pixel438_OnOff_552 = 820479129,
  ICC_LISD_Pixel439_OnOff_552 = 820511898,
  ICC_LISD_Pixel440_OnOff_552 = 820544667,
  ICC_LISD_Pixel441_OnOff_552 = 820577436,
  ICC_LISD_Pixel442_OnOff_552 = 820610205,
  ICC_LISD_Pixel443_OnOff_552 = 820642974,
  ICC_LISD_Pixel444_OnOff_552 = 820675743,
  ICC_LISD_Pixel445_OnOff_552 = 820708512,
  ICC_LISD_Pixel446_OnOff_552 = 820741281,
  ICC_LISD_Pixel447_OnOff_552 = 820774050,
  ICC_LISD_Pixel448_OnOff_552 = 820806819,
  ICC_LISD_Pixel449_OnOff_552 = 820839588,
  ICC_LISD_Pixel450_OnOff_552 = 820872357,
  ICC_LISD_Pixel451_OnOff_552 = 820905126,
  ICC_LISD_Pixel452_OnOff_552 = 820937895,
  ICC_LISD_Pixel453_OnOff_552 = 820970664,
  ICC_LISD_Pixel454_OnOff_552 = 821003433,
  ICC_LISD_Pixel455_OnOff_552 = 821036202,
  ICC_LISD_Pixel456_OnOff_552 = 821068971,
  ICC_LISD_Pixel457_OnOff_552 = 821101740,
  ICC_LISD_Pixel458_OnOff_552 = 821134509,
  ICC_LISD_Pixel459_OnOff_552 = 821167278,
  ICC_LISD_Pixel460_OnOff_552 = 821200047,
  ICC_LISD_Pixel461_OnOff_552 = 821232816,
  ICC_LISD_Pixel462_OnOff_552 = 821265585,
  ICC_LISD_Pixel463_OnOff_552 = 821298354,
  ICC_LISD_Pixel464_OnOff_552 = 821331123,
  ICC_LISD_Pixel465_OnOff_552 = 821363892,
  ICC_LISD_Pixel466_OnOff_552 = 821396661,
  ICC_LISD_Pixel467_OnOff_552 = 821429430,
  ICC_LISD_Pixel468_OnOff_552 = 821462199,
  ICC_LISD_Pixel469_OnOff_552 = 821494968,
  ICC_LISD_Pixel470_OnOff_552 = 821527737,
  ICC_LISD_Pixel471_OnOff_552 = 821560506,
  ICC_LISD_Pixel472_OnOff_552 = 821593275,
  ICC_LISD_Pixel473_OnOff_552 = 821626044,
  ICC_LISD_Pixel474_OnOff_552 = 821658813,
  ICC_LISD_Pixel475_OnOff_552 = 821691582,
  ICC_LISD_Pixel476_OnOff_552 = 821724351,
  ICC_LISD_Pixel477_OnOff_552 = 821757120,
  ICC_LISD_Pixel478_OnOff_552 = 821789889,
  ICC_LISD_Pixel479_OnOff_552 = 821822658,
  ICC_LISD_Pixel480_OnOff_552 = 821855427,
  ICC_LISD_Pixel481_OnOff_552 = 821888196,
  ICC_LISD_Pixel482_OnOff_552 = 821920965,
  ICC_LISD_Pixel483_OnOff_552 = 821953734,
  ICC_LISD_Pixel484_OnOff_552 = 821986503,
  ICC_LISD_Pixel485_OnOff_552 = 822019272,
  ICC_LISD_Pixel486_OnOff_552 = 822052041,
  ICC_LISD_Pixel487_OnOff_552 = 822084810,
  ICC_LISD_Pixel488_OnOff_552 = 822117579,
  ICC_LISD_Pixel489_OnOff_552 = 822150348,
  ICC_LISD_Pixel490_OnOff_552 = 822183117,
  ICC_LISD_Pixel491_OnOff_552 = 822215886,
  ICC_LISD_Pixel492_OnOff_552 = 822248655,
  ICC_LISD_Pixel493_OnOff_552 = 822281424,
  ICC_LISD_Pixel494_OnOff_552 = 822314193,
  ICC_LISD_Pixel495_OnOff_552 = 822346962,
  ICC_LISD_Pixel496_OnOff_552 = 822379731,
  ICC_LISD_Pixel497_OnOff_552 = 822412500,
  ICC_LISD_Pixel498_OnOff_552 = 822445269,
  ICC_LISD_Pixel499_OnOff_552 = 822478038,
  ICC_LISD_Pixel500_OnOff_552 = 822510807,
  ICC_LISD_Pixel501_OnOff_552 = 822543576,
  ICC_LISD_Pixel502_OnOff_552 = 822576345,
  ICC_LISD_Pixel503_OnOff_552 = 822609114,
  ICC_LISD_Pixel504_OnOff_552 = 822641883,
  ICC_RISD_FrameCounter_554 = 156402908,
  ICC_RISD_RollingCounter_554 = 156435677,
  ICC_RISD_Pixel1_OnOff_554 = 822674654,
  ICC_RISD_Pixel2_OnOff_554 = 822707423,
  ICC_RISD_Pixel3_OnOff_554 = 822740192,
  ICC_RISD_Pixel4_OnOff_554 = 822772961,
  ICC_RISD_Pixel5_OnOff_554 = 822805730,
  ICC_RISD_Pixel6_OnOff_554 = 822838499,
  ICC_RISD_Pixel7_OnOff_554 = 822871268,
  ICC_RISD_Pixel8_OnOff_554 = 822904037,
  ICC_RISD_Pixel9_OnOff_554 = 822936806,
  ICC_RISD_Pixel10_OnOff_554 = 822969575,
  ICC_RISD_Pixel11_OnOff_554 = 823002344,
  ICC_RISD_Pixel12_OnOff_554 = 823035113,
  ICC_RISD_Pixel13_OnOff_554 = 823067882,
  ICC_RISD_Pixel14_OnOff_554 = 823100651,
  ICC_RISD_Pixel15_OnOff_554 = 823133420,
  ICC_RISD_Pixel16_OnOff_554 = 823166189,
  ICC_RISD_Pixel17_OnOff_554 = 823198958,
  ICC_RISD_Pixel18_OnOff_554 = 823231727,
  ICC_RISD_Pixel19_OnOff_554 = 823264496,
  ICC_RISD_Pixel20_OnOff_554 = 823297265,
  ICC_RISD_Pixel21_OnOff_554 = 823330034,
  ICC_RISD_Pixel22_OnOff_554 = 823362803,
  ICC_RISD_Pixel23_OnOff_554 = 823395572,
  ICC_RISD_Pixel24_OnOff_554 = 823428341,
  ICC_RISD_Pixel25_OnOff_554 = 823461110,
  ICC_RISD_Pixel26_OnOff_554 = 823493879,
  ICC_RISD_Pixel27_OnOff_554 = 823526648,
  ICC_RISD_Pixel28_OnOff_554 = 823559417,
  ICC_RISD_Pixel29_OnOff_554 = 823592186,
  ICC_RISD_Pixel30_OnOff_554 = 823624955,
  ICC_RISD_Pixel31_OnOff_554 = 823657724,
  ICC_RISD_Pixel32_OnOff_554 = 823690493,
  ICC_RISD_Pixel33_OnOff_554 = 823723262,
  ICC_RISD_Pixel34_OnOff_554 = 823756031,
  ICC_RISD_Pixel35_OnOff_554 = 823788800,
  ICC_RISD_Pixel36_OnOff_554 = 823821569,
  ICC_RISD_Pixel37_OnOff_554 = 823854338,
  ICC_RISD_Pixel38_OnOff_554 = 823887107,
  ICC_RISD_Pixel39_OnOff_554 = 823919876,
  ICC_RISD_Pixel40_OnOff_554 = 823952645,
  ICC_RISD_Pixel41_OnOff_554 = 823985414,
  ICC_RISD_Pixel42_OnOff_554 = 824018183,
  ICC_RISD_Pixel43_OnOff_554 = 824050952,
  ICC_RISD_Pixel44_OnOff_554 = 824083721,
  ICC_RISD_Pixel45_OnOff_554 = 824116490,
  ICC_RISD_Pixel46_OnOff_554 = 824149259,
  ICC_RISD_Pixel47_OnOff_554 = 824182028,
  ICC_RISD_Pixel48_OnOff_554 = 824214797,
  ICC_RISD_Pixel49_OnOff_554 = 824247566,
  ICC_RISD_Pixel50_OnOff_554 = 824280335,
  ICC_RISD_Pixel51_OnOff_554 = 824313104,
  ICC_RISD_Pixel52_OnOff_554 = 824345873,
  ICC_RISD_Pixel53_OnOff_554 = 824378642,
  ICC_RISD_Pixel54_OnOff_554 = 824411411,
  ICC_RISD_Pixel55_OnOff_554 = 824444180,
  ICC_RISD_Pixel56_OnOff_554 = 824476949,
  ICC_RISD_Pixel57_OnOff_554 = 824509718,
  ICC_RISD_Pixel58_OnOff_554 = 824542487,
  ICC_RISD_Pixel59_OnOff_554 = 824575256,
  ICC_RISD_Pixel60_OnOff_554 = 824608025,
  ICC_RISD_Pixel61_OnOff_554 = 824640794,
  ICC_RISD_Pixel62_OnOff_554 = 824673563,
  ICC_RISD_Pixel63_OnOff_554 = 824706332,
  ICC_RISD_Pixel64_OnOff_554 = 824739101,
  ICC_RISD_Pixel65_OnOff_554 = 824771870,
  ICC_RISD_Pixel66_OnOff_554 = 824804639,
  ICC_RISD_Pixel67_OnOff_554 = 824837408,
  ICC_RISD_Pixel68_OnOff_554 = 824870177,
  ICC_RISD_Pixel69_OnOff_554 = 824902946,
  ICC_RISD_Pixel70_OnOff_554 = 824935715,
  ICC_RISD_Pixel71_OnOff_554 = 824968484,
  ICC_RISD_Pixel72_OnOff_554 = 825001253,
  ICC_RISD_Pixel73_OnOff_554 = 825034022,
  ICC_RISD_Pixel74_OnOff_554 = 825066791,
  ICC_RISD_Pixel75_OnOff_554 = 825099560,
  ICC_RISD_Pixel76_OnOff_554 = 825132329,
  ICC_RISD_Pixel77_OnOff_554 = 825165098,
  ICC_RISD_Pixel78_OnOff_554 = 825197867,
  ICC_RISD_Pixel79_OnOff_554 = 825230636,
  ICC_RISD_Pixel80_OnOff_554 = 825263405,
  ICC_RISD_Pixel81_OnOff_554 = 825296174,
  ICC_RISD_Pixel82_OnOff_554 = 825328943,
  ICC_RISD_Pixel83_OnOff_554 = 825361712,
  ICC_RISD_Pixel84_OnOff_554 = 825394481,
  ICC_RISD_Pixel85_OnOff_554 = 825427250,
  ICC_RISD_Pixel86_OnOff_554 = 825460019,
  ICC_RISD_Pixel87_OnOff_554 = 825492788,
  ICC_RISD_Pixel88_OnOff_554 = 825525557,
  ICC_RISD_Pixel89_OnOff_554 = 825558326,
  ICC_RISD_Pixel90_OnOff_554 = 825591095,
  ICC_RISD_Pixel91_OnOff_554 = 825623864,
  ICC_RISD_Pixel92_OnOff_554 = 825656633,
  ICC_RISD_Pixel93_OnOff_554 = 825689402,
  ICC_RISD_Pixel94_OnOff_554 = 825722171,
  ICC_RISD_Pixel95_OnOff_554 = 825754940,
  ICC_RISD_Pixel96_OnOff_554 = 825787709,
  ICC_RISD_Pixel97_OnOff_554 = 825820478,
  ICC_RISD_Pixel98_OnOff_554 = 825853247,
  ICC_RISD_Pixel99_OnOff_554 = 825886016,
  ICC_RISD_Pixel100_OnOff_554 = 825918785,
  ICC_RISD_Pixel101_OnOff_554 = 825951554,
  ICC_RISD_Pixel102_OnOff_554 = 825984323,
  ICC_RISD_Pixel103_OnOff_554 = 826017092,
  ICC_RISD_Pixel104_OnOff_554 = 826049861,
  ICC_RISD_Pixel105_OnOff_554 = 826082630,
  ICC_RISD_Pixel106_OnOff_554 = 826115399,
  ICC_RISD_Pixel107_OnOff_554 = 826148168,
  ICC_RISD_Pixel108_OnOff_554 = 826180937,
  ICC_RISD_Pixel109_OnOff_554 = 826213706,
  ICC_RISD_Pixel110_OnOff_554 = 826246475,
  ICC_RISD_Pixel111_OnOff_554 = 826279244,
  ICC_RISD_Pixel112_OnOff_554 = 826312013,
  ICC_RISD_Pixel113_OnOff_554 = 826344782,
  ICC_RISD_Pixel114_OnOff_554 = 826377551,
  ICC_RISD_Pixel115_OnOff_554 = 826410320,
  ICC_RISD_Pixel116_OnOff_554 = 826443089,
  ICC_RISD_Pixel117_OnOff_554 = 826475858,
  ICC_RISD_Pixel118_OnOff_554 = 826508627,
  ICC_RISD_Pixel119_OnOff_554 = 826541396,
  ICC_RISD_Pixel120_OnOff_554 = 826574165,
  ICC_RISD_Pixel121_OnOff_554 = 826606934,
  ICC_RISD_Pixel122_OnOff_554 = 826639703,
  ICC_RISD_Pixel123_OnOff_554 = 826672472,
  ICC_RISD_Pixel124_OnOff_554 = 826705241,
  ICC_RISD_Pixel125_OnOff_554 = 826738010,
  ICC_RISD_Pixel126_OnOff_554 = 826770779,
  ICC_RISD_Pixel127_OnOff_554 = 826803548,
  ICC_RISD_Pixel128_OnOff_554 = 826836317,
  ICC_RISD_Pixel129_OnOff_554 = 826869086,
  ICC_RISD_Pixel130_OnOff_554 = 826901855,
  ICC_RISD_Pixel131_OnOff_554 = 826934624,
  ICC_RISD_Pixel132_OnOff_554 = 826967393,
  ICC_RISD_Pixel133_OnOff_554 = 827000162,
  ICC_RISD_Pixel134_OnOff_554 = 827032931,
  ICC_RISD_Pixel135_OnOff_554 = 827065700,
  ICC_RISD_Pixel136_OnOff_554 = 827098469,
  ICC_RISD_Pixel137_OnOff_554 = 827131238,
  ICC_RISD_Pixel138_OnOff_554 = 827164007,
  ICC_RISD_Pixel139_OnOff_554 = 827196776,
  ICC_RISD_Pixel140_OnOff_554 = 827229545,
  ICC_RISD_Pixel141_OnOff_554 = 827262314,
  ICC_RISD_Pixel142_OnOff_554 = 827295083,
  ICC_RISD_Pixel143_OnOff_554 = 827327852,
  ICC_RISD_Pixel144_OnOff_554 = 827360621,
  ICC_RISD_Pixel145_OnOff_554 = 827393390,
  ICC_RISD_Pixel146_OnOff_554 = 827426159,
  ICC_RISD_Pixel147_OnOff_554 = 827458928,
  ICC_RISD_Pixel148_OnOff_554 = 827491697,
  ICC_RISD_Pixel149_OnOff_554 = 827524466,
  ICC_RISD_Pixel150_OnOff_554 = 827557235,
  ICC_RISD_Pixel151_OnOff_554 = 827590004,
  ICC_RISD_Pixel152_OnOff_554 = 827622773,
  ICC_RISD_Pixel153_OnOff_554 = 827655542,
  ICC_RISD_Pixel154_OnOff_554 = 827688311,
  ICC_RISD_Pixel155_OnOff_554 = 827721080,
  ICC_RISD_Pixel156_OnOff_554 = 827753849,
  ICC_RISD_Pixel157_OnOff_554 = 827786618,
  ICC_RISD_Pixel158_OnOff_554 = 827819387,
  ICC_RISD_Pixel159_OnOff_554 = 827852156,
  ICC_RISD_Pixel160_OnOff_554 = 827884925,
  ICC_RISD_Pixel161_OnOff_554 = 827917694,
  ICC_RISD_Pixel162_OnOff_554 = 827950463,
  ICC_RISD_Pixel163_OnOff_554 = 827983232,
  ICC_RISD_Pixel164_OnOff_554 = 828016001,
  ICC_RISD_Pixel165_OnOff_554 = 828048770,
  ICC_RISD_Pixel166_OnOff_554 = 828081539,
  ICC_RISD_Pixel167_OnOff_554 = 828114308,
  ICC_RISD_Pixel168_OnOff_554 = 828147077,
  ICC_RISD_Pixel169_OnOff_554 = 828179846,
  ICC_RISD_Pixel170_OnOff_554 = 828212615,
  ICC_RISD_Pixel171_OnOff_554 = 828245384,
  ICC_RISD_Pixel172_OnOff_554 = 828278153,
  ICC_RISD_Pixel173_OnOff_554 = 828310922,
  ICC_RISD_Pixel174_OnOff_554 = 828343691,
  ICC_RISD_Pixel175_OnOff_554 = 828376460,
  ICC_RISD_Pixel176_OnOff_554 = 828409229,
  ICC_RISD_Pixel177_OnOff_554 = 828441998,
  ICC_RISD_Pixel178_OnOff_554 = 828474767,
  ICC_RISD_Pixel179_OnOff_554 = 828507536,
  ICC_RISD_Pixel180_OnOff_554 = 828540305,
  ICC_RISD_Pixel181_OnOff_554 = 828573074,
  ICC_RISD_Pixel182_OnOff_554 = 828605843,
  ICC_RISD_Pixel183_OnOff_554 = 828638612,
  ICC_RISD_Pixel184_OnOff_554 = 828671381,
  ICC_RISD_Pixel185_OnOff_554 = 828704150,
  ICC_RISD_Pixel186_OnOff_554 = 828736919,
  ICC_RISD_Pixel187_OnOff_554 = 828769688,
  ICC_RISD_Pixel188_OnOff_554 = 828802457,
  ICC_RISD_Pixel189_OnOff_554 = 828835226,
  ICC_RISD_Pixel190_OnOff_554 = 828867995,
  ICC_RISD_Pixel191_OnOff_554 = 828900764,
  ICC_RISD_Pixel192_OnOff_554 = 828933533,
  ICC_RISD_Pixel193_OnOff_554 = 828966302,
  ICC_RISD_Pixel194_OnOff_554 = 828999071,
  ICC_RISD_Pixel195_OnOff_554 = 829031840,
  ICC_RISD_Pixel196_OnOff_554 = 829064609,
  ICC_RISD_Pixel197_OnOff_554 = 829097378,
  ICC_RISD_Pixel198_OnOff_554 = 829130147,
  ICC_RISD_Pixel199_OnOff_554 = 829162916,
  ICC_RISD_Pixel200_OnOff_554 = 829195685,
  ICC_RISD_Pixel201_OnOff_554 = 829228454,
  ICC_RISD_Pixel202_OnOff_554 = 829261223,
  ICC_RISD_Pixel203_OnOff_554 = 829293992,
  ICC_RISD_Pixel204_OnOff_554 = 829326761,
  ICC_RISD_Pixel205_OnOff_554 = 829359530,
  ICC_RISD_Pixel206_OnOff_554 = 829392299,
  ICC_RISD_Pixel207_OnOff_554 = 829425068,
  ICC_RISD_Pixel208_OnOff_554 = 829457837,
  ICC_RISD_Pixel209_OnOff_554 = 829490606,
  ICC_RISD_Pixel210_OnOff_554 = 829523375,
  ICC_RISD_Pixel211_OnOff_554 = 829556144,
  ICC_RISD_Pixel212_OnOff_554 = 829588913,
  ICC_RISD_Pixel213_OnOff_554 = 829621682,
  ICC_RISD_Pixel214_OnOff_554 = 829654451,
  ICC_RISD_Pixel215_OnOff_554 = 829687220,
  ICC_RISD_Pixel216_OnOff_554 = 829719989,
  ICC_RISD_Pixel217_OnOff_554 = 829752758,
  ICC_RISD_Pixel218_OnOff_554 = 829785527,
  ICC_RISD_Pixel219_OnOff_554 = 829818296,
  ICC_RISD_Pixel220_OnOff_554 = 829851065,
  ICC_RISD_Pixel221_OnOff_554 = 829883834,
  ICC_RISD_Pixel222_OnOff_554 = 829916603,
  ICC_RISD_Pixel223_OnOff_554 = 829949372,
  ICC_RISD_Pixel224_OnOff_554 = 829982141,
  ICC_RISD_Pixel225_OnOff_554 = 830014910,
  ICC_RISD_Pixel226_OnOff_554 = 830047679,
  ICC_RISD_Pixel227_OnOff_554 = 830080448,
  ICC_RISD_Pixel228_OnOff_554 = 830113217,
  ICC_RISD_Pixel229_OnOff_554 = 830145986,
  ICC_RISD_Pixel230_OnOff_554 = 830178755,
  ICC_RISD_Pixel231_OnOff_554 = 830211524,
  ICC_RISD_Pixel232_OnOff_554 = 830244293,
  ICC_RISD_Pixel233_OnOff_554 = 830277062,
  ICC_RISD_Pixel234_OnOff_554 = 830309831,
  ICC_RISD_Pixel235_OnOff_554 = 830342600,
  ICC_RISD_Pixel236_OnOff_554 = 830375369,
  ICC_RISD_Pixel237_OnOff_554 = 830408138,
  ICC_RISD_Pixel238_OnOff_554 = 830440907,
  ICC_RISD_Pixel239_OnOff_554 = 830473676,
  ICC_RISD_Pixel240_OnOff_554 = 830506445,
  ICC_RISD_Pixel241_OnOff_554 = 830539214,
  ICC_RISD_Pixel242_OnOff_554 = 830571983,
  ICC_RISD_Pixel243_OnOff_554 = 830604752,
  ICC_RISD_Pixel244_OnOff_554 = 830637521,
  ICC_RISD_Pixel245_OnOff_554 = 830670290,
  ICC_RISD_Pixel246_OnOff_554 = 830703059,
  ICC_RISD_Pixel247_OnOff_554 = 830735828,
  ICC_RISD_Pixel248_OnOff_554 = 830768597,
  ICC_RISD_Pixel249_OnOff_554 = 830801366,
  ICC_RISD_Pixel250_OnOff_554 = 830834135,
  ICC_RISD_Pixel251_OnOff_554 = 830866904,
  ICC_RISD_Pixel252_OnOff_554 = 830899673,
  ICC_RISD_Pixel253_OnOff_554 = 830932442,
  ICC_RISD_Pixel254_OnOff_554 = 830965211,
  ICC_RISD_Pixel255_OnOff_554 = 830997980,
  ICC_RISD_Pixel256_OnOff_554 = 831030749,
  ICC_RISD_Pixel257_OnOff_554 = 831063518,
  ICC_RISD_Pixel258_OnOff_554 = 831096287,
  ICC_RISD_Pixel259_OnOff_554 = 831129056,
  ICC_RISD_Pixel260_OnOff_554 = 831161825,
  ICC_RISD_Pixel261_OnOff_554 = 831194594,
  ICC_RISD_Pixel262_OnOff_554 = 831227363,
  ICC_RISD_Pixel263_OnOff_554 = 831260132,
  ICC_RISD_Pixel264_OnOff_554 = 831292901,
  ICC_RISD_Pixel265_OnOff_554 = 831325670,
  ICC_RISD_Pixel266_OnOff_554 = 831358439,
  ICC_RISD_Pixel267_OnOff_554 = 831391208,
  ICC_RISD_Pixel268_OnOff_554 = 831423977,
  ICC_RISD_Pixel269_OnOff_554 = 831456746,
  ICC_RISD_Pixel270_OnOff_554 = 831489515,
  ICC_RISD_Pixel271_OnOff_554 = 831522284,
  ICC_RISD_Pixel272_OnOff_554 = 831555053,
  ICC_RISD_Pixel273_OnOff_554 = 831587822,
  ICC_RISD_Pixel274_OnOff_554 = 831620591,
  ICC_RISD_Pixel275_OnOff_554 = 831653360,
  ICC_RISD_Pixel276_OnOff_554 = 831686129,
  ICC_RISD_Pixel277_OnOff_554 = 831718898,
  ICC_RISD_Pixel278_OnOff_554 = 831751667,
  ICC_RISD_Pixel279_OnOff_554 = 831784436,
  ICC_RISD_Pixel280_OnOff_554 = 831817205,
  ICC_RISD_Pixel281_OnOff_554 = 831849974,
  ICC_RISD_Pixel282_OnOff_554 = 831882743,
  ICC_RISD_Pixel283_OnOff_554 = 831915512,
  ICC_RISD_Pixel284_OnOff_554 = 831948281,
  ICC_RISD_Pixel285_OnOff_554 = 831981050,
  ICC_RISD_Pixel286_OnOff_554 = 832013819,
  ICC_RISD_Pixel287_OnOff_554 = 832046588,
  ICC_RISD_Pixel288_OnOff_554 = 832079357,
  ICC_RISD_Pixel289_OnOff_554 = 832112126,
  ICC_RISD_Pixel290_OnOff_554 = 832144895,
  ICC_RISD_Pixel291_OnOff_554 = 832177664,
  ICC_RISD_Pixel292_OnOff_554 = 832210433,
  ICC_RISD_Pixel293_OnOff_554 = 832243202,
  ICC_RISD_Pixel294_OnOff_554 = 832275971,
  ICC_RISD_Pixel295_OnOff_554 = 832308740,
  ICC_RISD_Pixel296_OnOff_554 = 832341509,
  ICC_RISD_Pixel297_OnOff_554 = 832374278,
  ICC_RISD_Pixel298_OnOff_554 = 832407047,
  ICC_RISD_Pixel299_OnOff_554 = 832439816,
  ICC_RISD_Pixel300_OnOff_554 = 832472585,
  ICC_RISD_Pixel301_OnOff_554 = 832505354,
  ICC_RISD_Pixel302_OnOff_554 = 832538123,
  ICC_RISD_Pixel303_OnOff_554 = 832570892,
  ICC_RISD_Pixel304_OnOff_554 = 832603661,
  ICC_RISD_Pixel305_OnOff_554 = 832636430,
  ICC_RISD_Pixel306_OnOff_554 = 832669199,
  ICC_RISD_Pixel307_OnOff_554 = 832701968,
  ICC_RISD_Pixel308_OnOff_554 = 832734737,
  ICC_RISD_Pixel309_OnOff_554 = 832767506,
  ICC_RISD_Pixel310_OnOff_554 = 832800275,
  ICC_RISD_Pixel311_OnOff_554 = 832833044,
  ICC_RISD_Pixel312_OnOff_554 = 832865813,
  ICC_RISD_Pixel313_OnOff_554 = 832898582,
  ICC_RISD_Pixel314_OnOff_554 = 832931351,
  ICC_RISD_Pixel315_OnOff_554 = 832964120,
  ICC_RISD_Pixel316_OnOff_554 = 832996889,
  ICC_RISD_Pixel317_OnOff_554 = 833029658,
  ICC_RISD_Pixel318_OnOff_554 = 833062427,
  ICC_RISD_Pixel319_OnOff_554 = 833095196,
  ICC_RISD_Pixel320_OnOff_554 = 833127965,
  ICC_RISD_Pixel321_OnOff_554 = 833160734,
  ICC_RISD_Pixel322_OnOff_554 = 833193503,
  ICC_RISD_Pixel323_OnOff_554 = 833226272,
  ICC_RISD_Pixel324_OnOff_554 = 833259041,
  ICC_RISD_Pixel325_OnOff_554 = 833291810,
  ICC_RISD_Pixel326_OnOff_554 = 833324579,
  ICC_RISD_Pixel327_OnOff_554 = 833357348,
  ICC_RISD_Pixel328_OnOff_554 = 833390117,
  ICC_RISD_Pixel329_OnOff_554 = 833422886,
  ICC_RISD_Pixel330_OnOff_554 = 833455655,
  ICC_RISD_Pixel331_OnOff_554 = 833488424,
  ICC_RISD_Pixel332_OnOff_554 = 833521193,
  ICC_RISD_Pixel333_OnOff_554 = 833553962,
  ICC_RISD_Pixel334_OnOff_554 = 833586731,
  ICC_RISD_Pixel335_OnOff_554 = 833619500,
  ICC_RISD_Pixel336_OnOff_554 = 833652269,
  ICC_RISD_Pixel337_OnOff_554 = 833685038,
  ICC_RISD_Pixel338_OnOff_554 = 833717807,
  ICC_RISD_Pixel339_OnOff_554 = 833750576,
  ICC_RISD_Pixel340_OnOff_554 = 833783345,
  ICC_RISD_Pixel341_OnOff_554 = 833816114,
  ICC_RISD_Pixel342_OnOff_554 = 833848883,
  ICC_RISD_Pixel343_OnOff_554 = 833881652,
  ICC_RISD_Pixel344_OnOff_554 = 833914421,
  ICC_RISD_Pixel345_OnOff_554 = 833947190,
  ICC_RISD_Pixel346_OnOff_554 = 833979959,
  ICC_RISD_Pixel347_OnOff_554 = 834012728,
  ICC_RISD_Pixel348_OnOff_554 = 834045497,
  ICC_RISD_Pixel349_OnOff_554 = 834078266,
  ICC_RISD_Pixel350_OnOff_554 = 834111035,
  ICC_RISD_Pixel351_OnOff_554 = 834143804,
  ICC_RISD_Pixel352_OnOff_554 = 834176573,
  ICC_RISD_Pixel353_OnOff_554 = 834209342,
  ICC_RISD_Pixel354_OnOff_554 = 834242111,
  ICC_RISD_Pixel355_OnOff_554 = 834274880,
  ICC_RISD_Pixel356_OnOff_554 = 834307649,
  ICC_RISD_Pixel357_OnOff_554 = 834340418,
  ICC_RISD_Pixel358_OnOff_554 = 834373187,
  ICC_RISD_Pixel359_OnOff_554 = 834405956,
  ICC_RISD_Pixel360_OnOff_554 = 834438725,
  ICC_RISD_Pixel361_OnOff_554 = 834471494,
  ICC_RISD_Pixel362_OnOff_554 = 834504263,
  ICC_RISD_Pixel363_OnOff_554 = 834537032,
  ICC_RISD_Pixel364_OnOff_554 = 834569801,
  ICC_RISD_Pixel365_OnOff_554 = 834602570,
  ICC_RISD_Pixel366_OnOff_554 = 834635339,
  ICC_RISD_Pixel367_OnOff_554 = 834668108,
  ICC_RISD_Pixel368_OnOff_554 = 834700877,
  ICC_RISD_Pixel369_OnOff_554 = 834733646,
  ICC_RISD_Pixel370_OnOff_554 = 834766415,
  ICC_RISD_Pixel371_OnOff_554 = 834799184,
  ICC_RISD_Pixel372_OnOff_554 = 834831953,
  ICC_RISD_Pixel373_OnOff_554 = 834864722,
  ICC_RISD_Pixel374_OnOff_554 = 834897491,
  ICC_RISD_Pixel375_OnOff_554 = 834930260,
  ICC_RISD_Pixel376_OnOff_554 = 834963029,
  ICC_RISD_Pixel377_OnOff_554 = 834995798,
  ICC_RISD_Pixel378_OnOff_554 = 835028567,
  ICC_RISD_Pixel379_OnOff_554 = 835061336,
  ICC_RISD_Pixel380_OnOff_554 = 835094105,
  ICC_RISD_Pixel381_OnOff_554 = 835126874,
  ICC_RISD_Pixel382_OnOff_554 = 835159643,
  ICC_RISD_Pixel383_OnOff_554 = 835192412,
  ICC_RISD_Pixel384_OnOff_554 = 835225181,
  ICC_RISD_Pixel385_OnOff_554 = 835257950,
  ICC_RISD_Pixel386_OnOff_554 = 835290719,
  ICC_RISD_Pixel387_OnOff_554 = 835323488,
  ICC_RISD_Pixel388_OnOff_554 = 835356257,
  ICC_RISD_Pixel389_OnOff_554 = 835389026,
  ICC_RISD_Pixel390_OnOff_554 = 835421795,
  ICC_RISD_Pixel391_OnOff_554 = 835454564,
  ICC_RISD_Pixel392_OnOff_554 = 835487333,
  ICC_RISD_Pixel393_OnOff_554 = 835520102,
  ICC_RISD_Pixel394_OnOff_554 = 835552871,
  ICC_RISD_Pixel395_OnOff_554 = 835585640,
  ICC_RISD_Pixel396_OnOff_554 = 835618409,
  ICC_RISD_Pixel397_OnOff_554 = 835651178,
  ICC_RISD_Pixel398_OnOff_554 = 835683947,
  ICC_RISD_Pixel399_OnOff_554 = 835716716,
  ICC_RISD_Pixel400_OnOff_554 = 835749485,
  ICC_RISD_Pixel401_OnOff_554 = 835782254,
  ICC_RISD_Pixel402_OnOff_554 = 835815023,
  ICC_RISD_Pixel403_OnOff_554 = 835847792,
  ICC_RISD_Pixel404_OnOff_554 = 835880561,
  ICC_RISD_Pixel405_OnOff_554 = 835913330,
  ICC_RISD_Pixel406_OnOff_554 = 835946099,
  ICC_RISD_Pixel407_OnOff_554 = 835978868,
  ICC_RISD_Pixel408_OnOff_554 = 836011637,
  ICC_RISD_Pixel409_OnOff_554 = 836044406,
  ICC_RISD_Pixel410_OnOff_554 = 836077175,
  ICC_RISD_Pixel411_OnOff_554 = 836109944,
  ICC_RISD_Pixel412_OnOff_554 = 836142713,
  ICC_RISD_Pixel413_OnOff_554 = 836175482,
  ICC_RISD_Pixel414_OnOff_554 = 836208251,
  ICC_RISD_Pixel415_OnOff_554 = 836241020,
  ICC_RISD_Pixel416_OnOff_554 = 836273789,
  ICC_RISD_Pixel417_OnOff_554 = 836306558,
  ICC_RISD_Pixel418_OnOff_554 = 836339327,
  ICC_RISD_Pixel419_OnOff_554 = 836372096,
  ICC_RISD_Pixel420_OnOff_554 = 836404865,
  ICC_RISD_Pixel421_OnOff_554 = 836437634,
  ICC_RISD_Pixel422_OnOff_554 = 836470403,
  ICC_RISD_Pixel423_OnOff_554 = 836503172,
  ICC_RISD_Pixel424_OnOff_554 = 836535941,
  ICC_RISD_Pixel425_OnOff_554 = 836568710,
  ICC_RISD_Pixel426_OnOff_554 = 836601479,
  ICC_RISD_Pixel427_OnOff_554 = 836634248,
  ICC_RISD_Pixel428_OnOff_554 = 836667017,
  ICC_RISD_Pixel429_OnOff_554 = 836699786,
  ICC_RISD_Pixel430_OnOff_554 = 836732555,
  ICC_RISD_Pixel431_OnOff_554 = 836765324,
  ICC_RISD_Pixel432_OnOff_554 = 836798093,
  ICC_RISD_Pixel433_OnOff_554 = 836830862,
  ICC_RISD_Pixel434_OnOff_554 = 836863631,
  ICC_RISD_Pixel435_OnOff_554 = 836896400,
  ICC_RISD_Pixel436_OnOff_554 = 836929169,
  ICC_RISD_Pixel437_OnOff_554 = 836961938,
  ICC_RISD_Pixel438_OnOff_554 = 836994707,
  ICC_RISD_Pixel439_OnOff_554 = 837027476,
  ICC_RISD_Pixel440_OnOff_554 = 837060245,
  ICC_RISD_Pixel441_OnOff_554 = 837093014,
  ICC_RISD_Pixel442_OnOff_554 = 837125783,
  ICC_RISD_Pixel443_OnOff_554 = 837158552,
  ICC_RISD_Pixel444_OnOff_554 = 837191321,
  ICC_RISD_Pixel445_OnOff_554 = 837224090,
  ICC_RISD_Pixel446_OnOff_554 = 837256859,
  ICC_RISD_Pixel447_OnOff_554 = 837289628,
  ICC_RISD_Pixel448_OnOff_554 = 837322397,
  ICC_RISD_Pixel449_OnOff_554 = 837355166,
  ICC_RISD_Pixel450_OnOff_554 = 837387935,
  ICC_RISD_Pixel451_OnOff_554 = 837420704,
  ICC_RISD_Pixel452_OnOff_554 = 837453473,
  ICC_RISD_Pixel453_OnOff_554 = 837486242,
  ICC_RISD_Pixel454_OnOff_554 = 837519011,
  ICC_RISD_Pixel455_OnOff_554 = 837551780,
  ICC_RISD_Pixel456_OnOff_554 = 837584549,
  ICC_RISD_Pixel457_OnOff_554 = 837617318,
  ICC_RISD_Pixel458_OnOff_554 = 837650087,
  ICC_RISD_Pixel459_OnOff_554 = 837682856,
  ICC_RISD_Pixel460_OnOff_554 = 837715625,
  ICC_RISD_Pixel461_OnOff_554 = 837748394,
  ICC_RISD_Pixel462_OnOff_554 = 837781163,
  ICC_RISD_Pixel463_OnOff_554 = 837813932,
  ICC_RISD_Pixel464_OnOff_554 = 837846701,
  ICC_RISD_Pixel465_OnOff_554 = 837879470,
  ICC_RISD_Pixel466_OnOff_554 = 837912239,
  ICC_RISD_Pixel467_OnOff_554 = 837945008,
  ICC_RISD_Pixel468_OnOff_554 = 837977777,
  ICC_RISD_Pixel469_OnOff_554 = 838010546,
  ICC_RISD_Pixel470_OnOff_554 = 838043315,
  ICC_RISD_Pixel471_OnOff_554 = 838076084,
  ICC_RISD_Pixel472_OnOff_554 = 838108853,
  ICC_RISD_Pixel473_OnOff_554 = 838141622,
  ICC_RISD_Pixel474_OnOff_554 = 838174391,
  ICC_RISD_Pixel475_OnOff_554 = 838207160,
  ICC_RISD_Pixel476_OnOff_554 = 838239929,
  ICC_RISD_Pixel477_OnOff_554 = 838272698,
  ICC_RISD_Pixel478_OnOff_554 = 838305467,
  ICC_RISD_Pixel479_OnOff_554 = 838338236,
  ICC_RISD_Pixel480_OnOff_554 = 838371005,
  ICC_RISD_Pixel481_OnOff_554 = 838403774,
  ICC_RISD_Pixel482_OnOff_554 = 838436543,
  ICC_RISD_Pixel483_OnOff_554 = 838469312,
  ICC_RISD_Pixel484_OnOff_554 = 838502081,
  ICC_RISD_Pixel485_OnOff_554 = 838534850,
  ICC_RISD_Pixel486_OnOff_554 = 838567619,
  ICC_RISD_Pixel487_OnOff_554 = 838600388,
  ICC_RISD_Pixel488_OnOff_554 = 838633157,
  ICC_RISD_Pixel489_OnOff_554 = 838665926,
  ICC_RISD_Pixel490_OnOff_554 = 838698695,
  ICC_RISD_Pixel491_OnOff_554 = 838731464,
  ICC_RISD_Pixel492_OnOff_554 = 838764233,
  ICC_RISD_Pixel493_OnOff_554 = 838797002,
  ICC_RISD_Pixel494_OnOff_554 = 838829771,
  ICC_RISD_Pixel495_OnOff_554 = 838862540,
  ICC_RISD_Pixel496_OnOff_554 = 838895309,
  ICC_RISD_Pixel497_OnOff_554 = 838928078,
  ICC_RISD_Pixel498_OnOff_554 = 838960847,
  ICC_RISD_Pixel499_OnOff_554 = 838993616,
  ICC_RISD_Pixel500_OnOff_554 = 839026385,
  ICC_RISD_Pixel501_OnOff_554 = 839059154,
  ICC_RISD_Pixel502_OnOff_554 = 839091923,
  ICC_RISD_Pixel503_OnOff_554 = 839124692,
  ICC_RISD_Pixel504_OnOff_554 = 839157461,
  ICC_LKSSettingModSt_556 = 156468950,
  ICC_LDWWarnType_556 = 156501719,
  ICC_LDWLDPSnvtySet_556 = 156534488,
  ICC_ELKSettingSt_556 = 156567257,
  ICC_ICAEnableBtnSts_556 = 156600026,
  ICC_FCWSettingSt_556 = 156632795,
  ICC_AEBSettingSt_556 = 156665564,
  ICC_ISLIWarnMod_556 = 156698333,
  ICC_Customize_buttons_556 = 156731102,
  ICC_AVM_TouchEvt_556 = 156763871,
  ICC_AVM_BtnPressInputValueX_556 = 269453024,
  ICC_AVM_BtnPressInputValueY_556 = 269485793,
  ICC_SetMsgTotal_54A = 269518562,
  ICC_SetMSDTotal_54A = 269551331,
  ICC_SetCallTimeThreshold_54A = 269584100,
  ICC_SetTodayCallTimeThreshold_54A = 269616869,
  ICC_SetCurretnCallTime_54A = 269649638,
  ICC_SetCurretnMsgThreshold_54A = 269682407,
  ICC_SetMsgTotalThreshold_54A = 269715176,
  ICC_SetMsdThreshold_54A = 269747945,
  ICC_SetCurrenMsdCnt_54A = 269780714,
  ICC_SetCustomMsgThreshold_54A = 269813483,
  ICC_SetCurrentCustomMsgCnt_54A = 269846252,
  ICC_flag_54C = 156796653,
  ICC_ActiveTsp_Req_54C = 156829422,
  ICC_ActiveSim_Req_54C = 156862191,
  ICC_GetMac_req_54C = 839190256,
  ICC_GetIccid_Vin_54C = 839223025,
  ICC_GivePlatformNumber_Flag_54C = 839255794,
  ICC_Platform_Phonenub1_54C = 156894963,
  ICC_Platform_Phonenub2_54C = 156927732,
  ICC_Platform_Phonenub3_54C = 156960501,
  ICC_Platform_Phonenub4_54C = 156993270,
  ICC_Platform_Phonenub5_54C = 157026039,
  ICC_Platform_Phonenub6_54C = 157058808,
  ICC_Platform_Phonenub7_54C = 157091577,
  ICC_Platform_Phonenub8_54C = 157124346,
  ICC_Platform_Phonenub9_54C = 157157115,
  ICC_Platform_Phonenub10_54C = 157189884,
  ICC_Platform_Phonenub11_54C = 157222653,
  ICC_Platform_Phonenub12_54C = 157255422,
  ICC_Platform_Phonenub13_54C = 157288191,
  ICC_Platform_Phonenub14_54C = 157320960,
  ICC_Platform_Phonenub15_54C = 157353729,
  ICC_Platform_Phonenub16_54C = 157386498,
  ICC_Platform_Phonenub17_54C = 157419267,
  ICC_Platform_Phonenub18_54C = 157452036,
  ICC_Platform_Phonenub19_54C = 157484805,
  ICC_Platform_Phonenub20_54C = 157517574,
  ICC_Mac_Flag_54C = 839288583,
  ICC_Mac0_54C = 157550344,
  ICC_Mac1_54C = 157583113,
  ICC_Mac2_54C = 157615882,
  ICC_Mac3_54C = 157648651,
  ICC_Mac4_54C = 157681420,
  ICC_Mac5_54C = 157714189,
  ICC_MemAttributeFL_5AD = 157746958,
  ICC_MemAttributeFR_5AD = 157779727,
  ICC_MemAttributeRL_5AD = 157812496,
  ICC_MemAttributeRR_5AD = 157845265,
  ICC_FLSeatHeatLvlCmd2_564 = 157878034,
  ICC_RLSeatHeatLvlCmd2_564 = 157910803,
  ICC_FLSeatVentLvlCmd2_564 = 157943572,
  ICC_RLSeatVentLvlCmd2_564 = 157976341,
  ICC_FRSeatHeatLvlCmd2_564 = 158009110,
  ICC_RRSeatHeatLvlCmd2_564 = 158041879,
  ICC_FRSeatVentLvlCmd2_564 = 158074648,
  ICC_RRSeatVentLvlCmd2_564 = 158107417,
  ICC_VINcode_10_573 = 158140186,
  ICC_VINcode_11_573 = 158172955,
  ICC_VINcode_12_573 = 158205724,
  ICC_VINcode_13_573 = 158238493,
  ICC_VINcode_14_573 = 158271262,
  ICC_VINcode_15_573 = 158304031,
  ICC_VINcode_16_573 = 158336800,
  ICC_VINcode_17_573 = 158369569,
  ICC_Auth_IMMO_45C = 536971042,
  VCC_Teach_IMMO_47F = 537003811,
  ICC_TotalFaultNum_520 = 158402340,
  ICC_FrameIndex_520 = 158435109,
  ICC_SupplierNum_520 = 158467878,
  ICC_FaultNum1_520 = 269879079,
  ICC_FaultNum2_520 = 269911848,
  ICC_FaultNum3_520 = 269944617,
  ICC_FaultNum4_520 = 269977386,
  ICC_FaultNum5_520 = 270010155,
  ICC_FaultNum6_520 = 270042924,
  ICC_FaultNum7_520 = 270075693,
  ICC_FaultNum8_520 = 270108462,
  ICC_FaultNum9_520 = 270141231,
  ICC_FaultNum10_520 = 270174000,
  ICC_FaultNum11_520 = 270206769,
  ICC_FaultNum12_520 = 270239538,
  ICC_FaultNum13_520 = 270272307,
  ICC_FaultNum14_520 = 270305076,
  ICC_FaultNum15_520 = 270337845,
  ICC_FaultNum16_520 = 270370614,
  ICC_FaultNum17_520 = 270403383,
  ICC_FaultNum18_520 = 270436152,
  ICC_FaultNum19_520 = 270468921,
  ICC_FaultNum20_520 = 270501690,
  Xcp_ICC_REQ_481 = 537036603,
  Xcp_ICC_RES_482 = 537069372,
  RearDefrostSts_35B = 839321405,
  FLZCU_9_PowerMode_49D = 158500670,
  FLZCU_9_ArmingSts_49D = 158533439,
  RearViewFoldSts_406 = 158566208,
  FLZCU_AlrmSts_406 = 158598977,
  FLZCU_SteeringMode_406 = 158631746,
  FLZCU_SuspensionDamping_406 = 158664515,
  FLZCU_SuspensionHeight_406 = 158697284,
  FLZCU_OBDDiagOccupy_406 = 158730053,
  FLZCU_AutoFoldSts_42F = 839354182,
  FLZCU_AutolockSts_42F = 839386951,
  FLZCU_FGHeat_Req_CmdFeedback_42F = 839419720,
  FLZCU_WindowInhibitSts_42F = 839452489,
  FLZCU_AutoReadLightSts_42F = 839485258,
  FLZCU_HeatingSW_42F = 839518027,
  FLZCU_AutoHeatingFb_42F = 839550796,
  FLZCU_SteerNeedMemory_42F = 839583565,
  FLZCU_RearNeedMemory_42F = 839616334,
  FLZCU_FLHandleFoldedStatus_42F = 158762831,
  FLZCU_RLHandleFoldedStatus_42F = 158795600,
  FLZCU_FRHandleFoldedStatus_42F = 158828369,
  FLZCU_RRHandleFoldedStatus_42F = 158861138,
  FLZCU_RvsExtMirrFbSts_42F = 158893907,
  FLZCU_LightMainSwitchSts_42F = 158926676,
  FLZCU_BeamDelaySts_42F = 158959445,
  FLZCU_LowBeamHighSts_42F = 158992214,
  FLZCU_WipeSensitivitySts_42F = 159024983,
  FLZCU_WipeMaintenanceSWSts_42F = 159057752,
  FLZCU_ImmoRequest_42F = 159090521,
  FLZCU_AlarmWarnSetSW_42F = 159123290,
  FLZCU_CleanModeStatus_42F = 159156059,
  FLZCU_FLReleaseLockSts_42F = 159188828,
  FLZCU_RearFogLightSts_42F = 839649117,
  FLZCU_FRFoglightSts_42F = 839681886,
  FLZCU_FLFoglightSts_42F = 839714655,
  FLZCU_RearFogLightFaults_42F = 839747424,
  FLZCU_HighBeamFaults_42F = 839780193,
  FLZCU_HighBeamSts_42F = 839812962,
  FLZCU_LowBeamSts_42F = 839845731,
  FLZCU_ParkUnlockEnableFb_42F = 839878500,
  FLZCU_RLReleaseLockSts_42F = 159221605,
  FLZCU_EasyEntryExitFb_42F = 159254374,
  FLZCU_HMASwSts_42F = 839911271,
  FLZCU_DaytimeLight_42F = 159287144,
  FLZCU_ParkLightFaults_42F = 839944041,
  FLZCU_LbatipVehPwrMod_42F = 839976810,
  FLZCU_LVPowICDsp_42F = 159319915,
  FLZCU_LockCarWinCloseFb_42F = 159352684,
  FLZCU_BrkLightSts_42F = 159385453,
  FLZCU_ReverseLightSts_42F = 159418222,
  FLZCU_LowWashLqdWarning_42F = 159450991,
  FLZCU_SteerWheelHeatingFb_42F = 159483760,
  FLZCU_FrontFogLightSts_42F = 840009585,
  FLZCU_NapAreaFb_42F = 159516530,
  FLZCU_NapTimeFb_42F = 159549299,
  FLZCU_NapAlarmStatus_42F = 159582068,
  FLZCU_NapFailCause_42F = 159614837,
  FLZCU_NapStatusFb_42F = 159647606,
  FLZCU_CleanModeFailCause_42F = 159680375,
  FLZCU_NapClosePrompt_42F = 159713144,
  FLZCU_AutoWipingInhibit_42F = 159745913,
  FLZCU_RLWinSts_42F = 159778682,
  FLZCU_FLWinSts_42F = 159811451,
  FLZCU_DaytimeLightFaults_42F = 159844220,
  FLZCU_FrontFogLightFaults_42F = 159876989,
  FLZCU_HomeLinkWelLightSetSts_42F = 159909758,
  FLZCU_HomeLinkArmHornSetSts_42F = 159942527,
  FLZCU_WelcomeOpenStas_42F = 159975296,
  FLZCU_WALOpenStas_42F = 160008065,
  FLZCU_UIROpenStas_42F = 160040834,
  FLZCU_ExtLiSwtClsSts_42F = 160073603,
  FLZCU_Totalodometerbackup_581 = 402753412,
  FLZCU_FLSeatHeiFb_4BB = 160106373,
  FLZCU_FLSeatLvlFb_4BB = 160139142,
  FLZCU_FLSeatBackAgFb_4BB = 160171911,
  FLZCU_FLSeatCushFb_4BB = 160204680,
  FLZCU_FLSeatLegSpprtFb_4BB = 160237449,
  FLZCU_SeatNeedMemory_4BB = 160270218,
  FLZCU_MemoryFb_4BB = 160302987,
  FLZCU_RecoverFb_4BB = 160335756,
  FLZCU_VINcode_10_595 = 160368525,
  FLZCU_VINcode_11_595 = 160401294,
  FLZCU_VINcode_12_595 = 160434063,
  FLZCU_VINcode_13_595 = 160466832,
  FLZCU_VINcode_14_595 = 160499601,
  FLZCU_VINcode_15_595 = 160532370,
  FLZCU_VINcode_16_595 = 160565139,
  FLZCU_VINcode_17_595 = 160597908,
  LHTurnlightSts_0x430_430 = 840042389,
  RHTurnlightSts_0x430_430 = 840075158,
  LHFdoorSts_0x430_430 = 160630679,
  LHFSeatBeltSW_0x430_430 = 840107928,
  TrunkSts_430 = 160663449,
  RLWinPosn_430 = 160696218,
  SeatHeatLevelsFL_430 = 160728987,
  SeatHeatLevelsRL_430 = 160761756,
  SeatVentLevelsFL_430 = 160794525,
  SeatVentLevelsRL_430 = 160827294,
  FLWinPosn_430 = 160860063,
  FLZCU_FrontWiperMaintenanceSts_430 = 160892832,
  FLZCU_RearWiperMaintenanceSts_430 = 160925601,
  VCU_2_G_CRC_4B9 = 160958370,
  VCU_2_G_RollgCntr_4B9 = 160991139,
  VCU_2_G_Resd_4B9 = 161023908,
  VCU_2_G_GasPedalPosInvalidData_4B9 = 840140709,
  VCU_2_G_PowerLimit_4B9 = 840173478,
  VCU_2_G_BrakeLightReq_4B9 = 840206247,
  VCU_2_G_VehicleSystemFailure_4B9 = 840239016,
  VCU_2_G_BootloadSts_4B9 = 840271785,
  VCU_2_G_DischgSts_4B9 = 840304554,
  VCU_2_G_BrakePedalStsValidData_4B9 = 840337323,
  VCU_2_G_ExhibitionMod_4B9 = 840370092,
  VCU_2_G_BrakePedalSts_4B9 = 840402861,
  VCU_2_G_DCDCEnable_4B9 = 840435630,
  VCU_2_G_HVReady_4B9 = 840468399,
  Res_VCU_2_G_Brake_state_4B9 = 840501168,
  VCU_2_G_VCUEnergyflow_4B9 = 161056689,
  VCU_2_G_DrvGearShiftFailureIndcn_4B9 = 161089458,
  VCU_2_G_GasPedalPosition_4B9 = 270534579,
  VCU_CLTC_RangeAval_4CD = 270567348,
  VCU_DynamicRangeAval_4CD = 270600117,
  VCU_HighspdRangeAval_4CD = 270632886,
  VCU_WLTC_RangeAval_4CD = 270665655,
  VCU_TenECAver_4EF = 270698424,
  VCU_twenty_fiveECAver_4EF = 270731193,
  VCU_fiftyECAver_4EF = 270763962,
  HCU_EmPwr_40A = 161122235,
  HCU_PowerModeFed_40A = 161155004,
  HCU_PowertrainFault_40A = 840533949,
  HCU_PowerModeChangeFail_40A = 840566718,
  HCU_ChargingSts_40A = 161187775,
  HCU_EnergyFlow_414 = 161220544,
  HCU_SetSOCFed_414 = 161253313,
  HCU_2_G_LowBatteryInfo_414 = 161286082,
  Fusa_4B4_5_4B4 = 161318851,
  Fusa_4B4_4_4B4 = 270796740,
  Fusa_4B4_3_4B4 = 161351621,
  Fusa_4B4_2_4B4 = 270829510,
  Fusa_4B4_1_4B4 = 161384391,
  VCU_1_G_CRC_4B4 = 161417160,
  VCU_1_G_RollgCntr_4B4 = 161449929,
  VCU_1_G_Pwr_4B4 = 270862282,
  VCU_1_G_PTReady_4B4 = 840599499,
  VCU_1_G_DriveMode_4B4 = 161482700,
  VCU_1_G_BookChrgSetResponse_4B4 = 161515469,
  VCU_1_G_HvBattKeepWarmActiveReq_4B4 = 161548238,
  VCU_1_G_PRNDGearAct_4B4 = 161581007,
  VCU_TowingMode_421 = 161613776,
  VCU_EngyConsuPer50Km_421 = 270895057,
  VCU_BookChrgSetFailRes_421 = 161646546,
  VCU_BookChrgFailRes_421 = 161679315,
  VCU_BookChrgStsFb_421 = 161712084,
  HCU_EngineSelfMaintain_421 = 161744853,
  EBS_U_BATT_413 = 270927830,
  FLZCU_CarMode_3FE = 161777623,
  FLZCU_SteerWhlposSts_3FE = 161810392,
  FLZCU_MedStatusFb_3FE = 161843161,
  FLZCU_MedFailCause_3FE = 161875930,
  FLZCU_MedAreaFb_3FE = 161908699,
  FLZCU_MedClosePrompt_3FE = 161941468,
  FLZCU_NapArea2Fb_3FE = 161974237,
  FLZCU_2LSeatLvltargetCmd_3FE = 162007006,
  FLZCU_2LSeatBackAgtargetCmd_3FE = 162039775,
  FLZCU_2LSeatLegSpprttargetCmd_3FE = 162072544,
  FLZCU_FLWinThermalStatus_3FE = 840632289,
  FLZCU_RLWinThermalStatus_3FE = 840665058,
  FLZCU_MonoAmbLigSts_3FE = 840697827,
  FLZCU_LogoChargingSts_3FE = 162105316,
  FLZCU_2RSeatLvltargetCmd_3FE = 162138085,
  FLZCU_2RSeatBackAgtargetCmd_3FE = 162170854,
  FLZCU_2RSeatLegSpprttargetCmd_3FE = 162203623,
  FLZCU_SeatVentLvlFL2_3FE = 162236392,
  FLZCU_SeatVentLvlRL2_3FE = 162269161,
  FLZCU_SeatHeatLvlFL2_3FE = 162301930,
  FLZCU_SeatHeatLvlRL2_3FE = 162334699,
  FLZCU_FLSitPosnSts_3FE = 162367468,
  FLZCU_SteerOverHeatWarn_3FE = 840730605,
  SAM_1_SteeringAngleSpeed_3FC = 162400238,
  SAM_1_SteeringAngle_3FC = 270960623,
  BMSH_MaxChgPwrPeak_3FC = 270993392,
  BMSH_MaxDchgPwrPeak_3FC = 271026161,
  FMCU_8_ISGF_SpdAct_3FC = 271058930,
  RMCU_1_Speed_3FC = 271091699,
  SAM_1_SteeringAngleVD_3FC = 840763380,
  SAM_1_SteeringAngleSpeedVD_3FC = 840796149,
  CSA_2_AllWarningInfo_3FC = 162433014,
  RELC_RoofCampLampConfigDisp_3FC = 840828919,
  RELC_TopLightConfigDisp_3FC = 840861688,
  EELC_APillarLightConfigDisp_3FC = 840894457,
  RELC_RoofCampLampStsL_3FC = 162465786,
  RELC_RoofCampLampStsR_3FC = 162498555,
  RELC_TopLightSts_3FC = 162531324,
  EELC_APillarYellowAmbientSts_3FC = 162564093,
  EELC_APillarWhiteAmbientSts_3FC = 162596862,
  EELC_APillarSpotLampSts_3FC = 162629631,
  HCU_4_G_AvgFuCns_476 = 271124480,
  HCU_4_G_AvgEnergyCns_476 = 271157249,
  HCU_4_G_SumEgyCns_476 = 271190018,
  HCU_V2lDisChgBck_522 = 162662403,
  HCU_DisChgMemFed_522 = 162695172,
  HCU_V2VDisChgBck_522 = 162727941,
  HCU_V2l_IntlDisChgBck_522 = 162760710,
  HCU_FuelDetnOpDefeated_522 = 162793479,
  HCU_FuelDetnModSwtFed_522 = 162826248,
  HCU_FuelDetnStateFed_522 = 162859017,
  HCU_HVDownRepairModeFed_522 = 162891786,
  HCU_TrailerMode_522 = 162924555,
  HCU_SumFuCns_522 = 271222796,
  HCU_SumElecCns_522 = 271255565,
  HCU_DrivePowerDispSetFed_522 = 162957326,
  HCU_KeyDriveModememory_522 = 162990095,
  HCU_DRIFTModeSts_522 = 163022864,
  HCU_EXPERTEcoFed_522 = 163055633,
  HCU_4LModeFed_522 = 840927250,
  HCU_EXPERTNormFed_522 = 163088403,
  HCU_SingleRegnRmn_522 = 840960020,
  HCU_EXPERTSportFed_522 = 163121173,
  HCU_CrossaxisFed_522 = 163153942,
  HCU_EXPERTRWDFed_522 = 163186711,
  HCU_EXPERTAWDFed_522 = 163219480,
  HCU_EXPERTAutoFed_522 = 163252249,
  HCU_CLIMBFed_522 = 163285018,
  HCU_StopChrgnFed_522 = 163317787,
  HCU_StopChrgnModeFed_522 = 163350556,
  HCU_ChrgnFctMemSt_522 = 163383325,
  HCU_LVPowSupSYSAbn_lit_522 = 840992798,
  HCU_IMMOReleaseSignal_522 = 841025567,
  HCU_PowerCut_522 = 841058336,
  HCU_ParkingChargeReminder_522 = 841091105,
  HCU_HEV_FunctionLimit_522 = 841123874,
  HCU_SetModeNotChange_522 = 841156643,
  HCU_UTURNSnowFed_522 = 841189412,
  HCU_DriveInhibit_GearP_522 = 841222181,
  HCU_AccleAbility_Limit_522 = 841254950,
  HCU_VehicleSpeedMax_Limit_522 = 841287719,
  HCU_EngineRunClmReq_522 = 841320488,
  HCU_DisconnectChagWarn_522 = 841353257,
  HCU_PowerModeChangeInhibit_522 = 841386026,
  HCU_DisChargeStopFault_522 = 163416107,
  HCU_DedicatedModeInhibit_522 = 841418796,
  HCU_LeaveVehicleWarning_522 = 841451565,
  HCU_TowingModeEna_522 = 841484334,
  HCU_PowerLevel_522 = 163448879,
  HCU_FrntEDLSts_522 = 163481648,
  HCU_RearEDLSts_522 = 163514417,
  HCU_DriveMode_JT_522 = 163547186,
  HCU_CruiseLimitSpeedValue_522 = 271288371,
  HCU_CCOSwtFed_522 = 163579956,
  HCU_SinglePedalMemFed_522 = 841517109,
  HCU_Pwr_522 = 271321142,
  HCU_SocManageFed_522 = 163612727,
  HCU_EgyRegenLevel_522 = 163645496,
  HCU_DrvGearShiftFailureIndcn_522 = 163678265,
  HCU_EXPERTEESPSet_522 = 163711034,
  HCU_UTURNSandFed_522 = 841549883,
  HCU_CruiseControlStatus_522 = 163743804,
  HCU_LTCDispFed_522 = 163776573,
  HCU_UTURNMudFed_522 = 841582654,
  HCU_UTURNGrassFed_522 = 841615423,
  HCU_EngyFlowToDisp_522 = 163809344,
  HCU_DriveModeChangeFail_522 = 841648193,
  HCU_iCCO_Warn_522 = 163842114,
  HCU_iTAS_Warn_522 = 163874883,
  HCU_DraggingModeRemind_522 = 163907652,
  HCU_25_EDLFed_522 = 163940421,
  HCU_25_Crossaxis_Warn_522 = 163973190,
  HCU_iTAS_Active_522 = 164005959,
  HCU_ExterTempLowHint_522 = 164038728,
  HCU_25_iTAS_SlopWarn_522 = 164071497,
  HCU_iTASSceneReq_522 = 164104266,
  HCU_DrivingModReq_522 = 164137035,
  HCU_25_VehSpdOffroadRmn_522 = 841680972,
  HCU_25_LaunchMod_Warn_522 = 164169805,
  HCU_25_SetVehSpdOffroad_522 = 164202574,
  HCU_25_iDRIFT_Warn_522 = 164235343,
  VCC_2_ConfigurationMessageTrg_433 = 841713744,
  VCC_3_CRC_470 = 164268113,
  VCC_3_RollgCntr_470 = 164300882,
  VCC_3_Resd1_470 = 164333651,
  VCC_3_SN_470 = 164366420,
  TDL_SwitchSts_4AC = 164399189,
  TDL_ColourModeSts_4AC = 164431958,
  TDL_RhythmSwSts_4AC = 164464727,
  TDL_ApiluminanceSts_4AC = 164497496,
  TDL_VioceFunctionDone_4AC = 164530265,
  TDL_FlowLightSwSts_4AC = 164563034,
  TDL_FlowLightModeSts_4AC = 164595803,
  TDL_RhythmModeSts_4AC = 164628572,
  TDL_256ColourSts_4AC = 271353949,
  FuelTankLidSts_499 = 841746526,
  FuelTankLidLockFailureSts_499 = 841779295,
  FuelTankLidSensorFailureSts_499 = 841812064,
  CWC_1_CRC1_455 = 164661345,
  CWC_1_RollgCntr1_455 = 164694114,
  CWC_1_Resd1_455 = 164726883,
  CWC_ChargingSts_455 = 164759652,
  CWC_workingSts_455 = 841844837,
  CWC_Phoneforgotten_455 = 841877606,
  CWC_Phoneforgotten_ON_OFF_Sts_455 = 841910375,
  CWC_1_ChrgFlt_455 = 164792424,
  CWC_ChargingSts_R_455 = 164825193,
  CWC_workingSts_R_455 = 841943146,
  CWC_Phoneforgotten_R_455 = 841975915,
  CWC_Phoneforgotten_ON_OFF_Sts_R_455 = 842008684,
  CWC_1_ChrgFlt_R_455 = 164857965,
  TBOX_1_BookChrgActiveReq_4CF = 164890734,
  TBOX_BookChrgSts_4CF = 164923503,
  TBOX_HvBattKeepWarmActiveReq_4CF = 164956272,
  TBOX_BookChrgSetReq_4CF = 164989041,
  TBOX_StopChrgReq_4CF = 842041458,
  TBOX_KeepWarmSet_4CF = 165021811,
  TBOX_HV_Req_4CF = 165054580,
  CurrentTimeYear_0x508_508 = 165087349,
  CurrentTimeMonth_0x508_508 = 165120118,
  CurrentTimeDay_0x508_508 = 165152887,
  CurrentTimeHour_0x508_508 = 165185656,
  CurrentTimeMinute_0x508_508 = 165218425,
  CurrentTimeSecond_0x508_508 = 165251194,
  TBOX_PetmodeSwitch_427 = 165283963,
  TBOX_BkChrgStartTimeDay_47C = 165316732,
  TBOX_BkChrgStartTimeeHour_47C = 165349501,
  TBOX_BkChrgStartTimeMin_47C = 165382270,
  TBOX_BkChrgStartTimeMonth_47C = 165415039,
  TBOX_BkChrgStartTimeYear_47C = 271386752,
  TBOX_BkChrgDuration_47C = 271419521,
  TBOX_BkChrgCycleType_47C = 165447810,
  TBOX_BkChrgCycleMon_47C = 165480579,
  TBOX_BkChrgCycleTues_47C = 165513348,
  TBOX_BkChrgCycleWen_47C = 165546117,
  TBOX_BkChrgCycleThur_47C = 165578886,
  TBOX_BkChrgCycleFri_47C = 165611655,
  TBOX_BkChrgCycleSat_47C = 165644424,
  TBOX_BkChrgCycleSun_47C = 165677193,
  TBOX_KeepWarmStrt_47C = 165709962,
  TBOX_KeepWarmStrtHour_47C = 165742731,
  TBOX_KeepWarmStrtMin_47C = 165775500,
  Fusa_48A_3_48A = 402786445,
  Fusa_48A_2_48A = 842074254,
  Fusa_48A_1_48A = 165808271,
  ONEBOX_1_G_CRC_48A = 165841040,
  ONEBOX_1_G_RollgCntr_48A = 165873809,
  VehicleSpeedVSOSig_48A = 271452306,
  VehicleSpeedVSOSigValidData_48A = 842107027,
  ESPSwitchStatus_48A = 842139796,
  ABSActive_48A = 842172565,
  ABSFailSts_48A = 842205334,
  EBDFailSts_48A = 842238103,
  TCSFailSts_48A = 842270872,
  VDCFailSts_48A = 842303641,
  TCSActive_48A = 842336410,
  VDCActive_48A = 842369179,
  BLRequestController_48A = 842401948,
  FuelRollingCounter_4B2 = 165906589,
  MILSts_4B2 = 165939358,
  GPFWarning_4B2 = 165972127,
  EngineSpeed_4B2 = 271485088,
  EngineSpeedValidData_4B2 = 842434721,
  EPCSts_4B2 = 842467490,
  EMS_1_G_EngineSts_4B2 = 842500259,
  EMS_EngineSts_Actual_4B2 = 842533028,
  OilPressureWarningLamp_4B2 = 842565797,
  RHFDoorSts_2AB = 166004902,
  FRZCU_1_ParkLightSts_2AB = 842598567,
  FRWinPosn_2AB = 166037672,
  RRWinPosn_2AB = 166070441,
  RHRDoorSts_2AB = 166103210,
  ACU_3_LongitudinalAccelerationN_21 = 271517867,
  ACU_3_LongitudinalAccelerationVDD_21 = 166135980,
  EngineCoolantTemperature_278 = 166168749,
  ASU_1_ASUSysFailrSts_4C1 = 166201518,
  ASU_1_SuspCurrentLvl_4C1 = 166234287,
  ASU_1_ECASSysSts_4C1 = 166267056,
  ASU_1_ECASErrSts_4C1 = 166299825,
  ASU_1_SuspTarLvl_4C1 = 166332594,
  ASU_1_EasyEntryEna_4C1 = 842631347,
  ASU_1_CDCErrSts_4C1 = 842664116,
  ASU_1_RearaxlewithTaildoorFb_4C1 = 842696885,
  ASU_1_Overheat_warning_4C1 = 842729654,
  ASU_1_MaintainMod_4C1 = 166365367,
  ASU_1_drivemodeheightlevFb_4C1 = 166398136,
  ASU_1_HMIFailFb_4C1 = 166430905,
  ASU_1_AssistPass_4C1 = 842762426,
  BMS_1_CRC1_102 = 166463675,
  BMS_1_RollgCntr1_102 = 166496444,
  BMS_1_Resd1_102 = 166529213,
  BMS_PackVoltage_102 = 271550654,
  BMS_PackCurrent_102 = 271583423,
  BMS_HvBattSts_102 = 166561984,
  BMS_SysSelfCheck_102 = 166594753,
  BMS_HVBattThermRunaway_102 = 166627522,
  BMS_1_CRC2_102 = 166660291,
  BMS_1_RollgCntr2_102 = 166693060,
  BMS_1_Resd2_102 = 166725829,
  BMS_ChargeVoltageReq_102 = 271616198,
  BMS_ChgCurrReq_102 = 271648967,
  BMS_InfraDcUActDc_102 = 271681736,
  BMS_1_CRC3_102 = 166758601,
  BMS_1_RollgCntr3_102 = 166791370,
  BMS_1_Resd3_102 = 166824139,
  BMS_InfraDcIActDc_102 = 271714508,
  BMS_HvBattDynChrgnILim_102 = 271747277,
  BMS_HvBattUChrgnLim_102 = 271780046,
  BMS_1_CRC4_102 = 166856911,
  BMS_1_RollgCntr4_102 = 166889680,
  BMS_1_Resd4_102 = 166922449,
  BMS_DChrgAPlusSt_102 = 842795218,
  BMS_ChrgDcIAvl_102 = 271812819,
  BMS_Insulation_R_102 = 271845588,
  BMS_ChrgDcCnctrDetd_102 = 166955221,
  BMS_DCChgMode_102 = 166987990,
  BMS_ChrgDcChrgnFltStopReas_102 = 167020759,
  BMS_1_CRC5_102 = 167053528,
  BMS_1_RollgCntr5_102 = 167086297,
  BMS_1_Resd5_102 = 167119066,
  BMS_ChargeRequest_Mode_102 = 167151835,
  BMS_ChgRemTime_102 = 271878364,
  BMS_1_StateOfChargeDis_102 = 167184605,
  BMS_StateOfEnergy_102 = 271911134,
  BMS_1_CRC6_102 = 167217375,
  BMS_1_RollgCntr6_102 = 167250144,
  BMS_1_Resd6_102 = 167282913,
  BMS_StateOfHealth_102 = 167315682,
  BMS_BlanceState_102 = 842828003,
  BMS_IsolationFault_102 = 167348452,
  BMS_HVILFault_DC_102 = 842860773,
  BMS_HVILFault_FMCU_102 = 842893542,
  BMS_HVILFault_RMCU_102 = 842926311,
  BMS_SOCDis_102 = 271943912,
  BMS_InfraDcIActDcMin_102 = 271976681,
  BMS_8_CRC1_50A = 167381226,
  BMS_8_RollgCntr1_50A = 167413995,
  BMS_8_Resd1_50A = 167446764,
  BMS_HvBattCellOverU_50A = 167479533,
  BMS_HvBattCellUnderU_50A = 167512302,
  BMS_HvBattUnderU_50A = 167545071,
  BMS_HvBattDischargeOverCurrent_50A = 167577840,
  BMS_HvBattChargeOverCurrent_50A = 167610609,
  BMS_HvBattOverU_50A = 167643378,
  BMS_HvBattMismatching_50A = 842959091,
  BMS_HvBattSocJump_50A = 842991860,
  BMS_HvBattCellUUnif_50A = 843024629,
  BMS_HvBattLowSoc_50A = 843057398,
  BMS_HvBattOverSoc_50A = 843090167,
  BMS_HvBattTempUniformity_50A = 843122936,
  BMS_BattInterCANErr_50A = 843155705,
  BMS_HvBattOverCharging_50A = 843188474,
  BMS_HvBattOverTemperature_50A = 167676155,
  FMCU_SysTempOvrInd_48B = 843221244,
  LHFTireTemperature_524 = 167708925,
  RHFTireTemperature_524 = 167741694,
  LHRTireTemperature_524 = 167774463,
  LHFTirePressure_524 = 167807232,
  RHFTirePressure_524 = 167840001,
  LHRTirePressure_524 = 167872770,
  RHRTirePressure_524 = 167905539,
  RHRTireTemperature_524 = 167938308,
  TirePositionWarning_LHFTire_52C = 167971077,
  TirePositionWarning_RHFTire_52C = 168003846,
  TirePositionWarning_LHRTire_52C = 168036615,
  TirePositionWarning_RHRTire_52C = 168069384,
  TirePressureWarningLampSts_52C = 168102153,
  TirePressureSystemFailSts_52C = 843254026,
  TMS_MotorLoopErrorLevel_3DF = 168134923,
  TMS_BatteryLoopErrorLevel_3DF = 168167692,
  FrontDeforestDisplaySts_4A4 = 843286797,
  TMS_DisplayActive_4A4 = 843319566,
  TMS_ZoneSelectionDisplaySts_4A4 = 843352335,
  TMS_AutoDisplaySts_4A4 = 843385104,
  TMS_FiltertchangeDisplaySts_4A4 = 843417873,
  TMS_AUTODefrostOnSts_4A4 = 843450642,
  TMS_AUTODefrostDisplaySts_4A4 = 843483411,
  TMS_RefInSufDispSts_4A4 = 843516180,
  TMS_CooltInSufDispSts_4A4 = 843548949,
  TMS_DLOnOffSts_4A4 = 843581718,
  TMS_DROnOffSts_4A4 = 843614487,
  TMS_PLOnOffSts_4A4 = 843647256,
  WorkingSts_4A4 = 843680025,
  TMS_PROnOffSts_4A4 = 843712794,
  TMS_DCOnOffSts_4A4 = 843745563,
  TMS_ACFastCool_4A4 = 843778332,
  TMS_ACFastHeat_4A4 = 843811101,
  TMS_LoveRemind_4A4 = 843843870,
  TMS_InteCleanCar_4A4 = 843876639,
  TMS_ACVentilationSts_4A4 = 843909408,
  TMS_AutoAirCleanSts_4A4 = 843942177,
  PM25Sts_4A4 = 843974946,
  TMS_ACModeCustomSts_4A4 = 168200483,
  TMS_ACRequestDisplaySts_4A4 = 168233252,
  TMS_AUTODefLevelSts_4A4 = 168266021,
  TMS_KeepWarm_4A4 = 168298790,
  TMS_DSwingSts_4A4 = 168331559,
  TMS_PSwingSts_4A4 = 168364328,
  TMS_TuisongDispSts_4A4 = 168397097,
  TMS_SetTemperature_L_C_4A4 = 168429866,
  TMS_SetTemperature_R_C_4A4 = 168462635,
  TMS_ModeAdjustDisplaySts_4A4 = 168495404,
  TMS_CirculationModeDisplaySts_4A4 = 168528173,
  TMS_IncarTemp_4A4 = 168560942,
  TMS_FliterLife_4A4 = 168593711,
  TMS_DLSwingUpDwnWPosn_4A4 = 168626480,
  TMS_DLSwingLeRiWPosn_4A4 = 168659249,
  TMS_DRSwingUpDwnWPosn_4A4 = 168692018,
  TMS_DRSwingLeRiWPosn_4A4 = 168724787,
  TMS_PLSwingUpDwnWPosn_4A4 = 168757556,
  TMS_PLSwingLeRiWPosn_4A4 = 168790325,
  TMS_PRSwingUpDwnWPosn_4A4 = 168823094,
  TMS_PRSwingLeRiWPosn_4A4 = 168855863,
  TMS_DCSwingLeRiWPosn_4A4 = 168888632,
  AirSwingSts_4A4 = 844007737,
  TemperatureUnit_4A4 = 844040506,
  BlowAdvanceOnSts_4A4 = 844073275,
  BlowDelayOffSts_4A4 = 844106044,
  TMS_PM25_ExtSts_4A4 = 844138813,
  TMS_PM25_IncarSts_4A4 = 844171582,
  TMS_AQS_Sts_4A4 = 844204351,
  PM25overproofDisplaySts_4A4 = 844237120,
  AQSDisplaySts_4A4 = 844269889,
  PM25AutoRunSts_4A4 = 844302658,
  PM25AutoRunSetSts_4A4 = 844335427,
  FragranceWelcomeModeSts_4A4 = 844368196,
  FragranceDisplaysts_4A4 = 844400965,
  TMS_AQS_Level_4A4 = 168921414,
  ACRequestCommand_4A4 = 168954183,
  PM25_Outcar_Level_4A4 = 168986952,
  PM25_Incar_Level_4A4 = 169019721,
  SetTemperature_L_F_4A4 = 169052490,
  SetTemperature_R_F_4A4 = 169085259,
  TMS_PM25_incar_4A4 = 272009548,
  TMS_PM25_ext_4A4 = 272042317,
  TMS_KeepWarmMemoryFb_4A4 = 169118030,
  Fusa_264_5_264 = 402819407,
  Fusa_264_4_264 = 169150800,
  Fusa_264_3_264 = 169183569,
  Fusa_264_2_264 = 169216338,
  Fusa_264_1_264 = 169249107,
  EPB_R_1_CRC1_264 = 169281876,
  EPB_R_1_RollgCntr1_264 = 169314645,
  EPB_1_FltLamp_264 = 169347414,
  EPB_1_ParkLamp_264 = 169380183,
  EPB_1_ActrSt_264 = 169412952,
  EPB_1_RWUSt_264 = 844433753,
  EPB_1_TextDisp_264 = 169445722,
  IPB_EPBErrorStatus_264 = 844466523,
  AVHWarningMessage_4A9 = 844499292,
  CDPActive_4A9 = 844532061,
  HDCFailSts_4A9 = 844564830,
  HDCCtrlSts_4A9 = 169478495,
  AVHSts_4A9 = 169511264,
  AVHFailSts_4A9 = 844597601,
  FWAWarningSts_4A9 = 169544034,
  BrakeFluidSts_4A9 = 169576803,
  BrakesystemFailSts_4A9 = 844630372,
  CST_Status_4AD = 169609573,
  BrakepedalFeelSts_4AD = 169642342,
  CST_SensitivitySts_4AD = 169675111,
  EBS_SOC_4B0 = 169707880,
  DriverSeatMsgStr_LvlSts_425 = 169740649,
  DriverSeatMsg_ModeSts_425 = 169773418,
  LeftSeatMsgStr_LvlSts_429 = 169806187,
  LeftSeatMsg_ModeSts_429 = 169838956,
  PassSeatMsgStr_LvlSts_42D = 169871725,
  PassSeatMsg_ModeSts_42D = 169904494,
  RightSeatMsgStr_LvlSts_43F = 169937263,
  RightSeatMsg_ModeSts_43F = 169970032,
  SRF_OperateSts_4A5 = 170002801,
  SRF_PositionSts_4A5 = 170035570,
  SRF_OverHeatProtect_4A5 = 844663155,
  SRF_Sunshade_OperateSts_4A5 = 170068340,
  Sunshade_OverHeatProtect_4A5 = 844695925,
  SRF_Sunroof_ActualSts_4A8 = 170101110,
  SRF_Sunshade_ActualSts_4A8 = 170133879,
  SRF_Sunshade_PositionSts_4A8 = 170166648,
  OBC_CC_ConnectSts_477 = 170199417,
  CDU_MILSts_477 = 844728698,
  BMS_BattFaultLampSts_50F = 844761467,
  BMS_OverTemp_LightSts_50F = 844794236,
  BMS_RemainChg_Time_50F = 272075133,
  BMS_ThermalRunawayDis_50F = 844827006,
  BMS_TBOXIsolationFault_50F = 170232191,
  BMS_PreWarmDis_50F = 170264960,
  BMS_HvBattSoeActDisChaCyc_5FB = 272107905,
  BMS_HvBattSoeActReChaCyc_5FB = 272140674,
  BMS_HvBattRatedEnergy_5FB = 272173443,
  BMS_HvBattRatedU_5FB = 272206212,
  BMS_HvBattTypeCode_5FB = 170297733,
  BMS_HvBattManufcCode_5FB = 402852230,
  BMS_HvBattTempSensorTotalNum_5FB = 272238983,
  BMS_HvBattTempSensorTempMaxNum_5FB = 170330504,
  BMS_HvBattTempSensorTempMinNum_5FB = 170363273,
  BMS_HvBattTempMaxNum_5FB = 170396042,
  BMS_HvBattTempMinNum_5FB = 170428811,
  BMS_BattCellTotalNum_5FB = 272271756,
  BMS_HvBattVoltMaxNum_5FB = 170461581,
  BMS_HvBattVoltMinNum_5FB = 170494350,
  BMS_ActualSOCMin_5FB = 272304527,
  BMS_ActualSOCMax_5FB = 272337296,
  BMS_HvBattCellVoltMax_Num_4C8 = 170527121,
  BMS_HvBattCellVoltMax_4C8 = 272370066,
  BMS_HvBattCellVoltMin_Num_4C8 = 170559891,
  BMS_HvBattCellVoltMin_4C8 = 272402836,
  BMS_Chg_Sts_0x4c8_4C8 = 170592661,
  EPS_2_Steer_ReturnRmd_Sts_4D3 = 170625430,
  EPS_2_Steer_ReturnRmd_4D3 = 170658199,
  EPS_2_MFS_ShakeSts_4D3 = 170690968,
  DWD_TextDisp_532 = 170723737,
  DWD_SoundIndcn_532 = 170756506,
  LSensorFailSts_532 = 844859803,
  RSensorFailSts_532 = 844892572,
  DWD_Distance_532 = 170789277,
  DWD_Switch_532 = 170822046,
  RADAR_1_CRC1_530 = 170854815,
  RADAR_1_RollgCntr1_530 = 170887584,
  RADAR_1_RadarDetectSts_530 = 170920353,
  RADAR_1_RadarWorkSts_530 = 844925346,
  RADAR_1_Resd1_530 = 844958115,
  RADAR_1_LHRRadarSensorDistance_530 = 170953124,
  RADAR_1_RHMRRadarSensorDistance_530 = 170985893,
  RADAR_1_LHMRRadarSensorDistance_530 = 171018662,
  RADAR_1_RHRRadarSensorDistance_530 = 171051431,
  RADAR_1_LHFRadarSensorDistance_530 = 171084200,
  RADAR_1_RHFRadarSensorDistance_530 = 171116969,
  RADAR_1_ParkingRadarSwSts_530 = 844990890,
  RADAR_1_RHMFRadarSensorDistance_530 = 171149739,
  RADAR_1_LHMFRadarSensorDistance_530 = 171182508,
  RADAR_1_AudibleBeepRate_530 = 171215277,
  EMS_EngineSpeed_Actual_103 = 272435630,
  FRZCU_FRSeatHeiFb_4D2 = 171248047,
  FRZCU_FRSeatLvlFb_4D2 = 171280816,
  FRZCU_FRSeatBackAgFb_4D2 = 171313585,
  FRZCU_FRSeatLegSpprtFb_4D2 = 171346354,
  FRZCU_FRWinSts_4D2 = 171379123,
  FRZCU_RRWinSts_4D2 = 171411892,
  FRZCU_FRSeatLegRestAngleFb_4D2 = 171444661,
  FRZCU_FRSeatCushFb_4D2 = 171477430,
  FRZCU_FRSeatNeedMemory_4D2 = 171510199,
  FRZCU_FRMemoryFb_4D2 = 171542968,
  FRZCU_FRRecoverFb_4D2 = 171575737,
  FRZCU_EasyEntryExitFb_4D2 = 171608506,
  FRZCU_EnjoyableSeatWarning_4D2 = 171641275,
  FRZCU_EnjoyableSeatSts_4D2 = 171674044,
  SeatHeatLevelsFR_4D2 = 171706813,
  SeatVentLevelsFR_4D2 = 171739582,
  SeatHeatLevelsRR_4D2 = 171772351,
  SeatVentLevelsRR_4D2 = 171805120,
  FRZCU_FRSeatFoldFb_4D2 = 171837889,
  MFSR_UP_SW_205 = 171870658,
  MFSR_DW_SW_205 = 171903427,
  MFSR_Left_SW_205 = 171936196,
  MFSR_Right_SW_205 = 171968965,
  MFSR_OK_SW_205 = 172001734,
  MFSR_Voice_SW_205 = 172034503,
  MFSR_Customize_205 = 172067272,
  TMS_AirPurgeReminderSts_448 = 172100041,
  TMS_BT_Reduce_Wind_SpeedSts_448 = 172132810,
  TMS_First_BlowingSts_448 = 172165579,
  TMS_CirculationInTunnelsSts_448 = 172198348,
  TMS_crosscountry_coolingSts_448 = 172231117,
  TMS_ParkingAirConditioningStatus_448 = 172263886,
  TMS_CoolantFillSts_448 = 172296655,
  TMS_UVC_ControlSts_448 = 172329424,
  TMS_UVC_LuminanceSts_448 = 172362193,
  TMS_ICC_Lightoff_Sts_448 = 172394962,
  TMS_ICC_Memoff_Sts_448 = 172427731,
  TMS_keepwarmSet_TemperatureSts_448 = 172460500,
  TMS_CFSSwitchFb_4D8 = 172493269,
  TDU_CFSPosOneType_4D8 = 172526038,
  TDU_CFSPosTwoType_4D8 = 172558807,
  TDU_CFSPosThreeType_4D8 = 172591576,
  TDU_CFSRunOut_4D8 = 172624345,
  TMS_CFSShortWarn_4D8 = 172657114,
  TMS_CFSLevelSetFb_4D8 = 172689883,
  TMS_CFSPosSetFb_4D8 = 172722652,
  TMS_CFSPosOneLife_4D8 = 172755421,
  TMS_CFSPosTwoLife_4D8 = 172788190,
  TMS_CFSPosThreeLife_4D8 = 172820959,
  TMS_PM25SwitchFb_4D8 = 172853728,
  TMS_ThrModeStsFb_4D8 = 172886497,
  TMS_ThrSetTempFb_C_4D8 = 172919266,
  TMS_ThrCLMSwitchFb_4D8 = 172952035,
  TMS_ThrAutoSwitchFb_4D8 = 172984804,
  TMS_ThrBlowSpeedLevelKeyStsFb_4D8 = 173017573,
  FRZCU_HoodSts_27E = 173050342,
  FRZCU_AuthenResult_27E = 845023719,
  FRZCU_PowerMode_27E = 173083112,
  FRZCU_TrunkSts_27E = 173115881,
  FRZCU_HandleSwitchStsFL_27E = 173148650,
  FRZCU_HandleSwitchStsFR_27E = 173181419,
  FRZCU_HandleSwitchStsRR_27E = 173214188,
  FRZCU_HandleSwitchStsRL_27E = 173246957,
  SeatLength_target_Passenger_27E = 173279726,
  SeatBackrest_target_Passenger_27E = 173312495,
  FRZCU_TrunkLockSts_27E = 173345264,
  FRZCU_HoodLockSts_27E = 173378033,
  FrontBackSpeedAdj_Cmd_27E = 173410802,
  FRZCU_GloveboxSW_27E = 845056499,
  FRZCU_FRReleaseLockSts_27E = 173443572,
  FRZCU_RRReleaseLockSts_27E = 173476341,
  FRZCU_PELockInKeyWarning_27E = 845089270,
  FRZCU_PowerOFFWarning_27E = 845122039,
  FRZCU_KeylessWarning_27E = 845154808,
  FRZCU_OTAPwrDwnReqFb_27E = 173509113,
  FRZCU_OTAPwrOnReqFb_27E = 173541882,
  PEPS_InformationSource_3B5 = 173574651,
  PEPS_OrderInformation_3B5 = 173607420,
  PEPS_IDInformation_3B5 = 173640189,
  PEPS_KeySts_3B5 = 173672958,
  PEPS_ETRUnLockReq_3B5 = 173705727,
  PEPS_WelcomeON_404 = 173738496,
  PEPS_VehicleSearchReq_404 = 173771265,
  PEPS_SATOReminder_404 = 173804034,
  PEPS_PLGUnlockReq_404 = 173836803,
  PEPS_RKETrunkSts_404 = 173869572,
  PEPS_SSBInhibitCWCSts_404 = 173902341,
  PEPS_WirelessChargingCtrSts_404 = 173935110,
  PEPS_EmgyShutdown_404 = 173967879,
  PEPS_SSBInputFailure_404 = 174000648,
  PEPS_SmartSystemWarning1_1_404 = 174033417,
  PEPS_SmartSystemWarning2_2_404 = 174066186,
  PEPS_SmartSystemWarning3_1_404 = 174098955,
  PEPS_SmartSystemWarning3_2_404 = 174131724,
  PEPS_SmartSystemWarning4_2_404 = 174164493,
  PEPS_SmartSystemWarning4_3_404 = 174197262,
  PEPS_SystemWarning_404 = 174230031,
  PEPS_BLTKeyPESts_404 = 174262800,
  BMS_2_G_CRC_50B = 174295569,
  BMS_2_G_RollgCntr_50B = 174328338,
  BMS_2_G_Resd_50B = 174361107,
  BMS_TBOXInsulation_R_50B = 272468500,
  BMS_TBOXErrorNumber_50B = 174393877,
  BMS_TBOXHvBattMaxT_50B = 174426646,
  BMS_TBOXHvBattMinT_50B = 174459415,
  BMS_SOCLight_50B = 174492184,
  RLCR_2_CRC1_450 = 174524953,
  RLCR_2_RollgCntr1_450 = 174557722,
  RLCR_2_Resd1_450 = 174590491,
  RLCR_2_RCWWarn_450 = 174623260,
  RLCR_2_RCTAWarn_450 = 845187613,
  RLCR_2_BSDLCWONOFFSts_450 = 174656030,
  RLCR_2_DOWONOFFSts_450 = 174688799,
  RLCR_2_RCWONOFFSts_450 = 174721568,
  RLCR_2_RCTARCTBONOFFSts_450 = 174754337,
  RRCR_2_CRC1_4F7 = 174787106,
  RRCR_2_RollgCntr1_4F7 = 174819875,
  RRCR_2_Resd1_4F7 = 174852644,
  RRCR_2_RCWWarn_4F7 = 174885413,
  RRCR_2_RCTAWarn_4F7 = 845220390,
  RRCR_2_BSDLCWONOFFSts_4F7 = 174918183,
  RRCR_2_DOWONOFFSts_4F7 = 174950952,
  RRCR_2_RCWONOFFSts_4F7 = 174983721,
  RRCR_2_RCTARCTBONOFFSts_4F7 = 175016490,
  ONEBOX_4_LHFWheelSpeedRPM_24B = 272501291,
  ONEBOX_4_RHFWheelSpeedRPM_24B = 272534060,
  LHRWheelSpeedRPM_24B = 272566829,
  RHRWheelSpeedRPM_24B = 272599598,
  LHRPulseCounterFailSts_24B = 845253167,
  LHRPulseCounter_24B = 272632368,
  RHRPulseCounterFailSts_24B = 845285937,
  RHRPulseCounter_24B = 272665138,
  RHFPulseCounterFailSts_24B = 845318707,
  RHFPulseCounter_24B = 272697908,
  LHFPulseCounterFailSts_24B = 845351477,
  LHFPulseCounter_24B = 272730678,
  LHFWheelDriveDirection_24B = 175049271,
  RHFWheelDriveDirection_24B = 175082040,
  LHRWheelDriveDirection_24B = 175114809,
  RHRWheelDriveDirection_24B = 175147578,
  FMCU_ErrLvlDis_53A = 175180347,
  FMCU_SpdAct_EDR_53A = 272763452,
  ISGF_ErrLvl_309 = 175213117,
  RMCU_ErrLvlDis_57B = 175245886,
  RMCU_Speed_EDR_57B = 272796223,
  BMS_PackVoltageDis_513 = 272828992,
  BMS_PackCurrentDis_513 = 272861761,
  ISGF_SysTempOvrInd_44B = 845384258,
  TCU_23_GBFaultstatus_46A = 175278659,
  TCU_23_TeachInState_46A = 175311428,
  ACU_1_AirBagFailSts_49C = 175344197,
  ACU_1_ThdRowLBeltWarning_49C = 845417030,
  ACU_1_ThdRowRBeltWarning_49C = 845449799,
  ACU_1_PsngrSeatBeltWarning_49C = 175376968,
  ACU_1_SecRowLBeltWarning_49C = 845482569,
  ACU_1_SecRowMBeltWarning_49C = 845515338,
  ACU_1_SecRowRBeltWarning_49C = 845548107,
  ACU_1_PsngrSeatOccupiedSts_49C = 175409740,
  BMS_44_PackPowerRealTime_53D = 272894541,
  BMS_Chg_Sts_0x32b_32B = 175442510,
  LISDShowCMDSts_4F5 = 845580879,
  LISDShowModeSts_4F5 = 175475280,
  LISD_TransmitStatus_4F5 = 175508049,
  LISD_DisplayStatus_4F5 = 175540818,
  LISD_ParkingShowModSts_4F5 = 175573587,
  LISD_ParkingShowCMDSts_4F5 = 175606356,
  RISDShowCMDSts_4D1 = 845613653,
  RISDShowModeSts_4D1 = 175639126,
  RISD_TransmitStatus_4D1 = 175671895,
  RISD_DisplayStatus_4D1 = 175704664,
  RISD_ParkingShowModSts_4D1 = 175737433,
  RISD_ParkingShowCMDSts_4D1 = 175770202,
  LC_DIYMusicShowModSts_491 = 175802971,
  LC_RandomMusicShowSts_491 = 175835740,
  LC_LiShowModSts_491 = 175868509,
  ILCF_1_LogoParkingSts_593 = 175901278,
  ILCF_1_LogoChargingSts_593 = 175934047,
  ILCF_1_DIYMusicShowModSts_593 = 175966816,
  ILCF_1_RandomMusicShowSts_593 = 175999585,
  ILCF_1_LogoColorAdjSts_593 = 272927330,
  ADS_5_CRC1_335 = 176032355,
  ADS_5_RollgCntr1_335 = 176065124,
  ADS_5_Resd1_335 = 176097893,
  ADS_5_ILOASts_335 = 176130662,
  ADS_5_ICAQuitReason_335 = 176163431,
  ADS_5_ISLISt_335 = 176196200,
  ADS_5_ILOATextInfo_335 = 176228969,
  ADS_5_ILOAActdirection_335 = 176261738,
  ADS_5_LKS_warning_335 = 845646443,
  ADS_5_ISLIOverSpeedWarn_335 = 176294508,
  ADS_5_ISLISpeedLimitSign_335 = 176327277,
  ADS_5_EgoLeftLineHeatgAg_335 = 272960110,
  ADS_5_EgoLeLineID_335 = 845679215,
  ADS_5_EgoLeLineColor_335 = 176360048,
  ADS_5_EgoLeLineTyp_335 = 176392817,
  ADS_5_EgoLeLineHozlDst_335 = 272992882,
  ADS_5_CRC2_335 = 176425587,
  ADS_5_RollgCntr2_335 = 176458356,
  ADS_5_Resd2_335 = 176491125,
  ADS_5_NeborLeLineID_335 = 845711990,
  ADS_5_NeborLeLineColor_335 = 176523895,
  ADS_5_NeborLeLineTyp_335 = 176556664,
  ADS_5_EgoLeLineCrvt_335 = 273025657,
  ADS_5_NeborLeLineHozlDst_335 = 273058426,
  ADS_5_NeborRiLineID_335 = 845744763,
  ADS_5_NeborRiLineColor_335 = 176589436,
  ADS_5_NeborRiLineTyp_335 = 176622205,
  ADS_5_NeborLeLineCrvt_335 = 273091198,
  ADS_5_NeborRiLineHozlDst_335 = 273123967,
  ADS_5_CRC3_335 = 176654976,
  ADS_5_RollgCntr3_335 = 176687745,
  ADS_5_Resd3_335 = 176720514,
  ADS_5_ELKSettingSt_335 = 176753283,
  ADS_5_LDWLDPSnvtySet_335 = 176786052,
  ADS_5_LDWWarnType_335 = 176818821,
  ADS_5_NeborRiLineCrvt_335 = 273156742,
  ADS_5_IESSwtSet_335 = 176851591,
  ADS_5_IHCSettingSt_335 = 176884360,
  ADS_5_ISLIWarningMod_335 = 176917129,
  ADS_5_ICAEnableBtnSts_335 = 176949898,
  ADS_5_FCM_Camera_textinfo_335 = 176982667,
  ADS_5_EgoRiLineID_335 = 177015436,
  ADS_5_EgoRiLineTyp_335 = 177048205,
  ADS_5_EgoRiLineColor_335 = 177080974,
  ADS_5_EgoRiLineHozlDst_335 = 273189519,
  ADS_5_CRC4_335 = 177113744,
  ADS_5_RollgCntr4_335 = 177146513,
  ADS_5_Resd4_335 = 177179282,
  ADS_5_EgoRightLineHeatgAg_335 = 273222291,
  ADS_5_EgoRiLineCrvt_335 = 273255060,
  ADS_5_NeborLeftLineHeatgAg_335 = 273287829,
  ADS_5_NeborRightLineHeatgAg_335 = 273320598,
  ADS_4_CRC1_333 = 177212055,
  ADS_4_RollgCntr1_333 = 177244824,
  ADS_4_Resd1_333 = 177277593,
  ADS_4_AEBStatus_333 = 177310362,
  ADS_4_ACCSts_333 = 177343131,
  ADS_4_TimeGapSet_333 = 177375900,
  ADS_4_FcwMode_333 = 177408669,
  ADS_4_AEBReqType_333 = 177441438,
  ADS_4_SetSpd_333 = 177474207,
  ADS_4_DAIStatus_333 = 177506976,
  ADS_4_ACCTextMessage_333 = 177539745,
  ADS_4_LongiTakeOverReq_333 = 845777570,
  ADS_4_SetSpdUnit_333 = 845810339,
  ADS_4_GoNotifier_333 = 177572516,
  ADS_4_DAISts_333 = 177605285,
  ADS_4_Radar_textinfo_333 = 177638054,
  ADS_4_SCF_PopoverReq_333 = 845843111,
  ADS_4_DrvrCfmSCFDispFb_333 = 845875880,
  ADS_4_SCF_SpdLimUnit_333 = 177670825,
  ADS_4_SCF_SpdLimSts_333 = 177703594,
  ADS_4_CRC2_333 = 177736363,
  ADS_4_RollgCntr2_333 = 177769132,
  ADS_4_Resd2_333 = 177801901,
  ADS_4_ACCObjTyp_333 = 177834670,
  ADS_4_FrntFarObjTyp_333 = 177867439,
  ADS_4_ACCObjLgtDstX_333 = 177900208,
  ADS_4_FrntFarObjID_333 = 845908657,
  ADS_4_Resd_333 = 845941426,
  ADS_4_ACCObjID_333 = 845974195,
  ADS_4_ACCObjHozDstY_333 = 273353396,
  ADS_4_FrntFarObjHozDstY_333 = 273386165,
  ADS_4_FrntFarObjLgtDstX_333 = 177932982,
  ADS_4_CRC3_333 = 177965751,
  ADS_4_RollgCntr3_333 = 177998520,
  ADS_4_Resd3_333 = 178031289,
  ADS_4_Resd_1_333 = 846006970,
  ADS_4_LeObjID_333 = 846039739,
  ADS_4_LeObjTyp_333 = 178064060,
  ADS_4_LeObjHozDstY_333 = 273418941,
  ADS_4_LeObjLgtDstX_333 = 178096830,
  ADS_4_RiObjID_333 = 846072511,
  ADS_4_RiObjTyp_333 = 178129600,
  ADS_4_RiObjHozDstY_333 = 273451713,
  ADS_4_RiObjLgtDstX_333 = 178162370,
  ADS_4_CRC4_333 = 178195139,
  ADS_4_RollgCntr4_333 = 178227908,
  ADS_4_Resd4_333 = 178260677,
  ADS_4_SCFSwtSet_333 = 178293446,
  ADS_4_AEBSettingSt_333 = 178326215,
  ADS_4_FCWSettingSt_333 = 178358984,
  ADS_4_DAISwtSet_333 = 178391753,
  ADS_4_CruiseAccelerateSts_333 = 178424522,
  ExternalTemperature_F_494 = 178457291,
  ExternalTemperature_C_494 = 178490060,
  BlowSpeedLevelDisplaySts_494 = 178522829,
  CCP_1_FLTempSwitchReq_47B = 178555598,
  CCP_1_FrontAutoACSwitchReq_47B = 846105295,
  CCP_1_FrontOFFSwitchReq_47B = 846138064,
  CCP_1_FBlowSpdCtrlSwitchReq_47B = 178588369,
  FLCCP_MainSetHeatReq_47B = 178621138,
  FLCCP_MainSetVentilationReq_47B = 178653907,
  TMS_PM25_Detect_453 = 846170836,
  TMS_CCP_FLTempSts_453 = 178686677,
  TMS_CCP_FrontAutoACSts_453 = 846203606,
  TMS_CCP_ACStatus_453 = 846236375,
  TMS_CCP_FrontOFFSts_453 = 846269144,
  TMS_CCP_FBlowSpdCtrlSts_453 = 178719449,
  TMS_CCP_FRTempSts_453 = 178752218,
  TMS_CCP_RecyMode_453 = 178784987,
  TMS_CCP_FrontDefrostSts_453 = 846301916,
  TMS_UVC_WorkingBfSts_453 = 178817757,
  TMS_UVC_LuminanceBfSts_453 = 178850526,
  TMS_CCP_ACFastCool_453 = 178883295,
  TMS_CCP_ACFastHeat_453 = 178916064,
  CCP_2_FRTempSwitchReq_497 = 178948833,
  CCP_2_RecyModeReq_497 = 178981602,
  CCP_2_DefrostModeSwitchReq_497 = 846334691,
  CCP_2_RearDefrostSwitchReq_497 = 846367460,
  CCP_2_FrontBlowSpdCtrlSwitchReq_497 = 179014373,
  FRCCP_DeputySetHeatReq_497 = 179047142,
  FRCCP_DeputySetVentilationReq_497 = 179079911,
  CCP_2_ACSwitchReq_497 = 846400232,
  CHB_App_Sts_50C = 179112681,
  CHB_AppMem_Sts_50C = 179145450,
  CHB_AppDelay_Sts_50C = 179178219,
  CHB_AppTimeset_Sts_50C = 179210988,
  CHB_AppCoolorheat_Sts_50C = 179243757,
  CHB_AppCoolset_Sts_50C = 179276526,
  CHB_AppHeatset_Sts_50C = 179309295,
  CHB_AppSterilization_Sts_50C = 179342064,
  CHB_AppItemsLeft_Sts_50C = 179374833,
  CHB_AppItemsLeftset_Sts_50C = 179407602,
  CHB_AppChbDoor_Sts_50C = 179440371,
  FuelTankLidSystemFailureSts_4D9 = 846433012,
  FuelTankPressureConditionReached_4D9 = 179473141,
  ACU_2_LateralAccelerationN_31 = 273484534,
  ACU_2_YawRateE_31 = 273517303,
  ACU_2_YawrateSigValidDataA_31 = 179505912,
  ACU_2_LateralAccelerationSigVDD_31 = 179538681,
  CSunshadeSts_400 = 179571450,
  CSunshadePercentSts_400 = 179604219,
  FRZCU_SeatHeatLvlFR2_400 = 179636988,
  FRZCU_SeatVentLvlFR2_400 = 179669757,
  FRZCU_SeatVentLvlRR2_400 = 179702526,
  FRZCU_SeatHeatLvlRR2_400 = 179735295,
  FRZCU_ContainerlightSts_400 = 179768064,
  FRZCU_FRWinThermalSts_400 = 846465793,
  FRZCU_RRWinThermalSts_400 = 846498562,
  FRZCU_CSunshadeThermalSts_400 = 846531331,
  OBC_ElectronicLockSts_49E = 179800836,
  OBC_WorkingMode_49E = 179833605,
  OBC_OutDischgCurr_49E = 273550086,
  OBC_OutDischgVolt_49E = 273582855,
  EPS_1_CRC_122 = 179866376,
  EPS_1_RollgCntr_122 = 179899145,
  EPSFailSts_122 = 179931914,
  EPSSteeringAngleCalibrationSts_122 = 846564107,
  EPS_EOTLearning_Sts_122 = 846596876,
  MFSL_UP_SW_201 = 179964685,
  MFSL_DW_SW_201 = 179997454,
  MFSL_Left_SW_201 = 180030223,
  MFSL_Right_SW_201 = 180062992,
  MFSL_OK_SW_201 = 180095761,
  MFSL_AVM_SW_201 = 180128530,
  MFSL_Customize_201 = 180161299,
  TBOX_PetmodeSetTemperature_480 = 180194068,
  PPMID_1_PPMICounter_4B8 = 846629653,
  PPMID_1_PPMISt_4B8 = 180226838,
  PPMID_1_HWSts_4B8 = 180259607,
  PPMID_1_BSRBPASts_4B8 = 180292376,
  ADS_3_ICAStatus_31A = 180325145,
  ADS_3_ICATextinfo_31A = 180357914,
  ADS_3_ACCSts_31A = 180390683,
  ADS_3_DriverHandsoffWarning_31A = 180423452,
  ADS_3_LKSMod_31A = 180456221,
  ADS_3_LKSSts_31A = 180488990,
  ADS_3_ELKSts_31A = 180521759,
  ADS_3_LKSLeftTrackingSt_31A = 180554528,
  ADS_3_LKSRightTrackingSt_31A = 180587297,
  ADS_3_ELKLeftActiveSt_31A = 180620066,
  ADS_3_ELKRightActiveSt_31A = 180652835,
  ASU_3_horizontalSts_483 = 180685604,
  ASU_3_horizontalmodeFb_483 = 180718373,
  OBC_DischgSts_48C = 180751142,
  ATCM_DriveModeSw_315 = 180783911,
  ATCM_XModeSw_315 = 180816680,
  ATCM_SwitchUTurnModeSet_315 = 846662441,
  BNCM_16_CRC1_4BE = 180849450,
  BNCM_16_RollgCntr1_4BE = 180882219,
  BNCM_16_Resd1_4BE = 180914988,
  BNCM_16_CMD_4BE = 273615661,
  BNCM_16_PDU1_4BE = 537103150,
  BNCM_16_PDU2_4BE = 537135919,
  BNCM_16_PDU3_4BE = 537168688,
  BNCM_16_PDU4_4BE = 402885425,
  RLCR_1_CRC1_447 = 180947762,
  RLCR_1_RollgCntr1_447 = 180980531,
  RLCR_1_Resd1_447 = 181013300,
  RLCR_1_SysSt_447 = 181046069,
  RLCR_1_BlkSts_447 = 846695222,
  RLCR_1_BSDWarn_447 = 181078839,
  RLCR_1_DOWWarn_447 = 181111608,
  RLCR_1_DowLock_447 = 181144377,
  RLCR_1_ELK_Collision_flag_447 = 181177146,
  RLCR_1_ELK_CollisionFlagValid_447 = 181209915,
  RRCR_1_CRC1_4F3 = 181242684,
  RRCR_1_RollgCntr1_4F3 = 181275453,
  RRCR_1_Resd1_4F3 = 181308222,
  RRCR_1_SysSt_4F3 = 181340991,
  RRCR_1_BlkSts_4F3 = 846728000,
  RRCR_1_BSDWarn_4F3 = 181373761,
  RRCR_1_DOWWarn_4F3 = 181406530,
  RRCR_1_DowLock_4F3 = 181439299,
  RRCR_1_ELK_Collision_flag_4F3 = 181472068,
  RRCR_1_ELK_CollisionFlagValid_4F3 = 181504837,
  ADS_2_AEBStatus_314 = 181537606,
  ADS_2_FCWStatus_314 = 181570375,
  FCM_2_IHCfunctionSts_314 = 181603144,
  LHTurnlightSts_0x23b_23B = 846760777,
  RHTurnlightSts_0x23b_23B = 846793546,
  LHFdoorSts_0x23b_23B = 181635915,
  LHFDoorLockSts_23B = 181668684,
  LHTurnSW_23B = 846826317,
  RHTurnSW_23B = 846859086,
  HazardLightSW_23B = 846891855,
  LHFSeatBeltSW_0x23b_23B = 846924624,
  DirectionIndLeft_23B = 846957393,
  DirectionIndRight_23B = 846990162,
  LHRdoorSts_2BB = 181701459,
  ChrgHoodSts_313 = 847022932,
  FRZCU_FRSitPosnSts_313 = 181734229,
  ADS_6_CRC1_20C = 181766998,
  ADS_6_RollgCntr1_20C = 181799767,
  ADS_6_Resd1_20C = 181832536,
  ADS_6_AVM_FuncSts_20C = 181865305,
  ADS_6_AVM_ButtonClick_20C = 181898074,
  ADS_6_Customize_buttonsFB_20C = 181930843,
  ADS_6_PAS_AudioTone_20C = 181963612,
  ONEBOX_7_BSW_Active_23C = 181996381,
  ONEBOX_7_iCCO_TgtVel_23C = 182029150,
  VCU_3_G_ActWheelTorqueFA_4BC = 273648479,
  VCU_3_G_ActWheelTorqueRA_4BC = 273681248,
  ADCC_SysReadySts_30F = 847055713,
  IMMO_Chall_ICC_456 = 537201506,
  IMMO_Teach_VCC_47D = 537234275,
  ADS_RCTAWarn_Left_40D = 847088484,
  ADS_RCTAWarn_Rigth_40D = 847121253,
  ADS_FCTAWarn_Left_40D = 847154022,
  ADS_FCTAWarn_Right_40D = 847186791,
  CSA_3_CRC1_3AF = 182061928,
  CSA_3_RollgCntr1_3AF = 182094697,
  CSA_3_Resd1_3AF = 182127466,
  FlwheelBearing_3AF = 273714027,
  FrwheelBearing_3AF = 273746796,
  BlwheelBearing_3AF = 273779565,
  BrwheelBearing_3AF = 273812334,
  CSA_3_CRC2_3AF = 182160239,
  CSA_3_RollgCntr2_3AF = 182193008,
  VehicleBearing_kg_3AF = 273845105,
  VehicleBearing_lb_3AF = 273877874,
  GoodsWeight_kg_3AF = 273910643,
  Goods_Weight_Ratio_3AF = 273943412,
  CSA_3_CRC3_3AF = 182225781,
  CSA_3_RollgCntr3_3AF = 182258550,
  CSA_3_Resd3_3AF = 182291319,
  GoodsWeight_lb_3AF = 273976184,
  OverWeightValue_kg_3AF = 274008953,
  OverWeightValue_lb_3AF = 274041722,
  OverWeightRatio_3AF = 182324091,
  OverWeightWarning_3AF = 847219580,
  OverLoadWarnShieldSetFed_3AF = 182356861,
  RLHS_2_AmbBrightness_460 = 274074494,
  RLHS_LiSwithReason_460 = 182389631,
  ASU_6_HMIFailFb_JT_352 = 182422400,
  TCM_1_TrailerSts_4AF = 847252353,
  SID_CLUSTER = 671091586,
  SID_OTA = 671091587,
  SID_LOG = 671088649,
  SID_DIAG = 671088650,
  SID_DUMMY = 2147483647
};
bool SignalIdSoc_IsValid(int value);
constexpr SignalIdSoc SignalIdSoc_MIN = DVR_1_DVRFailReasons_541;
constexpr SignalIdSoc SignalIdSoc_MAX = SID_DUMMY;
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SignalIdSoc_descriptor();
template<typename T>
inline const std::string& SignalIdSoc_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SignalIdSoc>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SignalIdSoc_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SignalIdSoc_descriptor(), enum_t_value);
}
inline bool SignalIdSoc_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SignalIdSoc* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SignalIdSoc>(
    SignalIdSoc_descriptor(), name, value);
}
enum GroupedSignalIdSoc : int {
  GSID_DUMMY = 2147483647
};
bool GroupedSignalIdSoc_IsValid(int value);
constexpr GroupedSignalIdSoc GroupedSignalIdSoc_MIN = GSID_DUMMY;
constexpr GroupedSignalIdSoc GroupedSignalIdSoc_MAX = GSID_DUMMY;
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* GroupedSignalIdSoc_descriptor();
template<typename T>
inline const std::string& GroupedSignalIdSoc_Name(T enum_t_value) {
  static_assert(::std::is_same<T, GroupedSignalIdSoc>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function GroupedSignalIdSoc_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    GroupedSignalIdSoc_descriptor(), enum_t_value);
}
inline bool GroupedSignalIdSoc_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, GroupedSignalIdSoc* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<GroupedSignalIdSoc>(
    GroupedSignalIdSoc_descriptor(), name, value);
}
// ===================================================================

class MsgGroupIdSoc final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:autolink.soc.platform.vehicle.pb.MsgGroupIdSoc) */ {
 public:
  inline MsgGroupIdSoc() : MsgGroupIdSoc(nullptr) {}
  ~MsgGroupIdSoc() override;
  explicit constexpr MsgGroupIdSoc(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MsgGroupIdSoc(const MsgGroupIdSoc& from);
  MsgGroupIdSoc(MsgGroupIdSoc&& from) noexcept
    : MsgGroupIdSoc() {
    *this = ::std::move(from);
  }

  inline MsgGroupIdSoc& operator=(const MsgGroupIdSoc& from) {
    CopyFrom(from);
    return *this;
  }
  inline MsgGroupIdSoc& operator=(MsgGroupIdSoc&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MsgGroupIdSoc& default_instance() {
    return *internal_default_instance();
  }
  static inline const MsgGroupIdSoc* internal_default_instance() {
    return reinterpret_cast<const MsgGroupIdSoc*>(
               &_MsgGroupIdSoc_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(MsgGroupIdSoc& a, MsgGroupIdSoc& b) {
    a.Swap(&b);
  }
  inline void Swap(MsgGroupIdSoc* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MsgGroupIdSoc* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MsgGroupIdSoc* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MsgGroupIdSoc>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MsgGroupIdSoc& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MsgGroupIdSoc& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MsgGroupIdSoc* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.soc.platform.vehicle.pb.MsgGroupIdSoc";
  }
  protected:
  explicit MsgGroupIdSoc(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGroupIdFieldNumber = 1,
  };
  // required .autolink.soc.platform.vehicle.pb.GroupIdSoc group_id = 1;
  bool has_group_id() const;
  private:
  bool _internal_has_group_id() const;
  public:
  void clear_group_id();
  ::autolink::soc::platform::vehicle::pb::GroupIdSoc group_id() const;
  void set_group_id(::autolink::soc::platform::vehicle::pb::GroupIdSoc value);
  private:
  ::autolink::soc::platform::vehicle::pb::GroupIdSoc _internal_group_id() const;
  void _internal_set_group_id(::autolink::soc::platform::vehicle::pb::GroupIdSoc value);
  public:

  // @@protoc_insertion_point(class_scope:autolink.soc.platform.vehicle.pb.MsgGroupIdSoc)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  int group_id_;
  friend struct ::TableStruct_autolink_2esoc_2eplatform_2evehicle_2eproto;
};
// -------------------------------------------------------------------

class MsgSignalIdSoc final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:autolink.soc.platform.vehicle.pb.MsgSignalIdSoc) */ {
 public:
  inline MsgSignalIdSoc() : MsgSignalIdSoc(nullptr) {}
  ~MsgSignalIdSoc() override;
  explicit constexpr MsgSignalIdSoc(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MsgSignalIdSoc(const MsgSignalIdSoc& from);
  MsgSignalIdSoc(MsgSignalIdSoc&& from) noexcept
    : MsgSignalIdSoc() {
    *this = ::std::move(from);
  }

  inline MsgSignalIdSoc& operator=(const MsgSignalIdSoc& from) {
    CopyFrom(from);
    return *this;
  }
  inline MsgSignalIdSoc& operator=(MsgSignalIdSoc&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MsgSignalIdSoc& default_instance() {
    return *internal_default_instance();
  }
  static inline const MsgSignalIdSoc* internal_default_instance() {
    return reinterpret_cast<const MsgSignalIdSoc*>(
               &_MsgSignalIdSoc_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(MsgSignalIdSoc& a, MsgSignalIdSoc& b) {
    a.Swap(&b);
  }
  inline void Swap(MsgSignalIdSoc* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MsgSignalIdSoc* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MsgSignalIdSoc* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MsgSignalIdSoc>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MsgSignalIdSoc& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MsgSignalIdSoc& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MsgSignalIdSoc* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.soc.platform.vehicle.pb.MsgSignalIdSoc";
  }
  protected:
  explicit MsgSignalIdSoc(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSignalIdFieldNumber = 1,
  };
  // required .autolink.soc.platform.vehicle.pb.SignalIdSoc signal_id = 1;
  bool has_signal_id() const;
  private:
  bool _internal_has_signal_id() const;
  public:
  void clear_signal_id();
  ::autolink::soc::platform::vehicle::pb::SignalIdSoc signal_id() const;
  void set_signal_id(::autolink::soc::platform::vehicle::pb::SignalIdSoc value);
  private:
  ::autolink::soc::platform::vehicle::pb::SignalIdSoc _internal_signal_id() const;
  void _internal_set_signal_id(::autolink::soc::platform::vehicle::pb::SignalIdSoc value);
  public:

  // @@protoc_insertion_point(class_scope:autolink.soc.platform.vehicle.pb.MsgSignalIdSoc)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  int signal_id_;
  friend struct ::TableStruct_autolink_2esoc_2eplatform_2evehicle_2eproto;
};
// -------------------------------------------------------------------

class MsgSignalValueSoc final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:autolink.soc.platform.vehicle.pb.MsgSignalValueSoc) */ {
 public:
  inline MsgSignalValueSoc() : MsgSignalValueSoc(nullptr) {}
  ~MsgSignalValueSoc() override;
  explicit constexpr MsgSignalValueSoc(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MsgSignalValueSoc(const MsgSignalValueSoc& from);
  MsgSignalValueSoc(MsgSignalValueSoc&& from) noexcept
    : MsgSignalValueSoc() {
    *this = ::std::move(from);
  }

  inline MsgSignalValueSoc& operator=(const MsgSignalValueSoc& from) {
    CopyFrom(from);
    return *this;
  }
  inline MsgSignalValueSoc& operator=(MsgSignalValueSoc&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MsgSignalValueSoc& default_instance() {
    return *internal_default_instance();
  }
  static inline const MsgSignalValueSoc* internal_default_instance() {
    return reinterpret_cast<const MsgSignalValueSoc*>(
               &_MsgSignalValueSoc_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(MsgSignalValueSoc& a, MsgSignalValueSoc& b) {
    a.Swap(&b);
  }
  inline void Swap(MsgSignalValueSoc* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MsgSignalValueSoc* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MsgSignalValueSoc* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MsgSignalValueSoc>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MsgSignalValueSoc& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MsgSignalValueSoc& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MsgSignalValueSoc* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.soc.platform.vehicle.pb.MsgSignalValueSoc";
  }
  protected:
  explicit MsgSignalValueSoc(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueBytesFieldNumber = 7,
    kValueBoolFieldNumber = 2,
    kValueU32FieldNumber = 3,
    kValueU64FieldNumber = 5,
    kValueS64FieldNumber = 6,
    kValueS32FieldNumber = 4,
    kValueTypeFieldNumber = 1,
  };
  // optional bytes value_bytes = 7;
  bool has_value_bytes() const;
  private:
  bool _internal_has_value_bytes() const;
  public:
  void clear_value_bytes();
  const std::string& value_bytes() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_value_bytes(ArgT0&& arg0, ArgT... args);
  std::string* mutable_value_bytes();
  PROTOBUF_NODISCARD std::string* release_value_bytes();
  void set_allocated_value_bytes(std::string* value_bytes);
  private:
  const std::string& _internal_value_bytes() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_value_bytes(const std::string& value);
  std::string* _internal_mutable_value_bytes();
  public:

  // optional bool value_bool = 2;
  bool has_value_bool() const;
  private:
  bool _internal_has_value_bool() const;
  public:
  void clear_value_bool();
  bool value_bool() const;
  void set_value_bool(bool value);
  private:
  bool _internal_value_bool() const;
  void _internal_set_value_bool(bool value);
  public:

  // optional uint32 value_u32 = 3;
  bool has_value_u32() const;
  private:
  bool _internal_has_value_u32() const;
  public:
  void clear_value_u32();
  uint32_t value_u32() const;
  void set_value_u32(uint32_t value);
  private:
  uint32_t _internal_value_u32() const;
  void _internal_set_value_u32(uint32_t value);
  public:

  // optional uint64 value_u64 = 5;
  bool has_value_u64() const;
  private:
  bool _internal_has_value_u64() const;
  public:
  void clear_value_u64();
  uint64_t value_u64() const;
  void set_value_u64(uint64_t value);
  private:
  uint64_t _internal_value_u64() const;
  void _internal_set_value_u64(uint64_t value);
  public:

  // optional sint64 value_s64 = 6;
  bool has_value_s64() const;
  private:
  bool _internal_has_value_s64() const;
  public:
  void clear_value_s64();
  int64_t value_s64() const;
  void set_value_s64(int64_t value);
  private:
  int64_t _internal_value_s64() const;
  void _internal_set_value_s64(int64_t value);
  public:

  // optional sint32 value_s32 = 4;
  bool has_value_s32() const;
  private:
  bool _internal_has_value_s32() const;
  public:
  void clear_value_s32();
  int32_t value_s32() const;
  void set_value_s32(int32_t value);
  private:
  int32_t _internal_value_s32() const;
  void _internal_set_value_s32(int32_t value);
  public:

  // required .autolink.soc.platform.vehicle.pb.ValueTypeSoc value_type = 1;
  bool has_value_type() const;
  private:
  bool _internal_has_value_type() const;
  public:
  void clear_value_type();
  ::autolink::soc::platform::vehicle::pb::ValueTypeSoc value_type() const;
  void set_value_type(::autolink::soc::platform::vehicle::pb::ValueTypeSoc value);
  private:
  ::autolink::soc::platform::vehicle::pb::ValueTypeSoc _internal_value_type() const;
  void _internal_set_value_type(::autolink::soc::platform::vehicle::pb::ValueTypeSoc value);
  public:

  // @@protoc_insertion_point(class_scope:autolink.soc.platform.vehicle.pb.MsgSignalValueSoc)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr value_bytes_;
  bool value_bool_;
  uint32_t value_u32_;
  uint64_t value_u64_;
  int64_t value_s64_;
  int32_t value_s32_;
  int value_type_;
  friend struct ::TableStruct_autolink_2esoc_2eplatform_2evehicle_2eproto;
};
// -------------------------------------------------------------------

class MsgGroupedSignalSoc final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:autolink.soc.platform.vehicle.pb.MsgGroupedSignalSoc) */ {
 public:
  inline MsgGroupedSignalSoc() : MsgGroupedSignalSoc(nullptr) {}
  ~MsgGroupedSignalSoc() override;
  explicit constexpr MsgGroupedSignalSoc(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MsgGroupedSignalSoc(const MsgGroupedSignalSoc& from);
  MsgGroupedSignalSoc(MsgGroupedSignalSoc&& from) noexcept
    : MsgGroupedSignalSoc() {
    *this = ::std::move(from);
  }

  inline MsgGroupedSignalSoc& operator=(const MsgGroupedSignalSoc& from) {
    CopyFrom(from);
    return *this;
  }
  inline MsgGroupedSignalSoc& operator=(MsgGroupedSignalSoc&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MsgGroupedSignalSoc& default_instance() {
    return *internal_default_instance();
  }
  static inline const MsgGroupedSignalSoc* internal_default_instance() {
    return reinterpret_cast<const MsgGroupedSignalSoc*>(
               &_MsgGroupedSignalSoc_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(MsgGroupedSignalSoc& a, MsgGroupedSignalSoc& b) {
    a.Swap(&b);
  }
  inline void Swap(MsgGroupedSignalSoc* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MsgGroupedSignalSoc* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MsgGroupedSignalSoc* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MsgGroupedSignalSoc>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MsgGroupedSignalSoc& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MsgGroupedSignalSoc& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MsgGroupedSignalSoc* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.soc.platform.vehicle.pb.MsgGroupedSignalSoc";
  }
  protected:
  explicit MsgGroupedSignalSoc(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGsValueFieldNumber = 2,
    kGsIdFieldNumber = 1,
  };
  // required .autolink.soc.platform.vehicle.pb.MsgSignalValueSoc gs_value = 2;
  bool has_gs_value() const;
  private:
  bool _internal_has_gs_value() const;
  public:
  void clear_gs_value();
  const ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc& gs_value() const;
  PROTOBUF_NODISCARD ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* release_gs_value();
  ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* mutable_gs_value();
  void set_allocated_gs_value(::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* gs_value);
  private:
  const ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc& _internal_gs_value() const;
  ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* _internal_mutable_gs_value();
  public:
  void unsafe_arena_set_allocated_gs_value(
      ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* gs_value);
  ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* unsafe_arena_release_gs_value();

  // required .autolink.soc.platform.vehicle.pb.GroupedSignalIdSoc gs_id = 1;
  bool has_gs_id() const;
  private:
  bool _internal_has_gs_id() const;
  public:
  void clear_gs_id();
  ::autolink::soc::platform::vehicle::pb::GroupedSignalIdSoc gs_id() const;
  void set_gs_id(::autolink::soc::platform::vehicle::pb::GroupedSignalIdSoc value);
  private:
  ::autolink::soc::platform::vehicle::pb::GroupedSignalIdSoc _internal_gs_id() const;
  void _internal_set_gs_id(::autolink::soc::platform::vehicle::pb::GroupedSignalIdSoc value);
  public:

  // @@protoc_insertion_point(class_scope:autolink.soc.platform.vehicle.pb.MsgGroupedSignalSoc)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* gs_value_;
  int gs_id_;
  friend struct ::TableStruct_autolink_2esoc_2eplatform_2evehicle_2eproto;
};
// -------------------------------------------------------------------

class MsgSignalSoc final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:autolink.soc.platform.vehicle.pb.MsgSignalSoc) */ {
 public:
  inline MsgSignalSoc() : MsgSignalSoc(nullptr) {}
  ~MsgSignalSoc() override;
  explicit constexpr MsgSignalSoc(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MsgSignalSoc(const MsgSignalSoc& from);
  MsgSignalSoc(MsgSignalSoc&& from) noexcept
    : MsgSignalSoc() {
    *this = ::std::move(from);
  }

  inline MsgSignalSoc& operator=(const MsgSignalSoc& from) {
    CopyFrom(from);
    return *this;
  }
  inline MsgSignalSoc& operator=(MsgSignalSoc&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MsgSignalSoc& default_instance() {
    return *internal_default_instance();
  }
  static inline const MsgSignalSoc* internal_default_instance() {
    return reinterpret_cast<const MsgSignalSoc*>(
               &_MsgSignalSoc_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(MsgSignalSoc& a, MsgSignalSoc& b) {
    a.Swap(&b);
  }
  inline void Swap(MsgSignalSoc* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MsgSignalSoc* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MsgSignalSoc* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MsgSignalSoc>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MsgSignalSoc& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MsgSignalSoc& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MsgSignalSoc* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.soc.platform.vehicle.pb.MsgSignalSoc";
  }
  protected:
  explicit MsgSignalSoc(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSValueFieldNumber = 3,
    kSStateFieldNumber = 2,
    kSIdFieldNumber = 1,
  };
  // required .autolink.soc.platform.vehicle.pb.MsgSignalValueSoc s_value = 3;
  bool has_s_value() const;
  private:
  bool _internal_has_s_value() const;
  public:
  void clear_s_value();
  const ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc& s_value() const;
  PROTOBUF_NODISCARD ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* release_s_value();
  ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* mutable_s_value();
  void set_allocated_s_value(::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* s_value);
  private:
  const ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc& _internal_s_value() const;
  ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* _internal_mutable_s_value();
  public:
  void unsafe_arena_set_allocated_s_value(
      ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* s_value);
  ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* unsafe_arena_release_s_value();

  // required .autolink.soc.platform.vehicle.pb.StateSoc s_state = 2;
  bool has_s_state() const;
  private:
  bool _internal_has_s_state() const;
  public:
  void clear_s_state();
  ::autolink::soc::platform::vehicle::pb::StateSoc s_state() const;
  void set_s_state(::autolink::soc::platform::vehicle::pb::StateSoc value);
  private:
  ::autolink::soc::platform::vehicle::pb::StateSoc _internal_s_state() const;
  void _internal_set_s_state(::autolink::soc::platform::vehicle::pb::StateSoc value);
  public:

  // required .autolink.soc.platform.vehicle.pb.SignalIdSoc s_id = 1;
  bool has_s_id() const;
  private:
  bool _internal_has_s_id() const;
  public:
  void clear_s_id();
  ::autolink::soc::platform::vehicle::pb::SignalIdSoc s_id() const;
  void set_s_id(::autolink::soc::platform::vehicle::pb::SignalIdSoc value);
  private:
  ::autolink::soc::platform::vehicle::pb::SignalIdSoc _internal_s_id() const;
  void _internal_set_s_id(::autolink::soc::platform::vehicle::pb::SignalIdSoc value);
  public:

  // @@protoc_insertion_point(class_scope:autolink.soc.platform.vehicle.pb.MsgSignalSoc)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* s_value_;
  int s_state_;
  int s_id_;
  friend struct ::TableStruct_autolink_2esoc_2eplatform_2evehicle_2eproto;
};
// -------------------------------------------------------------------

class MsgGroupSoc final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:autolink.soc.platform.vehicle.pb.MsgGroupSoc) */ {
 public:
  inline MsgGroupSoc() : MsgGroupSoc(nullptr) {}
  ~MsgGroupSoc() override;
  explicit constexpr MsgGroupSoc(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MsgGroupSoc(const MsgGroupSoc& from);
  MsgGroupSoc(MsgGroupSoc&& from) noexcept
    : MsgGroupSoc() {
    *this = ::std::move(from);
  }

  inline MsgGroupSoc& operator=(const MsgGroupSoc& from) {
    CopyFrom(from);
    return *this;
  }
  inline MsgGroupSoc& operator=(MsgGroupSoc&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MsgGroupSoc& default_instance() {
    return *internal_default_instance();
  }
  static inline const MsgGroupSoc* internal_default_instance() {
    return reinterpret_cast<const MsgGroupSoc*>(
               &_MsgGroupSoc_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(MsgGroupSoc& a, MsgGroupSoc& b) {
    a.Swap(&b);
  }
  inline void Swap(MsgGroupSoc* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MsgGroupSoc* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MsgGroupSoc* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MsgGroupSoc>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MsgGroupSoc& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MsgGroupSoc& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MsgGroupSoc* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "autolink.soc.platform.vehicle.pb.MsgGroupSoc";
  }
  protected:
  explicit MsgGroupSoc(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGSignalsFieldNumber = 3,
    kGStateFieldNumber = 2,
    kGIdFieldNumber = 1,
  };
  // repeated .autolink.soc.platform.vehicle.pb.MsgGroupedSignalSoc g_signals = 3;
  int g_signals_size() const;
  private:
  int _internal_g_signals_size() const;
  public:
  void clear_g_signals();
  ::autolink::soc::platform::vehicle::pb::MsgGroupedSignalSoc* mutable_g_signals(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::soc::platform::vehicle::pb::MsgGroupedSignalSoc >*
      mutable_g_signals();
  private:
  const ::autolink::soc::platform::vehicle::pb::MsgGroupedSignalSoc& _internal_g_signals(int index) const;
  ::autolink::soc::platform::vehicle::pb::MsgGroupedSignalSoc* _internal_add_g_signals();
  public:
  const ::autolink::soc::platform::vehicle::pb::MsgGroupedSignalSoc& g_signals(int index) const;
  ::autolink::soc::platform::vehicle::pb::MsgGroupedSignalSoc* add_g_signals();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::soc::platform::vehicle::pb::MsgGroupedSignalSoc >&
      g_signals() const;

  // required .autolink.soc.platform.vehicle.pb.StateSoc g_state = 2;
  bool has_g_state() const;
  private:
  bool _internal_has_g_state() const;
  public:
  void clear_g_state();
  ::autolink::soc::platform::vehicle::pb::StateSoc g_state() const;
  void set_g_state(::autolink::soc::platform::vehicle::pb::StateSoc value);
  private:
  ::autolink::soc::platform::vehicle::pb::StateSoc _internal_g_state() const;
  void _internal_set_g_state(::autolink::soc::platform::vehicle::pb::StateSoc value);
  public:

  // required .autolink.soc.platform.vehicle.pb.GroupIdSoc g_id = 1;
  bool has_g_id() const;
  private:
  bool _internal_has_g_id() const;
  public:
  void clear_g_id();
  ::autolink::soc::platform::vehicle::pb::GroupIdSoc g_id() const;
  void set_g_id(::autolink::soc::platform::vehicle::pb::GroupIdSoc value);
  private:
  ::autolink::soc::platform::vehicle::pb::GroupIdSoc _internal_g_id() const;
  void _internal_set_g_id(::autolink::soc::platform::vehicle::pb::GroupIdSoc value);
  public:

  // @@protoc_insertion_point(class_scope:autolink.soc.platform.vehicle.pb.MsgGroupSoc)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::soc::platform::vehicle::pb::MsgGroupedSignalSoc > g_signals_;
  int g_state_;
  int g_id_;
  friend struct ::TableStruct_autolink_2esoc_2eplatform_2evehicle_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// MsgGroupIdSoc

// required .autolink.soc.platform.vehicle.pb.GroupIdSoc group_id = 1;
inline bool MsgGroupIdSoc::_internal_has_group_id() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool MsgGroupIdSoc::has_group_id() const {
  return _internal_has_group_id();
}
inline void MsgGroupIdSoc::clear_group_id() {
  group_id_ = 2147483647;
  _has_bits_[0] &= ~0x00000001u;
}
inline ::autolink::soc::platform::vehicle::pb::GroupIdSoc MsgGroupIdSoc::_internal_group_id() const {
  return static_cast< ::autolink::soc::platform::vehicle::pb::GroupIdSoc >(group_id_);
}
inline ::autolink::soc::platform::vehicle::pb::GroupIdSoc MsgGroupIdSoc::group_id() const {
  // @@protoc_insertion_point(field_get:autolink.soc.platform.vehicle.pb.MsgGroupIdSoc.group_id)
  return _internal_group_id();
}
inline void MsgGroupIdSoc::_internal_set_group_id(::autolink::soc::platform::vehicle::pb::GroupIdSoc value) {
  assert(::autolink::soc::platform::vehicle::pb::GroupIdSoc_IsValid(value));
  _has_bits_[0] |= 0x00000001u;
  group_id_ = value;
}
inline void MsgGroupIdSoc::set_group_id(::autolink::soc::platform::vehicle::pb::GroupIdSoc value) {
  _internal_set_group_id(value);
  // @@protoc_insertion_point(field_set:autolink.soc.platform.vehicle.pb.MsgGroupIdSoc.group_id)
}

// -------------------------------------------------------------------

// MsgSignalIdSoc

// required .autolink.soc.platform.vehicle.pb.SignalIdSoc signal_id = 1;
inline bool MsgSignalIdSoc::_internal_has_signal_id() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool MsgSignalIdSoc::has_signal_id() const {
  return _internal_has_signal_id();
}
inline void MsgSignalIdSoc::clear_signal_id() {
  signal_id_ = 134217728;
  _has_bits_[0] &= ~0x00000001u;
}
inline ::autolink::soc::platform::vehicle::pb::SignalIdSoc MsgSignalIdSoc::_internal_signal_id() const {
  return static_cast< ::autolink::soc::platform::vehicle::pb::SignalIdSoc >(signal_id_);
}
inline ::autolink::soc::platform::vehicle::pb::SignalIdSoc MsgSignalIdSoc::signal_id() const {
  // @@protoc_insertion_point(field_get:autolink.soc.platform.vehicle.pb.MsgSignalIdSoc.signal_id)
  return _internal_signal_id();
}
inline void MsgSignalIdSoc::_internal_set_signal_id(::autolink::soc::platform::vehicle::pb::SignalIdSoc value) {
  assert(::autolink::soc::platform::vehicle::pb::SignalIdSoc_IsValid(value));
  _has_bits_[0] |= 0x00000001u;
  signal_id_ = value;
}
inline void MsgSignalIdSoc::set_signal_id(::autolink::soc::platform::vehicle::pb::SignalIdSoc value) {
  _internal_set_signal_id(value);
  // @@protoc_insertion_point(field_set:autolink.soc.platform.vehicle.pb.MsgSignalIdSoc.signal_id)
}

// -------------------------------------------------------------------

// MsgSignalValueSoc

// required .autolink.soc.platform.vehicle.pb.ValueTypeSoc value_type = 1;
inline bool MsgSignalValueSoc::_internal_has_value_type() const {
  bool value = (_has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool MsgSignalValueSoc::has_value_type() const {
  return _internal_has_value_type();
}
inline void MsgSignalValueSoc::clear_value_type() {
  value_type_ = 3;
  _has_bits_[0] &= ~0x00000040u;
}
inline ::autolink::soc::platform::vehicle::pb::ValueTypeSoc MsgSignalValueSoc::_internal_value_type() const {
  return static_cast< ::autolink::soc::platform::vehicle::pb::ValueTypeSoc >(value_type_);
}
inline ::autolink::soc::platform::vehicle::pb::ValueTypeSoc MsgSignalValueSoc::value_type() const {
  // @@protoc_insertion_point(field_get:autolink.soc.platform.vehicle.pb.MsgSignalValueSoc.value_type)
  return _internal_value_type();
}
inline void MsgSignalValueSoc::_internal_set_value_type(::autolink::soc::platform::vehicle::pb::ValueTypeSoc value) {
  assert(::autolink::soc::platform::vehicle::pb::ValueTypeSoc_IsValid(value));
  _has_bits_[0] |= 0x00000040u;
  value_type_ = value;
}
inline void MsgSignalValueSoc::set_value_type(::autolink::soc::platform::vehicle::pb::ValueTypeSoc value) {
  _internal_set_value_type(value);
  // @@protoc_insertion_point(field_set:autolink.soc.platform.vehicle.pb.MsgSignalValueSoc.value_type)
}

// optional bool value_bool = 2;
inline bool MsgSignalValueSoc::_internal_has_value_bool() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool MsgSignalValueSoc::has_value_bool() const {
  return _internal_has_value_bool();
}
inline void MsgSignalValueSoc::clear_value_bool() {
  value_bool_ = false;
  _has_bits_[0] &= ~0x00000002u;
}
inline bool MsgSignalValueSoc::_internal_value_bool() const {
  return value_bool_;
}
inline bool MsgSignalValueSoc::value_bool() const {
  // @@protoc_insertion_point(field_get:autolink.soc.platform.vehicle.pb.MsgSignalValueSoc.value_bool)
  return _internal_value_bool();
}
inline void MsgSignalValueSoc::_internal_set_value_bool(bool value) {
  _has_bits_[0] |= 0x00000002u;
  value_bool_ = value;
}
inline void MsgSignalValueSoc::set_value_bool(bool value) {
  _internal_set_value_bool(value);
  // @@protoc_insertion_point(field_set:autolink.soc.platform.vehicle.pb.MsgSignalValueSoc.value_bool)
}

// optional uint32 value_u32 = 3;
inline bool MsgSignalValueSoc::_internal_has_value_u32() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool MsgSignalValueSoc::has_value_u32() const {
  return _internal_has_value_u32();
}
inline void MsgSignalValueSoc::clear_value_u32() {
  value_u32_ = 0u;
  _has_bits_[0] &= ~0x00000004u;
}
inline uint32_t MsgSignalValueSoc::_internal_value_u32() const {
  return value_u32_;
}
inline uint32_t MsgSignalValueSoc::value_u32() const {
  // @@protoc_insertion_point(field_get:autolink.soc.platform.vehicle.pb.MsgSignalValueSoc.value_u32)
  return _internal_value_u32();
}
inline void MsgSignalValueSoc::_internal_set_value_u32(uint32_t value) {
  _has_bits_[0] |= 0x00000004u;
  value_u32_ = value;
}
inline void MsgSignalValueSoc::set_value_u32(uint32_t value) {
  _internal_set_value_u32(value);
  // @@protoc_insertion_point(field_set:autolink.soc.platform.vehicle.pb.MsgSignalValueSoc.value_u32)
}

// optional sint32 value_s32 = 4;
inline bool MsgSignalValueSoc::_internal_has_value_s32() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool MsgSignalValueSoc::has_value_s32() const {
  return _internal_has_value_s32();
}
inline void MsgSignalValueSoc::clear_value_s32() {
  value_s32_ = 0;
  _has_bits_[0] &= ~0x00000020u;
}
inline int32_t MsgSignalValueSoc::_internal_value_s32() const {
  return value_s32_;
}
inline int32_t MsgSignalValueSoc::value_s32() const {
  // @@protoc_insertion_point(field_get:autolink.soc.platform.vehicle.pb.MsgSignalValueSoc.value_s32)
  return _internal_value_s32();
}
inline void MsgSignalValueSoc::_internal_set_value_s32(int32_t value) {
  _has_bits_[0] |= 0x00000020u;
  value_s32_ = value;
}
inline void MsgSignalValueSoc::set_value_s32(int32_t value) {
  _internal_set_value_s32(value);
  // @@protoc_insertion_point(field_set:autolink.soc.platform.vehicle.pb.MsgSignalValueSoc.value_s32)
}

// optional uint64 value_u64 = 5;
inline bool MsgSignalValueSoc::_internal_has_value_u64() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool MsgSignalValueSoc::has_value_u64() const {
  return _internal_has_value_u64();
}
inline void MsgSignalValueSoc::clear_value_u64() {
  value_u64_ = uint64_t{0u};
  _has_bits_[0] &= ~0x00000008u;
}
inline uint64_t MsgSignalValueSoc::_internal_value_u64() const {
  return value_u64_;
}
inline uint64_t MsgSignalValueSoc::value_u64() const {
  // @@protoc_insertion_point(field_get:autolink.soc.platform.vehicle.pb.MsgSignalValueSoc.value_u64)
  return _internal_value_u64();
}
inline void MsgSignalValueSoc::_internal_set_value_u64(uint64_t value) {
  _has_bits_[0] |= 0x00000008u;
  value_u64_ = value;
}
inline void MsgSignalValueSoc::set_value_u64(uint64_t value) {
  _internal_set_value_u64(value);
  // @@protoc_insertion_point(field_set:autolink.soc.platform.vehicle.pb.MsgSignalValueSoc.value_u64)
}

// optional sint64 value_s64 = 6;
inline bool MsgSignalValueSoc::_internal_has_value_s64() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool MsgSignalValueSoc::has_value_s64() const {
  return _internal_has_value_s64();
}
inline void MsgSignalValueSoc::clear_value_s64() {
  value_s64_ = int64_t{0};
  _has_bits_[0] &= ~0x00000010u;
}
inline int64_t MsgSignalValueSoc::_internal_value_s64() const {
  return value_s64_;
}
inline int64_t MsgSignalValueSoc::value_s64() const {
  // @@protoc_insertion_point(field_get:autolink.soc.platform.vehicle.pb.MsgSignalValueSoc.value_s64)
  return _internal_value_s64();
}
inline void MsgSignalValueSoc::_internal_set_value_s64(int64_t value) {
  _has_bits_[0] |= 0x00000010u;
  value_s64_ = value;
}
inline void MsgSignalValueSoc::set_value_s64(int64_t value) {
  _internal_set_value_s64(value);
  // @@protoc_insertion_point(field_set:autolink.soc.platform.vehicle.pb.MsgSignalValueSoc.value_s64)
}

// optional bytes value_bytes = 7;
inline bool MsgSignalValueSoc::_internal_has_value_bytes() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool MsgSignalValueSoc::has_value_bytes() const {
  return _internal_has_value_bytes();
}
inline void MsgSignalValueSoc::clear_value_bytes() {
  value_bytes_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& MsgSignalValueSoc::value_bytes() const {
  // @@protoc_insertion_point(field_get:autolink.soc.platform.vehicle.pb.MsgSignalValueSoc.value_bytes)
  return _internal_value_bytes();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MsgSignalValueSoc::set_value_bytes(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 value_bytes_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:autolink.soc.platform.vehicle.pb.MsgSignalValueSoc.value_bytes)
}
inline std::string* MsgSignalValueSoc::mutable_value_bytes() {
  std::string* _s = _internal_mutable_value_bytes();
  // @@protoc_insertion_point(field_mutable:autolink.soc.platform.vehicle.pb.MsgSignalValueSoc.value_bytes)
  return _s;
}
inline const std::string& MsgSignalValueSoc::_internal_value_bytes() const {
  return value_bytes_.Get();
}
inline void MsgSignalValueSoc::_internal_set_value_bytes(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  value_bytes_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* MsgSignalValueSoc::_internal_mutable_value_bytes() {
  _has_bits_[0] |= 0x00000001u;
  return value_bytes_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* MsgSignalValueSoc::release_value_bytes() {
  // @@protoc_insertion_point(field_release:autolink.soc.platform.vehicle.pb.MsgSignalValueSoc.value_bytes)
  if (!_internal_has_value_bytes()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = value_bytes_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (value_bytes_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    value_bytes_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void MsgSignalValueSoc::set_allocated_value_bytes(std::string* value_bytes) {
  if (value_bytes != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  value_bytes_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value_bytes,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (value_bytes_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    value_bytes_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:autolink.soc.platform.vehicle.pb.MsgSignalValueSoc.value_bytes)
}

// -------------------------------------------------------------------

// MsgGroupedSignalSoc

// required .autolink.soc.platform.vehicle.pb.GroupedSignalIdSoc gs_id = 1;
inline bool MsgGroupedSignalSoc::_internal_has_gs_id() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool MsgGroupedSignalSoc::has_gs_id() const {
  return _internal_has_gs_id();
}
inline void MsgGroupedSignalSoc::clear_gs_id() {
  gs_id_ = 2147483647;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::autolink::soc::platform::vehicle::pb::GroupedSignalIdSoc MsgGroupedSignalSoc::_internal_gs_id() const {
  return static_cast< ::autolink::soc::platform::vehicle::pb::GroupedSignalIdSoc >(gs_id_);
}
inline ::autolink::soc::platform::vehicle::pb::GroupedSignalIdSoc MsgGroupedSignalSoc::gs_id() const {
  // @@protoc_insertion_point(field_get:autolink.soc.platform.vehicle.pb.MsgGroupedSignalSoc.gs_id)
  return _internal_gs_id();
}
inline void MsgGroupedSignalSoc::_internal_set_gs_id(::autolink::soc::platform::vehicle::pb::GroupedSignalIdSoc value) {
  assert(::autolink::soc::platform::vehicle::pb::GroupedSignalIdSoc_IsValid(value));
  _has_bits_[0] |= 0x00000002u;
  gs_id_ = value;
}
inline void MsgGroupedSignalSoc::set_gs_id(::autolink::soc::platform::vehicle::pb::GroupedSignalIdSoc value) {
  _internal_set_gs_id(value);
  // @@protoc_insertion_point(field_set:autolink.soc.platform.vehicle.pb.MsgGroupedSignalSoc.gs_id)
}

// required .autolink.soc.platform.vehicle.pb.MsgSignalValueSoc gs_value = 2;
inline bool MsgGroupedSignalSoc::_internal_has_gs_value() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || gs_value_ != nullptr);
  return value;
}
inline bool MsgGroupedSignalSoc::has_gs_value() const {
  return _internal_has_gs_value();
}
inline void MsgGroupedSignalSoc::clear_gs_value() {
  if (gs_value_ != nullptr) gs_value_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc& MsgGroupedSignalSoc::_internal_gs_value() const {
  const ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* p = gs_value_;
  return p != nullptr ? *p : reinterpret_cast<const ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc&>(
      ::autolink::soc::platform::vehicle::pb::_MsgSignalValueSoc_default_instance_);
}
inline const ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc& MsgGroupedSignalSoc::gs_value() const {
  // @@protoc_insertion_point(field_get:autolink.soc.platform.vehicle.pb.MsgGroupedSignalSoc.gs_value)
  return _internal_gs_value();
}
inline void MsgGroupedSignalSoc::unsafe_arena_set_allocated_gs_value(
    ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* gs_value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(gs_value_);
  }
  gs_value_ = gs_value;
  if (gs_value) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:autolink.soc.platform.vehicle.pb.MsgGroupedSignalSoc.gs_value)
}
inline ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* MsgGroupedSignalSoc::release_gs_value() {
  _has_bits_[0] &= ~0x00000001u;
  ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* temp = gs_value_;
  gs_value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* MsgGroupedSignalSoc::unsafe_arena_release_gs_value() {
  // @@protoc_insertion_point(field_release:autolink.soc.platform.vehicle.pb.MsgGroupedSignalSoc.gs_value)
  _has_bits_[0] &= ~0x00000001u;
  ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* temp = gs_value_;
  gs_value_ = nullptr;
  return temp;
}
inline ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* MsgGroupedSignalSoc::_internal_mutable_gs_value() {
  _has_bits_[0] |= 0x00000001u;
  if (gs_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc>(GetArenaForAllocation());
    gs_value_ = p;
  }
  return gs_value_;
}
inline ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* MsgGroupedSignalSoc::mutable_gs_value() {
  ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* _msg = _internal_mutable_gs_value();
  // @@protoc_insertion_point(field_mutable:autolink.soc.platform.vehicle.pb.MsgGroupedSignalSoc.gs_value)
  return _msg;
}
inline void MsgGroupedSignalSoc::set_allocated_gs_value(::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* gs_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete gs_value_;
  }
  if (gs_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc>::GetOwningArena(gs_value);
    if (message_arena != submessage_arena) {
      gs_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, gs_value, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  gs_value_ = gs_value;
  // @@protoc_insertion_point(field_set_allocated:autolink.soc.platform.vehicle.pb.MsgGroupedSignalSoc.gs_value)
}

// -------------------------------------------------------------------

// MsgSignalSoc

// required .autolink.soc.platform.vehicle.pb.SignalIdSoc s_id = 1;
inline bool MsgSignalSoc::_internal_has_s_id() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool MsgSignalSoc::has_s_id() const {
  return _internal_has_s_id();
}
inline void MsgSignalSoc::clear_s_id() {
  s_id_ = 134217728;
  _has_bits_[0] &= ~0x00000004u;
}
inline ::autolink::soc::platform::vehicle::pb::SignalIdSoc MsgSignalSoc::_internal_s_id() const {
  return static_cast< ::autolink::soc::platform::vehicle::pb::SignalIdSoc >(s_id_);
}
inline ::autolink::soc::platform::vehicle::pb::SignalIdSoc MsgSignalSoc::s_id() const {
  // @@protoc_insertion_point(field_get:autolink.soc.platform.vehicle.pb.MsgSignalSoc.s_id)
  return _internal_s_id();
}
inline void MsgSignalSoc::_internal_set_s_id(::autolink::soc::platform::vehicle::pb::SignalIdSoc value) {
  assert(::autolink::soc::platform::vehicle::pb::SignalIdSoc_IsValid(value));
  _has_bits_[0] |= 0x00000004u;
  s_id_ = value;
}
inline void MsgSignalSoc::set_s_id(::autolink::soc::platform::vehicle::pb::SignalIdSoc value) {
  _internal_set_s_id(value);
  // @@protoc_insertion_point(field_set:autolink.soc.platform.vehicle.pb.MsgSignalSoc.s_id)
}

// required .autolink.soc.platform.vehicle.pb.StateSoc s_state = 2;
inline bool MsgSignalSoc::_internal_has_s_state() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool MsgSignalSoc::has_s_state() const {
  return _internal_has_s_state();
}
inline void MsgSignalSoc::clear_s_state() {
  s_state_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::autolink::soc::platform::vehicle::pb::StateSoc MsgSignalSoc::_internal_s_state() const {
  return static_cast< ::autolink::soc::platform::vehicle::pb::StateSoc >(s_state_);
}
inline ::autolink::soc::platform::vehicle::pb::StateSoc MsgSignalSoc::s_state() const {
  // @@protoc_insertion_point(field_get:autolink.soc.platform.vehicle.pb.MsgSignalSoc.s_state)
  return _internal_s_state();
}
inline void MsgSignalSoc::_internal_set_s_state(::autolink::soc::platform::vehicle::pb::StateSoc value) {
  assert(::autolink::soc::platform::vehicle::pb::StateSoc_IsValid(value));
  _has_bits_[0] |= 0x00000002u;
  s_state_ = value;
}
inline void MsgSignalSoc::set_s_state(::autolink::soc::platform::vehicle::pb::StateSoc value) {
  _internal_set_s_state(value);
  // @@protoc_insertion_point(field_set:autolink.soc.platform.vehicle.pb.MsgSignalSoc.s_state)
}

// required .autolink.soc.platform.vehicle.pb.MsgSignalValueSoc s_value = 3;
inline bool MsgSignalSoc::_internal_has_s_value() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || s_value_ != nullptr);
  return value;
}
inline bool MsgSignalSoc::has_s_value() const {
  return _internal_has_s_value();
}
inline void MsgSignalSoc::clear_s_value() {
  if (s_value_ != nullptr) s_value_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc& MsgSignalSoc::_internal_s_value() const {
  const ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* p = s_value_;
  return p != nullptr ? *p : reinterpret_cast<const ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc&>(
      ::autolink::soc::platform::vehicle::pb::_MsgSignalValueSoc_default_instance_);
}
inline const ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc& MsgSignalSoc::s_value() const {
  // @@protoc_insertion_point(field_get:autolink.soc.platform.vehicle.pb.MsgSignalSoc.s_value)
  return _internal_s_value();
}
inline void MsgSignalSoc::unsafe_arena_set_allocated_s_value(
    ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* s_value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(s_value_);
  }
  s_value_ = s_value;
  if (s_value) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:autolink.soc.platform.vehicle.pb.MsgSignalSoc.s_value)
}
inline ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* MsgSignalSoc::release_s_value() {
  _has_bits_[0] &= ~0x00000001u;
  ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* temp = s_value_;
  s_value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* MsgSignalSoc::unsafe_arena_release_s_value() {
  // @@protoc_insertion_point(field_release:autolink.soc.platform.vehicle.pb.MsgSignalSoc.s_value)
  _has_bits_[0] &= ~0x00000001u;
  ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* temp = s_value_;
  s_value_ = nullptr;
  return temp;
}
inline ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* MsgSignalSoc::_internal_mutable_s_value() {
  _has_bits_[0] |= 0x00000001u;
  if (s_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc>(GetArenaForAllocation());
    s_value_ = p;
  }
  return s_value_;
}
inline ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* MsgSignalSoc::mutable_s_value() {
  ::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* _msg = _internal_mutable_s_value();
  // @@protoc_insertion_point(field_mutable:autolink.soc.platform.vehicle.pb.MsgSignalSoc.s_value)
  return _msg;
}
inline void MsgSignalSoc::set_allocated_s_value(::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc* s_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete s_value_;
  }
  if (s_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::autolink::soc::platform::vehicle::pb::MsgSignalValueSoc>::GetOwningArena(s_value);
    if (message_arena != submessage_arena) {
      s_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, s_value, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  s_value_ = s_value;
  // @@protoc_insertion_point(field_set_allocated:autolink.soc.platform.vehicle.pb.MsgSignalSoc.s_value)
}

// -------------------------------------------------------------------

// MsgGroupSoc

// required .autolink.soc.platform.vehicle.pb.GroupIdSoc g_id = 1;
inline bool MsgGroupSoc::_internal_has_g_id() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool MsgGroupSoc::has_g_id() const {
  return _internal_has_g_id();
}
inline void MsgGroupSoc::clear_g_id() {
  g_id_ = 2147483647;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::autolink::soc::platform::vehicle::pb::GroupIdSoc MsgGroupSoc::_internal_g_id() const {
  return static_cast< ::autolink::soc::platform::vehicle::pb::GroupIdSoc >(g_id_);
}
inline ::autolink::soc::platform::vehicle::pb::GroupIdSoc MsgGroupSoc::g_id() const {
  // @@protoc_insertion_point(field_get:autolink.soc.platform.vehicle.pb.MsgGroupSoc.g_id)
  return _internal_g_id();
}
inline void MsgGroupSoc::_internal_set_g_id(::autolink::soc::platform::vehicle::pb::GroupIdSoc value) {
  assert(::autolink::soc::platform::vehicle::pb::GroupIdSoc_IsValid(value));
  _has_bits_[0] |= 0x00000002u;
  g_id_ = value;
}
inline void MsgGroupSoc::set_g_id(::autolink::soc::platform::vehicle::pb::GroupIdSoc value) {
  _internal_set_g_id(value);
  // @@protoc_insertion_point(field_set:autolink.soc.platform.vehicle.pb.MsgGroupSoc.g_id)
}

// required .autolink.soc.platform.vehicle.pb.StateSoc g_state = 2;
inline bool MsgGroupSoc::_internal_has_g_state() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool MsgGroupSoc::has_g_state() const {
  return _internal_has_g_state();
}
inline void MsgGroupSoc::clear_g_state() {
  g_state_ = 0;
  _has_bits_[0] &= ~0x00000001u;
}
inline ::autolink::soc::platform::vehicle::pb::StateSoc MsgGroupSoc::_internal_g_state() const {
  return static_cast< ::autolink::soc::platform::vehicle::pb::StateSoc >(g_state_);
}
inline ::autolink::soc::platform::vehicle::pb::StateSoc MsgGroupSoc::g_state() const {
  // @@protoc_insertion_point(field_get:autolink.soc.platform.vehicle.pb.MsgGroupSoc.g_state)
  return _internal_g_state();
}
inline void MsgGroupSoc::_internal_set_g_state(::autolink::soc::platform::vehicle::pb::StateSoc value) {
  assert(::autolink::soc::platform::vehicle::pb::StateSoc_IsValid(value));
  _has_bits_[0] |= 0x00000001u;
  g_state_ = value;
}
inline void MsgGroupSoc::set_g_state(::autolink::soc::platform::vehicle::pb::StateSoc value) {
  _internal_set_g_state(value);
  // @@protoc_insertion_point(field_set:autolink.soc.platform.vehicle.pb.MsgGroupSoc.g_state)
}

// repeated .autolink.soc.platform.vehicle.pb.MsgGroupedSignalSoc g_signals = 3;
inline int MsgGroupSoc::_internal_g_signals_size() const {
  return g_signals_.size();
}
inline int MsgGroupSoc::g_signals_size() const {
  return _internal_g_signals_size();
}
inline void MsgGroupSoc::clear_g_signals() {
  g_signals_.Clear();
}
inline ::autolink::soc::platform::vehicle::pb::MsgGroupedSignalSoc* MsgGroupSoc::mutable_g_signals(int index) {
  // @@protoc_insertion_point(field_mutable:autolink.soc.platform.vehicle.pb.MsgGroupSoc.g_signals)
  return g_signals_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::soc::platform::vehicle::pb::MsgGroupedSignalSoc >*
MsgGroupSoc::mutable_g_signals() {
  // @@protoc_insertion_point(field_mutable_list:autolink.soc.platform.vehicle.pb.MsgGroupSoc.g_signals)
  return &g_signals_;
}
inline const ::autolink::soc::platform::vehicle::pb::MsgGroupedSignalSoc& MsgGroupSoc::_internal_g_signals(int index) const {
  return g_signals_.Get(index);
}
inline const ::autolink::soc::platform::vehicle::pb::MsgGroupedSignalSoc& MsgGroupSoc::g_signals(int index) const {
  // @@protoc_insertion_point(field_get:autolink.soc.platform.vehicle.pb.MsgGroupSoc.g_signals)
  return _internal_g_signals(index);
}
inline ::autolink::soc::platform::vehicle::pb::MsgGroupedSignalSoc* MsgGroupSoc::_internal_add_g_signals() {
  return g_signals_.Add();
}
inline ::autolink::soc::platform::vehicle::pb::MsgGroupedSignalSoc* MsgGroupSoc::add_g_signals() {
  ::autolink::soc::platform::vehicle::pb::MsgGroupedSignalSoc* _add = _internal_add_g_signals();
  // @@protoc_insertion_point(field_add:autolink.soc.platform.vehicle.pb.MsgGroupSoc.g_signals)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::autolink::soc::platform::vehicle::pb::MsgGroupedSignalSoc >&
MsgGroupSoc::g_signals() const {
  // @@protoc_insertion_point(field_list:autolink.soc.platform.vehicle.pb.MsgGroupSoc.g_signals)
  return g_signals_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace pb
}  // namespace vehicle
}  // namespace platform
}  // namespace soc
}  // namespace autolink

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::autolink::soc::platform::vehicle::pb::MsgIDSoc> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::autolink::soc::platform::vehicle::pb::MsgIDSoc>() {
  return ::autolink::soc::platform::vehicle::pb::MsgIDSoc_descriptor();
}
template <> struct is_proto_enum< ::autolink::soc::platform::vehicle::pb::StateSoc> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::autolink::soc::platform::vehicle::pb::StateSoc>() {
  return ::autolink::soc::platform::vehicle::pb::StateSoc_descriptor();
}
template <> struct is_proto_enum< ::autolink::soc::platform::vehicle::pb::ValueTypeSoc> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::autolink::soc::platform::vehicle::pb::ValueTypeSoc>() {
  return ::autolink::soc::platform::vehicle::pb::ValueTypeSoc_descriptor();
}
template <> struct is_proto_enum< ::autolink::soc::platform::vehicle::pb::InvalidIdSoc> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::autolink::soc::platform::vehicle::pb::InvalidIdSoc>() {
  return ::autolink::soc::platform::vehicle::pb::InvalidIdSoc_descriptor();
}
template <> struct is_proto_enum< ::autolink::soc::platform::vehicle::pb::GroupIdSoc> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::autolink::soc::platform::vehicle::pb::GroupIdSoc>() {
  return ::autolink::soc::platform::vehicle::pb::GroupIdSoc_descriptor();
}
template <> struct is_proto_enum< ::autolink::soc::platform::vehicle::pb::SignalIdSoc> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::autolink::soc::platform::vehicle::pb::SignalIdSoc>() {
  return ::autolink::soc::platform::vehicle::pb::SignalIdSoc_descriptor();
}
template <> struct is_proto_enum< ::autolink::soc::platform::vehicle::pb::GroupedSignalIdSoc> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::autolink::soc::platform::vehicle::pb::GroupedSignalIdSoc>() {
  return ::autolink::soc::platform::vehicle::pb::GroupedSignalIdSoc_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_autolink_2esoc_2eplatform_2evehicle_2eproto
