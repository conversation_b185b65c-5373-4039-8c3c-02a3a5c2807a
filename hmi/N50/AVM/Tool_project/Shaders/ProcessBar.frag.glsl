uniform sampler2D Texture;
varying mediump vec2 vTexCoord;
uniform mediump float curProcess; //0-1 当前进度
uniform mediump float allProcess; //0-1 总进度
uniform mediump vec4 bgColor;     //背景颜色
uniform lowp int progressType;   //0圆形进度 1长形

void main()
{
    precision mediump float;
    vec4 color = texture2D(Texture, vTexCoord);
    vec4 result = vec4(1,1,1,1);
    float progress = curProcess/allProcess;
    if(progressType==0)
    {
        vec2 newuv = vTexCoord-vec2(0.5,0.5);
        float c = atan(newuv.x,newuv.y);
        float p = c/6.283+0.5;
        p=p*step(length(newuv),0.5);
        float a=p*step(progress,p);
        result.a=a*allProcess/curProcess;
        result=result*bgColor;
    }else if(progressType==1)
    {
        result = result*step(vTexCoord.x,progress)*bgColor;
    }
    
    gl_FragColor.rgba = result;
}