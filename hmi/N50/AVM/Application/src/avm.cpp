// Use kanzi.hpp only when you are learning to develop Kanzi applications.
// To improve compilation time in production projects, include only the header files of the Kanzi functionality you are using.
#include <kanzi/kanzi.hpp>
#include "logicdatasource_module.hpp"
// #include "alarmplugin_module.hpp"
// #include "localizationplugin_module.hpp"
#include <fcntl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/mman.h>
//Add Screen Capture
#include "kanzi/core.ui/bitmap/kzc_screen_capture.h"
#include "FunctionAVMManager.hpp"

#ifdef QNX
#include <kanzi/core.ui/platform/windowing/qnx_screen/kzs_window_native.h>
#include <kanzi/core.ui/platform/windowing/qnx_screen/kzs_desktop_native.h>
#include <kanzi/core.ui/platform/windowing/common/kzs_desktop.h>
#include <screen/screen.h>
#endif

#define CLUSTER_WIDTH     2560
#define CLUSTER_HEIGHT    1440
#define BMP_SIZE      2560*1440*4

using namespace kanzi;
#include "FunctionProxyCollection.hpp"
#define LOG_TAG "HMI_AVM"
class AVM : public ExampleApplication
{
public:

    virtual void onConfigure(ApplicationProperties& configuration) KZ_OVERRIDE
    {
        configuration.binaryName = "avm.kzb.cfg";
		configuration.defaultWindowProperties.groupName = string("AVM");
        configuration.defaultWindowProperties.style = KZS_WINDOW_STYLE_FULL_SCREEN;
    }
    // Pass mmaped file to Kanzi on startup.
    virtual void onStartup() KZ_OVERRIDE
    {
      HMI_AVM_LOG_INFO("Cluster hmi avm onStartup start!");
      int mapFlags = MAP_PRIVATE;
      int fileDescriptor = open("./avm.kzb", O_RDONLY);
      struct stat buffer;
      int ret = fstat(fileDescriptor, &buffer);
      if (ret == -1)
      {
          kzThrowException(runtime_error("Can not get size of the file."));
      }
      const size_t fileSize = buffer.st_size;
      void* mmappedAddress = mmap(0, fileSize, PROT_READ, mapFlags, fileDescriptor, 0);
      unique_ptr<File> file(new ReadOnlyMemoryFile(reinterpret_cast<byte*>(mmappedAddress), fileSize));
      unique_ptr<KzbFile> kzbFile(new KzbFile(getDomain(), kanzi::move(file)));
      getEngine()->m_startupPrefabUrl = "kzb://" + kzbFile->getProjectName() + "/StartupPrefab";
      getResourceManager()->addKzbFile(kanzi::move(kzbFile));
      HMI_AVM_LOG_INFO("AVM onStartup avm.kzb loaded!");

    }

    virtual void onProjectLoaded() KZ_OVERRIDE
    {
        // Project file has been loaded from .kzb file.
#ifdef  QNX
        setWindowName();
        HMI_AVM_LOG_INFO("Cluster hmi avm onProjectLoaded");
#endif //  QNX
        // Add initialization code here.
    }
#ifdef QNX
    void setWindowName()
    {
        std::shared_ptr<GlGraphicsOutput> output = dynamic_pointer_cast<GlGraphicsOutput>(getGraphicsOutput());
        screen_window_t mainWindow = *kzsWindowNativeGetScreenWindow(kzsWindowGetNative(output->getWindow()));
        screen_context_t mContext= *kzsDesktopNativeGetScreenContext(kzsDesktopGetNative(output->getDesktop()));
        int ndisplays = 0;
        screen_get_context_property_iv(mContext, SCREEN_PROPERTY_DISPLAY_COUNT, &ndisplays);
        screen_display_t* screen_dpy = (screen_display_t*)calloc(ndisplays, sizeof(&ndisplays));
        screen_get_context_property_pv(mContext, SCREEN_PROPERTY_DISPLAYS, (void**)screen_dpy);
        int target_id = 4;
        int curID = 0;
        for (int i = 0; i < ndisplays; ++i)
        {
            (void)screen_get_display_property_iv(screen_dpy[i], SCREEN_PROPERTY_ID, &curID);
            //printf("actual_id: %d\n",curID);
            if(target_id == curID) {
                screen_set_window_property_pv(mainWindow, SCREEN_PROPERTY_DISPLAY, (void**)&screen_dpy[i]);
                break;
            }
        }
    }
#endif // QNX
	
	virtual void registerMetadataOverride(ObjectFactory& /*factory*/) KZ_OVERRIDE
    {   
        Domain* domain = getDomain();
        KanziComponentsModule::registerModule(domain);
#if !defined(ANDROID) && !defined(KANZI_API_IMPORT)
        LogicDataSourceModule::registerModule(domain);
        // AlarmPluginModule::registerModule(domain);
        // LocalizationPluginModule::registerModule(domain);
#endif
    }
    virtual void onPostRender() KZ_OVERRIDE
    {
        if (mRenderCount < 2){
            mRenderCount ++;
            HMI_AVM_LOG_INFO("AVM onPostRender, render count: %d", mRenderCount);
            // AutoLink::Frameworks::CM::CarPropertyManager::Get().SetPropertyValue("custom.hmi.HMI_RENDER_STATE", mRenderCount);
        }
        if (LogicAdapter::mLogExportState == LOG_EXPORT_START) {
           mContent = new unsigned char[BMP_SIZE];
           if(mContent != nullptr){
               memset(mContent,0,BMP_SIZE);
               kanzi::BitmapImageUniquePtr mbitmap = kzcScreenCaptureToImage(CLUSTER_WIDTH,CLUSTER_HEIGHT,kanzi::GraphicsFormatR8G8B8A8_UNORM);
               memcpy( mContent, mbitmap->getData(), BMP_SIZE );
               std::ostringstream str;
               str << "/logdata/avm_raw_rgba8888";
               std::string filename = str.str();
               std::ofstream(filename, std::ios::binary).write(reinterpret_cast<char*>(mContent), BMP_SIZE);
               delete[] mContent;
               mContent = nullptr;
           }
           else{
                HMI_AVM_LOG_INFO("AVM bitMap mem alloc failed");
           }
           LogicAdapter::mLogExportState = LOG_EXPORT_WAIT;
       }
    }

    virtual void updateOverride(std::chrono::milliseconds deltaTime) KZ_OVERRIDE
    {

        if(LogicAdapter::mSTRState == STR_SUSPEND)
        {
            if(!mSuspendPurged) {
                getDomain()->getResourceManager()->purge();
                mSuspendPurged = true;
                mResumePurged = false;
            }
            return;
        }
        else
        {
            if(!mResumePurged) {
                getDomain()->getResourceManager()->purge();
                mResumePurged = true;
                mSuspendPurged = false;
            }
        }
        Application::updateOverride(deltaTime);
    }

    virtual void renderOverride() KZ_OVERRIDE
    {
        if(LogicAdapter::mSTRState==STR_SUSPEND)
        {
            return;
        }
        Application::renderOverride();

    }
private:
    unsigned char* mContent;
    uint mRenderCount{0};
    bool mSuspendPurged{false};
    bool mResumePurged{true};
};

kanzi::Application* createApplication()
{
    return new AVM;
}




