# This file is used to override default values used by the Ant build system.
#
# This file must be checked in Version Control Systems, as it is
# integral to the build system of your project.

# This file is only used by the Ant script.

# You can use this to override default values such as
#  'source.dir' for the location of your java source folder and
#  'out.dir' for the location of your output folder.

# You can also use it define how the release builds are signed by declaring
# the following properties:
#  'key.store' for the location of your keystore and
#  'key.alias' for the name of the key to use.
# The password will be asked during the build when you use the 'release' target.

key.alias=kanzi_testing_key
key.store.password=rightware
key.store=${engine.dir}/configs/platforms/android/kanzi_testing_key.keystore
key.alias.password=rightware
out.dir=../../../output/android
engine.dir=D:/KanziWorkspace/KanziWorkspace_3_6_18_47/Engine
