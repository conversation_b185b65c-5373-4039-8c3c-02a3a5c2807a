﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project">
  <properties xmlns:d2p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>ProjectPluginReferences</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>ProjectPluginReferences</propertyTypeReference>
        <value
          i:type="d2p1:ArrayOfstring" />
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>StartupScreen</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>StartupScreen</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
          i:type="d5p1:ProjectItemReference">
          <d5p1:ContentType>ABSOLUTE</d5p1:ContentType>
          <d5p1:IsResourceReference>false</d5p1:IsResourceReference>
          <d5p1:referredItemPath>
            <d5p1:pathString>TripComputer/Screens/Screen/</d5p1:pathString>
          </d5p1:referredItemPath>
          <d5p1:textContent
            i:nil="true" />
        </value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>PreviewWindowBackgroundColor</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>PreviewWindowBackgroundColor</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base.DataTypes"
          i:type="d5p1:HSLAColor">
          <d5p1:alpha>0</d5p1:alpha>
          <d5p1:hue>0</d5p1:hue>
          <d5p1:luminance>0</d5p1:luminance>
          <d5p1:saturation>0</d5p1:saturation>
        </value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>MessageLimitPerFrame</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>MessageLimitPerFrame</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:int">1000</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>HalfFloatTextureFormat</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>HalfFloatTextureFormat</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:boolean">false</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>HalfFloatTextureFormatLinear</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>HalfFloatTextureFormatLinear</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:boolean">false</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>HalfFloatColorAttachment</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>HalfFloatColorAttachment</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:boolean">false</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>RenderToMipmapLevels</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>RenderToMipmapLevels</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:boolean">false</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>ExternalTexture</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>ExternalTexture</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:boolean">false</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>EditingSequence</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>EditingSequence</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
          i:type="d5p1:ProjectItemReference">
          <d5p1:ContentType>ABSOLUTE</d5p1:ContentType>
          <d5p1:IsResourceReference>false</d5p1:IsResourceReference>
          <d5p1:referredItemPath
            i:nil="true" />
          <d5p1:textContent
            i:nil="true" />
        </value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>Description</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>Description</propertyTypeReference>
        <value
          i:nil="true" />
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>DefaultLocalizationTable</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>DefaultLocalizationTable</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
          i:type="d5p1:ProjectItemReference">
          <d5p1:ContentType>ABSOLUTE</d5p1:ContentType>
          <d5p1:IsResourceReference>false</d5p1:IsResourceReference>
          <d5p1:referredItemPath
            i:nil="true" />
          <d5p1:textContent
            i:nil="true" />
        </value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>GlobalTimelineStartTime</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>GlobalTimelineStartTime</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:float">0</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>GlobalTimelineEndTime</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>GlobalTimelineEndTime</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:float">10</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>ProjectDefaultBuildConfiguration</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>ProjectDefaultBuildConfiguration</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
          i:type="d5p1:ProjectItemReference">
          <d5p1:ContentType>ABSOLUTE</d5p1:ContentType>
          <d5p1:IsResourceReference>false</d5p1:IsResourceReference>
          <d5p1:referredItemPath
            i:nil="true" />
          <d5p1:textContent
            i:nil="true" />
        </value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>BinaryExportDirectory</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>BinaryExportDirectory</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:string">..\ClusterFunction\Application\bin</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>ApplicationExportDirectory</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>ApplicationExportDirectory</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:string">Application Player</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>ProjectResourceKeepAliveBehavior</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>ProjectResourceKeepAliveBehavior</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ResourceDictionaryItems"
          i:type="d5p1:ResourceKeepAliveBehavior">DEFAULT</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>ProjectShaderKeepAliveBehavior</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>ProjectShaderKeepAliveBehavior</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ResourceDictionaryItems"
          i:type="d5p1:ResourceKeepAliveBehavior">KEEP_ALIVE</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>ExportedKzbFormat</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>ExportedKzbFormat</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
          i:type="d5p1:ExportedKzbFormat">Kzb</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>IsMasterProject</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>IsMasterProject</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:boolean">true</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>KZBEndianness</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>KZBEndianness</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base.BinaryExport"
          i:type="d5p1:EndianBinaryWriter.EndiannessType">LITTLE_ENDIAN</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>OptimizeMeshes</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>OptimizeMeshes</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:boolean">false</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>TargetPlatformVertexCacheSize</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>TargetPlatformVertexCacheSize</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:int">24</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>PlotAnimations</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>PlotAnimations</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:boolean">false</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>ProjectDefaultVertexAttributeDataType</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>ProjectDefaultVertexAttributeDataType</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.MeshItems"
          i:type="d5p1:MeshAttributeDataType">FLOAT_16</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>RoundImagesToNearestPowerOf2</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>RoundImagesToNearestPowerOf2</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:boolean">false</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>ProjectRemoveICCProfilesOfPngs</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>ProjectRemoveICCProfilesOfPngs</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:boolean">true</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>ProjectPngCompressionLevel</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>ProjectPngCompressionLevel</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base.DataTypes"
          i:type="d5p1:PngCompressionLevel">Default</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>ProjectExportShaderSourceCode</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>ProjectExportShaderSourceCode</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:boolean">true</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>PropertyNamespace</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>PropertyNamespace</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:string">TripComputer</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>ProjectExportMainKzbWithBakedThemes</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>ProjectExportMainKzbWithBakedThemes</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:boolean">false</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>ProjectGroupByThemeNameInBakedThemeExport</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>ProjectGroupByThemeNameInBakedThemeExport</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:boolean">true</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>IsLocalizationEnabled</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>IsLocalizationEnabled</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:boolean">false</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>PreviewOpenGLESWrapper</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>PreviewOpenGLESWrapper</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.Application"
          i:type="d5p1:OpenGLESWrapper">NONE</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>PreviewDebugReleaseConfiguration</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>PreviewDebugReleaseConfiguration</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
          i:type="d5p1:DebugReleaseConfiguration">RELEASE</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>PreviewVisualStudioVersion</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>PreviewVisualStudioVersion</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
          i:type="d5p1:VisualStudioVersion">VS2015</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>PreviewWorkingDirectory</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>PreviewWorkingDirectory</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:string">&lt;KanziStudioBin&gt;</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>FullScreenPreviewLayer</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>FullScreenPreviewLayer</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:boolean">false</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>ShowChildrenInLayerThumbnails</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>ShowChildrenInLayerThumbnails</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:boolean">true</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>CompositionDesignSize</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>CompositionDesignSize</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base.DataTypes"
          i:type="d5p1:Vector2D">
          <d5p1:X>-1</d5p1:X>
          <d5p1:Y>-1</d5p1:Y>
        </value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>DefaultMaterial</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>DefaultMaterial</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
          i:type="d5p1:ProjectItemReference">
          <d5p1:ContentType>ABSOLUTE</d5p1:ContentType>
          <d5p1:IsResourceReference>false</d5p1:IsResourceReference>
          <d5p1:referredItemPath
            i:nil="true" />
          <d5p1:textContent
            i:nil="true" />
        </value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>DefaultTexture</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>DefaultTexture</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
          i:type="d5p1:ProjectItemReference">
          <d5p1:ContentType>ABSOLUTE</d5p1:ContentType>
          <d5p1:IsResourceReference>false</d5p1:IsResourceReference>
          <d5p1:referredItemPath
            i:nil="true" />
          <d5p1:textContent
            i:nil="true" />
        </value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>DefaultCubeMapTexture</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>DefaultCubeMapTexture</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
          i:type="d5p1:ProjectItemReference">
          <d5p1:ContentType>ABSOLUTE</d5p1:ContentType>
          <d5p1:IsResourceReference>false</d5p1:IsResourceReference>
          <d5p1:referredItemPath
            i:nil="true" />
          <d5p1:textContent
            i:nil="true" />
        </value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>ImportMaterialType</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>ImportMaterialType</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
          i:type="d5p1:ProjectItemReference">
          <d5p1:ContentType>ABSOLUTE</d5p1:ContentType>
          <d5p1:IsResourceReference>false</d5p1:IsResourceReference>
          <d5p1:referredItemPath
            i:nil="true" />
          <d5p1:textContent
            i:nil="true" />
        </value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>ProjectUsePremultipliedAlpha</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>ProjectUsePremultipliedAlpha</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:boolean">true</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>BinaryFileName</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>BinaryFileName</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:string">&lt;project_name&gt;.kzb</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>ProjectDescriptionsFileName</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>ProjectDescriptionsFileName</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:string">&lt;project_name&gt;_descriptions.txt</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>TargetGraphicsApi</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>TargetGraphicsApi</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
          i:type="d5p1:TargetGraphicsApi">ES20</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>ProfileExportType</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>ProfileExportType</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ProfileItems"
          i:type="d5p1:ProfileExportType">PATCH</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>IsAssetPackage</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>IsAssetPackage</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:boolean">false</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>ProjectResourceDefaultCrossProjectVisibility</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>ProjectResourceDefaultCrossProjectVisibility</propertyTypeReference>
        <value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
          i:type="d5p1:CrossProjectVisibility">Private</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>Name</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>Name</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:string">TripComputer</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
    <d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      <d2p1:Key>CreationTime</d2p1:Key>
      <d2p1:Value>
        <isHidden
          i:nil="true" />
        <isReadOnly
          i:nil="true" />
        <propertyTypeReference>CreationTime</propertyTypeReference>
        <value xmlns:d5p1="http://www.w3.org/2001/XMLSchema"
          i:type="d5p1:dateTime">2024-04-11T09:50:43.6594522Z</value>
      </d2p1:Value>
    </d2p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
  </properties>
  <SerializedName>TripComputer</SerializedName>
  <children>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.AnimationItems"
      i:type="d3p1:AnimationClipLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Animation Clips</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6385084Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Animation Clips</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ScreenItems"
      i:type="d3p1:ScreenLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Screens</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6385084Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Screens</SerializedName>
      <children>
        <ProjectItem
          i:type="d3p1:Screen">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Node.Locale</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>Node.Locale</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string"></value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Window.MetricsType</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>false</isReadOnly>
                <propertyTypeReference>Window.MetricsType</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base.DataTypes"
                  i:type="d9p1:LengthType">RELATIVE</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Window.Width</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>false</isReadOnly>
                <propertyTypeReference>Window.Width</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">1</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Window.Height</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>false</isReadOnly>
                <propertyTypeReference>Window.Height</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">1</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Window.Orientation</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>Window.Orientation</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:ScreenOrientation">LANDSCAPE</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ScreenIsolationPreviewType</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ScreenIsolationPreviewType</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.Utilities"
                  i:type="d9p1:IsolationPreviewType">NONE</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ScreenPreviewRootLayer</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ScreenPreviewRootLayer</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:ProjectItemReference">
                  <d9p1:ContentType>ABSOLUTE</d9p1:ContentType>
                  <d9p1:IsResourceReference>false</d9p1:IsResourceReference>
                  <d9p1:referredItemPath
                    i:nil="true" />
                  <d9p1:textContent
                    i:nil="true" />
                </value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ScreenPreviewPrefabModel</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ScreenPreviewPrefabModel</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:ProjectItemReference">
                  <d9p1:ContentType>ABSOLUTE</d9p1:ContentType>
                  <d9p1:IsResourceReference>false</d9p1:IsResourceReference>
                  <d9p1:referredItemPath
                    i:nil="true" />
                  <d9p1:textContent
                    i:nil="true" />
                </value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ScreenPreviewMaterial</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ScreenPreviewMaterial</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:ProjectItemReference">
                  <d9p1:ContentType>ABSOLUTE</d9p1:ContentType>
                  <d9p1:IsResourceReference>false</d9p1:IsResourceReference>
                  <d9p1:referredItemPath
                    i:nil="true" />
                  <d9p1:textContent
                    i:nil="true" />
                </value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ScreenMaterialPreviewMesh</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ScreenMaterialPreviewMesh</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:ProjectItemReference">
                  <d9p1:ContentType>ABSOLUTE</d9p1:ContentType>
                  <d9p1:IsResourceReference>false</d9p1:IsResourceReference>
                  <d9p1:referredItemPath
                    i:nil="true" />
                  <d9p1:textContent
                    i:nil="true" />
                </value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ScreenPreviewMesh</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ScreenPreviewMesh</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:ProjectItemReference">
                  <d9p1:ContentType>ABSOLUTE</d9p1:ContentType>
                  <d9p1:IsResourceReference>false</d9p1:IsResourceReference>
                  <d9p1:referredItemPath
                    i:nil="true" />
                  <d9p1:textContent
                    i:nil="true" />
                </value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ScreenIsolationPreviewFilteringRoot</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ScreenIsolationPreviewFilteringRoot</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:ProjectItemReference">
                  <d9p1:ContentType>ABSOLUTE</d9p1:ContentType>
                  <d9p1:IsResourceReference>false</d9p1:IsResourceReference>
                  <d9p1:referredItemPath
                    i:nil="true" />
                  <d9p1:textContent
                    i:nil="true" />
                </value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>WindowAbsoluteWidth</d6p1:Key>
              <d6p1:Value>
                <isHidden>true</isHidden>
                <isReadOnly>false</isReadOnly>
                <propertyTypeReference>WindowAbsoluteWidth</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">480</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>WindowAbsoluteHeight</d6p1:Key>
              <d6p1:Value>
                <isHidden>true</isHidden>
                <isReadOnly>false</isReadOnly>
                <propertyTypeReference>WindowAbsoluteHeight</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">800</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>WindowRelativeWidth</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>false</isReadOnly>
                <propertyTypeReference>WindowRelativeWidth</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">1</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>WindowRelativeHeight</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>false</isReadOnly>
                <propertyTypeReference>WindowRelativeHeight</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">1</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ScreenResolution</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ScreenResolution</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:ScreenResolution">CUSTOM</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Screen.ClearColor</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>Screen.ClearColor</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base.DataTypes"
                  i:type="d9p1:HSLAColor">
                  <d9p1:alpha>1</d9p1:alpha>
                  <d9p1:hue>0</d9p1:hue>
                  <d9p1:luminance>0</d9p1:luminance>
                  <d9p1:saturation>0</d9p1:saturation>
                </value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>EffectiveDataContext</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>EffectiveDataContext</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.DataSourceItems"
                  i:type="d9p1:DataContext">
                  <d9p1:dataObjectPath></d9p1:dataObjectPath>
                  <d9p1:dataSourceReference xmlns:d10p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project">
                    <d10p1:ContentType>ABSOLUTE</d10p1:ContentType>
                    <d10p1:IsResourceReference>false</d10p1:IsResourceReference>
                    <d10p1:referredItemPath
                      i:nil="true" />
                    <d10p1:textContent
                      i:nil="true" />
                  </d9p1:dataSourceReference>
                </value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">Screen</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6395059Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>Screen</SerializedName>
          <children>
            <ProjectItem xmlns:d7p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ResourceDictionaryItems"
              i:type="d7p1:ResourceDictionaryItem">
              <properties xmlns:d8p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>ResourceDictionaryLinkedDictionaries</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>ResourceDictionaryLinkedDictionaries</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ArrayOfProjectItemReference" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Name</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>Name</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:string">&lt;ResourceDictionaryInNode&gt;</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>CreationTime</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>CreationTime</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:dateTime">2024-04-11T09:50:43.6395059Z</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              </properties>
              <SerializedName>&lt;ResourceDictionaryInNode&gt;</SerializedName>
              <children />
              <dataSource
                i:nil="true" />
              <isHidden>true</isHidden>
              <isReadOnly>false</isReadOnly>
              <d7p1:entries />
            </ProjectItem>
            <ProjectItem xmlns:d7p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.LayerItems"
              i:type="d7p1:PageHost">
              <properties xmlns:d8p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>PageHost.DefaultSubPage</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>PageHost.DefaultSubPage</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ProjectItemReference">
                      <d11p1:ContentType>ABSOLUTE</d11p1:ContentType>
                      <d11p1:IsResourceReference>false</d11p1:IsResourceReference>
                      <d11p1:referredItemPath
                        i:nil="true" />
                      <d11p1:textContent
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>PageHost.LoopSubPages</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>PageHost.LoopSubPages</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:boolean">true</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Page.AutoActivate</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>Page.AutoActivate</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:boolean">false</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Message.Page.Activated</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>Message.Page.Activated</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.UserInterfaceItems.Events"
                      i:type="d11p1:EventSubscriptionList">
                      <properties />
                      <d11p1:iTriggers>
                        <d8p1:anyType
                          i:type="d11p1:EventSubscriptionList_old">
                          <properties>
                            <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                              <d8p1:Key>MessageTrigger.SetHandled</d8p1:Key>
                              <d8p1:Value>
                                <isHidden
                                  i:nil="true" />
                                <isReadOnly
                                  i:nil="true" />
                                <propertyTypeReference>MessageTrigger.SetHandled</propertyTypeReference>
                                <value xmlns:d17p1="http://www.w3.org/2001/XMLSchema"
                                  i:type="d17p1:boolean">true</value>
                              </d8p1:Value>
                            </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                            <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                              <d8p1:Key>MessageTrigger.RoutingMode</d8p1:Key>
                              <d8p1:Value>
                                <isHidden
                                  i:nil="true" />
                                <isReadOnly
                                  i:nil="true" />
                                <propertyTypeReference>MessageTrigger.RoutingMode</propertyTypeReference>
                                <value
                                  i:type="d11p1:MessageRoutingMode">BUBBLING</value>
                              </d8p1:Value>
                            </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                            <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                              <d8p1:Key>MessageTrigger.MessageSource</d8p1:Key>
                              <d8p1:Value>
                                <isHidden
                                  i:nil="true" />
                                <isReadOnly
                                  i:nil="true" />
                                <propertyTypeReference>MessageTrigger.MessageSource</propertyTypeReference>
                                <value xmlns:d17p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                                  i:type="d17p1:ProjectItemReference">
                                  <d17p1:ContentType>OBJECT_NODE_REFERENCE</d17p1:ContentType>
                                  <d17p1:IsResourceReference>false</d17p1:IsResourceReference>
                                  <d17p1:referredItemPath>
                                    <d17p1:pathString>relative://.</d17p1:pathString>
                                  </d17p1:referredItemPath>
                                  <d17p1:textContent
                                    i:nil="true" />
                                </value>
                              </d8p1:Value>
                            </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                          </properties>
                          <d11p1:ID>f6121115-c132-4d9f-9f48-2be0ed811665</d11p1:ID>
                          <d11p1:actions />
                          <d11p1:conditions
                            i:nil="true" />
                          <d11p1:iConditions />
                          <d11p1:subscriptions
                            i:nil="true" />
                        </d8p1:anyType>
                      </d11p1:iTriggers>
                      <d11p1:subscriptions
                        i:nil="true" />
                      <d11p1:triggers
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Message.Page.Deactivated</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>Message.Page.Deactivated</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.UserInterfaceItems.Events"
                      i:type="d11p1:EventSubscriptionList">
                      <properties />
                      <d11p1:iTriggers>
                        <d8p1:anyType
                          i:type="d11p1:EventSubscriptionList_old">
                          <properties>
                            <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                              <d8p1:Key>MessageTrigger.SetHandled</d8p1:Key>
                              <d8p1:Value>
                                <isHidden
                                  i:nil="true" />
                                <isReadOnly
                                  i:nil="true" />
                                <propertyTypeReference>MessageTrigger.SetHandled</propertyTypeReference>
                                <value xmlns:d17p1="http://www.w3.org/2001/XMLSchema"
                                  i:type="d17p1:boolean">true</value>
                              </d8p1:Value>
                            </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                            <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                              <d8p1:Key>MessageTrigger.RoutingMode</d8p1:Key>
                              <d8p1:Value>
                                <isHidden
                                  i:nil="true" />
                                <isReadOnly
                                  i:nil="true" />
                                <propertyTypeReference>MessageTrigger.RoutingMode</propertyTypeReference>
                                <value
                                  i:type="d11p1:MessageRoutingMode">BUBBLING</value>
                              </d8p1:Value>
                            </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                            <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                              <d8p1:Key>MessageTrigger.MessageSource</d8p1:Key>
                              <d8p1:Value>
                                <isHidden
                                  i:nil="true" />
                                <isReadOnly
                                  i:nil="true" />
                                <propertyTypeReference>MessageTrigger.MessageSource</propertyTypeReference>
                                <value xmlns:d17p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                                  i:type="d17p1:ProjectItemReference">
                                  <d17p1:ContentType>OBJECT_NODE_REFERENCE</d17p1:ContentType>
                                  <d17p1:IsResourceReference>false</d17p1:IsResourceReference>
                                  <d17p1:referredItemPath>
                                    <d17p1:pathString>relative://.</d17p1:pathString>
                                  </d17p1:referredItemPath>
                                  <d17p1:textContent
                                    i:nil="true" />
                                </value>
                              </d8p1:Value>
                            </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                          </properties>
                          <d11p1:ID>bdf149e9-01ce-4d62-9377-949b274ac0d7</d11p1:ID>
                          <d11p1:actions />
                          <d11p1:conditions
                            i:nil="true" />
                          <d11p1:iConditions />
                          <d11p1:subscriptions
                            i:nil="true" />
                        </d8p1:anyType>
                      </d11p1:iTriggers>
                      <d11p1:subscriptions
                        i:nil="true" />
                      <d11p1:triggers
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Tags</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>Tags</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ArrayOfProjectItemReference" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>EffectiveDataContext</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>EffectiveDataContext</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.DataSourceItems"
                      i:type="d11p1:DataContext">
                      <d11p1:dataObjectPath></d11p1:dataObjectPath>
                      <d11p1:dataSourceReference xmlns:d12p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project">
                        <d12p1:ContentType>ABSOLUTE</d12p1:ContentType>
                        <d12p1:IsResourceReference>false</d12p1:IsResourceReference>
                        <d12p1:referredItemPath
                          i:nil="true" />
                        <d12p1:textContent
                          i:nil="true" />
                      </d11p1:dataSourceReference>
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Name</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>Name</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:string">RootPage</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>CreationTime</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>CreationTime</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:dateTime">2024-04-11T09:50:43.6484819Z</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Node2D.BackgroundBrush</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>Node2D.BackgroundBrush</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ProjectItemReference">
                      <d11p1:ContentType>ABSOLUTE</d11p1:ContentType>
                      <d11p1:IsResourceReference>false</d11p1:IsResourceReference>
                      <d11p1:referredItemPath
                        i:nil="true" />
                      <d11p1:textContent
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>PageHost.Transitions</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>PageHost.Transitions</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ProjectItemReference">
                      <d11p1:ContentType>ABSOLUTE</d11p1:ContentType>
                      <d11p1:IsResourceReference>false</d11p1:IsResourceReference>
                      <d11p1:referredItemPath
                        i:nil="true" />
                      <d11p1:textContent
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>CompositionDesignSize</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>CompositionDesignSize</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base.DataTypes"
                      i:type="d11p1:Vector2D">
                      <d11p1:X>-1</d11p1:X>
                      <d11p1:Y>-1</d11p1:Y>
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              </properties>
              <SerializedName>RootPage</SerializedName>
              <children />
              <dataSource
                i:nil="true" />
              <isHidden>false</isHidden>
              <isReadOnly>false</isReadOnly>
              <d7p1:defaultTransitions xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Studio.PluginInterface.DataTypes" />
              <d7p1:transitions xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Studio.PluginInterface.DataTypes" />
            </ProjectItem>
          </children>
          <dataSource
            i:nil="true" />
          <isHidden>false</isHidden>
          <isReadOnly>false</isReadOnly>
        </ProjectItem>
      </children>
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.MaterialItems"
      i:type="d3p1:MaterialTypeLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Material Types</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6395059Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Material Types</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.MaterialItems"
      i:type="d3p1:MaterialLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Materials</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6395059Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Materials</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.BrushItems"
      i:type="d3p1:BrushLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Brushes</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6395059Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Brushes</SerializedName>
      <children>
        <ProjectItem
          i:type="d3p1:Brush">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>BrushType</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>BrushType</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:ProjectItemReference">
                  <d9p1:ContentType>ABSOLUTE</d9p1:ContentType>
                  <d9p1:IsResourceReference>false</d9p1:IsResourceReference>
                  <d9p1:referredItemPath>
                    <d9p1:pathString></d9p1:pathString>
                  </d9p1:referredItemPath>
                  <d9p1:textContent
                    i:nil="true" />
                </value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">&lt;Isolation Viewport Color Background Brush&gt;</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6524703Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ColorBrush.Color</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ColorBrush.Color</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base.DataTypes"
                  i:type="d9p1:HSLAColor">
                  <d9p1:alpha>1</d9p1:alpha>
                  <d9p1:hue>0</d9p1:hue>
                  <d9p1:luminance>0.498039216</d9p1:luminance>
                  <d9p1:saturation>0</d9p1:saturation>
                </value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>IsInternalEditTimeContent</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">true</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>&lt;Isolation Viewport Color Background Brush&gt;</SerializedName>
          <children />
          <dataSource
            i:nil="true" />
          <isHidden>true</isHidden>
          <isReadOnly>true</isReadOnly>
          <d3p1:serializedPluginContentTypeName>Kanzi.ColorBrush</d3p1:serializedPluginContentTypeName>
        </ProjectItem>
      </children>
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.MeshItems"
      i:type="d3p1:MeshLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Mesh Data</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6405033Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Mesh Data</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem
      i:type="ColladaLibrariesGroupItem">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">COLLADA Libraries</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6405033Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>COLLADA Libraries</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>true</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.AnimationItems"
      i:type="d3p1:AnimationLibraryGroupItem">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Animations</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6405033Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Animations</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>true</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.AnimationItems"
      i:type="d3p1:TimelineSequenceLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Timeline Sequences</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6405033Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Timeline Sequences</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.BinaryShaders"
      i:type="d3p1:BinaryShaderFormatLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Shader Formats</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6405033Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Shader Formats</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.SceneGraphItems"
      i:type="d3p1:SceneObjectLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Objects</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6405033Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Objects</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ComposerItems"
      i:type="d3p1:ComposerLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Render Passes</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6405033Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Render Passes</SerializedName>
      <children>
        <ProjectItem xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.RenderPassItems"
          i:type="d5p1:RenderPass">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.RenderPassCamera</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.RenderPassCamera</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:ProjectItemReference">
                  <d9p1:ContentType>ABSOLUTE</d9p1:ContentType>
                  <d9p1:IsResourceReference>false</d9p1:IsResourceReference>
                  <d9p1:referredItemPath
                    i:nil="true" />
                  <d9p1:textContent
                    i:nil="true" />
                </value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.RenderPassObjectSource</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.RenderPassObjectSource</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:ProjectItemReference">
                  <d9p1:ContentType>ABSOLUTE</d9p1:ContentType>
                  <d9p1:IsResourceReference>false</d9p1:IsResourceReference>
                  <d9p1:referredItemPath>
                    <d9p1:pathString>TripComputer/Object Sources/Root Object Source/</d9p1:pathString>
                  </d9p1:referredItemPath>
                  <d9p1:textContent
                    i:nil="true" />
                </value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.RenderPassMaterial</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.RenderPassMaterial</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:ProjectItemReference">
                  <d9p1:ContentType>ABSOLUTE</d9p1:ContentType>
                  <d9p1:IsResourceReference>false</d9p1:IsResourceReference>
                  <d9p1:referredItemPath
                    i:nil="true" />
                  <d9p1:textContent
                    i:nil="true" />
                </value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.FrustumCullingOn</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.FrustumCullingOn</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">true</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.CullMode</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.CullMode</propertyTypeReference>
                <value
                  i:type="d5p1:RenderPass.CullMode">BACK</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.RenderOnce</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.RenderOnce</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">false</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.ColorBufferClearEnabled</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.ColorBufferClearEnabled</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">false</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.ColorWriteMode</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.ColorWriteMode</propertyTypeReference>
                <value
                  i:type="d5p1:RenderPass.ColorWriteMode">RGBA</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.DepthBufferClearEnabled</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.DepthBufferClearEnabled</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">true</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.DepthBufferClearValue</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.DepthBufferClearValue</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">1</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.DepthBufferTestEnabled</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.DepthBufferTestEnabled</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">true</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.DepthBufferWriteEnabled</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.DepthBufferWriteEnabled</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">true</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.StencilBufferClearEnabled</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.StencilBufferClearEnabled</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">false</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.StencilBufferClearValue</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.StencilBufferClearValue</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:int">0</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.StencilFailOperation</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.StencilFailOperation</propertyTypeReference>
                <value
                  i:type="d5p1:RenderPass.StencilOperation">KEEP</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.StencilPassDepthFailOperation</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.StencilPassDepthFailOperation</propertyTypeReference>
                <value
                  i:type="d5p1:RenderPass.StencilOperation">KEEP</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.StencilPassDepthPassOperation</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.StencilPassDepthPassOperation</propertyTypeReference>
                <value
                  i:type="d5p1:RenderPass.StencilOperation">KEEP</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.StencilFunction</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.StencilFunction</propertyTypeReference>
                <value
                  i:type="d5p1:RenderPass.StencilFunction">ALWAYS</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.StencilFunctionReferenceValue</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.StencilFunctionReferenceValue</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:int">0</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.StencilFunctionReferenceMask</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.StencilFunctionReferenceMask</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:int">255</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.StencilBufferTestEnabled</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.StencilBufferTestEnabled</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">false</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.RenderPassScissorEnabled</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.RenderPassScissorEnabled</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">false</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.RenderPassScissorX</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.RenderPassScissorX</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">0</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.RenderPassScissorY</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.RenderPassScissorY</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">0</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.RenderPassScissorWidth</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.RenderPassScissorWidth</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">1</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.RenderPassScissorHeight</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.RenderPassScissorHeight</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">1</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.RenderPassScissorType</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.RenderPassScissorType</propertyTypeReference>
                <value
                  i:type="d5p1:RenderPass.ViewportType">RELATIVE</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.RenderPassRenderTargetMipmapLevel</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.RenderPassRenderTargetMipmapLevel</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:int">0</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.RenderPassRenderTargetGenerateMipmap</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.RenderPassRenderTargetGenerateMipmap</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">false</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.RenderPassRenderTargetResolve</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.RenderPassRenderTargetResolve</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">false</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.ViewportX</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.ViewportX</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">0</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.ViewportY</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.ViewportY</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">0</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.ViewportWidth</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.ViewportWidth</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">1</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.ViewportHeight</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.ViewportHeight</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">1</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LegacyRenderPass.ViewportType</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LegacyRenderPass.ViewportType</propertyTypeReference>
                <value
                  i:type="d5p1:RenderPass.ViewportType">RELATIVE</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ViewportRelativeX</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ViewportRelativeX</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">0</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ViewportRelativeY</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ViewportRelativeY</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">0</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ViewportRelativeWidth</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ViewportRelativeWidth</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">1</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ViewportRelativeHeight</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ViewportRelativeHeight</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">1</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ViewportAbsoluteX</d6p1:Key>
              <d6p1:Value>
                <isHidden>true</isHidden>
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ViewportAbsoluteX</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">0</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ViewportAbsoluteY</d6p1:Key>
              <d6p1:Value>
                <isHidden>true</isHidden>
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ViewportAbsoluteY</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">0</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ViewportAbsoluteWidth</d6p1:Key>
              <d6p1:Value>
                <isHidden>true</isHidden>
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ViewportAbsoluteWidth</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">480</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ViewportAbsoluteHeight</d6p1:Key>
              <d6p1:Value>
                <isHidden>true</isHidden>
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ViewportAbsoluteHeight</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">800</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>RenderPassAbsoluteScissorX</d6p1:Key>
              <d6p1:Value>
                <isHidden>true</isHidden>
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>RenderPassAbsoluteScissorX</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">0</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>RenderPassAbsoluteScissorY</d6p1:Key>
              <d6p1:Value>
                <isHidden>true</isHidden>
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>RenderPassAbsoluteScissorY</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">0</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>RenderPassAbsoluteScissorWidth</d6p1:Key>
              <d6p1:Value>
                <isHidden>true</isHidden>
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>RenderPassAbsoluteScissorWidth</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">480</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>RenderPassAbsoluteScissorHeight</d6p1:Key>
              <d6p1:Value>
                <isHidden>true</isHidden>
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>RenderPassAbsoluteScissorHeight</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">800</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>RenderPassRelativeScissorX</d6p1:Key>
              <d6p1:Value>
                <isHidden>true</isHidden>
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>RenderPassRelativeScissorX</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">0</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>RenderPassRelativeScissorY</d6p1:Key>
              <d6p1:Value>
                <isHidden>true</isHidden>
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>RenderPassRelativeScissorY</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">0</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>RenderPassRelativeScissorWidth</d6p1:Key>
              <d6p1:Value>
                <isHidden>true</isHidden>
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>RenderPassRelativeScissorWidth</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">1</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>RenderPassRelativeScissorHeight</d6p1:Key>
              <d6p1:Value>
                <isHidden>true</isHidden>
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>RenderPassRelativeScissorHeight</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:float">1</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>RenderPassType</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>RenderPassType</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:ProjectItemReference">
                  <d9p1:ContentType>ABSOLUTE</d9p1:ContentType>
                  <d9p1:IsResourceReference>false</d9p1:IsResourceReference>
                  <d9p1:referredItemPath>
                    <d9p1:pathString></d9p1:pathString>
                  </d9p1:referredItemPath>
                  <d9p1:textContent
                    i:nil="true" />
                </value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">&lt;Preview Scene Render Pass&gt;</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6534687Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>IsInternalEditTimeContent</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">true</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>&lt;Preview Scene Render Pass&gt;</SerializedName>
          <children />
          <dataSource
            i:nil="true" />
          <isHidden>true</isHidden>
          <isReadOnly>true</isReadOnly>
          <d3p1:serializedPluginContentTypeName>Kanzi.LegacyRenderPass</d3p1:serializedPluginContentTypeName>
        </ProjectItem>
      </children>
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.PipelineItems"
      i:type="d3p1:PipelineItemLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Object Sources</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6405033Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Object Sources</SerializedName>
      <children>
        <ProjectItem
          i:type="d3p1:RootObjectSource">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">Root Object Source</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6464909Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>Root Object Source</SerializedName>
          <children />
          <dataSource
            i:nil="true" />
          <isHidden>false</isHidden>
          <isReadOnly>true</isReadOnly>
        </ProjectItem>
        <ProjectItem
          i:type="d3p1:ObjectTypeFilterObjectSource">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ObjectTypeFilterObjectType</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ObjectTypeFilterObjectType</propertyTypeReference>
                <value
                  i:type="d3p1:ObjectTypeFilterObjectType">LIGHT</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ObjectTypeFilterOperation</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ObjectTypeFilterOperation</propertyTypeReference>
                <value
                  i:type="d3p1:ObjectSetFilterOperation">INCLUDE</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>PipelineItemObjectSource</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>PipelineItemObjectSource</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:ProjectItemReference">
                  <d9p1:ContentType>ABSOLUTE</d9p1:ContentType>
                  <d9p1:IsResourceReference>false</d9p1:IsResourceReference>
                  <d9p1:referredItemPath>
                    <d9p1:pathString>TripComputer/Object Sources/Root Object Source/</d9p1:pathString>
                  </d9p1:referredItemPath>
                  <d9p1:textContent
                    i:nil="true" />
                </value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">&lt;Lights&gt;</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6594522Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>IsInternalEditTimeContent</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">true</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>&lt;Lights&gt;</SerializedName>
          <children />
          <dataSource
            i:nil="true" />
          <isHidden>true</isHidden>
          <isReadOnly>true</isReadOnly>
        </ProjectItem>
        <ProjectItem
          i:type="d3p1:ContainsPropertyFilterObjectSource">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ContainsPropertyFilterPropertyTypeName</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ContainsPropertyFilterPropertyTypeName</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties.PropertyType"
                  i:type="d9p1:PropertyTypeReference">
                  <d9p1:isExpression>false</d9p1:isExpression>
                  <d9p1:propertyTypeName>IsolationPreviewIncludeFilter</d9p1:propertyTypeName>
                </value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ContainsPropertyFilterOperation</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ContainsPropertyFilterOperation</propertyTypeReference>
                <value
                  i:type="d3p1:ObjectSetFilterOperation">INCLUDE</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>PipelineItemObjectSource</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>PipelineItemObjectSource</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:ProjectItemReference">
                  <d9p1:ContentType>ABSOLUTE</d9p1:ContentType>
                  <d9p1:IsResourceReference>false</d9p1:IsResourceReference>
                  <d9p1:referredItemPath>
                    <d9p1:pathString>TripComputer/Object Sources/Root Object Source/</d9p1:pathString>
                  </d9p1:referredItemPath>
                  <d9p1:textContent
                    i:nil="true" />
                </value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">&lt;Contains Property Filter for Isolation Manager&gt;</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6594522Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>IsInternalEditTimeContent</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">true</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>&lt;Contains Property Filter for Isolation Manager&gt;</SerializedName>
          <children />
          <dataSource
            i:nil="true" />
          <isHidden>true</isHidden>
          <isReadOnly>true</isReadOnly>
        </ProjectItem>
        <ProjectItem
          i:type="d3p1:CombineObjectSource">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CombineObjectSourceInputs</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CombineObjectSourceInputs</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:ArrayOfProjectItemReference">
                  <d9p1:ProjectItemReference>
                    <d9p1:ContentType>ABSOLUTE</d9p1:ContentType>
                    <d9p1:IsResourceReference>false</d9p1:IsResourceReference>
                    <d9p1:referredItemPath>
                      <d9p1:pathString>TripComputer/Object Sources/&lt;Lights&gt;/</d9p1:pathString>
                    </d9p1:referredItemPath>
                    <d9p1:textContent
                      i:nil="true" />
                  </d9p1:ProjectItemReference>
                  <d9p1:ProjectItemReference>
                    <d9p1:ContentType>ABSOLUTE</d9p1:ContentType>
                    <d9p1:IsResourceReference>false</d9p1:IsResourceReference>
                    <d9p1:referredItemPath>
                      <d9p1:pathString>TripComputer/Object Sources/&lt;Contains Property Filter for Isolation Manager&gt;/</d9p1:pathString>
                    </d9p1:referredItemPath>
                    <d9p1:textContent
                      i:nil="true" />
                  </d9p1:ProjectItemReference>
                </value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">&lt;Combine Object Source&gt;</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6594522Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>IsInternalEditTimeContent</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">true</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>&lt;Combine Object Source&gt;</SerializedName>
          <children />
          <dataSource
            i:nil="true" />
          <isHidden>true</isHidden>
          <isReadOnly>true</isReadOnly>
        </ProjectItem>
      </children>
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.PropertyTypeItems"
      i:type="d3p1:PropertyTypeWrapperLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Property Types</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6405033Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Property Types</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.SceneGraphItems"
      i:type="d3p1:SceneLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Scenes</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6415003Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Scenes</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.TrajectoryItems"
      i:type="d3p1:TrajectoryLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Trajectories</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6415003Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Trajectories</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.TransitionItems"
      i:type="d3p1:TransitionLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Transitions</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6415003Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Transitions</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.SplineItems"
      i:type="d3p1:SplineLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Splines</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6415003Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Splines</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.PrefabItems"
      i:type="d3p1:PrefabLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Prefabs</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6415003Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Prefabs</SerializedName>
      <children>
        <ProjectItem
          i:type="d3p1:LayerPrefabTemplate">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ResourceCrossProjectVisibility</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ResourceCrossProjectVisibility</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:CrossProjectVisibility">Inherit</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ResourceKeepAliveBehavior</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ResourceKeepAliveBehavior</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ResourceDictionaryItems"
                  i:type="d9p1:ResourceKeepAliveBehavior">INHERIT</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">&lt;Isolation Preview Viewport&gt;</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6524703Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>IsInternalEditTimeContent</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">true</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>&lt;Isolation Preview Viewport&gt;</SerializedName>
          <children>
            <ProjectItem xmlns:d7p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.LayerItems"
              i:type="d7p1:ViewportLayer">
              <properties xmlns:d8p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>ViewportLayerScene</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>ViewportLayerScene</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ProjectItemReference">
                      <d11p1:ContentType>ABSOLUTE</d11p1:ContentType>
                      <d11p1:IsResourceReference>false</d11p1:IsResourceReference>
                      <d11p1:referredItemPath
                        i:nil="true" />
                      <d11p1:textContent
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Node2D.ForegroundHint</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>Node2D.ForegroundHint</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:int">1</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Tags</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>Tags</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ArrayOfProjectItemReference" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>EffectiveDataContext</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>EffectiveDataContext</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.DataSourceItems"
                      i:type="d11p1:DataContext">
                      <d11p1:dataObjectPath></d11p1:dataObjectPath>
                      <d11p1:dataSourceReference xmlns:d12p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project">
                        <d12p1:ContentType>ABSOLUTE</d12p1:ContentType>
                        <d12p1:IsResourceReference>false</d12p1:IsResourceReference>
                        <d12p1:referredItemPath
                          i:nil="true" />
                        <d12p1:textContent
                          i:nil="true" />
                      </d11p1:dataSourceReference>
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Name</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly>true</isReadOnly>
                    <propertyTypeReference>Name</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:string">&lt;Isolation Preview Viewport&gt;</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>CreationTime</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>CreationTime</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:dateTime">2024-04-11T09:50:43.6524703Z</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>ExternalResourceScopes</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>ExternalResourceScopes</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ArrayOfProjectItemReference" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>IsInternalEditTimeContent</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly>true</isReadOnly>
                    <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:boolean">true</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Node2D.BackgroundBrush</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>Node2D.BackgroundBrush</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ProjectItemReference">
                      <d11p1:ContentType>ABSOLUTE</d11p1:ContentType>
                      <d11p1:IsResourceReference>false</d11p1:IsResourceReference>
                      <d11p1:referredItemPath>
                        <d11p1:pathString>TripComputer/Brushes/&lt;Isolation Viewport Color Background Brush&gt;/</d11p1:pathString>
                      </d11p1:referredItemPath>
                      <d11p1:textContent
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              </properties>
              <SerializedName>&lt;Isolation Preview Viewport&gt;</SerializedName>
              <children>
                <ProjectItem xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ResourceDictionaryItems"
                  i:type="d9p1:ResourceDictionaryItem">
                  <properties xmlns:d10p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>ResourceDictionaryLinkedDictionaries</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>ResourceDictionaryLinkedDictionaries</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ArrayOfProjectItemReference" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Name</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Name</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:string">&lt;ResourceDictionaryInNode&gt;</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>CreationTime</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>CreationTime</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:dateTime">2024-04-11T09:50:43.6524703Z</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  </properties>
                  <SerializedName>&lt;ResourceDictionaryInNode&gt;</SerializedName>
                  <children />
                  <dataSource
                    i:nil="true" />
                  <isHidden>true</isHidden>
                  <isReadOnly>false</isReadOnly>
                  <d9p1:entries />
                </ProjectItem>
                <ProjectItem xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.SceneGraphItems"
                  i:type="d9p1:ObjectPrefabPlaceholder">
                  <properties xmlns:d10p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Node3DPrefabPlaceholderTemplate</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Node3DPrefabPlaceholderTemplate</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ProjectItemReference">
                          <d13p1:ContentType>ABSOLUTE</d13p1:ContentType>
                          <d13p1:IsResourceReference>false</d13p1:IsResourceReference>
                          <d13p1:referredItemPath
                            i:nil="true" />
                          <d13p1:textContent
                            i:nil="true" />
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Tags</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Tags</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ArrayOfProjectItemReference" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>ImportedFrom</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>ImportedFrom</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ProjectItemReference">
                          <d13p1:ContentType>ABSOLUTE</d13p1:ContentType>
                          <d13p1:IsResourceReference>false</d13p1:IsResourceReference>
                          <d13p1:referredItemPath
                            i:nil="true" />
                          <d13p1:textContent
                            i:nil="true" />
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>IDInImportSource</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>IDInImportSource</propertyTypeReference>
                        <value
                          i:nil="true" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>OriginalNameInImportSource</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>OriginalNameInImportSource</propertyTypeReference>
                        <value
                          i:nil="true" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>EffectiveDataContext</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>EffectiveDataContext</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.DataSourceItems"
                          i:type="d13p1:DataContext">
                          <d13p1:dataObjectPath></d13p1:dataObjectPath>
                          <d13p1:dataSourceReference xmlns:d14p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project">
                            <d14p1:ContentType>ABSOLUTE</d14p1:ContentType>
                            <d14p1:IsResourceReference>false</d14p1:IsResourceReference>
                            <d14p1:referredItemPath
                              i:nil="true" />
                            <d14p1:textContent
                              i:nil="true" />
                          </d13p1:dataSourceReference>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Name</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly>true</isReadOnly>
                        <propertyTypeReference>Name</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:string">&lt;Isolation Preview Scene Placeholder&gt;</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>CreationTime</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>CreationTime</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:dateTime">2024-04-11T09:50:43.6544662Z</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>IsInternalEditTimeContent</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly>true</isReadOnly>
                        <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:boolean">true</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  </properties>
                  <SerializedName>&lt;Isolation Preview Scene Placeholder&gt;</SerializedName>
                  <children />
                  <dataSource
                    i:nil="true" />
                  <isHidden>true</isHidden>
                  <isReadOnly>true</isReadOnly>
                  <d9p1:isPrivate>false</d9p1:isPrivate>
                </ProjectItem>
              </children>
              <dataSource
                i:nil="true" />
              <isHidden>true</isHidden>
              <isReadOnly>true</isReadOnly>
            </ProjectItem>
          </children>
          <dataSource
            i:nil="true" />
          <isHidden>true</isHidden>
          <isReadOnly>true</isReadOnly>
        </ProjectItem>
        <ProjectItem
          i:type="d3p1:ObjectPrefabTemplate">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ResourceCrossProjectVisibility</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ResourceCrossProjectVisibility</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:CrossProjectVisibility">Inherit</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ResourceKeepAliveBehavior</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ResourceKeepAliveBehavior</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ResourceDictionaryItems"
                  i:type="d9p1:ResourceKeepAliveBehavior">INHERIT</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">&lt;Isolation Preview Set&gt;</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6544662Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>IsInternalEditTimeContent</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">true</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>&lt;Isolation Preview Set&gt;</SerializedName>
          <children>
            <ProjectItem xmlns:d7p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.SceneGraphItems"
              i:type="d7p1:EmptyNode">
              <properties xmlns:d8p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Tags</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>Tags</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ArrayOfProjectItemReference" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>ImportedFrom</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>ImportedFrom</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ProjectItemReference">
                      <d11p1:ContentType>ABSOLUTE</d11p1:ContentType>
                      <d11p1:IsResourceReference>false</d11p1:IsResourceReference>
                      <d11p1:referredItemPath
                        i:nil="true" />
                      <d11p1:textContent
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>IDInImportSource</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>IDInImportSource</propertyTypeReference>
                    <value
                      i:nil="true" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>OriginalNameInImportSource</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>OriginalNameInImportSource</propertyTypeReference>
                    <value
                      i:nil="true" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>EffectiveDataContext</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>EffectiveDataContext</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.DataSourceItems"
                      i:type="d11p1:DataContext">
                      <d11p1:dataObjectPath></d11p1:dataObjectPath>
                      <d11p1:dataSourceReference xmlns:d12p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project">
                        <d12p1:ContentType>ABSOLUTE</d12p1:ContentType>
                        <d12p1:IsResourceReference>false</d12p1:IsResourceReference>
                        <d12p1:referredItemPath
                          i:nil="true" />
                        <d12p1:textContent
                          i:nil="true" />
                      </d11p1:dataSourceReference>
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Name</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly>true</isReadOnly>
                    <propertyTypeReference>Name</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:string">&lt;Isolation Preview Set&gt;</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>CreationTime</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>CreationTime</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:dateTime">2024-04-11T09:50:43.6544662Z</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>ExternalResourceScopes</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>ExternalResourceScopes</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ArrayOfProjectItemReference" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>IsInternalEditTimeContent</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly>true</isReadOnly>
                    <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:boolean">true</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              </properties>
              <SerializedName>&lt;Isolation Preview Set&gt;</SerializedName>
              <children>
                <ProjectItem xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ResourceDictionaryItems"
                  i:type="d9p1:ResourceDictionaryItem">
                  <properties xmlns:d10p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>ResourceDictionaryLinkedDictionaries</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>ResourceDictionaryLinkedDictionaries</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ArrayOfProjectItemReference" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Name</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Name</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:string">&lt;ResourceDictionaryInNode&gt;</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>CreationTime</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>CreationTime</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:dateTime">2024-04-11T09:50:43.6544662Z</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  </properties>
                  <SerializedName>&lt;ResourceDictionaryInNode&gt;</SerializedName>
                  <children />
                  <dataSource
                    i:nil="true" />
                  <isHidden>true</isHidden>
                  <isReadOnly>false</isReadOnly>
                  <d9p1:entries />
                </ProjectItem>
                <ProjectItem
                  i:type="d7p1:LightNode">
                  <properties xmlns:d10p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>LightType</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>LightType</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties.PropertyType"
                          i:type="d13p1:PropertyTypeReference">
                          <d13p1:isExpression>false</d13p1:isExpression>
                          <d13p1:propertyTypeName>DirectionalLight</d13p1:propertyTypeName>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Tags</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Tags</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ArrayOfProjectItemReference" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>ImportedFrom</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>ImportedFrom</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ProjectItemReference">
                          <d13p1:ContentType>ABSOLUTE</d13p1:ContentType>
                          <d13p1:IsResourceReference>false</d13p1:IsResourceReference>
                          <d13p1:referredItemPath
                            i:nil="true" />
                          <d13p1:textContent
                            i:nil="true" />
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>IDInImportSource</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>IDInImportSource</propertyTypeReference>
                        <value
                          i:nil="true" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>OriginalNameInImportSource</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>OriginalNameInImportSource</propertyTypeReference>
                        <value
                          i:nil="true" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>EffectiveDataContext</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>EffectiveDataContext</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.DataSourceItems"
                          i:type="d13p1:DataContext">
                          <d13p1:dataObjectPath></d13p1:dataObjectPath>
                          <d13p1:dataSourceReference xmlns:d14p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project">
                            <d14p1:ContentType>ABSOLUTE</d14p1:ContentType>
                            <d14p1:IsResourceReference>false</d14p1:IsResourceReference>
                            <d14p1:referredItemPath
                              i:nil="true" />
                            <d14p1:textContent
                              i:nil="true" />
                          </d13p1:dataSourceReference>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Name</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly>true</isReadOnly>
                        <propertyTypeReference>Name</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:string">Directional Light 1</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>CreationTime</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>CreationTime</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:dateTime">2024-04-11T09:50:43.6544662Z</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>LightEnabled</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>LightEnabled</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:boolean">true</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>DirectionalLightColor</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>DirectionalLightColor</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base.DataTypes"
                          i:type="d13p1:HSLAColor">
                          <d13p1:alpha>1</d13p1:alpha>
                          <d13p1:hue>0</d13p1:hue>
                          <d13p1:luminance>0.101960786</d13p1:luminance>
                          <d13p1:saturation>0</d13p1:saturation>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>LightColorScale</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>LightColorScale</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:float">1</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>DirectionalLightDirection</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>DirectionalLightDirection</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/System.Windows.Media.Media3D"
                          i:type="d13p1:Vector3D">
                          <d13p1:_x>0</d13p1:_x>
                          <d13p1:_y>0</d13p1:_y>
                          <d13p1:_z>0</d13p1:_z>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>IsInternalEditTimeContent</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly>true</isReadOnly>
                        <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:boolean">true</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Node3D.LayoutTransformation</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Node3D.LayoutTransformation</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base.DataTypes"
                          i:type="d13p1:SRTTransformation">
                          <d13p1:isScaleUniform>true</d13p1:isScaleUniform>
                          <d13p1:rotation xmlns:d14p1="http://schemas.datacontract.org/2004/07/System.Windows.Media.Media3D">
                            <d14p1:_x>-105</d14p1:_x>
                            <d14p1:_y>4</d14p1:_y>
                            <d14p1:_z>-5</d14p1:_z>
                          </d13p1:rotation>
                          <d13p1:scale xmlns:d14p1="http://schemas.datacontract.org/2004/07/System.Windows.Media.Media3D">
                            <d14p1:_x>1</d14p1:_x>
                            <d14p1:_y>1</d14p1:_y>
                            <d14p1:_z>1</d14p1:_z>
                          </d13p1:scale>
                          <d13p1:translation xmlns:d14p1="http://schemas.datacontract.org/2004/07/System.Windows.Media.Media3D">
                            <d14p1:_x>0</d14p1:_x>
                            <d14p1:_y>17.06</d14p1:_y>
                            <d14p1:_z>0</d14p1:_z>
                          </d13p1:translation>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  </properties>
                  <SerializedName>Directional Light 1</SerializedName>
                  <children />
                  <dataSource
                    i:nil="true" />
                  <isHidden>true</isHidden>
                  <isReadOnly>true</isReadOnly>
                  <d7p1:isPrivate>false</d7p1:isPrivate>
                </ProjectItem>
                <ProjectItem
                  i:type="d7p1:LightNode">
                  <properties xmlns:d10p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>LightType</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>LightType</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties.PropertyType"
                          i:type="d13p1:PropertyTypeReference">
                          <d13p1:isExpression>false</d13p1:isExpression>
                          <d13p1:propertyTypeName>PointLight</d13p1:propertyTypeName>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Tags</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Tags</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ArrayOfProjectItemReference" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>ImportedFrom</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>ImportedFrom</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ProjectItemReference">
                          <d13p1:ContentType>ABSOLUTE</d13p1:ContentType>
                          <d13p1:IsResourceReference>false</d13p1:IsResourceReference>
                          <d13p1:referredItemPath
                            i:nil="true" />
                          <d13p1:textContent
                            i:nil="true" />
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>IDInImportSource</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>IDInImportSource</propertyTypeReference>
                        <value
                          i:nil="true" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>OriginalNameInImportSource</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>OriginalNameInImportSource</propertyTypeReference>
                        <value
                          i:nil="true" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>EffectiveDataContext</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>EffectiveDataContext</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.DataSourceItems"
                          i:type="d13p1:DataContext">
                          <d13p1:dataObjectPath></d13p1:dataObjectPath>
                          <d13p1:dataSourceReference xmlns:d14p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project">
                            <d14p1:ContentType>ABSOLUTE</d14p1:ContentType>
                            <d14p1:IsResourceReference>false</d14p1:IsResourceReference>
                            <d14p1:referredItemPath
                              i:nil="true" />
                            <d14p1:textContent
                              i:nil="true" />
                          </d13p1:dataSourceReference>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Name</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly>true</isReadOnly>
                        <propertyTypeReference>Name</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:string">Point Light 1</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>CreationTime</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>CreationTime</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:dateTime">2024-04-11T09:50:43.6554622Z</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>LightEnabled</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>LightEnabled</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:boolean">true</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>PointLightColor</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>PointLightColor</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base.DataTypes"
                          i:type="d13p1:HSLAColor">
                          <d13p1:alpha>1</d13p1:alpha>
                          <d13p1:hue>0</d13p1:hue>
                          <d13p1:luminance>0.145098045</d13p1:luminance>
                          <d13p1:saturation>0</d13p1:saturation>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>PointLightRadius</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>PointLightRadius</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:float">0</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>LightColorScale</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>LightColorScale</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:float">1</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>PointLightPosition</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>PointLightPosition</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/System.Windows.Media.Media3D"
                          i:type="d13p1:Vector3D">
                          <d13p1:_x>0</d13p1:_x>
                          <d13p1:_y>0</d13p1:_y>
                          <d13p1:_z>0</d13p1:_z>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>PointLightAttenuation</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>PointLightAttenuation</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/System.Windows.Media.Media3D"
                          i:type="d13p1:Vector3D">
                          <d13p1:_x>1</d13p1:_x>
                          <d13p1:_y>0</d13p1:_y>
                          <d13p1:_z>0</d13p1:_z>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>IsInternalEditTimeContent</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly>true</isReadOnly>
                        <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:boolean">true</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Node3D.LayoutTransformation</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Node3D.LayoutTransformation</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base.DataTypes"
                          i:type="d13p1:SRTTransformation">
                          <d13p1:isScaleUniform>true</d13p1:isScaleUniform>
                          <d13p1:rotation xmlns:d14p1="http://schemas.datacontract.org/2004/07/System.Windows.Media.Media3D">
                            <d14p1:_x>0</d14p1:_x>
                            <d14p1:_y>0</d14p1:_y>
                            <d14p1:_z>0</d14p1:_z>
                          </d13p1:rotation>
                          <d13p1:scale xmlns:d14p1="http://schemas.datacontract.org/2004/07/System.Windows.Media.Media3D">
                            <d14p1:_x>1</d14p1:_x>
                            <d14p1:_y>1</d14p1:_y>
                            <d14p1:_z>1</d14p1:_z>
                          </d13p1:scale>
                          <d13p1:translation xmlns:d14p1="http://schemas.datacontract.org/2004/07/System.Windows.Media.Media3D">
                            <d14p1:_x>11.71</d14p1:_x>
                            <d14p1:_y>9</d14p1:_y>
                            <d14p1:_z>0</d14p1:_z>
                          </d13p1:translation>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  </properties>
                  <SerializedName>Point Light 1</SerializedName>
                  <children />
                  <dataSource
                    i:nil="true" />
                  <isHidden>true</isHidden>
                  <isReadOnly>true</isReadOnly>
                  <d7p1:isPrivate>false</d7p1:isPrivate>
                </ProjectItem>
                <ProjectItem
                  i:type="d7p1:LightNode">
                  <properties xmlns:d10p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>LightType</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>LightType</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties.PropertyType"
                          i:type="d13p1:PropertyTypeReference">
                          <d13p1:isExpression>false</d13p1:isExpression>
                          <d13p1:propertyTypeName>SpotLight</d13p1:propertyTypeName>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Tags</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Tags</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ArrayOfProjectItemReference" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>ImportedFrom</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>ImportedFrom</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ProjectItemReference">
                          <d13p1:ContentType>ABSOLUTE</d13p1:ContentType>
                          <d13p1:IsResourceReference>false</d13p1:IsResourceReference>
                          <d13p1:referredItemPath
                            i:nil="true" />
                          <d13p1:textContent
                            i:nil="true" />
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>IDInImportSource</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>IDInImportSource</propertyTypeReference>
                        <value
                          i:nil="true" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>OriginalNameInImportSource</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>OriginalNameInImportSource</propertyTypeReference>
                        <value
                          i:nil="true" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>EffectiveDataContext</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>EffectiveDataContext</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.DataSourceItems"
                          i:type="d13p1:DataContext">
                          <d13p1:dataObjectPath></d13p1:dataObjectPath>
                          <d13p1:dataSourceReference xmlns:d14p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project">
                            <d14p1:ContentType>ABSOLUTE</d14p1:ContentType>
                            <d14p1:IsResourceReference>false</d14p1:IsResourceReference>
                            <d14p1:referredItemPath
                              i:nil="true" />
                            <d14p1:textContent
                              i:nil="true" />
                          </d13p1:dataSourceReference>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Name</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly>true</isReadOnly>
                        <propertyTypeReference>Name</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:string">Spot Light 1</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>CreationTime</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>CreationTime</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:dateTime">2024-04-11T09:50:43.6554622Z</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>LightEnabled</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>LightEnabled</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:boolean">true</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>SpotLightColor</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>SpotLightColor</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base.DataTypes"
                          i:type="d13p1:HSLAColor">
                          <d13p1:alpha>1</d13p1:alpha>
                          <d13p1:hue>0</d13p1:hue>
                          <d13p1:luminance>0.431372553</d13p1:luminance>
                          <d13p1:saturation>0</d13p1:saturation>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>LightColorScale</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>LightColorScale</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:float">1</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>SpotLightCutoffAngle</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>SpotLightCutoffAngle</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:float">90</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>SpotLightInnerAngle</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>SpotLightInnerAngle</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:float">80</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>SpotLightExponent</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>SpotLightExponent</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:float">1</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>SpotLightPosition</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>SpotLightPosition</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/System.Windows.Media.Media3D"
                          i:type="d13p1:Vector3D">
                          <d13p1:_x>0</d13p1:_x>
                          <d13p1:_y>0</d13p1:_y>
                          <d13p1:_z>0</d13p1:_z>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>SpotLightDirection</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>SpotLightDirection</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/System.Windows.Media.Media3D"
                          i:type="d13p1:Vector3D">
                          <d13p1:_x>0</d13p1:_x>
                          <d13p1:_y>0</d13p1:_y>
                          <d13p1:_z>0</d13p1:_z>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>SpotLightAttenuation</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>SpotLightAttenuation</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/System.Windows.Media.Media3D"
                          i:type="d13p1:Vector3D">
                          <d13p1:_x>1</d13p1:_x>
                          <d13p1:_y>0</d13p1:_y>
                          <d13p1:_z>0</d13p1:_z>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>IsInternalEditTimeContent</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly>true</isReadOnly>
                        <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:boolean">true</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Node3D.LayoutTransformation</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Node3D.LayoutTransformation</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base.DataTypes"
                          i:type="d13p1:SRTTransformation">
                          <d13p1:isScaleUniform>true</d13p1:isScaleUniform>
                          <d13p1:rotation xmlns:d14p1="http://schemas.datacontract.org/2004/07/System.Windows.Media.Media3D">
                            <d14p1:_x>-116</d14p1:_x>
                            <d14p1:_y>146</d14p1:_y>
                            <d14p1:_z>32</d14p1:_z>
                          </d13p1:rotation>
                          <d13p1:scale xmlns:d14p1="http://schemas.datacontract.org/2004/07/System.Windows.Media.Media3D">
                            <d14p1:_x>1</d14p1:_x>
                            <d14p1:_y>1</d14p1:_y>
                            <d14p1:_z>1</d14p1:_z>
                          </d13p1:scale>
                          <d13p1:translation xmlns:d14p1="http://schemas.datacontract.org/2004/07/System.Windows.Media.Media3D">
                            <d14p1:_x>-13.71</d14p1:_x>
                            <d14p1:_y>9.03</d14p1:_y>
                            <d14p1:_z>3.68</d14p1:_z>
                          </d13p1:translation>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  </properties>
                  <SerializedName>Spot Light 1</SerializedName>
                  <children />
                  <dataSource
                    i:nil="true" />
                  <isHidden>true</isHidden>
                  <isReadOnly>true</isReadOnly>
                  <d7p1:isPrivate>false</d7p1:isPrivate>
                </ProjectItem>
                <ProjectItem
                  i:type="d7p1:CameraNode">
                  <properties xmlns:d10p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Node3D.LayoutTransformation</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Node3D.LayoutTransformation</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base.DataTypes"
                          i:type="d13p1:SRTTransformation">
                          <d13p1:isScaleUniform>true</d13p1:isScaleUniform>
                          <d13p1:rotation xmlns:d14p1="http://schemas.datacontract.org/2004/07/System.Windows.Media.Media3D">
                            <d14p1:_x>-15</d14p1:_x>
                            <d14p1:_y>0</d14p1:_y>
                            <d14p1:_z>0</d14p1:_z>
                          </d13p1:rotation>
                          <d13p1:scale xmlns:d14p1="http://schemas.datacontract.org/2004/07/System.Windows.Media.Media3D">
                            <d14p1:_x>1</d14p1:_x>
                            <d14p1:_y>1</d14p1:_y>
                            <d14p1:_z>1</d14p1:_z>
                          </d13p1:scale>
                          <d13p1:translation xmlns:d14p1="http://schemas.datacontract.org/2004/07/System.Windows.Media.Media3D">
                            <d14p1:_x>0</d14p1:_x>
                            <d14p1:_y>4.6999998092651367</d14p1:_y>
                            <d14p1:_z>15</d14p1:_z>
                          </d13p1:translation>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Tags</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Tags</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ArrayOfProjectItemReference" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>ImportedFrom</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>ImportedFrom</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ProjectItemReference">
                          <d13p1:ContentType>ABSOLUTE</d13p1:ContentType>
                          <d13p1:IsResourceReference>false</d13p1:IsResourceReference>
                          <d13p1:referredItemPath
                            i:nil="true" />
                          <d13p1:textContent
                            i:nil="true" />
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>IDInImportSource</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>IDInImportSource</propertyTypeReference>
                        <value
                          i:nil="true" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>OriginalNameInImportSource</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>OriginalNameInImportSource</propertyTypeReference>
                        <value
                          i:nil="true" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>EffectiveDataContext</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>EffectiveDataContext</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.DataSourceItems"
                          i:type="d13p1:DataContext">
                          <d13p1:dataObjectPath></d13p1:dataObjectPath>
                          <d13p1:dataSourceReference xmlns:d14p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project">
                            <d14p1:ContentType>ABSOLUTE</d14p1:ContentType>
                            <d14p1:IsResourceReference>false</d14p1:IsResourceReference>
                            <d14p1:referredItemPath
                              i:nil="true" />
                            <d14p1:textContent
                              i:nil="true" />
                          </d13p1:dataSourceReference>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Name</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly>true</isReadOnly>
                        <propertyTypeReference>Name</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:string">Camera</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>CreationTime</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>CreationTime</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:dateTime">2024-04-11T09:50:43.6554622Z</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>IsInternalEditTimeContent</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly>true</isReadOnly>
                        <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:boolean">true</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  </properties>
                  <SerializedName>Camera</SerializedName>
                  <children />
                  <dataSource
                    i:nil="true" />
                  <isHidden>true</isHidden>
                  <isReadOnly>true</isReadOnly>
                  <d7p1:isPrivate>false</d7p1:isPrivate>
                </ProjectItem>
              </children>
              <dataSource
                i:nil="true" />
              <isHidden>true</isHidden>
              <isReadOnly>true</isReadOnly>
              <d7p1:isPrivate>false</d7p1:isPrivate>
            </ProjectItem>
          </children>
          <dataSource
            i:nil="true" />
          <isHidden>true</isHidden>
          <isReadOnly>true</isReadOnly>
        </ProjectItem>
        <ProjectItem
          i:type="d3p1:ObjectPrefabTemplate">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ResourceCrossProjectVisibility</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ResourceCrossProjectVisibility</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:CrossProjectVisibility">Inherit</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ResourceKeepAliveBehavior</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ResourceKeepAliveBehavior</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ResourceDictionaryItems"
                  i:type="d9p1:ResourceKeepAliveBehavior">INHERIT</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">&lt;Isolation Preview Scene&gt;</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6564598Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>IsInternalEditTimeContent</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">true</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>&lt;Isolation Preview Scene&gt;</SerializedName>
          <children>
            <ProjectItem xmlns:d7p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.SceneGraphItems"
              i:type="d7p1:Scene">
              <properties xmlns:d8p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Scene.HitTestCamera</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>Scene.HitTestCamera</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ProjectItemReference">
                      <d11p1:ContentType>ABSOLUTE</d11p1:ContentType>
                      <d11p1:IsResourceReference>false</d11p1:IsResourceReference>
                      <d11p1:referredItemPath
                        i:nil="true" />
                      <d11p1:textContent
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Scene.Camera</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>Scene.Camera</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ProjectItemReference">
                      <d11p1:ContentType>OBJECT_NODE_REFERENCE</d11p1:ContentType>
                      <d11p1:IsResourceReference>false</d11p1:IsResourceReference>
                      <d11p1:referredItemPath>
                        <d11p1:pathString>relative://&lt;Preview Scene Lights Placeholder&gt;/Camera</d11p1:pathString>
                      </d11p1:referredItemPath>
                      <d11p1:textContent
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Scene.RenderPass</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>Scene.RenderPass</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ProjectItemReference">
                      <d11p1:ContentType>ABSOLUTE</d11p1:ContentType>
                      <d11p1:IsResourceReference>false</d11p1:IsResourceReference>
                      <d11p1:referredItemPath>
                        <d11p1:pathString>TripComputer/Render Passes/&lt;Preview Scene Render Pass&gt;/</d11p1:pathString>
                      </d11p1:referredItemPath>
                      <d11p1:textContent
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>SceneBackgroundTimelineSequence</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>SceneBackgroundTimelineSequence</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ProjectItemReference">
                      <d11p1:ContentType>ABSOLUTE</d11p1:ContentType>
                      <d11p1:IsResourceReference>false</d11p1:IsResourceReference>
                      <d11p1:referredItemPath
                        i:nil="true" />
                      <d11p1:textContent
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>SceneComposerOverride</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>SceneComposerOverride</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ProjectItemReference">
                      <d11p1:ContentType>ABSOLUTE</d11p1:ContentType>
                      <d11p1:IsResourceReference>false</d11p1:IsResourceReference>
                      <d11p1:referredItemPath
                        i:nil="true" />
                      <d11p1:textContent
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>SceneEnvironmentTexture</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>SceneEnvironmentTexture</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ProjectItemReference">
                      <d11p1:ContentType>ABSOLUTE</d11p1:ContentType>
                      <d11p1:IsResourceReference>false</d11p1:IsResourceReference>
                      <d11p1:referredItemPath
                        i:nil="true" />
                      <d11p1:textContent
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>ScenePreviewCamera</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>ScenePreviewCamera</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ProjectItemReference">
                      <d11p1:ContentType>ABSOLUTE</d11p1:ContentType>
                      <d11p1:IsResourceReference>false</d11p1:IsResourceReference>
                      <d11p1:referredItemPath
                        i:nil="true" />
                      <d11p1:textContent
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>GlobalAmbient</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>GlobalAmbient</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base.DataTypes"
                      i:type="d11p1:HSLAColor">
                      <d11p1:alpha>1</d11p1:alpha>
                      <d11p1:hue>0</d11p1:hue>
                      <d11p1:luminance>1</d11p1:luminance>
                      <d11p1:saturation>0</d11p1:saturation>
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Tags</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>Tags</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ArrayOfProjectItemReference" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>ImportedFrom</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>ImportedFrom</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ProjectItemReference">
                      <d11p1:ContentType>ABSOLUTE</d11p1:ContentType>
                      <d11p1:IsResourceReference>false</d11p1:IsResourceReference>
                      <d11p1:referredItemPath
                        i:nil="true" />
                      <d11p1:textContent
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>IDInImportSource</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>IDInImportSource</propertyTypeReference>
                    <value
                      i:nil="true" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>OriginalNameInImportSource</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>OriginalNameInImportSource</propertyTypeReference>
                    <value
                      i:nil="true" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>EffectiveDataContext</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>EffectiveDataContext</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.DataSourceItems"
                      i:type="d11p1:DataContext">
                      <d11p1:dataObjectPath></d11p1:dataObjectPath>
                      <d11p1:dataSourceReference xmlns:d12p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project">
                        <d12p1:ContentType>ABSOLUTE</d12p1:ContentType>
                        <d12p1:IsResourceReference>false</d12p1:IsResourceReference>
                        <d12p1:referredItemPath
                          i:nil="true" />
                        <d12p1:textContent
                          i:nil="true" />
                      </d11p1:dataSourceReference>
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Name</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly>true</isReadOnly>
                    <propertyTypeReference>Name</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:string">&lt;Isolation Preview Scene&gt;</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>CreationTime</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>CreationTime</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:dateTime">2024-04-11T09:50:43.6564598Z</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>ExternalResourceScopes</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>ExternalResourceScopes</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ArrayOfProjectItemReference" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>IsInternalEditTimeContent</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly>true</isReadOnly>
                    <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:boolean">true</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              </properties>
              <SerializedName>&lt;Isolation Preview Scene&gt;</SerializedName>
              <children>
                <ProjectItem xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ResourceDictionaryItems"
                  i:type="d9p1:ResourceDictionaryItem">
                  <properties xmlns:d10p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>ResourceDictionaryLinkedDictionaries</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>ResourceDictionaryLinkedDictionaries</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ArrayOfProjectItemReference" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Name</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Name</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:string">&lt;ResourceDictionaryInNode&gt;</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>CreationTime</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>CreationTime</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:dateTime">2024-04-11T09:50:43.6564598Z</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  </properties>
                  <SerializedName>&lt;ResourceDictionaryInNode&gt;</SerializedName>
                  <children />
                  <dataSource
                    i:nil="true" />
                  <isHidden>true</isHidden>
                  <isReadOnly>false</isReadOnly>
                  <d9p1:entries />
                </ProjectItem>
                <ProjectItem
                  i:type="d7p1:ObjectPrefabPlaceholder">
                  <properties xmlns:d10p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Node3DPrefabPlaceholderTemplate</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Node3DPrefabPlaceholderTemplate</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ProjectItemReference">
                          <d13p1:ContentType>ABSOLUTE</d13p1:ContentType>
                          <d13p1:IsResourceReference>false</d13p1:IsResourceReference>
                          <d13p1:referredItemPath>
                            <d13p1:pathString>TripComputer/Prefabs/&lt;Isolation Preview Set&gt;/</d13p1:pathString>
                          </d13p1:referredItemPath>
                          <d13p1:textContent
                            i:nil="true" />
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Tags</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Tags</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ArrayOfProjectItemReference" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>ImportedFrom</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>ImportedFrom</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ProjectItemReference">
                          <d13p1:ContentType>ABSOLUTE</d13p1:ContentType>
                          <d13p1:IsResourceReference>false</d13p1:IsResourceReference>
                          <d13p1:referredItemPath
                            i:nil="true" />
                          <d13p1:textContent
                            i:nil="true" />
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>IDInImportSource</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>IDInImportSource</propertyTypeReference>
                        <value
                          i:nil="true" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>OriginalNameInImportSource</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>OriginalNameInImportSource</propertyTypeReference>
                        <value
                          i:nil="true" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>EffectiveDataContext</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>EffectiveDataContext</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.DataSourceItems"
                          i:type="d13p1:DataContext">
                          <d13p1:dataObjectPath></d13p1:dataObjectPath>
                          <d13p1:dataSourceReference xmlns:d14p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project">
                            <d14p1:ContentType>ABSOLUTE</d14p1:ContentType>
                            <d14p1:IsResourceReference>false</d14p1:IsResourceReference>
                            <d14p1:referredItemPath
                              i:nil="true" />
                            <d14p1:textContent
                              i:nil="true" />
                          </d13p1:dataSourceReference>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Name</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly>true</isReadOnly>
                        <propertyTypeReference>Name</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:string">&lt;Preview Scene Lights Placeholder&gt;</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>CreationTime</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>CreationTime</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:dateTime">2024-04-11T09:50:43.6564598Z</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>IsInternalEditTimeContent</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly>true</isReadOnly>
                        <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:boolean">true</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  </properties>
                  <SerializedName>&lt;Preview Scene Lights Placeholder&gt;</SerializedName>
                  <children />
                  <dataSource
                    i:nil="true" />
                  <isHidden>true</isHidden>
                  <isReadOnly>true</isReadOnly>
                  <d7p1:isPrivate>false</d7p1:isPrivate>
                </ProjectItem>
                <ProjectItem
                  i:type="d7p1:ObjectPrefabPlaceholder">
                  <properties xmlns:d10p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Node3DPrefabPlaceholderTemplate</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Node3DPrefabPlaceholderTemplate</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ProjectItemReference">
                          <d13p1:ContentType>ABSOLUTE</d13p1:ContentType>
                          <d13p1:IsResourceReference>false</d13p1:IsResourceReference>
                          <d13p1:referredItemPath
                            i:nil="true" />
                          <d13p1:textContent
                            i:nil="true" />
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Tags</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Tags</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ArrayOfProjectItemReference" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>ImportedFrom</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>ImportedFrom</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ProjectItemReference">
                          <d13p1:ContentType>ABSOLUTE</d13p1:ContentType>
                          <d13p1:IsResourceReference>false</d13p1:IsResourceReference>
                          <d13p1:referredItemPath
                            i:nil="true" />
                          <d13p1:textContent
                            i:nil="true" />
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>IDInImportSource</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>IDInImportSource</propertyTypeReference>
                        <value
                          i:nil="true" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>OriginalNameInImportSource</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>OriginalNameInImportSource</propertyTypeReference>
                        <value
                          i:nil="true" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>EffectiveDataContext</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>EffectiveDataContext</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.DataSourceItems"
                          i:type="d13p1:DataContext">
                          <d13p1:dataObjectPath></d13p1:dataObjectPath>
                          <d13p1:dataSourceReference xmlns:d14p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project">
                            <d14p1:ContentType>ABSOLUTE</d14p1:ContentType>
                            <d14p1:IsResourceReference>false</d14p1:IsResourceReference>
                            <d14p1:referredItemPath
                              i:nil="true" />
                            <d14p1:textContent
                              i:nil="true" />
                          </d13p1:dataSourceReference>
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Name</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly>true</isReadOnly>
                        <propertyTypeReference>Name</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:string">&lt;Isolation preview model placeholder&gt;</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>CreationTime</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>CreationTime</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:dateTime">2024-04-11T09:50:43.6584541Z</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>IsInternalEditTimeContent</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly>true</isReadOnly>
                        <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:boolean">true</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  </properties>
                  <SerializedName>&lt;Isolation preview model placeholder&gt;</SerializedName>
                  <children />
                  <dataSource
                    i:nil="true" />
                  <isHidden>true</isHidden>
                  <isReadOnly>true</isReadOnly>
                  <d7p1:isPrivate>false</d7p1:isPrivate>
                </ProjectItem>
              </children>
              <dataSource
                i:nil="true" />
              <isHidden>true</isHidden>
              <isReadOnly>true</isReadOnly>
              <d7p1:isPrivate>false</d7p1:isPrivate>
            </ProjectItem>
          </children>
          <dataSource
            i:nil="true" />
          <isHidden>true</isHidden>
          <isReadOnly>true</isReadOnly>
        </ProjectItem>
        <ProjectItem
          i:type="d3p1:ObjectPrefabTemplate">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ResourceCrossProjectVisibility</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ResourceCrossProjectVisibility</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:CrossProjectVisibility">Inherit</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ResourceKeepAliveBehavior</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ResourceKeepAliveBehavior</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ResourceDictionaryItems"
                  i:type="d9p1:ResourceKeepAliveBehavior">INHERIT</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">&lt;Material Preview&gt;</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6574574Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>IsInternalEditTimeContent</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">true</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>&lt;Material Preview&gt;</SerializedName>
          <children>
            <ProjectItem xmlns:d7p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.SceneGraphItems"
              i:type="d7p1:SphereMeshNode">
              <properties xmlns:d8p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>SphereRadius</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>SphereRadius</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:float">1</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>PrimitiveMeshGenerateTangents</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>PrimitiveMeshGenerateTangents</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:boolean">false</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>PrimitiveMeshGenerateNormals</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>PrimitiveMeshGenerateNormals</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:boolean">true</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>MeshMaterial</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>MeshMaterial</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ProjectItemReference">
                      <d11p1:ContentType>ABSOLUTE</d11p1:ContentType>
                      <d11p1:IsResourceReference>false</d11p1:IsResourceReference>
                      <d11p1:referredItemPath
                        i:nil="true" />
                      <d11p1:textContent
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>GpuResourceMemoryType</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>GpuResourceMemoryType</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:GpuResourceMemoryType">GPU_ONLY</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Tags</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>Tags</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ArrayOfProjectItemReference" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>ImportedFrom</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>ImportedFrom</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ProjectItemReference">
                      <d11p1:ContentType>ABSOLUTE</d11p1:ContentType>
                      <d11p1:IsResourceReference>false</d11p1:IsResourceReference>
                      <d11p1:referredItemPath
                        i:nil="true" />
                      <d11p1:textContent
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>IDInImportSource</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>IDInImportSource</propertyTypeReference>
                    <value
                      i:nil="true" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>OriginalNameInImportSource</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>OriginalNameInImportSource</propertyTypeReference>
                    <value
                      i:nil="true" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>EffectiveDataContext</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>EffectiveDataContext</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.DataSourceItems"
                      i:type="d11p1:DataContext">
                      <d11p1:dataObjectPath></d11p1:dataObjectPath>
                      <d11p1:dataSourceReference xmlns:d12p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project">
                        <d12p1:ContentType>ABSOLUTE</d12p1:ContentType>
                        <d12p1:IsResourceReference>false</d12p1:IsResourceReference>
                        <d12p1:referredItemPath
                          i:nil="true" />
                        <d12p1:textContent
                          i:nil="true" />
                      </d11p1:dataSourceReference>
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Name</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly>true</isReadOnly>
                    <propertyTypeReference>Name</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:string">&lt;Material Preview&gt;</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>CreationTime</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>CreationTime</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:dateTime">2024-04-11T09:50:43.6574574Z</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>ExternalResourceScopes</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>ExternalResourceScopes</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ArrayOfProjectItemReference" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>IsInternalEditTimeContent</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly>true</isReadOnly>
                    <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:boolean">true</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              </properties>
              <SerializedName>&lt;Material Preview&gt;</SerializedName>
              <children>
                <ProjectItem xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ResourceDictionaryItems"
                  i:type="d9p1:ResourceDictionaryItem">
                  <properties xmlns:d10p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>ResourceDictionaryLinkedDictionaries</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>ResourceDictionaryLinkedDictionaries</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ArrayOfProjectItemReference" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Name</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Name</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:string">&lt;ResourceDictionaryInNode&gt;</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>CreationTime</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>CreationTime</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:dateTime">2024-04-11T09:50:43.6574574Z</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  </properties>
                  <SerializedName>&lt;ResourceDictionaryInNode&gt;</SerializedName>
                  <children />
                  <dataSource
                    i:nil="true" />
                  <isHidden>true</isHidden>
                  <isReadOnly>false</isReadOnly>
                  <d9p1:entries />
                </ProjectItem>
                <ProjectItem xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.MeshItems"
                  i:type="d9p1:SphereMesh">
                  <properties xmlns:d10p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>SphereRadius</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>SphereRadius</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:float">0</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>PrimitiveMeshGenerateTangents</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>PrimitiveMeshGenerateTangents</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:boolean">false</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>PrimitiveMeshGenerateNormals</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>PrimitiveMeshGenerateNormals</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:boolean">true</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>MeshMaterial</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>MeshMaterial</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ProjectItemReference">
                          <d13p1:ContentType>ABSOLUTE</d13p1:ContentType>
                          <d13p1:IsResourceReference>false</d13p1:IsResourceReference>
                          <d13p1:referredItemPath
                            i:nil="true" />
                          <d13p1:textContent
                            i:nil="true" />
                        </value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>GpuResourceMemoryType</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>GpuResourceMemoryType</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:GpuResourceMemoryType">GPU_ONLY</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Name</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Name</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:string">PrimitiveMeshInternal</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>CreationTime</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>CreationTime</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:dateTime">2024-04-11T09:50:43.6574574Z</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  </properties>
                  <SerializedName>PrimitiveMeshInternal</SerializedName>
                  <children />
                  <dataSource
                    i:nil="true" />
                  <isHidden>true</isHidden>
                  <isReadOnly>false</isReadOnly>
                </ProjectItem>
              </children>
              <dataSource
                i:nil="true" />
              <isHidden>true</isHidden>
              <isReadOnly>true</isReadOnly>
              <d7p1:isPrivate>false</d7p1:isPrivate>
            </ProjectItem>
          </children>
          <dataSource
            i:nil="true" />
          <isHidden>true</isHidden>
          <isReadOnly>true</isReadOnly>
        </ProjectItem>
        <ProjectItem
          i:type="d3p1:ObjectPrefabTemplate">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ResourceCrossProjectVisibility</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ResourceCrossProjectVisibility</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:CrossProjectVisibility">Inherit</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ResourceKeepAliveBehavior</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ResourceKeepAliveBehavior</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ResourceDictionaryItems"
                  i:type="d9p1:ResourceKeepAliveBehavior">INHERIT</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">&lt;Mesh Preview&gt;</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6574574Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>IsInternalEditTimeContent</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">true</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>&lt;Mesh Preview&gt;</SerializedName>
          <children>
            <ProjectItem xmlns:d7p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.SceneGraphItems"
              i:type="d7p1:MeshNode">
              <properties xmlns:d8p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Model3D.Mesh</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>Model3D.Mesh</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ProjectItemReference">
                      <d11p1:ContentType>ABSOLUTE</d11p1:ContentType>
                      <d11p1:IsResourceReference>false</d11p1:IsResourceReference>
                      <d11p1:referredItemPath
                        i:nil="true" />
                      <d11p1:textContent
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Tags</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>Tags</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ArrayOfProjectItemReference" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>ImportedFrom</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>ImportedFrom</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ProjectItemReference">
                      <d11p1:ContentType>ABSOLUTE</d11p1:ContentType>
                      <d11p1:IsResourceReference>false</d11p1:IsResourceReference>
                      <d11p1:referredItemPath
                        i:nil="true" />
                      <d11p1:textContent
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>IDInImportSource</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>IDInImportSource</propertyTypeReference>
                    <value
                      i:nil="true" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>OriginalNameInImportSource</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>OriginalNameInImportSource</propertyTypeReference>
                    <value
                      i:nil="true" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>EffectiveDataContext</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>EffectiveDataContext</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.DataSourceItems"
                      i:type="d11p1:DataContext">
                      <d11p1:dataObjectPath></d11p1:dataObjectPath>
                      <d11p1:dataSourceReference xmlns:d12p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project">
                        <d12p1:ContentType>ABSOLUTE</d12p1:ContentType>
                        <d12p1:IsResourceReference>false</d12p1:IsResourceReference>
                        <d12p1:referredItemPath
                          i:nil="true" />
                        <d12p1:textContent
                          i:nil="true" />
                      </d11p1:dataSourceReference>
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Name</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly>true</isReadOnly>
                    <propertyTypeReference>Name</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:string">&lt;Mesh Preview&gt;</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>CreationTime</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>CreationTime</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:dateTime">2024-04-11T09:50:43.6584541Z</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>ExternalResourceScopes</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>ExternalResourceScopes</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ArrayOfProjectItemReference" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>IsInternalEditTimeContent</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly>true</isReadOnly>
                    <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:boolean">true</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              </properties>
              <SerializedName>&lt;Mesh Preview&gt;</SerializedName>
              <children>
                <ProjectItem xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ResourceDictionaryItems"
                  i:type="d9p1:ResourceDictionaryItem">
                  <properties xmlns:d10p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>ResourceDictionaryLinkedDictionaries</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>ResourceDictionaryLinkedDictionaries</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ArrayOfProjectItemReference" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Name</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Name</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:string">&lt;ResourceDictionaryInNode&gt;</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>CreationTime</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>CreationTime</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:dateTime">2024-04-11T09:50:43.6584541Z</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  </properties>
                  <SerializedName>&lt;ResourceDictionaryInNode&gt;</SerializedName>
                  <children />
                  <dataSource
                    i:nil="true" />
                  <isHidden>true</isHidden>
                  <isReadOnly>false</isReadOnly>
                  <d9p1:entries />
                </ProjectItem>
              </children>
              <dataSource
                i:nil="true" />
              <isHidden>true</isHidden>
              <isReadOnly>true</isReadOnly>
              <d7p1:isPrivate>false</d7p1:isPrivate>
            </ProjectItem>
          </children>
          <dataSource
            i:nil="true" />
          <isHidden>true</isHidden>
          <isReadOnly>true</isReadOnly>
        </ProjectItem>
        <ProjectItem
          i:type="d3p1:ObjectPrefabTemplate">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ResourceCrossProjectVisibility</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ResourceCrossProjectVisibility</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                  i:type="d9p1:CrossProjectVisibility">Inherit</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ResourceKeepAliveBehavior</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ResourceKeepAliveBehavior</propertyTypeReference>
                <value xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ResourceDictionaryItems"
                  i:type="d9p1:ResourceKeepAliveBehavior">INHERIT</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">&lt;Material Preview (Custom Mesh)&gt;</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6584541Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>IsInternalEditTimeContent</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">true</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>&lt;Material Preview (Custom Mesh)&gt;</SerializedName>
          <children>
            <ProjectItem xmlns:d7p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.SceneGraphItems"
              i:type="d7p1:MeshNode">
              <properties xmlns:d8p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Model3D.Mesh</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>Model3D.Mesh</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ProjectItemReference">
                      <d11p1:ContentType>ABSOLUTE</d11p1:ContentType>
                      <d11p1:IsResourceReference>false</d11p1:IsResourceReference>
                      <d11p1:referredItemPath
                        i:nil="true" />
                      <d11p1:textContent
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Tags</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>Tags</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ArrayOfProjectItemReference" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>ImportedFrom</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>ImportedFrom</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ProjectItemReference">
                      <d11p1:ContentType>ABSOLUTE</d11p1:ContentType>
                      <d11p1:IsResourceReference>false</d11p1:IsResourceReference>
                      <d11p1:referredItemPath
                        i:nil="true" />
                      <d11p1:textContent
                        i:nil="true" />
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>IDInImportSource</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>IDInImportSource</propertyTypeReference>
                    <value
                      i:nil="true" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>OriginalNameInImportSource</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>OriginalNameInImportSource</propertyTypeReference>
                    <value
                      i:nil="true" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>EffectiveDataContext</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>EffectiveDataContext</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.DataSourceItems"
                      i:type="d11p1:DataContext">
                      <d11p1:dataObjectPath></d11p1:dataObjectPath>
                      <d11p1:dataSourceReference xmlns:d12p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project">
                        <d12p1:ContentType>ABSOLUTE</d12p1:ContentType>
                        <d12p1:IsResourceReference>false</d12p1:IsResourceReference>
                        <d12p1:referredItemPath
                          i:nil="true" />
                        <d12p1:textContent
                          i:nil="true" />
                      </d11p1:dataSourceReference>
                    </value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Name</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly>true</isReadOnly>
                    <propertyTypeReference>Name</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:string">&lt;Material Preview (Custom Mesh)&gt;</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>CreationTime</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>CreationTime</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:dateTime">2024-04-11T09:50:43.6584541Z</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>ExternalResourceScopes</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>ExternalResourceScopes</propertyTypeReference>
                    <value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                      i:type="d11p1:ArrayOfProjectItemReference" />
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>IsInternalEditTimeContent</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly>true</isReadOnly>
                    <propertyTypeReference>IsInternalEditTimeContent</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:boolean">true</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              </properties>
              <SerializedName>&lt;Material Preview (Custom Mesh)&gt;</SerializedName>
              <children>
                <ProjectItem xmlns:d9p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ResourceDictionaryItems"
                  i:type="d9p1:ResourceDictionaryItem">
                  <properties xmlns:d10p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>ResourceDictionaryLinkedDictionaries</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>ResourceDictionaryLinkedDictionaries</propertyTypeReference>
                        <value xmlns:d13p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project"
                          i:type="d13p1:ArrayOfProjectItemReference" />
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>Name</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>Name</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:string">&lt;ResourceDictionaryInNode&gt;</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                    <d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                      <d10p1:Key>CreationTime</d10p1:Key>
                      <d10p1:Value>
                        <isHidden
                          i:nil="true" />
                        <isReadOnly
                          i:nil="true" />
                        <propertyTypeReference>CreationTime</propertyTypeReference>
                        <value xmlns:d13p1="http://www.w3.org/2001/XMLSchema"
                          i:type="d13p1:dateTime">2024-04-11T09:50:43.6584541Z</value>
                      </d10p1:Value>
                    </d10p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  </properties>
                  <SerializedName>&lt;ResourceDictionaryInNode&gt;</SerializedName>
                  <children />
                  <dataSource
                    i:nil="true" />
                  <isHidden>true</isHidden>
                  <isReadOnly>false</isReadOnly>
                  <d9p1:entries />
                </ProjectItem>
              </children>
              <dataSource
                i:nil="true" />
              <isHidden>true</isHidden>
              <isReadOnly>true</isReadOnly>
              <d7p1:isPrivate>false</d7p1:isPrivate>
            </ProjectItem>
          </children>
          <dataSource
            i:nil="true" />
          <isHidden>true</isHidden>
          <isReadOnly>true</isReadOnly>
        </ProjectItem>
      </children>
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ProjectReferences"
      i:type="d3p1:ProjectReferenceLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Project References</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6415003Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Project References</SerializedName>
      <children>
        <ProjectItem
          i:type="d3p1:ProjectReferenceItem">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ProjectReferencePath</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ProjectReferencePath</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">..\Resource\Resource.kzproj</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>AutomaticKzbExport</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>AutomaticKzbExport</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">false</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>LoadProjectToMemory</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>LoadProjectToMemory</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">true</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>ShowInPreview</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>ShowInPreview</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">true</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">Resource</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:54:08.8281445Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>Resource</SerializedName>
          <children />
          <dataSource
            i:nil="true" />
          <isHidden>false</isHidden>
          <isReadOnly>false</isReadOnly>
        </ProjectItem>
      </children>
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem
      i:type="SceneNodeDataGroupItem">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Object Data</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6415003Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Object Data</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>true</isReadOnly>
    </ProjectItem>
    <ProjectItem
      i:type="ComposingGroupItem">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Rendering</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6415003Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Rendering</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>true</isReadOnly>
    </ProjectItem>
    <ProjectItem
      i:type="ResourceGroupItem">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Resources objects</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6415003Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Resources objects</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>true</isReadOnly>
    </ProjectItem>
    <ProjectItem
      i:type="MaterialGroupItem">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Materials and Textures</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6415003Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Materials and Textures</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>true</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.UserInterfaceItems"
      i:type="d3p1:ComponentTypeLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Components</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.642498Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Components</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.DataSourceItems"
      i:type="d3p1:DataSourceTypeLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">DataSourceTypes</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.642498Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>DataSourceTypes</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.RenderPassItems"
      i:type="d3p1:RenderPassTypeLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">RenderPassTypes</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.642498Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>RenderPassTypes</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.BrushItems"
      i:type="d3p1:BrushTypeLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">BrushTypes</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.642498Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>BrushTypes</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.NodeComponents"
      i:type="d3p1:NodeComponentTypeLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">NodeComponentTypes</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.642498Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>NodeComponentTypes</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.NodeComponents"
      i:type="d3p1:TriggerActionTypeLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">TriggerActionTypes</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.642498Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>TriggerActionTypes</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.NodeComponents"
      i:type="d3p1:MessageTypeLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">MessageTypes</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.642498Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>MessageTypes</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ProfileItems"
      i:type="d3p1:ProfileLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Profiles</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.642498Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Profiles</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.EnginePlugins"
      i:type="d3p1:EnginePluginLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Kanzi Engine Plugins</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.642498Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Kanzi Engine Plugins</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.Connect"
      i:type="d3p1:ConnectServiceLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Kanzi Connect</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.642498Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Kanzi Connect</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>true</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.Connect"
      i:type="d3p1:ConnectUserServiceLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Connect User Library</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6434943Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Connect User Library</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>true</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.Shortcuts"
      i:type="d3p1:ShortcutLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Bookmarks</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6434943Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Bookmarks</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.LayerItems"
      i:type="d3p1:LayerLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Layers</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6434943Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Layers</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.UserInterfaceItems"
      i:type="d3p1:UserInterfaceGroupItem">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">User Interface</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6434943Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>User Interface</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>true</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.AnimationItems"
      i:type="d3p1:AnimationLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Animation Data</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6434943Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Animation Data</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.TagItems"
      i:type="d3p1:TagLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Tags</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6434943Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Tags</SerializedName>
      <children>
        <ProjectItem
          i:type="d3p1:Tag">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>TagIsInherited</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>TagIsInherited</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:boolean">true</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">Transparent</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6524703Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>Transparent</SerializedName>
          <children />
          <dataSource
            i:nil="true" />
          <isHidden>false</isHidden>
          <isReadOnly>true</isReadOnly>
        </ProjectItem>
      </children>
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ResourceThemingItems"
      i:type="d3p1:ThemeLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Themes</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6434943Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Themes</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.TagItems"
      i:type="d3p1:ResourceExportTagLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Resource Export Tags</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6434943Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Resource Export Tags</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>true</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.StudioPluginItems"
      i:type="d3p1:StudioPluginItemLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Kanzi Studio Plugin Content</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6434943Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Kanzi Studio Plugin Content</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ResourceLocalizationItems"
      i:type="d3p1:LocaleLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Localization</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6434943Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Localization</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.DataSourceItems"
      i:type="d3p1:DataSourceLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Data Sources</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6434943Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Data Sources</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.PageItems"
      i:type="d3p1:PageTransitionCollectionLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Page Transitions</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6434943Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Page Transitions</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.ResourceFileItems"
      i:type="d3p1:ResourceFilesItem">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Resource Files</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6434943Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Resource Files</SerializedName>
      <children>
        <ProjectItem
          i:type="d3p1:ColladaResourceFileDirectory">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">3D Assets</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6434943Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>3D Assets</SerializedName>
          <children />
          <dataSource
            i:nil="true" />
          <isHidden>false</isHidden>
          <isReadOnly>false</isReadOnly>
          <d3p1:extensions xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
            <d6p1:string>.dae</d6p1:string>
            <d6p1:string>.fbx</d6p1:string>
            <d6p1:string>.dae</d6p1:string>
            <d6p1:string>.obj</d6p1:string>
          </d3p1:extensions>
          <d3p1:fileSystemNameOverride
            i:nil="true" />
          <d3p1:relativePath>3D Assets</d3p1:relativePath>
        </ProjectItem>
        <ProjectItem
          i:type="d3p1:ImageResourceFileDirectory">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">Images</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6444956Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>Images</SerializedName>
          <children>
            <ProjectItem
              i:type="d3p1:ImageResourceFileDirectory">
              <properties xmlns:d8p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Name</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly>true</isReadOnly>
                    <propertyTypeReference>Name</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:string">cubemaps</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>CreationTime</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>CreationTime</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:dateTime">2024-04-11T09:50:43.7129087Z</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              </properties>
              <SerializedName>cubemaps</SerializedName>
              <children />
              <dataSource
                i:nil="true" />
              <isHidden>true</isHidden>
              <isReadOnly>false</isReadOnly>
              <d3p1:extensions xmlns:d8p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
                <d8p1:string>.bmp</d8p1:string>
                <d8p1:string>.dds</d8p1:string>
                <d8p1:string>.exr</d8p1:string>
                <d8p1:string>.gif</d8p1:string>
                <d8p1:string>.hdr</d8p1:string>
                <d8p1:string>.ico</d8p1:string>
                <d8p1:string>.iff</d8p1:string>
                <d8p1:string>.jng</d8p1:string>
                <d8p1:string>.jpg</d8p1:string>
                <d8p1:string>.jpeg</d8p1:string>
                <d8p1:string>.jif</d8p1:string>
                <d8p1:string>.mng</d8p1:string>
                <d8p1:string>.pcx</d8p1:string>
                <d8p1:string>.pmb</d8p1:string>
                <d8p1:string>.pgm</d8p1:string>
                <d8p1:string>.ppm</d8p1:string>
                <d8p1:string>.pfm</d8p1:string>
                <d8p1:string>.png</d8p1:string>
                <d8p1:string>.pict</d8p1:string>
                <d8p1:string>.psd</d8p1:string>
                <d8p1:string>.ras</d8p1:string>
                <d8p1:string>.sgi</d8p1:string>
                <d8p1:string>.tga</d8p1:string>
                <d8p1:string>.tiff</d8p1:string>
                <d8p1:string>.tif</d8p1:string>
                <d8p1:string>.wbmp</d8p1:string>
                <d8p1:string>.webp</d8p1:string>
                <d8p1:string>.xmb</d8p1:string>
                <d8p1:string>.xpm</d8p1:string>
              </d3p1:extensions>
              <d3p1:fileSystemNameOverride
                i:nil="true" />
              <d3p1:relativePath>Images\cubemaps</d3p1:relativePath>
            </ProjectItem>
          </children>
          <dataSource
            i:nil="true" />
          <isHidden>false</isHidden>
          <isReadOnly>false</isReadOnly>
          <d3p1:extensions xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
            <d6p1:string>.bmp</d6p1:string>
            <d6p1:string>.dds</d6p1:string>
            <d6p1:string>.exr</d6p1:string>
            <d6p1:string>.gif</d6p1:string>
            <d6p1:string>.hdr</d6p1:string>
            <d6p1:string>.ico</d6p1:string>
            <d6p1:string>.iff</d6p1:string>
            <d6p1:string>.jng</d6p1:string>
            <d6p1:string>.jpg</d6p1:string>
            <d6p1:string>.jpeg</d6p1:string>
            <d6p1:string>.jif</d6p1:string>
            <d6p1:string>.mng</d6p1:string>
            <d6p1:string>.pcx</d6p1:string>
            <d6p1:string>.pmb</d6p1:string>
            <d6p1:string>.pgm</d6p1:string>
            <d6p1:string>.ppm</d6p1:string>
            <d6p1:string>.pfm</d6p1:string>
            <d6p1:string>.png</d6p1:string>
            <d6p1:string>.pict</d6p1:string>
            <d6p1:string>.psd</d6p1:string>
            <d6p1:string>.ras</d6p1:string>
            <d6p1:string>.sgi</d6p1:string>
            <d6p1:string>.tga</d6p1:string>
            <d6p1:string>.tiff</d6p1:string>
            <d6p1:string>.tif</d6p1:string>
            <d6p1:string>.wbmp</d6p1:string>
            <d6p1:string>.webp</d6p1:string>
            <d6p1:string>.xmb</d6p1:string>
            <d6p1:string>.xpm</d6p1:string>
          </d3p1:extensions>
          <d3p1:fileSystemNameOverride
            i:nil="true" />
          <d3p1:relativePath>Images</d3p1:relativePath>
        </ProjectItem>
        <ProjectItem
          i:type="d3p1:ScriptResourceFileDirectory">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">Scripts</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6444956Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>Scripts</SerializedName>
          <children />
          <dataSource
            i:nil="true" />
          <isHidden>false</isHidden>
          <isReadOnly>false</isReadOnly>
          <d3p1:extensions xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
            <d6p1:string>.js</d6p1:string>
          </d3p1:extensions>
          <d3p1:fileSystemNameOverride
            i:nil="true" />
          <d3p1:relativePath>Scripts</d3p1:relativePath>
        </ProjectItem>
        <ProjectItem
          i:type="d3p1:ShaderSourceResourceFileDirectory">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">Shaders</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6444956Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>Shaders</SerializedName>
          <children />
          <dataSource
            i:nil="true" />
          <isHidden>false</isHidden>
          <isReadOnly>false</isReadOnly>
          <d3p1:extensions xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
            <d6p1:string>.vert</d6p1:string>
            <d6p1:string>.frag</d6p1:string>
            <d6p1:string>.glsl</d6p1:string>
          </d3p1:extensions>
          <d3p1:fileSystemNameOverride
            i:nil="true" />
          <d3p1:relativePath>Shaders</d3p1:relativePath>
        </ProjectItem>
        <ProjectItem
          i:type="d3p1:FontResourceFileDirectory">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">Fonts</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6444956Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>Fonts</SerializedName>
          <children />
          <dataSource
            i:nil="true" />
          <isHidden>false</isHidden>
          <isReadOnly>false</isReadOnly>
          <d3p1:extensions xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
            <d6p1:string>.ttf</d6p1:string>
            <d6p1:string>.fnt</d6p1:string>
            <d6p1:string>.png</d6p1:string>
            <d6p1:string>.jpg</d6p1:string>
            <d6p1:string>.jpeg</d6p1:string>
            <d6p1:string>.otf</d6p1:string>
            <d6p1:string>.LTT</d6p1:string>
          </d3p1:extensions>
          <d3p1:fileSystemNameOverride
            i:nil="true" />
          <d3p1:relativePath>Fonts</d3p1:relativePath>
        </ProjectItem>
        <ProjectItem
          i:type="d3p1:SourceAssetResourceFileDirectory">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">SourceAssets</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6444956Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>SourceAssets</SerializedName>
          <children />
          <dataSource
            i:nil="true" />
          <isHidden>false</isHidden>
          <isReadOnly>false</isReadOnly>
          <d3p1:extensions xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays"
            i:nil="true" />
          <d3p1:fileSystemNameOverride
            i:nil="true" />
          <d3p1:relativePath>SourceAssets</d3p1:relativePath>
        </ProjectItem>
        <ProjectItem
          i:type="d3p1:GenericResourceFileDirectory">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">Generic</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6444956Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>Generic</SerializedName>
          <children />
          <dataSource
            i:nil="true" />
          <isHidden>false</isHidden>
          <isReadOnly>false</isReadOnly>
          <d3p1:extensions xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays"
            i:nil="true" />
          <d3p1:fileSystemNameOverride
            i:nil="true" />
          <d3p1:relativePath>Generic</d3p1:relativePath>
        </ProjectItem>
        <ProjectItem xmlns:d5p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.Application"
          i:type="d5p1:ApplicationConfigurationResourceFileDirectory">
          <properties xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>Name</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly>true</isReadOnly>
                <propertyTypeReference>Name</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:string">Applications</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
            <d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              <d6p1:Key>CreationTime</d6p1:Key>
              <d6p1:Value>
                <isHidden
                  i:nil="true" />
                <isReadOnly
                  i:nil="true" />
                <propertyTypeReference>CreationTime</propertyTypeReference>
                <value xmlns:d9p1="http://www.w3.org/2001/XMLSchema"
                  i:type="d9p1:dateTime">2024-04-11T09:50:43.6444956Z</value>
              </d6p1:Value>
            </d6p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          </properties>
          <SerializedName>Applications</SerializedName>
          <children>
            <ProjectItem
              i:type="d5p1:DefaultApplicationConfiguration">
              <properties xmlns:d8p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Name</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly>true</isReadOnly>
                    <propertyTypeReference>Name</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:string">Default Preview (GL, Visual Studio 2013 Debug)</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>CreationTime</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>CreationTime</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:dateTime">2024-04-11T09:50:43.645494Z</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              </properties>
              <SerializedName>Default Preview (GL, Visual Studio 2013 Debug)</SerializedName>
              <children />
              <dataSource
                i:nil="true" />
              <isHidden>true</isHidden>
              <isReadOnly>false</isReadOnly>
              <d5p1:targetApi>GL21</d5p1:targetApi>
            </ProjectItem>
            <ProjectItem
              i:type="d5p1:DefaultApplicationConfiguration">
              <properties xmlns:d8p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Name</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly>true</isReadOnly>
                    <propertyTypeReference>Name</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:string">Default Preview (GL, Visual Studio 2013 Release)</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>CreationTime</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>CreationTime</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:dateTime">2024-04-11T09:50:43.645494Z</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              </properties>
              <SerializedName>Default Preview (GL, Visual Studio 2013 Release)</SerializedName>
              <children />
              <dataSource
                i:nil="true" />
              <isHidden>true</isHidden>
              <isReadOnly>false</isReadOnly>
              <d5p1:targetApi>GL21</d5p1:targetApi>
            </ProjectItem>
            <ProjectItem
              i:type="d5p1:DefaultApplicationConfiguration">
              <properties xmlns:d8p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Name</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly>true</isReadOnly>
                    <propertyTypeReference>Name</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:string">Default Preview (GL, Visual Studio 2015 Debug)</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>CreationTime</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>CreationTime</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:dateTime">2024-04-11T09:50:43.645494Z</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              </properties>
              <SerializedName>Default Preview (GL, Visual Studio 2015 Debug)</SerializedName>
              <children />
              <dataSource
                i:nil="true" />
              <isHidden>true</isHidden>
              <isReadOnly>false</isReadOnly>
              <d5p1:targetApi>GL21</d5p1:targetApi>
            </ProjectItem>
            <ProjectItem
              i:type="d5p1:DefaultApplicationConfiguration">
              <properties xmlns:d8p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>Name</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly>true</isReadOnly>
                    <propertyTypeReference>Name</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:string">Default Preview (GL, Visual Studio 2015 Release)</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                <d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
                  <d8p1:Key>CreationTime</d8p1:Key>
                  <d8p1:Value>
                    <isHidden
                      i:nil="true" />
                    <isReadOnly
                      i:nil="true" />
                    <propertyTypeReference>CreationTime</propertyTypeReference>
                    <value xmlns:d11p1="http://www.w3.org/2001/XMLSchema"
                      i:type="d11p1:dateTime">2024-04-11T09:50:43.645494Z</value>
                  </d8p1:Value>
                </d8p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
              </properties>
              <SerializedName>Default Preview (GL, Visual Studio 2015 Release)</SerializedName>
              <children />
              <dataSource
                i:nil="true" />
              <isHidden>true</isHidden>
              <isReadOnly>false</isReadOnly>
              <d5p1:targetApi>GL21</d5p1:targetApi>
            </ProjectItem>
          </children>
          <dataSource
            i:nil="true" />
          <isHidden>false</isHidden>
          <isReadOnly>false</isReadOnly>
          <d3p1:extensions xmlns:d6p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
          <d3p1:fileSystemNameOverride
            i:nil="true" />
          <d3p1:relativePath>Applications</d3p1:relativePath>
        </ProjectItem>
      </children>
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.TextureItems"
      i:type="d3p1:TextureLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Textures</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6464909Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Textures</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.Styles"
      i:type="d3p1:StyleLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Styles</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6464909Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Styles</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.StateManagers"
      i:type="d3p1:StateManagerLibrary">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">State Managers</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6464909Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>State Managers</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>false</isReadOnly>
    </ProjectItem>
    <ProjectItem xmlns:d3p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Project.PreviewToolsItems"
      i:type="d3p1:GuideGroupItem">
      <properties xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>Name</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly>true</isReadOnly>
            <propertyTypeReference>Name</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:string">Guides</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
        <d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
          <d4p1:Key>CreationTime</d4p1:Key>
          <d4p1:Value>
            <isHidden
              i:nil="true" />
            <isReadOnly
              i:nil="true" />
            <propertyTypeReference>CreationTime</propertyTypeReference>
            <value xmlns:d7p1="http://www.w3.org/2001/XMLSchema"
              i:type="d7p1:dateTime">2024-04-11T09:50:43.6464909Z</value>
          </d4p1:Value>
        </d4p1:KeyValueOfstringDynamicPropertyeuDbPpRS>
      </properties>
      <SerializedName>Guides</SerializedName>
      <children />
      <dataSource
        i:nil="true" />
      <isHidden>false</isHidden>
      <isReadOnly>true</isReadOnly>
    </ProjectItem>
  </children>
  <dataSource
    i:nil="true" />
  <isHidden>false</isHidden>
  <isReadOnly>false</isReadOnly>
  <FileFormatVersionString>Kanzi2:62,Kanzi3:66</FileFormatVersionString>
  <VirtualLayoutProjectSpecificData xmlns:d2p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Presentation.Base.VirtualLayouts"
    i:type="d2p1:VirtualLayoutSetProjectSpecificData">
    <d2p1:VirtualLayoutProjectSpecificData xmlns:d3p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
      <d3p1:KeyValueOfguidVirtualLayoutProjectSpecificDataw36UbEVW>
        <d3p1:Key>f774b42a-8e31-4ed7-96f7-74c3c2f8b342</d3p1:Key>
        <d3p1:Value>
          <d2p1:WindowProjectSpecificData>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>Project_Explorer</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base" xmlns:d8p2="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Presentation.Application.ProjectView"
                i:type="d8p2:ProjectExplorerState.ProjectDependentData">
                <d8p2:ViewModeDataItems>
                  <d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                    <d3p1:Key>FULL</d3p1:Key>
                    <d3p1:Value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.ApplicationCommon.Windows.WindowStates">
                      <d11p1:ExpandedNodePaths>
                        <d3p1:string>/Screens/Screen</d3p1:string>
                        <d3p1:string>/Screens/Screen/RootPage</d3p1:string>
                      </d11p1:ExpandedNodePaths>
                      <d11p1:RootSpecificDataItems>
                        <d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                          <d3p1:Key>/ClusterFunction/Screens</d3p1:Key>
                          <d3p1:Value>
                            <d3p1:string>/Screens</d3p1:string>
                            <d3p1:string>/Screens/Screen</d3p1:string>
                            <d3p1:string>/Screens/Screen/RootPage</d3p1:string>
                            <d3p1:string>/Screens/Screen/RootPage/DataLayer</d3p1:string>
                            <d3p1:string>/Screens/Screen/RootPage/Contents</d3p1:string>
                            <d3p1:string>/Screens/Screen/RootPage/Contents/BG</d3p1:string>
                            <d3p1:string>/Screens/Screen/RootPage/Contents/PowerOn</d3p1:string>
                            <d3p1:string>/Screens/Screen/RootPage/Contents/PowerOn/Menu</d3p1:string>
                            <d3p1:string>/Screens/Screen/RootPage/Contents/PowerOn/Warn</d3p1:string>
                          </d3p1:Value>
                        </d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                        <d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                          <d3p1:Key>/Gauge/Screens</d3p1:Key>
                          <d3p1:Value>
                            <d3p1:string>/Screens</d3p1:string>
                            <d3p1:string>/Screens/Screen</d3p1:string>
                            <d3p1:string>/Screens/Screen/RootPage</d3p1:string>
                          </d3p1:Value>
                        </d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                        <d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                          <d3p1:Key>/FixedDisplay/Screens</d3p1:Key>
                          <d3p1:Value>
                            <d3p1:string>/Screens</d3p1:string>
                            <d3p1:string>/Screens/Screen</d3p1:string>
                            <d3p1:string>/Screens/Screen/RootPage</d3p1:string>
                            <d3p1:string>/Screens/Screen/RootPage/FixDisRoot (FixDisRoot)</d3p1:string>
                          </d3p1:Value>
                        </d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                        <d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                          <d3p1:Key>/Menu/Screens</d3p1:Key>
                          <d3p1:Value>
                            <d3p1:string>/Screens</d3p1:string>
                            <d3p1:string>/Screens/Screen</d3p1:string>
                            <d3p1:string>/Screens/Screen/RootPage</d3p1:string>
                            <d3p1:string>/Screens/Screen/RootPage/MenuRoot (MenuRoot)</d3p1:string>
                          </d3p1:Value>
                        </d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                        <d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                          <d3p1:Key>/TripComputer/Screens</d3p1:Key>
                          <d3p1:Value>
                            <d3p1:string>/Screens/Screen</d3p1:string>
                            <d3p1:string>/Screens/Screen/RootPage</d3p1:string>
                          </d3p1:Value>
                        </d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                      </d11p1:RootSpecificDataItems>
                      <d11p1:ScrollPosition>0</d11p1:ScrollPosition>
                      <d11p1:SelectedNodePaths />
                    </d3p1:Value>
                  </d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                  <d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                    <d3p1:Key>LAYERS</d3p1:Key>
                    <d3p1:Value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.ApplicationCommon.Windows.WindowStates">
                      <d11p1:ExpandedNodePaths />
                      <d11p1:RootSpecificDataItems />
                      <d11p1:ScrollPosition>0</d11p1:ScrollPosition>
                      <d11p1:SelectedNodePaths />
                    </d3p1:Value>
                  </d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                </d8p2:ViewModeDataItems>
              </d3p1:Value>
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>Property_Editor</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base" xmlns:d8p2="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Presentation.Application.PropertyEditing"
                i:type="d8p2:PropertyEditorState.ProjectDependentData">
                <d8p2:ContentType>PROPERTIES</d8p2:ContentType>
                <d8p2:ReferencesToEditedItems />
                <d8p2:UseProjectItemSelectionAsSource>true</d8p2:UseProjectItemSelectionAsSource>
              </d3p1:Value>
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>Trigger_Editor</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base" xmlns:d8p2="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Presentation.Application.PropertyEditing"
                i:type="d8p2:PropertyEditorState.ProjectDependentData">
                <d8p2:ContentType>TRIGGERS</d8p2:ContentType>
                <d8p2:ReferencesToEditedItems />
                <d8p2:UseProjectItemSelectionAsSource>true</d8p2:UseProjectItemSelectionAsSource>
              </d3p1:Value>
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>Project_Explorer_Global_Resources</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base" xmlns:d8p2="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Presentation.Application.ProjectView"
                i:type="d8p2:ProjectExplorerState.ProjectDependentData">
                <d8p2:ViewModeDataItems>
                  <d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                    <d3p1:Key>RESOURCES</d3p1:Key>
                    <d3p1:Value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.ApplicationCommon.Windows.WindowStates">
                      <d11p1:ExpandedNodePaths>
                        <d3p1:string>/Resources objects</d3p1:string>
                        <d3p1:string>/Resources objects/Materials and Textures</d3p1:string>
                        <d3p1:string>/Resources objects/Materials and Textures/Materials</d3p1:string>
                        <d3p1:string>/Resources objects/Materials and Textures/Material Types</d3p1:string>
                        <d3p1:string>/Resources objects/Materials and Textures/Textures</d3p1:string>
                        <d3p1:string>/Resources objects/Materials and Textures/Brushes</d3p1:string>
                        <d3p1:string>/Resources objects/Page Transitions</d3p1:string>
                        <d3p1:string>/Resources objects/Project References</d3p1:string>
                        <d3p1:string>/Resources objects/Resource Files</d3p1:string>
                        <d3p1:string>/Resources objects/Resource Files/Images</d3p1:string>
                        <d3p1:string>/Resources objects/Styles</d3p1:string>
                      </d11p1:ExpandedNodePaths>
                      <d11p1:RootSpecificDataItems>
                        <d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                          <d3p1:Key>/ClusterFunction/Resources objects</d3p1:Key>
                          <d3p1:Value>
                            <d3p1:string>/Resources objects</d3p1:string>
                            <d3p1:string>/Resources objects/Kanzi Engine Plugins/LocalizationPlugin</d3p1:string>
                            <d3p1:string>/Resources objects/Kanzi Engine Plugins/LogicDataSource</d3p1:string>
                            <d3p1:string>/Resources objects/Localization</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures/Materials</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures/Material Types</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures/Textures</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures/Brushes</d3p1:string>
                            <d3p1:string>/Resources objects/Page Transitions</d3p1:string>
                            <d3p1:string>/Resources objects/Property Types</d3p1:string>
                            <d3p1:string>/Resources objects/Property Types/Cluster</d3p1:string>
                            <d3p1:string>/Resources objects/Rendering/Object Sources</d3p1:string>
                            <d3p1:string>/Resources objects/Resource Files/Images</d3p1:string>
                            <d3p1:string>/Resources objects/Resource Files/Shaders</d3p1:string>
                            <d3p1:string>/Resources objects/Styles</d3p1:string>
                            <d3p1:string>/Resources objects/Themes</d3p1:string>
                          </d3p1:Value>
                        </d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                        <d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                          <d3p1:Key>/Gauge/Resources objects</d3p1:Key>
                          <d3p1:Value>
                            <d3p1:string>/Resources objects</d3p1:string>
                            <d3p1:string>/Resources objects/Animations</d3p1:string>
                            <d3p1:string>/Resources objects/Animations/Animation Data</d3p1:string>
                            <d3p1:string>/Resources objects/Kanzi Engine Plugins</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures/Materials</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures/Material Types</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures/Textures</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures/Brushes</d3p1:string>
                            <d3p1:string>/Resources objects/Page Transitions</d3p1:string>
                            <d3p1:string>/Resources objects/Project References</d3p1:string>
                            <d3p1:string>/Resources objects/Property Types</d3p1:string>
                            <d3p1:string>/Resources objects/Resource Files</d3p1:string>
                            <d3p1:string>/Resources objects/Resource Files/Images</d3p1:string>
                            <d3p1:string>/Resources objects/Resource Files/Shaders</d3p1:string>
                            <d3p1:string>/Resources objects/State Managers</d3p1:string>
                            <d3p1:string>/Resources objects/Styles</d3p1:string>
                            <d3p1:string>/Resources objects/Tags</d3p1:string>
                            <d3p1:string>/Resources objects/Themes</d3p1:string>
                            <d3p1:string>/Resources objects/Trajectories</d3p1:string>
                          </d3p1:Value>
                        </d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                        <d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                          <d3p1:Key>/FixedDisplay/Resources objects</d3p1:Key>
                          <d3p1:Value>
                            <d3p1:string>/Resources objects</d3p1:string>
                            <d3p1:string>/Resources objects/Animations</d3p1:string>
                            <d3p1:string>/Resources objects/Kanzi Engine Plugins</d3p1:string>
                            <d3p1:string>/Resources objects/Kanzi Engine Plugins/LocalizationPlugin</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures/Materials</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures/Material Types</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures/Brushes</d3p1:string>
                            <d3p1:string>/Resources objects/Page Transitions</d3p1:string>
                            <d3p1:string>/Resources objects/Project References</d3p1:string>
                            <d3p1:string>/Resources objects/Property Types</d3p1:string>
                            <d3p1:string>/Resources objects/Resource Files</d3p1:string>
                            <d3p1:string>/Resources objects/Resource Files/Images</d3p1:string>
                            <d3p1:string>/Resources objects/Resource Files/Shaders</d3p1:string>
                            <d3p1:string>/Resources objects/Styles</d3p1:string>
                            <d3p1:string>/Resources objects/Themes</d3p1:string>
                            <d3p1:string>/Resources objects/Trajectories</d3p1:string>
                          </d3p1:Value>
                        </d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                        <d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                          <d3p1:Key>/Menu/Resources objects</d3p1:Key>
                          <d3p1:Value>
                            <d3p1:string>/Resources objects</d3p1:string>
                            <d3p1:string>/Resources objects/Animations</d3p1:string>
                            <d3p1:string>/Resources objects/Animations/Animation Clips</d3p1:string>
                            <d3p1:string>/Resources objects/Animations/Animation Data</d3p1:string>
                            <d3p1:string>/Resources objects/Kanzi Engine Plugins</d3p1:string>
                            <d3p1:string>/Resources objects/Kanzi Engine Plugins/LocalizationPlugin</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures/Materials</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures/Material Types</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures/Textures</d3p1:string>
                            <d3p1:string>/Resources objects/Page Transitions</d3p1:string>
                            <d3p1:string>/Resources objects/Project References</d3p1:string>
                            <d3p1:string>/Resources objects/Property Types</d3p1:string>
                            <d3p1:string>/Resources objects/Resource Files</d3p1:string>
                            <d3p1:string>/Resources objects/Resource Files/Images</d3p1:string>
                            <d3p1:string>/Resources objects/State Managers</d3p1:string>
                            <d3p1:string>/Resources objects/Styles</d3p1:string>
                            <d3p1:string>/Resources objects/Themes</d3p1:string>
                            <d3p1:string>/Resources objects/Trajectories</d3p1:string>
                          </d3p1:Value>
                        </d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                        <d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                          <d3p1:Key>/TripComputer/Resources objects</d3p1:Key>
                          <d3p1:Value>
                            <d3p1:string>/Resources objects</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures/Materials</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures/Material Types</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures/Textures</d3p1:string>
                            <d3p1:string>/Resources objects/Materials and Textures/Brushes</d3p1:string>
                            <d3p1:string>/Resources objects/Page Transitions</d3p1:string>
                            <d3p1:string>/Resources objects/Project References</d3p1:string>
                            <d3p1:string>/Resources objects/Resource Files</d3p1:string>
                            <d3p1:string>/Resources objects/Resource Files/Images</d3p1:string>
                            <d3p1:string>/Resources objects/Styles</d3p1:string>
                          </d3p1:Value>
                        </d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                      </d11p1:RootSpecificDataItems>
                      <d11p1:ScrollPosition>0</d11p1:ScrollPosition>
                      <d11p1:SelectedNodePaths>
                        <d3p1:string>/Resources objects/Project References/Resource</d3p1:string>
                      </d11p1:SelectedNodePaths>
                    </d3p1:Value>
                  </d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                </d8p2:ViewModeDataItems>
              </d3p1:Value>
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>Dictionaries</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base"
                i:nil="true" />
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>Project_Explorer_Materials</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base" xmlns:d8p2="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Presentation.Application.ProjectView"
                i:type="d8p2:ProjectExplorerState.ProjectDependentData">
                <d8p2:ViewModeDataItems>
                  <d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                    <d3p1:Key>MATERIALS</d3p1:Key>
                    <d3p1:Value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.ApplicationCommon.Windows.WindowStates">
                      <d11p1:ExpandedNodePaths />
                      <d11p1:RootSpecificDataItems />
                      <d11p1:ScrollPosition>0</d11p1:ScrollPosition>
                      <d11p1:SelectedNodePaths />
                    </d3p1:Value>
                  </d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                </d8p2:ViewModeDataItems>
              </d3p1:Value>
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>Project_Explorer_Textures</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base" xmlns:d8p2="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Presentation.Application.ProjectView"
                i:type="d8p2:ProjectExplorerState.ProjectDependentData">
                <d8p2:ViewModeDataItems>
                  <d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                    <d3p1:Key>TEXTURES</d3p1:Key>
                    <d3p1:Value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.ApplicationCommon.Windows.WindowStates">
                      <d11p1:ExpandedNodePaths />
                      <d11p1:RootSpecificDataItems />
                      <d11p1:ScrollPosition>0</d11p1:ScrollPosition>
                      <d11p1:SelectedNodePaths />
                    </d3p1:Value>
                  </d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                </d8p2:ViewModeDataItems>
              </d3p1:Value>
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>Project_Explorer_State_Managers</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base" xmlns:d8p2="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Presentation.Application.ProjectView"
                i:type="d8p2:ProjectExplorerState.ProjectDependentData">
                <d8p2:ViewModeDataItems>
                  <d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                    <d3p1:Key>STATE_MANAGERS</d3p1:Key>
                    <d3p1:Value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.ApplicationCommon.Windows.WindowStates">
                      <d11p1:ExpandedNodePaths />
                      <d11p1:RootSpecificDataItems />
                      <d11p1:ScrollPosition>0</d11p1:ScrollPosition>
                      <d11p1:SelectedNodePaths />
                    </d3p1:Value>
                  </d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                </d8p2:ViewModeDataItems>
              </d3p1:Value>
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>Project_Explorer_Prefabs</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base" xmlns:d8p2="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Presentation.Application.ProjectView"
                i:type="d8p2:ProjectExplorerState.ProjectDependentData">
                <d8p2:ViewModeDataItems>
                  <d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                    <d3p1:Key>PREFABS</d3p1:Key>
                    <d3p1:Value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.ApplicationCommon.Windows.WindowStates">
                      <d11p1:ExpandedNodePaths />
                      <d11p1:RootSpecificDataItems>
                        <d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                          <d3p1:Key>/ClusterFunction/Prefabs</d3p1:Key>
                          <d3p1:Value>
                            <d3p1:string>/Prefabs</d3p1:string>
                            <d3p1:string>/Prefabs/ADASTT</d3p1:string>
                            <d3p1:string>/Prefabs/BgRoot</d3p1:string>
                            <d3p1:string>/Prefabs/Charge</d3p1:string>
                            <d3p1:string>/Prefabs/FixDis</d3p1:string>
                            <d3p1:string>/Prefabs/Gauge</d3p1:string>
                            <d3p1:string>/Prefabs/Menu</d3p1:string>
                            <d3p1:string>/Prefabs/Warning</d3p1:string>
                          </d3p1:Value>
                        </d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                        <d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                          <d3p1:Key>/Gauge/Prefabs</d3p1:Key>
                          <d3p1:Value>
                            <d3p1:string>/Prefabs</d3p1:string>
                            <d3p1:string>/Prefabs/GaugeRoot</d3p1:string>
                          </d3p1:Value>
                        </d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                        <d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                          <d3p1:Key>/FixedDisplay/Prefabs</d3p1:Key>
                          <d3p1:Value>
                            <d3p1:string>/Prefabs</d3p1:string>
                            <d3p1:string>/Prefabs/FixDisRoot</d3p1:string>
                          </d3p1:Value>
                        </d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                        <d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                          <d3p1:Key>/Menu/Prefabs</d3p1:Key>
                          <d3p1:Value>
                            <d3p1:string>/Prefabs</d3p1:string>
                            <d3p1:string>/Prefabs/MenuRoot</d3p1:string>
                          </d3p1:Value>
                        </d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                        <d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                          <d3p1:Key>/TripComputer/Prefabs</d3p1:Key>
                          <d3p1:Value />
                        </d3p1:KeyValueOfstringArrayOfstringty7Ep6D1>
                      </d11p1:RootSpecificDataItems>
                      <d11p1:ScrollPosition>0</d11p1:ScrollPosition>
                      <d11p1:SelectedNodePaths />
                    </d3p1:Value>
                  </d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                </d8p2:ViewModeDataItems>
              </d3p1:Value>
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>Project_Explorer_Animations</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base" xmlns:d8p2="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Presentation.Application.ProjectView"
                i:type="d8p2:ProjectExplorerState.ProjectDependentData">
                <d8p2:ViewModeDataItems>
                  <d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                    <d3p1:Key>ANIMATIONS</d3p1:Key>
                    <d3p1:Value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.ApplicationCommon.Windows.WindowStates">
                      <d11p1:ExpandedNodePaths />
                      <d11p1:RootSpecificDataItems />
                      <d11p1:ScrollPosition>0</d11p1:ScrollPosition>
                      <d11p1:SelectedNodePaths />
                    </d3p1:Value>
                  </d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                </d8p2:ViewModeDataItems>
              </d3p1:Value>
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>Project_Explorer_Composing</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base" xmlns:d8p2="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Presentation.Application.ProjectView"
                i:type="d8p2:ProjectExplorerState.ProjectDependentData">
                <d8p2:ViewModeDataItems>
                  <d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                    <d3p1:Key>COMPOSING</d3p1:Key>
                    <d3p1:Value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.ApplicationCommon.Windows.WindowStates">
                      <d11p1:ExpandedNodePaths />
                      <d11p1:RootSpecificDataItems />
                      <d11p1:ScrollPosition>0</d11p1:ScrollPosition>
                      <d11p1:SelectedNodePaths />
                    </d3p1:Value>
                  </d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                </d8p2:ViewModeDataItems>
              </d3p1:Value>
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>Project_Explorer_Resource_Files</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base" xmlns:d8p2="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Presentation.Application.ProjectView"
                i:type="d8p2:ProjectExplorerState.ProjectDependentData">
                <d8p2:ViewModeDataItems>
                  <d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                    <d3p1:Key>RESOURCE_FILES</d3p1:Key>
                    <d3p1:Value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.ApplicationCommon.Windows.WindowStates">
                      <d11p1:ExpandedNodePaths />
                      <d11p1:RootSpecificDataItems />
                      <d11p1:ScrollPosition>0</d11p1:ScrollPosition>
                      <d11p1:SelectedNodePaths />
                    </d3p1:Value>
                  </d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                </d8p2:ViewModeDataItems>
              </d3p1:Value>
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>Project_Explorer_Component_Types</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base" xmlns:d8p2="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Presentation.Application.ProjectView"
                i:type="d8p2:ProjectExplorerState.ProjectDependentData">
                <d8p2:ViewModeDataItems>
                  <d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                    <d3p1:Key>USER_INTERFACE</d3p1:Key>
                    <d3p1:Value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.ApplicationCommon.Windows.WindowStates">
                      <d11p1:ExpandedNodePaths />
                      <d11p1:RootSpecificDataItems />
                      <d11p1:ScrollPosition>0</d11p1:ScrollPosition>
                      <d11p1:SelectedNodePaths />
                    </d3p1:Value>
                  </d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                </d8p2:ViewModeDataItems>
              </d3p1:Value>
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>Project_Explorer_Bookmarks</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base" xmlns:d8p2="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Presentation.Application.ProjectView"
                i:type="d8p2:ProjectExplorerState.ProjectDependentData">
                <d8p2:ViewModeDataItems>
                  <d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                    <d3p1:Key>SHORTCUTS</d3p1:Key>
                    <d3p1:Value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.ApplicationCommon.Windows.WindowStates">
                      <d11p1:ExpandedNodePaths />
                      <d11p1:RootSpecificDataItems />
                      <d11p1:ScrollPosition>0</d11p1:ScrollPosition>
                      <d11p1:SelectedNodePaths />
                    </d3p1:Value>
                  </d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                </d8p2:ViewModeDataItems>
              </d3p1:Value>
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>Project_Explorer_Property_Types</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base" xmlns:d8p2="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Presentation.Application.ProjectView"
                i:type="d8p2:ProjectExplorerState.ProjectDependentData">
                <d8p2:ViewModeDataItems>
                  <d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                    <d3p1:Key>PROPERTY_TYPES</d3p1:Key>
                    <d3p1:Value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.ApplicationCommon.Windows.WindowStates">
                      <d11p1:ExpandedNodePaths />
                      <d11p1:RootSpecificDataItems />
                      <d11p1:ScrollPosition>0</d11p1:ScrollPosition>
                      <d11p1:SelectedNodePaths />
                    </d3p1:Value>
                  </d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                </d8p2:ViewModeDataItems>
              </d3p1:Value>
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>Project_Explorer_Meshes</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base" xmlns:d8p2="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Presentation.Application.ProjectView"
                i:type="d8p2:ProjectExplorerState.ProjectDependentData">
                <d8p2:ViewModeDataItems>
                  <d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                    <d3p1:Key>MESHES</d3p1:Key>
                    <d3p1:Value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.ApplicationCommon.Windows.WindowStates">
                      <d11p1:ExpandedNodePaths />
                      <d11p1:RootSpecificDataItems />
                      <d11p1:ScrollPosition>0</d11p1:ScrollPosition>
                      <d11p1:SelectedNodePaths />
                    </d3p1:Value>
                  </d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                </d8p2:ViewModeDataItems>
              </d3p1:Value>
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>Project_Explorer_Trajectories</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base" xmlns:d8p2="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Presentation.Application.ProjectView"
                i:type="d8p2:ProjectExplorerState.ProjectDependentData">
                <d8p2:ViewModeDataItems>
                  <d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                    <d3p1:Key>TRAJECTORIES</d3p1:Key>
                    <d3p1:Value xmlns:d11p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.ApplicationCommon.Windows.WindowStates">
                      <d11p1:ExpandedNodePaths />
                      <d11p1:RootSpecificDataItems />
                      <d11p1:ScrollPosition>0</d11p1:ScrollPosition>
                      <d11p1:SelectedNodePaths />
                    </d3p1:Value>
                  </d3p1:KeyValueOfProjectExplorerViewModeProjectExplorerState.ProjectDependentData.ViewModeDataZii17Iwv>
                </d8p2:ViewModeDataItems>
              </d3p1:Value>
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>Assets</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base" xmlns:d8p2="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Presentation.Application.Assets"
                i:type="d8p2:AssetsWindowState.ProjectSpecificData">
                <d8p2:FilteredTypeNames />
                <d8p2:SearchText
                  i:nil="true" />
                <d8p2:ShowFactoryContent>false</d8p2:ShowFactoryContent>
              </d3p1:Value>
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>PageHierarchyEditor</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base" xmlns:d8p2="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.ApplicationCommon.Plugin"
                i:type="d8p2:PluginWindowHostState.ProjectDependentData">
                <d8p2:TypeName>Rightware.Kanzi.Studio.Plugins.PageEditor.Hierarchy.HierarchyPluginWindowState, PageEditor, Version=*******, Culture=neutral, PublicKeyToken=null</d8p2:TypeName>
                <d8p2:XMLData>&lt;?xml version="1.0" encoding="utf-16"?&gt;
&lt;HierarchyPluginWindowState xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/PageEditor.Hierarchy"&gt;
  &lt;editedItemPaths xmlns:d2p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Studio.PluginInterface" /&gt;
  &lt;Settings xmlns:d2p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Studio.Plugins.PageEditor.Hierarchy"&gt;
    &lt;d2p1:isAutoLayoutEnabled&gt;true&lt;/d2p1:isAutoLayoutEnabled&gt;
    &lt;d2p1:isLayoutsVisualized&gt;false&lt;/d2p1:isLayoutsVisualized&gt;
    &lt;d2p1:isPlaceholdersVisualized&gt;true&lt;/d2p1:isPlaceholdersVisualized&gt;
    &lt;d2p1:isTransitionEditorEnabled&gt;false&lt;/d2p1:isTransitionEditorEnabled&gt;
  &lt;/Settings&gt;
  &lt;Viewports xmlns:d2p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Studio.Plugins.PageEditor.Hierarchy"&gt;
    &lt;d2p1:HierarchyControl.SerializedState&gt;
      &lt;d2p1:Position xmlns:d4p1="http://schemas.datacontract.org/2004/07/System.Windows"&gt;
        &lt;d4p1:_x&gt;0&lt;/d4p1:_x&gt;
        &lt;d4p1:_y&gt;0&lt;/d4p1:_y&gt;
      &lt;/d2p1:Position&gt;
      &lt;d2p1:ProjectItem&gt;Screens/Screen&lt;/d2p1:ProjectItem&gt;
      &lt;d2p1:RootItemPath&gt;&lt;/d2p1:RootItemPath&gt;
      &lt;d2p1:RootItemPosition xmlns:d4p1="http://schemas.datacontract.org/2004/07/System.Windows"&gt;
        &lt;d4p1:_x&gt;0&lt;/d4p1:_x&gt;
        &lt;d4p1:_y&gt;0&lt;/d4p1:_y&gt;
      &lt;/d2p1:RootItemPosition&gt;
      &lt;d2p1:Scale xmlns:d4p1="http://schemas.datacontract.org/2004/07/System.Windows"&gt;
        &lt;d4p1:_x&gt;1&lt;/d4p1:_x&gt;
        &lt;d4p1:_y&gt;1&lt;/d4p1:_y&gt;
      &lt;/d2p1:Scale&gt;
    &lt;/d2p1:HierarchyControl.SerializedState&gt;
  &lt;/Viewports&gt;
&lt;/HierarchyPluginWindowState&gt;</d8p2:XMLData>
              </d3p1:Value>
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>StateToolsEditor</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base" xmlns:d8p2="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.ApplicationCommon.Plugin"
                i:type="d8p2:PluginWindowHostState.ProjectDependentData">
                <d8p2:TypeName>Rightware.Kanzi.Studio.PluginInterface.PluginWindowState, PluginInterface, Version=*******, Culture=neutral, PublicKeyToken=null</d8p2:TypeName>
                <d8p2:XMLData>&lt;?xml version="1.0" encoding="utf-16"?&gt;
&lt;PluginWindowState xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Studio.PluginInterface"&gt;
  &lt;editedItemPaths xmlns:d2p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" /&gt;
&lt;/PluginWindowState&gt;</d8p2:XMLData>
              </d3p1:Value>
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
            <d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
              <d3p1:Key>DataSources</d3p1:Key>
              <d3p1:Value xmlns:d8p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Base" xmlns:d8p2="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.ApplicationCommon.Plugin"
                i:type="d8p2:PluginWindowHostState.ProjectDependentData">
                <d8p2:TypeName>Rightware.Kanzi.Studio.PluginInterface.PluginWindowState, PluginInterface, Version=*******, Culture=neutral, PublicKeyToken=null</d8p2:TypeName>
                <d8p2:XMLData>&lt;?xml version="1.0" encoding="utf-16"?&gt;
&lt;PluginWindowState xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Studio.PluginInterface"&gt;
  &lt;editedItemPaths xmlns:d2p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" /&gt;
&lt;/PluginWindowState&gt;</d8p2:XMLData>
              </d3p1:Value>
            </d3p1:KeyValueOfstringSerializableObjectEu9UlzsU>
          </d2p1:WindowProjectSpecificData>
        </d3p1:Value>
      </d3p1:KeyValueOfguidVirtualLayoutProjectSpecificDataw36UbEVW>
    </d2p1:VirtualLayoutProjectSpecificData>
  </VirtualLayoutProjectSpecificData>
  <compositionManager>
    <compositions>
      <CompositionViewModel>
        <currentlyActive>true</currentlyActive>
        <projectItemReference>
          <ContentType>ABSOLUTE</ContentType>
          <IsResourceReference>false</IsResourceReference>
          <referredItemPath>
            <pathString>TripComputer/Screens/Screen/</pathString>
          </referredItemPath>
          <textContent
            i:nil="true" />
        </projectItemReference>
      </CompositionViewModel>
    </compositions>
  </compositionManager>
  <fileFormatVersion>62</fileFormatVersion>
  <isNewProject>false</isNewProject>
  <projectsPropertyTypeLibrary xmlns:d2p1="http://schemas.datacontract.org/2004/07/Rightware.Kanzi.Tool.Logic.Properties">
    <d2p1:propertyTypes xmlns:d3p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
  </projectsPropertyTypeLibrary>
  <version>*********</version>
</Project>