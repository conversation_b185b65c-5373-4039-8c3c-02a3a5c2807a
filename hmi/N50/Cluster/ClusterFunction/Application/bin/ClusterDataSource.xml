<?xml version="1.0"?>
<Cluster>
    <PowerMode type="int">0</PowerMode>
    <DayNightMode type="int">0</DayNightMode>
    <CarTypeCfg type="int">0</CarTypeCfg> <!-- 车型配置：0：N50 1：N51 -->
    <LanguageSet type="int">0</LanguageSet> <!-- 语言设置：0：汉语 1：英语 -->
    <DriverType type="int">0</DriverType> <!-- 驱动类型：0：EV纯电 1：增程 -->
    <FullScreenSts type="int">0</FullScreenSts>
	<CarCfgCharging type="int">0</CarCfgCharging> <!-- 充电口开启：0：电动充电口 1：手动充电口 -->
</Cluster>
<Gauge>
    <EVDTEValue type="string"></EVDTEValue>
    <EVDTEValueValid type="bool">false</EVDTEValueValid>
    <EVDTEDispType type="int">0</EVDTEDispType>
    <SpeedValue type="int">0</SpeedValue>
    <SpeedValid type="bool">false</SpeedValid>
    <PowerValue type="int">0</PowerValue>
    <GearType type="int">0</GearType>
    <BatteryColor type="int">0</BatteryColor>
    <FuelPercent type="int">0</FuelPercent>
    <DTEValueValid type="int">0</DTEValueValid>
    <DTEValue type="string"></DTEValue>
    <DriveMode type="int">0</DriveMode>
    <PowerPercent type="int">0</PowerPercent>
    <DrivePowerLimit type="int">100</DrivePowerLimit>
    <EnergyFeedLimit type="int">100</EnergyFeedLimit>
    <PullMode type="int">0</PullMode>
    <EnergyMode type="int">0</EnergyMode>
    <BatteryTextType type="int">0</BatteryTextType>
    <FuelLight type="int">0</FuelLight>
</Gauge>

<ETC>

</ETC>

<Menu>
    <IVIDisplaySts type="int">0</IVIDisplaySts> <!-- IVI中控屏能力状态：0: 中控尚未具备中控屏的显示能力 1: 中控具备中控屏的显示能力 -->
    <DoorLockSts type="int">0</DoorLockSts>
    <DoorLockGray type="int">0</DoorLockGray> <!-- 按钮置灰：0：未置灰 1：置灰 -->
    <ChgPortSts type="int">0</ChgPortSts>
    <ChgPortGray type="int">0</ChgPortGray>
    <TrunkSts type="int">0</TrunkSts>
    <TrunkGray type="int">0</TrunkGray>
    <RearMirSts type="int">0</RearMirSts>
    <RearMirGray type="int">0</RearMirGray>
    <FuelPortSts type="int">0</FuelPortSts>
    <FuelPortGray type="int">0</FuelPortGray>
	<TranslationAniCtrl type="int">0</TranslationAniCtrl> <!-- 转场动画 1：P到D档 2：D到P档 -->
</Menu>
<FixedInfo>

</FixedInfo>

<TripComputer>

</TripComputer>

<Warning>
    <DisplayWarnID type="int">0</DisplayWarnID>
    <FLDoorState type="int">0</FLDoorState>
    <FRDoorState type="int">0</FRDoorState>
    <RLDoorState type="int">0</RLDoorState>
    <RRDoorState type="int">0</RRDoorState>
    <HoodState type="int">0</HoodState>
    <TrunkState type="int">0</TrunkState>
    <FLSafetyBeltState type="int">0</FLSafetyBeltState>
    <FRSafetyBeltState type="int">0</FRSafetyBeltState>
    <RLSafetyBeltState type="int">0</RLSafetyBeltState>
    <RMSafetyBeltState type="int">0</RMSafetyBeltState>
    <RRSafetyBeltState type="int">0</RRSafetyBeltState>
	<NoaOddDist type="string"></NoaOddDist>
</Warning>

<ADAS>
    <SLASpeedLight type="int">0</SLASpeedLight>
    <ACCLightSts type="int">0</ACCLightSts>
    <ACCLightValue type="int">0</ACCLightValue>
	<LowBeamLight type="int">0</LowBeamLight>
	<HighBeamLight type="int">0</HighBeamLight>
	<LeftTurnLight type="int">0</LeftTurnLight>
	<RightTurnLight type="int">0</RightTurnLight>
	<PositionLight type="int">0</PositionLight>
	<BrkLampSts type="int">0</BrkLampSts>
	<RearFogLight type="int">0</RearFogLight>
	<RvsLampSts type="int">0</RvsLampSts>
</ADAS>

<Charge>
    <ChargeState type="int">0</ChargeState>
    <ChargeCurrent type="string">--</ChargeCurrent>
    <ChargePower type="string">--</ChargePower>
	<ChargePowerValid type="int">0</ChargePowerValid>
	<ChargeCurrentValid type="int">0</ChargeCurrentValid>
    <ChargeRemainTimeHour type="int">0</ChargeRemainTimeHour>
    <ChargeRemainTimeMin type="int">0</ChargeRemainTimeMin>
    <ChargeRemainTimeVisible type="bool">0</ChargeRemainTimeVisible>
    <BookChargeStartTime type="string">--:--</BookChargeStartTime>
    <BookChargeEndTime type="string">--:--</BookChargeEndTime>
    <BookChargeStartVisible type="bool">false</BookChargeStartVisible>
    <BookChargeEndVisible type="bool">false</BookChargeEndVisible>
    <ChargeTimeFormat  type="bool">false</ChargeTimeFormat>
    <ChargeFreq type="int">0</ChargeFreq>
    <BookChargeEndDay type="int">0</BookChargeEndDay>
    <ChargeEndTimeFormat  type="bool">false</ChargeEndTimeFormat>
    <ChargeStartTimeFormat  type="bool">false</ChargeStartTimeFormat>
</Charge>

<BackGround>
    
</BackGround>

