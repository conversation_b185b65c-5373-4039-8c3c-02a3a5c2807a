#!/bin/sh
PROJECT_PATH=/apps
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:$PROJECT_PATH/lib/
file_path="data.txt"

if [ ! -f "$file_path" ]; then
    echo "文件不存在：$file_path"
    exit 1
fi

while IFS= read -r line; do
    if [ -n "$line" ]; then
        name=$(echo $line | cut -d',' -f1)
        value=$(echo $line | cut -d',' -f2)
        #echo "First part: $name" "Second part: $value"
        ./carpropmgrdbgr -s -n "$name" -v "$value"
        sleep 3
    fi
done < "$file_path"