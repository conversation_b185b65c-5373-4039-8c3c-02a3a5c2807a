syntax = "proto2";
option optimize_for = LITE_RUNTIME;

package autolink;

enum PowerMode {
    PWR_MODE_NONE = 0;
    PWR_MODE_STR = 1;
    PWR_MODE_STANDBY = 2;
    PWR_MODE_RUN = 3;
    PWR_MODE_SLEEP = 4;
    PWR_MODE_ABNORMAL = 5;
    PWR_MODE_TEMP_RUN = 6;
    PWR_MODE_CHARGGING = 7;
}

enum PowerMessageId {
    MSG_POWERSERVICE_TO_GATEWAY  = 0;//车联powerhal往卓驭进程发送fdbus指令
    MSG_GATEWAY_TO_POWERSERVICE  = 1;//卓驭进程往车联powerhal发送fdbus指令
    MSG_POWERSERVICE_TO_AUTOLINK = 2;//车联powerhal往车联内部进程发送fdbus指令
    MSG_AUTOLINK_TO_POWERSERVICE = 3;//车联内部进程往powerhal往发送fdbus指令
    MSG_POWERSERVICE_TO_ALL      = 4;//车联powerhal往所有client端发送指令
}

enum PowerEventId {
    PWR_SOC_REQ_POWERON_REASON_EVENT      = 0;//Soc请求获取开机原因
    PWR_MCU_RESP_POWERON_REASON_EVENT     = 1;//MCU返回开机原因
    PWR_SOC_HEART_BEAT_SWITCH_EVENT       = 2;//Soc请求打开或者关闭心跳
    PWR_MCU_HEART_BEAT_SWITCH_RESP_EVENT  = 3;//MCU收到Soc心跳开关操作的回应
    PWR_SOC_HEART_BEAT_EVENT              = 4;//Soc给MCU定时发送心跳信号
    PWR_SOC_POWER_READY_EVENT             = 5;//Soc请求进行通信握手
    PWR_MCU_POWER_READY_RESP_EVENT        = 6;//MCU收到Soc的 PWR_SOC_POWER_READY_EVENT 后回复
    PWR_SOC_POWER_READY_SUCC_EVENT        = 7;//Soc收到 PWR_MCU_POWER_READY_RESP_EVENT 回复，完成上电握手流程
    PWR_SOC_REQ_VOLTAGE_EVENT             = 8;//Soc请求获取当前电压值
    PWR_MCU_RESP_VOLTAGE_EVENT            = 9;//MCU返回当前电压值
    PWR_SOC_REQ_POWER_MODE_EVENT          = 10;//Soc请求获取当前电源模式
    PWR_MCU_RESP_POWER_MODE_EVENT         = 11;//MCU返回当前电源模式
    PWR_SOC_POWER_MODE_REPLY_EVENT        = 12;//Soc收到MCU电源模式进行反馈握手
    PWR_SOC_REQ_RESET_SOC_EVENT           = 13;//Soc请求MCU重启Soc
    PWR_MCU_REQ_RESET_EVENT               = 14;//mcu要求Soc重启
    PWR_MCU_POWER_ON_EVENT                = 15;//MCU告知Soc已经开机
    PWR_SOC_RESP_POWER_OFF_EVENT          = 16;//Soc收到 PWR_MCU_REQ_POWER_OFF_EVENT 后答复握手
    PWR_MCU_REQ_POWER_OFF_EVENT           = 17;//MCU通知Soc开始下电流程
    PWR_SOC_RESP_POWER_OFF_NOW_EVENT      = 18;//Soc回复 PWR_SOC_RESP_POWER_OFF_EVENT 后，准备好第一阶段关机流程，再回复此信号
    PWR_SOC_REQ_NETWORK_KEEP_EVENT        = 19;//Soc请求保持CAN网络
    PWR_SOC_SET_TEMPRUN_TIME_EVENT        = 20;//Soc向MCU设置临时模式时间
    PWR_SOC_REENTER_TEMPRUN_MODE_EVENT    = 21;//Soc请求MCU重新进入临时模式
    PWR_MCU_TEMPRUN_TIMEOUT_EVENT         = 22;//MCU通知Soc临时模式超时
    PWR_MCU_REPLY_HEART_BEAT_EVENT        = 23;//MCU收到SOC心跳信号PWR_SOC_HEART_BEAT_EVENT回复
    PWR_MCU_REJECT_RESET_REASON_EVENT     = 24;//MCU告知拒绝Soc重启请求原因
    PWR_SOC_STR_FUNC_SWITCH_ONOFF_EVENT   = 25;//STR功能开关,每次开机由android告知 value=0关闭 value=开启
    PWR_SOC_REQ_POWER_STATE_EVENT         = 26;//Soc请求获取当前整车电源状态
    PWR_MCU_RESP_POWER_STATE_EVENT        = 27;//MCU返回当前整车电源状态      0: power-off 1:power-on
    PWR_SOC_POWER_STATE_REPLY_EVENT       = 28;//Soc收到MCU整车电源状态进行反馈握手

    PWR_INVALID_EVENT                     = 29;//无效ID
}

message PowerPropValue {
    required PowerEventId EventId    = 1;
    required uint64 value            = 2;
}