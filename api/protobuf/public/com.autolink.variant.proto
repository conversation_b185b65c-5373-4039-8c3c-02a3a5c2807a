syntax = "proto2";

package autolink.platform.variant.pb;

enum ValueType {
  VALUE_TYPE_UNKNOWN = 0;
  VALUE_TYPE_INT32 = 1;
  VALUE_TYPE_INT32_VEC = 2;
  VALUE_TYPE_INT64 = 3;
  VALUE_TYPE_INT64_VEC = 4;
  VALUE_TYPE_FLOAT = 5;//不建议使用，建议乘以精度处理
  VALUE_TYPE_FLOAT_VEC = 6;//不建议使用，建议乘以精度处理
  VALUE_TYPE_DOUBLE = 7;//不建议使用，建议乘以精度处理
  VALUE_TYPE_DOUBLE_VEC = 8;//不建议使用，建议乘以精度处理
  VALUE_TYPE_STRING = 9;
  VALUE_TYPE_STRING_VEC = 10;
  VALUE_TYPE_BYTES = 11;
}

//---------------begin------
// 定义数组类型
message Int32Array {
  required int32 size = 1;
  repeated int32 values = 2;
}

message Int64Array {
  required int32 size = 1;
  repeated int64 values = 2;
}

message Uint32Array {
  required int32 size = 1;
  repeated uint32 values = 2;
}

message Uint64Array {
  required int32 size = 1;
  repeated uint64 values = 2;
}

message FloatArray {
  required int32 size = 1;
  repeated float values = 2;
}

message DoubleArray {
  required int32 size = 1;
  repeated double values = 2;
}

message StringArray {
  repeated int32 sizes = 1;
  repeated string values = 2;
}

message ByteArray {
  required int32 size = 1;
  required bytes value = 2;
}
//---------------end------


// 定义配置的值
message VariantValue {
  optional ValueType type = 1; //根据元数据信息判断
  optional int64 timestamp = 2;//最后更新时间
  optional string extras = 6;
  oneof value {
    Int32Array int32_array = 7;
    Int64Array int64_array = 8;
    Uint32Array uint32_array = 9;
    Uint64Array uint64_array = 10;
    FloatArray float_array = 11;
    DoubleArray double_array = 12;
    StringArray string_array = 13;
    ByteArray bytes_value = 14;
  }
}

//下面是fdbus传输的消息定义
//fdbus传输时配置值相关的错误信息
enum ErrorCode {
  ERR_OK = 0; //没有错误
  ERR_NO_EXIST = 1;//键不存在
  ERR_RD_ONLY = 2; //只读配置不允许修改；
  ERR_ACCESS = 3;//权限不够
  ERR_INVALID_PARAMS = 4 ;//参数错误
  ERR_UNKNOWN = 14;
}

enum MessageOpType {
  OP_SET = 0; //设置配置
  OP_GET = 1; //读取配置
  OP_SET_ALL = 2; //批量设置配置
  OP_GET_ALL = 3; //批量读取配置
  OP_CHECK_CRC = 4;//检查值的crc
  OP_SUBSCRIBE = 85;//订阅关键配置（整车配置字）
  OP_UNSUBSCRIBE = 86;//取消订阅配置
  OP_SUBSCRIBE_COMMON = 87;//普通消息订阅
  //OP_HEATBEAT = 4;//客户端发给服务端心跳
};

enum VariantClient {
  ANDROID_VARIANT = 0;//ANDROID
  QNX_ADAS = 1;
}

//定义fdbus传输的消息
message VariantMessage {
  required string key = 2;//键名称
  optional VariantValue value= 3;//值
}

//请求set/get单条配置数据
message VariantMessageReq {
  required MessageOpType op_type = 1;
  required VariantClient client = 2;
  optional VariantMessage msg = 3;
}

//请求set/get单条配置数据响应
message VariantMessageResp {
  required ErrorCode code = 1;
  optional VariantMessage msg = 2;
  optional string err_msg = 3;//出错时传递该字段
}

//用于一次同步多条配置的请求
message VariantMessagePackReq {
  required MessageOpType op_type = 1;
  required VariantClient client = 2;
  repeated VariantMessage msgs = 3;
  repeated string keys = 4;//获取部分配置时使用
}

//一次同步多条配置的请求响应
message VariantMessagePackResp {
  required ErrorCode code = 1;
  repeated VariantMessage msgs = 2;
  optional string err_msg = 3;//出错时传递该字段
}
