syntax = "proto2";
option optimize_for = LITE_RUNTIME;

package autolink.vehicle;

enum ValueType {
	STRING    = 0;
	BOOL      = 1;
	INT32     = 2;
	INT32_VEC = 3;
	INT64     = 4;
	INT64_VEC = 5;
	FLOAT     = 6;
	FLOAT_VEC = 7;
	BYTES     = 8;
}

enum AreaType {
    GLOBAL = 1;
    WINDOW = 3;
    MIRROR = 4;
    SEAT   = 5;
    DOOR   = 6;
    WHEEL  = 7;
    BODY   = 8;
}

enum PROPSTATUS {
	AVAILABLE     = 0;
	NOT_AVAILABLE = 1;
	ERROR         = 2;
}

enum Access {
    NONE       = 0;
    READ       = 1;
    WRITE      = 2;
    READ_WRITE = 3;
}

enum ChangeMode {
    STATIC    = 0;
    ON_CHANGE = 1;
    CONTINOUS = 2;
}

message ViClientPropGetReq
{
	required int32 propId = 1;
	required int32 areaId = 2;
}

message ViSubscribeReq
{
    required int32 propId = 1;
    optional int32 sampleRate = 2 [default = 0];
}

message ViUnsubscribeReq
{
    repeated int32 propId = 1;
}

message ViPropConfigGetReq
{
    required int32 msgId = 1;
}

message AreaConfig
{
    optional int32 areaId        = 1 [default = 0];
    optional int32 minInt32Value = 2 [default = 0];
    optional int32 maxInt32Value = 3 [default = 0];
    optional int64 minInt64Value = 4 [default = 0];
    optional int64 maxInt64Value = 5 [default = 0];
    optional float minFloatValue = 6 [default = 0.0];
    optional float maxFloatValue = 7 [default = 0.0];
}

message ViClientPropConfig
{
    required int32 propId           = 1;
    required AreaType areatype      = 2;
    required ValueType valuetype    = 3;
    required Access access          = 4;
    required ChangeMode changemode  = 5;
    repeated AreaConfig areaConfigs = 6;
    optional int32 minSampleRate    = 7 [default = 0];
    optional int32 maxSampleRate    = 8 [default = 0];
}

message ViClientPropConfigs
{
    repeated ViClientPropConfig config = 1;
}

message RawValue {
	repeated int32 int32Values   = 1;
	repeated int64 int64Values   = 2;
	repeated float floatValues   = 3;
	optional bytes bytesValues   = 4;
	optional string stringValues = 5;
}

message ViClientPropValue
{
	required int32 propId          = 1;
	required ValueType valueType   = 2;
	required int32 areaId          = 3;
	optional int64 timestamp       = 4 [default = 0];
	optional PROPSTATUS propStatus = 5 [default = AVAILABLE];
	required RawValue data         = 6;
	optional string csStr		   = 7;
}

enum WarningValue {
	OFF    = 0;
	ON     = 1;
}

message WarningData {
	required int32 warningId       = 1;
	required WarningValue value    = 2;
}

message ViWarningValue
{
	required int32 propId          = 1;
	optional ValueType valueType   = 2 [default = INT32_VEC];
	required int32 areaId          = 3;
	optional int64 timestamp       = 4 [default = 0];
	optional PROPSTATUS propStatus = 5 [default = AVAILABLE];
	repeated WarningData data      = 6;
}

message ViClientPropValues {
	repeated ViClientPropValue propValue = 1;
}

enum repsonseSts {
	FAILED    = 0;
	SUCCESS   = 1;
}

message ViResponse {
	required repsonseSts status = 1;
}
