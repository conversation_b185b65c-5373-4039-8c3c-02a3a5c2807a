/**
* @swcomponent Autolink DID - API
* @{
* @file        com.autolink.cluster.diag.proto
* @brief       enum definition of Autolink cluster DIDs/RIDs
* @}
**/
syntax = "proto2";
package autolink.cluster.diag.pb;
/**
* Message ID Definition used in QNX
**/
enum ClusterFdbusMessageId
{
    /**
    * Description:Interface for 22 service
    * Request Message: DiagReadDidRequest
    * Response Message: DiagReadDidResponse
    **/
    MSG_CLUSTER_READ_DID         = 0x10;

    /**
    * Description:Interface for 2E service
    * Request Message: DiagWriteDidData
    * Response Message: DiagWriteDidResponse
    **/
    MSG_CLUSTER_WRITE_DID        = 0x11;

    /**
    * Description:Interface for 31 service
    * Request Message: RoutineCtrlDid
    * Response Message: RoutineCtrlResponse
    **/
    MSG_CLUSTER_ROUTINE_DID      = 0x12;

    /**
    * Description:Interface for 2F service
    * Request Message: IOCtrlDid
    * Response Message: IOCtrlResponse
    **/
    MSG_CLUSTER_IO_DID           = 0x13;
}
/**
* Enum Definition for Routine Control DID sub-function type
**/
enum enRoutineCtrlDidSubFn
{
    /**
    * 01 startRoutine
    **/
    DID_SUB_FUNCTION_SET                  = 0x01;
    /**
    * 02 stopRoutine
    **/
    DID_SUB_FUNCTION_STOP                 = 0x02;
    /**
    * 03 requestRoutineResults
    **/
    DID_SUB_FUNCTION_GET                  = 0x03;
    /**
    * INVALID
    **/
    DID_SUB_FUNCTION_INVALID              = 0xFF;
}

/**
* Enum Definition for 2F service DID sub-function type
**/
enum enInputOutputCtrlDidSubFn
{
    /**
    * 00 returnControlToECU
    **/
    IOCTRL_RETURN_CONTROL_TO_ECU        = 0x00;
    /**
    * 01 resetToDefault
    **/
    IOCTRL_RESET_TO_DEFAULT             = 0x01;
    /**
    * 02 freezeCurrentState
    **/
    IOCTRL_FREEZE_CURRENT_STATE         = 0x02;
    /**
    * 03 shortTermAdjustment
    **/
    IOCTRL_SHORT_TERM_ADJUSTMENT        = 0x03;
    /**
    * INVALID
    **/
    IOCTRL_SUB_FUNCTION_INVALID         = 0xFF;
}

/**
* Status used to identify the result of a specific implement
**/
enum Status
{
    RESULT_OK                           = 0;  // Ok
    ERROR_UNKNOWN                       = 1;  // Unknow error, used as default error status
    ERROR_NOT_SUPPORTED_DID             = 2;  // Not supported did, did not implemented or write read-only did
    ERROR_DATA_VERIFY_FAILED            = 3;  // Data verify failed
}

/**
* Enum Definition for Fdbus Code communication
**/
enum MsgDiagFdbusCode
{
    /**
    * Description: Diag Fdbus Code
    * 1. QNX diag server broadcast request to other modules
    * 2. other modules subscribe QNX diag server to get request
    * other modules such as [Android client][Diagnostic protocol stack client]
    **/
    MSG_DIAG_THIRD_DATA_SEND             = 0x70;
    /**
    * Description: Diag Fdbus Code
    * 1. QNX diag server onInvoke processed response from other modules
    * 2. other modules Invoke processed response to QNX diag server
    **/
    MSG_DIAG_THIRD_DATA_RECV             = 0x71;
    /**
    * Description: Diag Fdbus Code
    * 1. QNX diag server onInvoke request from other modules
    * 2. other modules Invoke request to QNX diag server
    * other modules, such as [ALC client][EOL Client]
    **/
    MSG_DIAG_REQUEST                     = 0x80;
    /**
    * Description: Diag Fdbus Code
    * 1. QNX diag server broadcast response to other modules
    * 2. other modules subscribe QNX diag server to get response(include timeout response)
    * other modules, such as [EOL Client]
    * [ALC client] should use the definition SOC_to_MCU code of ALC, not this
    **/
    MSG_DIAG_RESPONSE                    = 0x81;
}

/**
* Message Definition for read DID request
**/
message DiagReadDidRequest {
    required uint32 did                 = 1;
}

/**
* Message Definition for read Did Data response
**/
message DiagReadDidResponse {
    required Status status                 = 1;
    required uint32 did                    = 2;
    optional bytes  data                   = 3;
}

/**
* Message Definition for Write Did Data request
**/
message DiagWriteDidData {
    required uint32 did                    = 1;
    required bytes data                    = 2;
}

/**
* Message Definition for write DID responsed
**/
message DiagWriteDidResponse {
    required Status status             = 1;
    required uint32 did                = 2;
}

/**
* Message Definition for diagnosis routine control did type
**/
message RoutineCtrlDid
{
    required uint32 rid                             = 1;

    required uint32 sub_fun                         = 2;

    optional bytes option_record                    = 3;
}

message RoutineCtrlResponse
{
    required Status status                          = 1;

    required uint32 rid                             = 2;

    required uint32 sub_fun                         = 3;

    optional bytes data                             = 4;
}

/**
* Message Definition for diagnosis IO control did type
**/
message IOCtrlDidRequest
{
    required uint32 did                             = 1;

    required enInputOutputCtrlDidSubFn sub_fun      = 2;

    optional bytes resquest_data                    = 3;
}

message IOCtrlResponse
{
    required Status status                          = 1;

    required uint32 did                             = 2;

    required enInputOutputCtrlDidSubFn sub_fun      = 3;

    optional bytes data                             = 4;
}

/**
* Message Definition for diagnosis dispatcher set
**/
message DiagModuleRequestSet
{
    required uint32 service_id                     = 1;

    optional DiagReadDidRequest did_read_req_set   = 2;

    optional DiagWriteDidData did_write_req_set    = 3;

    optional RoutineCtrlDid rid_req_set            = 4;

    optional IOCtrlDidRequest ioc_req_set          = 5;
}

message DiagModuleResponseSet
{
    required uint32 service_id                        = 1;

    optional DiagReadDidResponse did_read_resp_set    = 2;

    optional DiagWriteDidResponse did_write_resp_set  = 3;

    optional RoutineCtrlResponse rid_resp_set         = 4;

    optional IOCtrlResponse ioc_resp_set              = 5;
}
