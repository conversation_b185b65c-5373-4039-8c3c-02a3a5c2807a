syntax = "proto2";

package autolink.platform.variant.pb;
import "com.autolink.variant.proto";

//----------元数据定义begin---
//定义数据的元信息
enum UpdateStrategy {
  UPDATE_STRATEGY_UNSPECIFIED = 0;  //为0时，protobuf3会使用默认值，不在线传输
  UPDATE_STRATEGY_OVERWRITE = 1;  //覆盖更新，下次重启生效
  UPDATE_STRATEGY_OVERWRITE_NOW = 2;  //立即覆盖更新，立即生效,考虑去掉
  UPDATE_STRATEGY_KEEP_EXISTING = 3;  //不更新
  UPDATE_STRATEGY_DELETE = 4;         //删除配置
  UPDATE_STRATEGY_RESET = 5;         //恢复默认配置值
}

message ValueRestrictInt32 {
  optional int32 min_int32_value = 1;
  required int32 max_int32_value = 2;
}

message ValueRestrictInt64 {
  optional int32 min_int64_value = 1;
  required int32 max_int64_value = 2;
}

message ValueRestrictFloat {
  optional float min_float_value = 1;
  required float max_float_value = 2;
  optional int32 precision = 3;  //最小精度
}

message ValueRestrictDouble {
  optional double min_double_value = 1;
  required double max_double_value = 2;
  optional int32 precision = 3;  //最小精度
}

message ValueRestrictString {
  required int32 max_length = 1;
  optional string regex_pattern = 2;  //预留，正则表达式验证字符串
}

//数组，包括字节数组使用这个约束
message ValueRestrictArray {
  optional int32 min_array_length = 1;  //数组最小长度
  required int32 max_array_length = 2;  //数组最大长度
}

//值约束，单个元素数据类型
message ValueRestrictSingleElement {
  required ValueType type = 1;
  oneof restrict {
    ValueRestrictInt32 restrict_int32 = 10;
    ValueRestrictInt64 restrict_int64 = 11;
    ValueRestrictFloat restrict_float = 12;
    ValueRestrictDouble restrict_double = 13;
    ValueRestrictString restrict_string = 14;
  }
}

message ValueRestrict {
  required ValueRestrictSingleElement value_restriction = 1;//单元素使用
  optional ValueRestrictArray array_restriction = 2;//数组，字节数组使用
};

//配置的存取操作权限
enum VariantOpMode {
  OP_R = 0;
  OP_RW = 1;
};

//单个车辆配置元数据（配置及值描述）
message VariantMeta {
  required VariantOpMode op_mode = 1;
  required UpdateStrategy update_strategy = 2;
  required ValueRestrict restriction = 3;//配置值描述
  optional string desc = 4;         //配置描述
  optional VariantValue def_value = 5; //默认值
  optional string group = 6;//the group that key was in
  optional bool persist = 7; //whether persist to psis
  optional bool persist_raw = 8; //whether persist in raw data
}

//所有的模块数据的元数据。
message VariantMetas{
  map<string, VariantMeta> metas = 3;//键值描述元数据
}
//----------元数据定义end-----