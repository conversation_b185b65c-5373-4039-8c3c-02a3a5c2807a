/*    Copyright(C) 2024 Autolink Information Technology Co.Ltd. All rights reserved.   */
/*                                                                                     */
/*                         The file is automatically generated.                        */
/*                      !!!!!! Manual changes are prohibited !!!!!                     */
/*                                                                                     */
/* latest change:                                                                      */
/*
      2025/04/03

      【custom.hmi】新增 LEFT_MENU_INDEX
*/

#ifndef SCCCOM_TX_CFG_H
#define SCCCOM_TX_CFG_H

#define SCC_TX_LIST \
        { "custom.mcu.DOIP_LINK_STATUS", std::bind(&SccComModule::OnQNXSetNotify, this, std::placeholders::_1, 1) }, \
        { "custom.mcu.OTA_ICC_STS", std::bind(&SccComModule::OnQNXSetNotify, this, std::placeholders::_1, 2) }, \

