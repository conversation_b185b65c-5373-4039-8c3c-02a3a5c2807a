#include "warning/include/warning_engine.h"
#include "warning/include/warning_property.h"
#include "autolink/midware/chime/include/signal_adapter.h"
#include "autolink/midware/chime/include/chime_def.h"

#define LOG_TAG "POP_ENGINE"

using namespace std;
using namespace AutoLink::Frameworks::CM;
using namespace AutoLink::Frameworks::Core;

namespace WarningModule {

AlarmEngine::AlarmEngine() 
    : currentWarning_(nullptr)
    , currPowerMode_(PWR_MODE_RUN)
    , isSelfCheckFinished_(false),
    needTriggerBackSecubelt_(true)
 {
}

void AlarmEngine::Init() {
    // 清理状态
    warningMap_.clear();
    pendingQueue_.clear();
    currentWarning_ = nullptr;
    timers_.resetAll();
    
    // 创建主定时器  100ms间隔
    if (!engineTimer_) {
        engineTimer_ = Timer::Create("WarningEngineTimer", 100, [this]() {
            timerProcess();
        });
    }
    
    if (!engineTimer_->IsRunning()) {
        engineTimer_->Start();
    }
    
    LOG_TAG_INFO("AlarmEngine initialized");
}



void AlarmEngine::triggerWarning(const WarnProperty& property, const std::string& msg) {

    //如果仅是后排安全带 判断当前周期是否被取消过 如果被取消过则不加入 安全带需求 ： 仅后排取消 条件a
    if(property.id == SEATBELT_UNFASTENED_WARNING)
    {
        handleBackSecubelt();
        if(!needTriggerBackSecubelt_)
        {
            return;
        }
    }

    printState("Trigger warning P" + std::to_string(property.priority) + 
               " ID:" + std::to_string(property.id));
    
    // 检查是否已存在

 

    auto it = warningMap_.find(property.id);
    if (it != warningMap_.end()) {


        if (it->second->isActive) {
            LOG_TAG_INFO("Warning %d already active, ignoring", property.id);
            return;
        }
        // 重新激活已存在的报警
        it->second->isActive = true;
        it->second->state = WarningState::PENDING;
        processNewWarning(it->second);
        return;
    }
    
    // 创建新报警
    auto warning = std::make_shared<Warning>(property, msg);
    warningMap_[property.id] = warning;
    
    processNewWarning(warning);
}
//处理新wanring 包括判断电源条件以及是否活跃 
void AlarmEngine::processNewWarning(std::shared_ptr<Warning> warning) {
    //检查电源条件 & ACTIVE
    if (!canDisplay(warning)) {
        updateWarningState(warning, WarningState::PENDING);
        addToPendingQueue(warning);
        return;
    }
    
    if (!currentWarning_) {
        setCurrentWarning(warning);
        return;
    }
    
    handleWarningByPriority(warning);
}
//判断是否打断
void AlarmEngine::handleWarningByPriority(std::shared_ptr<Warning> newWarning) {
    if (!currentWarning_) {
        setCurrentWarning(newWarning);
        return;
    }
    
    // 高优先级打断低优先级
    if (hasHigherPriority(newWarning, currentWarning_)) {
        if (canInterrupt(newWarning)) {
            interruptCurrentWarning(newWarning);
        } else {
            // 等待最小播放时间
            addToPendingQueue(newWarning);
        }
        return;
    }
    
    // 同级或低优先级，加入队列
    addToPendingQueue(newWarning);
}

void AlarmEngine::interruptCurrentWarning(std::shared_ptr<Warning> newWarning) {
    if (!currentWarning_) return;
    
    printState("Interrupt: P" + std::to_string(newWarning->priority) + 
               " interrupts P" + std::to_string(currentWarning_->priority));
    
    // 计算当前报警的剩余时间
    calculateRemainingTime(currentWarning_);
    
    // 累计已播放的显示时间
    double playedTime = timers_.normalDisplayCount * 0.1;  // 转换为秒
    currentWarning_->accumulatedDisplayTime += playedTime;
    
    LOG_TAG_INFO("Warning %d interrupted, played %.1fs, accumulated %.1fs", 
                 currentWarning_->id, playedTime, currentWarning_->accumulatedDisplayTime);
    
    // 检查后排安全带90秒超时
    if (hasOnlyRearSeatbeltUnfastened() && 
        currentWarning_->accumulatedDisplayTime >= 90.0) {
        LOG_TAG_INFO("Rear seatbelt %d reached 90s during interrupt, will not resume", 
                     currentWarning_->id);
        // 不加入队列，直接结束
        updateWarningState(currentWarning_, WarningState::INACTIVE);
        setCurrentWarning(newWarning);
        return;
    }
    
    // 将当前报警移到队列
    updateWarningState(currentWarning_, WarningState::INTERRUPTED);
    addToPendingQueue(currentWarning_);
    
    // 设置新的当前报警
    setCurrentWarning(newWarning);
}
//setCur完全只用来设置当前的wanring 不会判断是否可以打断 走到这里就说明可以直接设置进去了 打断什么的全都处理好了
void AlarmEngine::setCurrentWarning(std::shared_ptr<Warning> warning) {
    // 清除之前的显示
    if (currentWarning_) {
        notifyHMI(currentWarning_->id, false);
        notifyChime(currentWarning_->id, false);
    }
    
    currentWarning_ = warning;
    timers_.resetAll();
    
    if (!warning) {
        printState("Display cleared");
        notifyAndroid();
        return;
    }
    
    // 更新状态
    updateWarningState(warning, WarningState::DISPLAYING);
    removeFromPendingQueue(warning->id);
    
    // 开始显示
    notifyHMI(warning->id, true);
    notifyChime(warning->id, true);
    
    // 启动定时器
    timers_.startMinDisplay();
    timers_.startNormalDisplay();
    
    printState("Start displaying P" + std::to_string(warning->priority) + 
               " ID:" + std::to_string(warning->id) + 
               " remaining:" + std::to_string(warning->remainingTime) + "s");
    
    notifyAndroid();
}

void AlarmEngine::clearWarning(uint32_t id) {

    printState("Clear warning ID:" + std::to_string(id));
    
    auto it = warningMap_.find(id);
    if (it == warningMap_.end()) {
        LOG_TAG_WARNING("Warning %d not found", id);
        return;
    }
    
    auto warning = it->second;
    warning->isActive = false;
    updateWarningState(warning, WarningState::INACTIVE);
    
    if (currentWarning_ && currentWarning_->id == id) {
        // 检查是否还在最小播放时间内
        if (timers_.minDisplayFlag) {
            LOG_TAG_INFO("Warning %d cleared but min display time not reached, will complete after min time", id);
            // 标记为待清除，但继续播放到最小时间
            currentWarning_->isActive = false;
            return;
        }
        
        // 已达到最小时间，可以立即清除
        completeCurrentWarning();
        startNextWarning();
    } else {
        removeFromPendingQueue(id);
    }
}

void AlarmEngine::completeCurrentWarning() {
    if (!currentWarning_) return;
    
    printState("Complete warning P" + std::to_string(currentWarning_->priority) + 
               " ID:" + std::to_string(currentWarning_->id));
    
    notifyHMI(currentWarning_->id, false);
    notifyChime(currentWarning_->id, false);
    
    // 清理已完成的报警
    warningMap_.erase(currentWarning_->id);
    currentWarning_ = nullptr;
    timers_.resetAll();
}

void AlarmEngine::startNextWarning() {
    auto nextWarning = getNextPendingWarning();
    if (nextWarning) {
        setCurrentWarning(nextWarning);
    } else {
        setCurrentWarning(nullptr);
    }
}

void AlarmEngine::handleRotationDisplay() {
    if (!currentWarning_ || !currentWarning_->isConstantDisplay) {
        return;
    }
    
    // 获取同级别的报警
    auto samePriorityWarnings = getSamePriorityWarnings(currentWarning_->priority);
    if (samePriorityWarnings.size() <= 1) {
        // 没有同级报警，重新开始播放当前报警
        timers_.startNormalDisplay();
        return;
    }
    
    printState("Rotation display for priority " + std::to_string(currentWarning_->priority));
    
    // 将当前报警移到队列末尾，重置播放时间为6秒
    currentWarning_->remainingTime = 6.0;  // 固定6秒
    addToPendingQueue(currentWarning_);
    
    // 显示下一个同级报警
    auto nextWarning = getNextPendingWarning();
    if (nextWarning) {
        nextWarning->remainingTime = 6.0;  // 确保下一个也是6秒
    }
    setCurrentWarning(nextWarning);
}

// 定时器处理方法
void AlarmEngine::timerProcess() {
    processStartupDelayTimer();  // 处理300ms启动延迟
    processMinDisplayTimer();
    processNormalDisplayTimer();
    processRotationTimer();
    
   // 定期清理非活跃报警
    cleanupInactiveWarnings(); 
}

void AlarmEngine::processMinDisplayTimer() {
    if (!timers_.minDisplayFlag) return;
    
    if (timers_.minDisplayCount < MIN_DISPLAY_TIME) {
        ++timers_.minDisplayCount;
    } else {
        timers_.minDisplayFlag = false;
        timers_.minDisplayCount = 0;
        onMinDisplayComplete();
    }
}

void AlarmEngine::processNormalDisplayTimer() {
    if (!timers_.normalDisplayFlag || !currentWarning_) return;
    
    // 如果报警已被清除但还在最小时间内，只播放到最小时间
    if (!currentWarning_->isActive && timers_.minDisplayFlag) {
        return; // 等待最小时间完成
    }
    
    // 计算目标时间（以100ms为单位）
    int32_t targetCount = static_cast<int32_t>(currentWarning_->remainingTime * 10);
    
    if (timers_.normalDisplayCount < targetCount) {
        ++timers_.normalDisplayCount;
    } else {
        timers_.normalDisplayFlag = false;
        timers_.normalDisplayCount = 0;
        onNormalDisplayComplete();
    }
}

void AlarmEngine::processRotationTimer() {
    if (!timers_.rotationFlag) return;
    
    if (timers_.rotationCount < ROTATION_TIME) {
        ++timers_.rotationCount;
    } else {
        timers_.rotationFlag = false;
        timers_.rotationCount = 0;
        onRotationComplete();
    }
}

void AlarmEngine::processStartupDelayTimer() {
    if (!timers_.startupDelayFlag) return;
    
    if (timers_.startupDelayCount < STARTUP_DELAY_TIME) {
        ++timers_.startupDelayCount;
    } else {
        timers_.startupDelayFlag = false;
        timers_.startupDelayCount = 0;
        onStartupDelayComplete();
    }
}

void AlarmEngine::onStartupDelayComplete() {
    printState("Startup delay complete - begin processing warnings");
    
    // 300ms延迟后开始处理队首报警
    if (!currentWarning_) {
        auto nextWarning = getNextPendingWarning();
        if (nextWarning) {
            setCurrentWarning(nextWarning);
        }
    }
}

// 定时器回调方法
void AlarmEngine::onMinDisplayComplete() {
    printState("Min display time complete");
    
    // 检查当前报警是否已被标记为非活跃（信号丢失）
    if (currentWarning_ && !currentWarning_->isActive) {
        LOG_TAG_INFO("Current warning %d was cleared during min display, completing now", 
                     currentWarning_->id);
        completeCurrentWarning();
        startNextWarning();
        return;
    }
    
    // 检查是否有更高优先级的报警等待
    auto nextWarning = getNextPendingWarning();
    if (nextWarning && hasHigherPriority(nextWarning, currentWarning_)) {
        interruptCurrentWarning(nextWarning);
    }
}

void AlarmEngine::onNormalDisplayComplete() {
    if (!currentWarning_) return;
    
    // 累计完整的显示时间
    currentWarning_->accumulatedDisplayTime += currentWarning_->normalShowTime;
    
    // 检查安全带90秒超时（仅后排未系时）
    if (currentWarning_->id == SEATBELT_UNFASTENED_WARNING && 
        hasOnlyRearSeatbeltUnfastened() &&
        currentWarning_->accumulatedDisplayTime >= 90.0) {
        LOG_TAG_INFO("Rear-only seatbelt reached 90s display time, completing");
        completeCurrentWarning();
        startNextWarning();
        return;
    }
    
    printState("Normal display time complete for P" + 
               std::to_string(currentWarning_->priority) + 
               " accumulated: " + std::to_string(currentWarning_->accumulatedDisplayTime) + "s");
    
    if (currentWarning_->isConstantDisplay) {
        // 常显报警，检查是否有同级报警需要轮询
        auto samePriorityWarnings = getSamePriorityWarnings(currentWarning_->priority);
        if (samePriorityWarnings.size() > 1) {
            // 有同级报警，直接切换（不等待轮询时间）
            handleRotationDisplay();
        } else {
            // 没有同级报警，重新开始播放当前报警
            timers_.startNormalDisplay();
        }
    } else {
        // 非常显报警，完成显示
        completeCurrentWarning();
        startNextWarning();
    }
}

void AlarmEngine::onRotationComplete() {
    LOG_TAG_INFO("Rotation time complete");
    handleRotationDisplay();
}

void AlarmEngine::handleUserAction() {
    if (!currentWarning_) return;

    bool SeatbeltNeedDismiss = false;
    bool frontUnfastened = activeSeatbeltsAndSpeed_.driver || activeSeatbeltsAndSpeed_.passenger;
    bool rearUnfastened = activeSeatbeltsAndSpeed_.rl || activeSeatbeltsAndSpeed_.rm || activeSeatbeltsAndSpeed_.rr;
    bool onlyRearUnfastened = !frontUnfastened && rearUnfastened;
    //当告警是安全带告警 并且满足仅后排安全带未系时 可以取消告警 并且 此次上电周期不再触发
    if(currentWarning_->id == SEATBELT_UNFASTENED_WARNING && onlyRearUnfastened){
        SeatbeltNeedDismiss = true;
        needTriggerBackSecubelt_ = false;
    }
    
    WarnId warnId = static_cast<WarnId>(currentWarning_->id);
    const WarnProperty& prop = WarningProperty::getInstance().getWarnProperty(warnId);
    
    if (!prop.isDismissible  && !SeatbeltNeedDismiss) {
        LOG_TAG_INFO("Warning %d is not dismissible", currentWarning_->id);
        return;
    }
    
    // 检查是否已达到最小显示时间
    if (timers_.minDisplayFlag) {
        LOG_TAG_INFO("User action ignored - min display time not reached");
        return;
    }
    
    printState("User dismissed warning " + std::to_string(currentWarning_->id));
    clearWarning(currentWarning_->id);
}

// 条件检查方法
bool AlarmEngine::canDisplay(std::shared_ptr<Warning> warning) const {
    WarnId warnId = static_cast<WarnId>(warning->id);
    const WarnProperty& prop = WarningProperty::getInstance().getWarnProperty(warnId);
    
    bool isActive = warning->isActive;
    bool powerModeOK = WarningProperty::getInstance().isBitSet(prop.powerMode, currPowerMode_);
    bool selfCheckOK = isSelfCheckFinished_;
    
    LOG_TAG_INFO("canDisplay check: ID=%d, isActive=%d, powerModeOK=%d(prop=0x%x,curr=%d), selfCheckOK=%d", 
                 warning->id, isActive, powerModeOK, prop.powerMode, currPowerMode_, selfCheckOK);
    
    return isActive && powerModeOK && selfCheckOK;
}

bool AlarmEngine::canInterrupt(std::shared_ptr<Warning> newWarning) const {
    if (!currentWarning_) return true;
    
    // 1级优先级可以无视最小播放时间
    if (isPriority1(newWarning)) return true;
    
    // 其他级别需要等待最小播放时间
    return !timers_.minDisplayFlag;
}

bool AlarmEngine::isPriority1(std::shared_ptr<Warning> warning) const {
    return warning && warning->priority == 1;
}

bool AlarmEngine::isSamePriority(std::shared_ptr<Warning> w1, std::shared_ptr<Warning> w2) const {
    return w1 && w2 && w1->priority == w2->priority;
}

bool AlarmEngine::hasHigherPriority(std::shared_ptr<Warning> w1, std::shared_ptr<Warning> w2) const {
    return w1 && w2 && w1->priority < w2->priority;
}

// 队列管理方法
void AlarmEngine::addToPendingQueue(std::shared_ptr<Warning> warning) {
    if (!warning) return;
    
    // 避免重复添加
    removeFromPendingQueue(warning->id);
    
    // 找到合适的插入位置：按优先级排序，同级插入到末尾
    auto insertPos = pendingQueue_.end();
    for (auto it = pendingQueue_.begin(); it != pendingQueue_.end(); ++it) {
        if ((*it)->priority > warning->priority) {
            insertPos = it;
            break;
        }
    }
    
    pendingQueue_.insert(insertPos, warning);
    
    printState("Added to pending queue: P" + std::to_string(warning->priority) + 
               " ID:" + std::to_string(warning->id));
}

void AlarmEngine::removeFromPendingQueue(int32_t warningId) {
    
    auto it = pendingQueue_.begin();
    while (it != pendingQueue_.end()) {
        if ((*it)->id == warningId) {
            it = pendingQueue_.erase(it);
        } else {
            ++it;
        }
    }
}

std::shared_ptr<Warning> AlarmEngine::getNextPendingWarning() const {
    for (auto& warning : pendingQueue_) {
        if (warning->isActive && canDisplay(warning)) {
            return warning;
        }
    }
    return nullptr;
}

std::vector<std::shared_ptr<Warning>> AlarmEngine::getSamePriorityWarnings(uint32_t priority) const {
    std::vector<std::shared_ptr<Warning>> result;
    
    // 包含当前显示的报警
    if (currentWarning_ && currentWarning_->priority == priority && currentWarning_->isActive) {
        result.push_back(currentWarning_);
    }
    
    // 包含队列中的同级报警
    for (auto& warning : pendingQueue_) {
        if (warning->priority == priority && warning->isActive && canDisplay(warning)) {
            result.push_back(warning);
        }
    }
    
    return result;
}


// 状态管理方法
void AlarmEngine::updateWarningState(std::shared_ptr<Warning> warning, WarningState state) {
    if (warning) {
        warning->state = state;
    }
}

void AlarmEngine::calculateRemainingTime(std::shared_ptr<Warning> warning) {
    if (!warning) return;
    
    // 计算已播放时间（以100ms为单位转换为秒）
    double playedTime = timers_.normalDisplayCount * 0.1;
    warning->remainingTime = std::max(0.0, warning->remainingTime - playedTime);
    
    LOG_TAG_INFO("Warning %d remaining time: %.1fs", warning->id, warning->remainingTime);
}

void AlarmEngine::resetWarningTime(std::shared_ptr<Warning> warning) {
    if (warning) {
        warning->remainingTime = warning->normalShowTime;
    }
}

// 通知接口
void AlarmEngine::notifyHMI(int32_t warningId, bool isActive) {

    if(warningId == SECUBLT_DRIVER || warningId == SECUBLT_PSNGER || warningId == SECUBLT_RL || warningId == SECUBLT_RM || warningId == SECUBLT_RR){
        warningId = SEATBELT_UNFASTENED_WARNING;
    }

    uint32_t displayValue = isActive ? warningId : 0;
    uint32_t statusValue = isActive ? 1 : 0;

    
    CarPropertyManager::Get().SetPropertyValue("custom.warning.WARNING_DISPLAY_POP", displayValue);
    CarPropertyManager::Get().SetPropertyValue("custom.warning.WARNING_SHOW_STS", statusValue);
    
    LOG_TAG_INFO("NotifyHMI: ID=%d, Display=%d, Status=%d", 
                warningId, displayValue, statusValue);
}

void AlarmEngine::notifyChime(int32_t warningId, bool isActive) {
    // 安全带报警需求与chime不一致 chime模块自行处理 warning不做通知
    if(warningId == SEATBELT_UNFASTENED_WARNING){
        return ;
    }
    auto status = isActive ? Chime::ChimeStatus::STATUS_ACTIVE: Chime::ChimeStatus::STATUS_INACTIVE;
    
    Chime::SignalAdapter::WarningStatusChange(warningId, status);
    
    LOG_TAG_INFO("NotifyChime: ID=%d, Status=%d", warningId, static_cast<int>(status));
}

void AlarmEngine::notifyAndroid() {
    std::vector<CarPropertyValue> historyList;
    
    // 添加当前显示的报警
    if (currentWarning_ && currentWarning_->isActive && currentWarning_->isNotifyCenter) {
        historyList.push_back(currentWarning_->id);
    }
    
    // 添加待处理的报警
    for (auto& warning : pendingQueue_) {
        if (warning->isActive && canDisplay(warning)&& currentWarning_->isNotifyCenter) {
            historyList.push_back(warning->id);
        }
    }
    
    // 如果没有报警，发送0
    if (historyList.empty()) {
        historyList.push_back(0);
    }
    
    CarPropertyManager::Get().SetPropertyValue("ivi.tx.C2I_WARN_HISTORY_LIST_STR", historyList);
    
    std::string ids;
    for (auto id : historyList) {
        ids += std::to_string(id.ToInt()) + " ";
    }
    LOG_TAG_INFO("NotifyAndroid: %s", ids.c_str());
}


void AlarmEngine::cleanupInactiveWarnings() {
    static int cleanupCounter = 0;
    if (++cleanupCounter < 50) return;  // 每5秒清理一次
    cleanupCounter = 0;
    
    auto it = warningMap_.begin();
    while (it != warningMap_.end()) {
        if (!it->second->isActive && it->second->state == WarningState::INACTIVE) {
            it = warningMap_.erase(it);
        } else {
            ++it;
        }
    }
}

void AlarmEngine::printState(const std::string& event) const {
    LOG_TAG_INFO("%s", event.c_str());
}

void AlarmEngine::setCurrPowerMode(PowerMode powerMode) {
    if (currPowerMode_ == powerMode) {
        return;
    }
    
    PowerMode oldMode = currPowerMode_;
    currPowerMode_ = powerMode;
    
    printState("Power mode changed: " + std::to_string(oldMode) + 
               " -> " + std::to_string(powerMode));
    
    // 保留原有的清理逻辑
    if (currentWarning_) {
        notifyHMI(currentWarning_->id, false);
        notifyChime(currentWarning_->id, false);
        currentWarning_ = nullptr;
    }
    
    timers_.resetAll();
    cleanupInactiveWarnings();

    if (powerMode == PowerMode::PWR_MODE_RUN) {
        timers_.startStartupDelay();
        printState("Started 300ms startup delay for RUN mode");
        return;  // 延迟后再处理报警
    }
    
    // 其他模式立即处理
    auto nextWarning = getNextPendingWarning();
    if (nextWarning) {
        setCurrentWarning(nextWarning);
    }
}

void AlarmEngine::clearAllWarnings() {
    LOG_TAG_INFO("Clearing all warnings on startup");
    
    // 清除当前显示的报警
    if (currentWarning_) {
        notifyHMI(currentWarning_->id, false);
        notifyChime(currentWarning_->id, false);
        currentWarning_ = nullptr;
    }
    
    // 清除所有报警映射和队列
    warningMap_.clear();
    pendingQueue_.clear();
    
    // 重置定时器
    timers_.resetAll();
    
    // 通知Android清除状态
    notifyAndroid();
    
    LOG_TAG_INFO("All warnings cleared");
}

void AlarmEngine::setActiveSeatbeltsAndSpeedOk(bool driver, bool passenger, bool rl, bool rm, bool rr,bool isSpeedOk) {
    activeSeatbeltsAndSpeed_.driver = driver;
    activeSeatbeltsAndSpeed_.passenger = passenger;
    activeSeatbeltsAndSpeed_.rl = rl;
    activeSeatbeltsAndSpeed_.rm = rm;
    activeSeatbeltsAndSpeed_.rr = rr;
    activeSeatbeltsAndSpeed_.isSpeedOk = isSpeedOk;
}

bool AlarmEngine::hasOnlyRearSeatbeltUnfastened() const {
    bool frontUnfastened = activeSeatbeltsAndSpeed_.driver || activeSeatbeltsAndSpeed_.passenger;
    bool rearUnfastened = activeSeatbeltsAndSpeed_.rl || activeSeatbeltsAndSpeed_.rm || activeSeatbeltsAndSpeed_.rr;
    bool isSpeedOk = activeSeatbeltsAndSpeed_.isSpeedOk;
    return !frontUnfastened && rearUnfastened && isSpeedOk;
}


void AlarmEngine:: handleBackSecubelt() {
    bool frontUnfastened = activeSeatbeltsAndSpeed_.driver || activeSeatbeltsAndSpeed_.passenger;
    bool rearUnfastened = activeSeatbeltsAndSpeed_.rl || activeSeatbeltsAndSpeed_.rm || activeSeatbeltsAndSpeed_.rr;
    //如果前排安全带触发了 那么needTriggerBackSecubelt_ = true; 表示仅后排安全带此次上电仍要触发
    if(frontUnfastened)
    {
        needTriggerBackSecubelt_ = true;
    }
}

} // namespace WarningModule
